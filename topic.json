[{"topic_id": "7560", "论文题目": "基于Pytorch的图像分类系统的构建", "论文类型": "实验研究", "论文内容": "基于Pytorch的图像分类系统（OpenCV、水果蔬菜分类、安卓端部署）的构建", "出题教师": "杨永霞", "所属系": "电子信息工程系"}, {"topic_id": "7561", "论文题目": "基于Hadoop、Hive和Spark的电商用户行为可视化分析系统", "论文类型": "实验研究", "论文内容": "基于Hadoop、Hive和Spark的电商用户行为可视化分析系统的构建", "出题教师": "杨永霞", "所属系": "电子信息工程系"}, {"topic_id": "7562", "论文题目": "基于大数据Hadoop、Hive/Spark的民宿可视化分析系统", "论文类型": "实验研究", "论文内容": "基于大数据Hadoop、Hive/Spark的民宿可视化分析系统的构建", "出题教师": "杨永霞", "所属系": "电子信息工程系"}, {"topic_id": "7620", "论文题目": "超声驻波悬浮系统的可控移动研究", "论文类型": "实验研究", "论文内容": "超声悬浮技术是一种先进的非接触传输技术，具有不需要与容器接触的优势；在需要超高纯度的生化制药、半导体制造以及化学合成等领域具有广泛的应用前景。但由于缺乏可靠的可控移动技术，目前超声悬浮还多停留在实验室阶段。本研究的目的是通过对基于换能器的超声驻波系统的研究，实现可控的超声悬浮移动。\r\n具体内容：\r\n1.利用单片机实现1个正弦信号的输出，要求：频率约20000-50000Hz，电压峰值约4~20V，相位360°可调。\r\n2.  将正弦波信号输出到换能器上，对向接一个反射面（自行设计），最终可形成驻波悬浮。\r\n3. 将反射面装载在由单片机控制的可移动支架上，实现至少一个维度的可控移动。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7627", "论文题目": "基于STM32的智能中药泡药煎药机", "论文类型": "实验研究", "论文内容": "中医的使用在广东地区有着悠久的历史传统。然而，中药的煎制需要经过泡药、煎药等消耗大量时间的过程。本设计通过在传统煎药壶上增加智能化设备，将上述过程自动化，从而使得普通上班族只需上班前作简单的设定，即可在下班后服用上煎制好的药物。", "出题教师": "贺帅", "所属系": "电子信息工程系"}, {"topic_id": "7629", "论文题目": "超声悬浮大尺寸物体研究", "论文类型": "实验研究", "论文内容": "超声悬浮技术是一种先进的非接触传输技术，具有不需要与容器接触的优势；在需要超高纯度的生化制药、半导体制造以及化学合成等领域具有潜在应用前景。但由于原理限制，很长一段时间超声悬浮技术只能对远小于半波长的物体进行悬浮，难以悬浮尺度近于或大于半波长的物体，这极大的限制了它的广泛应用前景。本研究的目的是通过研究构建多换能器驻波共振系统，实现尺度近于或大于半波长的大颗粒物体的超声悬浮。\r\n具体内容：\r\n1.利用单片机实现至少2以上个正弦信号输出，要求：频率约20000-50000Hz，电压峰值约4~20V，相位360°可调。\r\n2.  将正弦波信号输出到一个或多个换能传感器上。\r\n3. 基于3D打印设备，设计并制备不同形态的换能传感器阵列或反射板形态，形成特殊的驻波声场，实现一个尺寸接近或大于半波长物体的超声悬浮。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7674", "论文题目": "基于机器学习的超声悬浮阵列可控移动研究", "论文类型": "实验研究", "论文内容": "超声悬浮技术是一种先进的非接触传输技术，具有不需要与容器接触的优势；在需要超高纯度的生化制药、半导体制造以及化学合成等领域具有潜在应用前景。但由于原理限制，很长一段时间超声悬浮技术只能对远小于半波长的物体进行悬浮，且难以实现大尺度的可控移动，这极大的限制了它的广泛应用前景。本研究的目的是通过构建超声换能器阵列，通过机器学习，实现被悬浮物体的可控移动。\r\n具体内容：\r\n1.利用单片机实现至少2以上个正弦信号输出，要求：频率约20000-50000Hz，电压峰值约4~20V，相位360°可调。\r\n2.  将正弦波信号输出到一个或多个换能传感器上。\r\n3. 基于3D打印设备，设计并制备换能传感器阵列。\r\n4. 利用机器学习，在不对阵列声场进行模拟的情况下，通过训练，智能控制阵列中的超声换能器的输出功率，相位等参数，实现被悬浮物体的可控移动。\r\n选该选题的同学，要求对机器学习人工智能比较了解。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7739", "论文题目": "基于蓝牙的土壤数据采集终端系统设计", "论文类型": "工程设计", "论文内容": "本文要求构建基于蓝牙的土壤数据采集系统，该系统要以STM32嵌入式微控制器为硬件核心，利用空气温湿度传感器、土壤湿度传感器、土壤氮磷钾传感器以及土壤多参数传感器来采集土壤环境的多种数据，然后通过蓝牙模块或者ZigBee模块将采集的数据发送出去，以供用户在手机终端或者PC端进行数据的接收查看。本系统支持蓝牙数据传输模式和ZigBee数据传输模式，用户可根据实际需要进行模式切换。系统还要配套报警模块和控制灌溉模块，当土壤环境的某个参数值超出一定的范围之后，对应的LED灯会亮起报警，当土壤湿度低于设定的最小值时，蜂鸣器响起报警，同时继电器吸合触发水泵进行抽水灌溉，当土壤湿度达到设定的最大值时，蜂鸣器停止报警,水泵停止工作。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7740", "论文题目": "基于云平台和WiFi的智能家居系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一款基于云平台和WiFi的智能家居系统。主要研究内容如下：采用WiFi作为系统的无线通信方式实现数据的传输，选用One NET云平台作为系统的远程监控工具，选用STM32为主控模块，其他模块包括WiFi模块、传感器模块、终端控制模块、电源模块等。同时基于One NET云平台开发一款手机APP，实现手机APP对智能家居系统的控制。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7741", "论文题目": "基于OneNET云平台及ai的智能家居总控系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一款基于OneNET云平台及ai的智能家居总控系统，实现对家中卧室、厨房等环境信息的监测和对家中电器的智能控制。该系统设计以OneNET云平台为核心，实现感知层和用户层的双向通信。在WxBit图形化编程平台系统(ai)上开发一款智能家居app，多线程控制多个STM32硬件系统，实现由一个手机软件控制多个硬件系统的功能。以OneNET云平台为数据中转站实现手机软件与家中厨房硬件系统和卧室硬件系统双向通信，并将OneNET云平台接收的数据流信息存入数据库记录。硬件系统选择STM32F103C8T6芯片为主控器，由传感器采集温度、湿度、光照强度、烟雾浓度等环境数据，并与其他电器联动实现自动控制；语音模块采用LD3320；使用按键控制各终端元器件实现手动控制；使用ESP8266模块通过WiFi6代技术和EDP传输协议实现与OneNET云平台双向通信。在OneNET云平台上设计触发器功能，当家中出现火灾或者天然气泄露时会立即发送预警并及时做出避险措施。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7743", "论文题目": "智能鱼缸系统设计", "论文类型": "工程设计", "论文内容": "本系统要求以STM32F407ZGT6微控芯片为系统核心，采用多种传感器对水质进行实时监测，并通过嵌入式系统进行数据采集和处理，实现对水质的自动调节和控制。此外，该系统还要集成自动投喂、增氧、补光等功能，能够有效维护鱼缸内的生态环境，提高鱼类的生存率和健康程度。\r\n整个系统包括两个部分，第一部分是以STM32F407ZGT6为核心的控制模块，完成对各个传感器检测数据的获取和存储，同时通过各类继电器控制增氧和换水等外部设备，以及通过WiFi模块将收集到的数据发送至云平台。第二部分是通过手机端APP来接收采集到的鱼缸内各类数据和发送相关指令操作，收集到的数据参数会实时显示到手机端，在此基础上用户可以在手机端设置相应参数来完成对各类硬件模块的控制。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7744", "论文题目": "基于STM32的矿工安全监测系统设计", "论文类型": "工程设计", "论文内容": "本文研究的内容总体上包括上位机和下位机两个部分。在下位机部分，系统以STM32单片机为核心控制器，通过LMT70温度传感器、ADS1292心率传感器、DW1000射频收发器和MPU6050运动传感器分别对人员的体温、心率、位置和加速度信息进行采集。系统下位机部分采用OLED屏幕显示人员的体温、心率以及位置，并且通过阈值判断，在数据出现异常时发出提醒。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7745", "论文题目": "基于STM32的驾驶行为监测系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一种基于STM32的双MCU(Micro Control Unit,MCU)驾驶行为监测系统，分为车载硬件终端和后台管理系统两个部分。通过采集惯性传感器数据，在终端完成对驾驶行为的识别判断，并对驾驶行为发生时的时间、定位等信息进行保存。使用4G将终端识别到的驾驶行为数据发送给后台管理系统，实现驾驶行为的统一管理。对急加速、急减速、急左转、急右转四种典型激烈驾驶操作行为及疲劳驾驶行为进行了特征分析，实现驾驶操作行为的识别判断；通过分析车辆不同状态下的惯性数据特征，实现车辆状态判断，进而对疲劳驾驶做出识别。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7746", "论文题目": "基于STM32的空气质量指数预测系统设计", "论文类型": "工程设计", "论文内容": "本文主要的研究工作如下:(1)提出面向室内空气质量指数的预测模型，实现对室内空气质量指数的高精度预测。(2)设计一个空气质量指数预测系统。系统由预测节点、无线通信节点和物联网平台3个部分组成。预测节点选用STM32系列芯片作为主控单元，其上部署预测模型。预测节点配备的传感器将采集数据按预测模型要求重构后输入模型，以获取实时预测结果，产生的相关数据由无线通信节点发往物联网平台，以Web界面的形式实现数据展示和远程控制。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7747", "论文题目": "基于STM32的土壤水分监测系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一套基于STM32的土壤水分监测系。主要研究内容如下：(1)设计基于STM32的土壤水分监测终端，该终端采集土壤含水量及其他影响含水量变化的主要环境因素数据，可将采集的数据通过LCD屏实时显示。同时设计土壤水分后台管理系统，该后台管理系统具有处理及存储土壤水分数据、远程配置监测终端相关参数等功能。(2)针对土壤含水量预测实时性低的问题，提出高实时性的土壤含水量预测方法。采用基于误差反向传播神经网络模型的建模方法，建立土壤含水量预测模型。最后将训练好的预测模型移植到STM32中，利用监测终端采集的数据对土壤含水量进行实时预测，并通过LCD屏实时显示预测结果。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7748", "论文题目": "基于STM32的人体生理信息采集系统设计", "论文类型": "工程设计", "论文内容": "本文要求利用STM32核心处理器作为控制器设计一款可穿戴的多路生理信息采集设备，可以实时监测人体多种生理信息，比如皮肤电、心率、温度、湿度、手腕姿态等。这些生理信息数据可以在采集设备的本地存储器存储，也可以通过采集设备的蓝牙芯片连接到智能手机或个人电脑上进行存储显示和分析。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7749", "论文题目": "基于STM32的智慧农业监测系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一种基于STM32的农业环境监测系统，结合嵌入式技术、物联网技术、人工智能技术、大数据技术，实现采集、传输、存储、查询并分析农作物的环境信息，智能控制农业外设，以科学手段指导农业生产。本文设计的智慧农业监测系统由数据采集模块、通信模块、用户终端、外设 4 个部分组成。通过空气温湿度、光照强度、土壤温湿度及氮磷钾含量、土壤 PH 值传感器采集数据，并通过 ZigBee 协议传输数据，用户可通过手机 APP 或者 PC 端直观查看环境的变化同时进行相应的外设调控。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7750", "论文题目": "基于语音识别的智能家居控制系统设计", "论文类型": "工程设计", "论文内容": "本设计要求采用 SNR6812 作为语音识别模块，STM32F103C8T6 作为主控制器，以实现家居系统的语音智能控制。系统具有温湿度采集，光照强度采集，电机驱动控制，液晶显示等功能。在保留手动控制的基础上，能实现灯光、风扇等家用设备的智能语音控制，并能够显示家居环境的系统信息。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7751", "论文题目": "无线可视化智慧农业管理系统设计", "论文类型": "工程设计", "论文内容": "本文设计开发一套无线可视化智慧农业管理系统，系统通过布置于农业现场的各传感器采集空气温度、空气湿度、光照强度、土壤湿度等环境参数。一方面，农户通过电脑客户端、手机 APP 实时监测农作物生长环境，并远程控制温室大棚进行灌溉、通风、遮光、采光等。另一方面，根据农作物与季节的改变，合理设置农业现场设备的工\r\n作阈值，以提高温室大棚智能决策模式下的控制精度，实现农业生产的精准管理。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7753", "论文题目": "基于数据融合的仓储环境管理系统设计", "论文类型": "工程设计", "论文内容": "本文设计一套仓储环境监控和管理系统。通过由温湿度传感器、光照传感器、红外传感器等组成的数据采集终端，对仓库的环境数据进行采集，再将环境信息发送到服务器进行数据整合、信息融合，最后用户可以通过pc客户端或手机端查看仓库的实时环境数据，并向数据采集终端发送控制命令，控制饲料仓库中的控制设备，如LED灯等设备，从而实现仓库环境信息的实时监控。最后基于系统客户端，对整个仓库的管理员登录权限、仓库环境信息、仓库控制设备和仓库货物信息进行管理。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7754", "论文题目": "餐厅服务机器人交互系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一款面向餐厅的服务机器人交互系统，包括人机交互面板系统、点餐前台软件系统、点餐后台软件系统；要求对基于人机交互系统的每个单元模块进行详细的功能需求描述，并对如何提高系统的性能进行具体分析；然后对人机交互系统的每个单元模块进行硬件方案选择与设计，并对其主控模块、显示触摸模块、语音播放模块、无线通信模块、存储模块等进行具体的硬件电路设计；接着对人机交互系统进行软件设计，包括交互面板系统软件设计、点餐前台软件设计、点餐后台软件设计。最终实现系统的集成以及应用。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7755", "论文题目": "基于人脸识别的智能安防监控系统设计", "论文类型": "工程设计", "论文内容": "本文要求的基于人脸识别的智能安防监控系统，主要由非法入侵实时报警系统、智能门禁系统、智能巡更系统组成。非法入侵实时报警系统可以准确识别非法人员的人脸信息并实时推送给相关人员进行报警；智能门禁系统可以有效防范人脸攻击；智能巡更系统可以通过巡更区域布控的监控设备实时采集并识别巡更人员信息，并实时绘制巡更轨迹，能极大提高巡更管理的效率。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7756", "论文题目": "一种语音控制的智能机器人", "论文类型": "工程设计", "论文内容": "本文研究并设计一种智能机器人，该机器人以STM32单片机作为控制核心，硬件包括电源模块、显示模块、交互模块及电机控制模块等，硬件电路分为底盘控制、身体控制与头部控制三部。软件包括嵌入式系统C语言程序和以Android系统为平台开发的软件程序，可精准识别语音指令，并进行语音控制，同时机器人参数可在系统软件平台内进行个性化设置。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7758", "论文题目": "基于目标检测的智能照明系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计一个智能照明控制系统，该系统通过读取通过网络传输的教室监控视频流，实时在视频流上进行人员检测，按照人员的区域分布控制对应区域的灯具开关，实现照明控制的智能化。其中使用头部检测的方法对教室内人员进行检测，通过将公开数据集与自制数据集相结合的方式训练目标检测模型。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7759", "论文题目": "基于嵌入式Linux的智能网联车载终端系统设计", "论文类型": "工程设计", "论文内容": "本文以智能网联汽车为应用背景，利用嵌入式技术、GPS定位技术、4G通信技术和串口通信技，设计一套智能网联车载终端系统，实现车辆导航定位，车辆状态信息采集存储上传以及对车辆的远程在线监控等。系统硬件部分采用基于ARM内核的芯片作为核心处理器，外围搭载GPS定位模块、EC204G通信模块、LCD触摸显示模块、电源模块以及串口调试模块。系统软件部分采用嵌入式Linux操作系统作为终端的运行控制平台，设计并实现车载终端系统工作的应用程序，主要包含GPS定位、4G无线传输、身份认证、司机考勤、用户暂离以及GUI界面设计等。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7760", "论文题目": "基于ESP32的智能家居系统的设计", "论文类型": "工程设计", "论文内容": "本文要求设计基于ESP32的智能家居系统，通过人工视觉传感器、触摸传感器和人体感应传感器，实现门禁识别、音乐门铃和入侵检测功能；利用温湿度传感器、光敏传感器、雨滴传感器和红外遥控实现对风扇、窗帘、窗户和灯光的控制；使用火焰传感器、气体传感器和环境传感器感知预测家中环境质量。系统终端通过无线通信的方式将设备采集的数据传输到One NET物联网云平台，One NET物联网云平台用于创建产品设备，管理数据流，实现设备与云平台之间的信息通信，并将采集的数据源用图表的形式展现在客户端。OneNET物联网云平台还具有数据存储功能，云平台上可以实时的显示监测数据信息，发布消息指令，远程控制家用电器设备。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7787", "论文题目": "基于STM32的数控开关电源设计", "论文类型": "工程设计", "论文内容": "本文要求设计一种基于STM32的数控开关电源，其主拓扑选用移相全桥软开关DC/DC变换结构，建立移相全桥软开关变换器的小信号模型，并求出移相全桥变换器的传递函数，选取控制效果较优的模糊自适应PID作为本设计的控制算法。最后以Matlab/Simulink为仿真平台，对数控电源系统进行仿真研究，设计搭建以控制芯片STM32为核心的电源样机。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7789", "论文题目": "基于物联网的仓储环境监测系统设计", "论文类型": "工程设计", "论文内容": "本文要求设计基于物联网的仓储环境安全监测系统，硬件设计主要包括低功耗蓝牙从机数据采集单元、主机数据中心单元两部分；软件设计主要实现蓝牙主从机间的通信、仓储环境参数的采集及传输、PC上位机数据监测中心软件对数据进行处理、显示及上传至服务器，服务器(OneNet)接收存储显示数据并可提供网络服务，最终实现仓储远程环境监测，并提供火灾和意外入侵报警等功。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7799", "论文题目": "台用太阳能照明系统", "论文类型": "工程设计", "论文内容": "设计一款太阳能充电及照明系统。当阳光强度足够时，可以直接对负载进行充电。需要有充电电流、电压的测量显示与温度的监控功能。充满电后必须停止充电，避免过充。太阳能储能并", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7800", "论文题目": "手机无线扩音系统", "论文类型": "软件开发", "论文内容": "要求实现从手机麦克风作作为声源采集，利用手机WIFI无线网路传输到扩音系统或者电脑进行扩音。需要做实验评测。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7801", "论文题目": "stm32单片机的智能风扇控制系统", "论文类型": "工程设计", "论文内容": "依据温度对风扇转速的反馈控制、手机的人工控制与风扇状况的监控显示，房间温度显示。需要实验评估", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7802", "论文题目": "太阳能锂电池充电系统", "论文类型": "工程设计", "论文内容": "设计一款太阳能充电器及其系统。当阳光强度足够时，可以直接对负载进行充电。需要有充电电流、电压的测量显示与温度的监控功能。充满电后必须停止充电，避免过充。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7803", "论文题目": "加湿雾化蒸脸装置的设计", "论文类型": "工程设计", "论文内容": "设计一款具有加热控制、雾化、数据记录、手机控制的加湿蒸脸器。需要实验验证。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7804", "论文题目": "人脸识别的门禁系统", "论文类型": "工程设计", "论文内容": "设计一款具有人脸识别门禁系统，并具有视频手机监控功能的门禁系统。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7805", "论文题目": "多功能门禁系统", "论文类型": "工程设计", "论文内容": "设计一款具有指纹、密码、以及手机等多种门禁开关模式，并具有视频手机监控功能的门禁系统。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7806", "论文题目": "微信排队叫号小程序分析与设计", "论文类型": "工程设计", "论文内容": "设计一个排队叫号系统，该系统可以高度自定义，并适用于多种场景的排队叫号也无需要求。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7807", "论文题目": "滴液检测与控制系统", "论文类型": "工程设计", "论文内容": "设计一个医用滴液控制系统。该系统能够自动检测到低液面位置，提醒医务人员，并停止滴液。要求做出下位机电路板与装置。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7808", "论文题目": "基于智能手机的网约车计费系统", "论文类型": "工程设计", "论文内容": "设计一个带地图的网约车计费系统，该系统需要考虑各种收费条件。可以高度自定义，并适用于多种场景的网约车旅程计费。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7809", "论文题目": "基于STM32的智能称重系统", "论文类型": "工程设计", "论文内容": "设计一款能与手机进行交互的电子秤系统，其具有量程设置，APP显示，超测显示等功能。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7810", "论文题目": "基于STM32的倒车雷达系统", "论文类型": "工程设计", "论文内容": "设计一款倒车雷达系统。该系统具有多个雷达，其具有图像提示功能，并具有语音提示。能够预警倒车安全。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7811", "论文题目": "基于深度学习的语音去燥研究", "论文类型": "实验研究", "论文内容": "掌握用深度学习对语音信号去噪的原理，并进行模型训练，对含噪语音信号实现去噪，并评估MMSE算法以及深度学习算法的去噪性能。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7812", "论文题目": "具有反馈控制的音频功放研究", "论文类型": "实验研究", "论文内容": "设计一款D类音频功放。利用PWM进行调制控制，设计H桥电路，设计高通滤波器进行输出过滤，设计其PCB，需要进行实验PCB板级评估验证或仿真测试。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7813", "论文题目": "二维码图像处理研究", "论文类型": "实验研究", "论文内容": "研究商用二维码的生成步骤与流程，研究二维码识别中的预处理算法，并对识别算法的速度、精确性进行实验评估。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7814", "论文题目": "基于深度学习的图像边缘检测研究", "论文类型": "实验研究", "论文内容": "研究深度学习架构，评估用于图象边缘检测的深度学习算法及其优缺点，实现对边缘检测的性能优化。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7815", "论文题目": "基于深度学习的医学图像分割研究", "论文类型": "实验研究", "论文内容": "研究深度学习架构，评估用于医学图象分割的深度学习算法及其优缺点，实现对分割算法的性能优化。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7840", "论文题目": "D类音频功放设计", "论文类型": "工程设计", "论文内容": "设计一款D类音频功放。利用PWM进行调制控制，设计H桥电路，设计高通滤波器进行输出过滤，设计其PCB，需要进行实验PCB板级评估验证或仿真测试。", "出题教师": "李宇", "所属系": "电子信息工程系"}, {"topic_id": "7842", "论文题目": "摩托车智能防盗系统的设计", "论文类型": "工程设计", "论文内容": "该设计主要采用以下原理；\r\n1. GPS定位技术：防盗器装备了GPS模块，通过接收卫星信号可以准确地定位摩托车的位置。一旦摩托车被盗，车主可以通过手机或其他设备追踪摩托车的实时位置。\r\n\r\n2. 震动传感器：防盗器内置了震动传感器，当有外部震动或撬动摩托车时，传感器会感知到并触发报警。这可以有效地吓退盗贼或引起周围人士的注意。\r\n\r\n3. 高分贝警报器：防盗器通常还搭载了一个高分贝的警报器。当传感器触发报警时，警报器会发出响亮的声音，以吸引更多人的注意，从而增加抓捕盗贼的可能性。\r\n\r\n4. 远程控制功能：一些摩托车防盗器还具有远程控制功能，车主可以通过手机或钥匙遥控器来激活或解除防盗器。这对于提供方便的同时也增加了摩托车的安全性。\r\n\r\n5. 电子锁技术：一些高级的防盗器采用了电子锁技术。这种锁机制通过使用密码或指纹识别来解锁，提供更高的安全性，防止盗贼暴力撬锁。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7843", "论文题目": "一种液体流量计的设计", "论文类型": "工程设计", "论文内容": "该设计主要采用以下原理（选择其中一种）：\r\n\r\n1. 差压流量计原理：\r\n差压流量计是一种常用的液体流量计，原理基于流体流动时的定律。差压流量计通常由一个流量测量元件和一个差压变送器组成。流体通过流量测量元件，例如流体进入一个流量节流装置，流体流过节流装置时会形成压力差，差压变送器测量这个压力差，并将其转换为流体流量。\r\n\r\n2. 电磁流量计原理：\r\n电磁流量计是利用液体通过导电体时，产生的感应电磁场的变化来测量流量的原理。电磁流量计通常由一个电磁流量传感器和一个转换器组成。流体通过电磁流量传感器时，通过导电性液体和电极之间的电压差来测量流量。由于液体的流速会影响电磁感应的电压差，因此可以根据测量的电压差来计算流量。\r\n\r\n3. 超声波流量计原理：\r\n超声波流量计是一种利用超声波传播在流体中的速度差来测量流量的原理。超声波流量计通常由一个超声传感器和一个转换器组成。超声波传感器发射超声波，并测量超声波传播在流体中的速度。根据声速的差异，可以计算流体的流速，进而得到流量。\r\n\r\n4. 质量流量计原理：\r\n质量流量计是利用物质的质量来测量流量的原理。质量流量计通常由一个物质传感器和一台质量流量计仪表组成。物质通过传感器时，传感器会测量物质的质量，并将其转换为流体的质量流量。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7844", "论文题目": "宿舍智能防火防盗系统的设计", "论文类型": "工程设计", "论文内容": "该设计是基于ARM处理器内和LM3S1138为主控制器的宿舍智能防火防盗报警系统。该系统可以判断宿舍是否发生火情，检测人员进出及非法入室情况，监测不同贵重物品的移动情况。宿舍节点控制器接收信号实现声光提示和液晶显示，并通过无线通讯模块传送给监控机实现异地监控，一部监控机可以远程监控多个宿舍，有利于学生宿舍的安全管理。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7845", "论文题目": "一种电子指南针的设计", "论文类型": "工程设计", "论文内容": "该设计的电子控制系统的核心采用的是 stm32 单片机自动控制系统，，借助于先进的磁场传感器，勘测并且获取所在地位和区域的磁场强度，依据勘测的相关数据，同时结合设定好的磁场数据，换算出角度，同时结合实际情况的强度变化，平衡偏差，进而获取现有的位置数据。电子指南针主要STM32F103C8T6单片机、LCD1602液晶显示、GY-271模块及电压组成。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7846", "论文题目": "基于USB接口的指纹图像采集与处理的研究", "论文类型": "工程设计", "论文内容": "整体功能：\r\n实现指纹的采集录入。\r\n实现指纹的对比，并展示对比结果，用LED灯和蜂鸣器提示。\r\n指纹信息存储到Flash的文件系统。\r\n实现USB模拟U盘，可连接计算机，在计算机上实现指纹录入的数据。\r\n\r\n所需核心器件为ATK-AS608 模块指纹识别模块和STM32单片机", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7851", "论文题目": "一种高精度的电子秤设计", "论文类型": "工程设计", "论文内容": "本设计采用STM32系列芯片，具有高性能、低成本、低功耗的嵌入式应用专门设计的ARM Cortex-M3内核，时钟频率可达72M,内置32K到128K的闪存，价格同比其他32位产品更低。因此本设计采用STM32F103RBT6作为主控制芯片，对数据进行采集，存储，显示，收送。\r\n整个电路可分为4个模块：单片机主控器、测量模块、IC卡读写模块、OLED显示模块。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7855", "论文题目": "图像增强算法研究与实现", "论文类型": "实验研究", "论文内容": "数字图像处理是指将图像信号转换成数字格式并利用计算机对其进行处理的过程。图像增强是数字图像处理的过程中经常采用的一种方法，它对提高图像质量起着重要的作用。\r\n\r\n该设计要求采用直方图增强、对比度增强、平滑和锐化等几种常用的增强方法，通过 Matlab 实验得出的实际处理效果来对比各种算法的优缺点，讨论不同的增强算法的技术要点，并对其图像增强方法进行性能评价。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7857", "论文题目": "图像分割算法研究与实现", "论文类型": "实验研究", "论文内容": "图像分割是数字图像处理中的关键技术之一。图像分割时将图像中有意义的特征部分提取出来，其有意义的特征有图像中的边缘、区域等，这是进一步进行图像识别、分析和理解的基础，虽然目前已研究出不少边缘提取，区域分割的方法，但还没有一种普通适用于各种图像的有效方法。\r\n\r\n该设计要求采用常用的图像分割，比如阈值法，区域法等，并比较不同分割算法的性能。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7858", "论文题目": "语音降噪算法研究与实现", "论文类型": "实验研究", "论文内容": "语音去噪(noise reduction)又被称为语音增强(speech enhancement)，主要是针对于有人声的音频进行处理，目的是去除那些背景噪声，增强音频中人声的可懂性(intelligibility)。其应用范围很广，可以用于人与人之间的语音通讯，也可以用于很多语音任务的预处理，比如Automatic speech recognition。\r\n\r\n该设计要求综述常用的语音增强算法，并采用三种以上降噪方法，实现语音降噪，比较其性能。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7860", "论文题目": "说话人识别算法研究与实现", "论文类型": "实验研究", "论文内容": "说话人识别（Speaker Recognition，SR），又称声纹识别（Voiceprint Recognition,VPR），顾名思义，即通过声音来识别出来“谁在说话”，是根据语音信号中的说话人个性信息来识别说话人身份的一项生物特征识别技术。\r\n\r\n该设计要求采用三种以上识别算法实现说话人识别，并分析不同算法的原理和特点。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7868", "论文题目": "一种基于单片机的照明控制系统设计", "论文类型": "工程设计", "论文内容": "基于51单片机的智能照明控制系统是一种高效、节能的智能化解决方案，它利用51单片机作为控制核心，结合传感器技术、通信技术等，实现对室内照明的智能化控制，以满足不同场景下的照明需求。\r\n该设计主要满足以下功能要求：\r\n 功能设计\r\n1、使用两个红外对管来检测是否有人进入办公室并进行人数统计，人数最多统计到二位数99人，并实时将人数显示到液晶屏LCD上，液晶屏同时显示实时日期/时间/星期；\r\n\r\n2、使用4个LED灯模拟办公室的照明灯，在符合条件开启时，人数为0时灯不亮，人数小于10人亮一个灯，10-20人亮二个灯，20-30人亮三个灯，大于30人则全亮四个灯；\r\n\r\n3、系统分自动/手动模式，可以通过按键切换模式，并有LED指示当前所在模式；\r\n\r\n4、在自动模式下，可以设定定时时间段，在定时时间段内，当办公室有人（人数大于0）的情况下，如果光线暗弱则自动打开照明灯，照明灯点亮个数根据人数而定，不在定时时间段或者办公室无人的情况下，关闭所有照明灯；\r\n\r\n5、另外在手动模式下，可以通过手动开关控制照明灯的亮灭，人数统计部分仍然生效；\r\n\r\n6、利用光敏电阻检测办公室的光线强弱；", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7869", "论文题目": "一种基于单片机的专用信号发生器", "论文类型": "工程设计", "论文内容": "信号发生器的设计任务就是产生三路信号，并且提供和主机通讯的软硬件接口。首先根据输出信号的频率和幅值进行编码，存储在单片机的ROM里，然后以一定的时间间隔依次将这些数字量送往 D/A进行转换输出，这样，只要循环不已的送数，在D/A的双极性输出端就可以得到信号波形。信号的输出时序受上位机控制。\r\n\r\n本设计采用AT89C4051组成一个最小的单片机系统。AT89C4051是Atmel公司的一款基于MSC51 内核的简化单片机，指令与标准的51单片机兼容，带有4K可重新编程片上程序存储器，128B的数据存储器，多达15条可编程I/O线，两个16位定时器 /计数器，片上模拟比较器，一个标准串行通讯口，内部带有振荡器和时钟电路。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7870", "论文题目": "基于Linux的智能家居系统的设计", "论文类型": "工程设计", "论文内容": "该设计要求，利用ARM架构的S3C6410作为手持终端的主控芯片，以Linux系统作为平台，QT设计友好的人机交互界面。结合多个传感器进行採集。使用TI的CC2530作为网络传感器和房子内部电器的控制器。而且利用串口通信实现数据的交互，利用RFID的门禁钥匙。通过PC机上的client软件实现远程的智能家居系统监控", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7871", "论文题目": "基于Linux的智能门禁系统设计", "论文类型": "工程设计", "论文内容": "指纹门禁系统是基于生物特征识别技术的一项高科技安全设施，近年来在国内外得到了广泛的应用，并已成为现代化建筑智能化的标志之一。对于一些核心机密部门，如重要机关、科研实验室、档案馆、民航机场等场所，指纹门禁系统可以提供高效、智能、便捷的授权控制。由于指纹具有携带方便、人人各异、终生不变的特点，因此利用指纹识别作为身份认证的手段，与传统的钥匙、密码相比，大大提高了安全性与可信性。\r\n\r\n该系统基于ARM芯片为核心器件，以指纹采集芯片作为硬件平台，以嵌入式Linux为软件平台。开发一套简便安全的门禁系统。", "出题教师": "徐文华", "所属系": "电子信息工程系"}, {"topic_id": "7903", "论文题目": "基于STM32的智能宠物投喂器", "论文类型": "工程设计", "论文内容": "本文设计的智能宠物喂食器通过STM32微控制器连接RTC模块实现定时功能，通过WiFi模块实现远程控制，通过电机驱动模块控制直流电机实现喂食。系统包括定时控制模块、远程控制模块和电机控制模块。本智能投喂器可以应用于宠物的定时喂食，通过RTC模块设置每日的喂食时间，自动控制喂食器，确保宠物的饮食规律。还可以通过WiFi模块实现远程控制喂食，用户可以通过手机或电脑随时随地控制喂食器，方便管理宠物的饮食。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "7963", "论文题目": "窗户的智能控制研究", "论文类型": "实验研究", "论文内容": "1. 通过雨水传感器，实现雨天/晴天的窗户智能开关控制。\r\n2. 通过气体传感器，当屋内甲醛/二氧化碳/煤气浓度过高时，窗户的智能开关。\r\n3. 通过湿度传感器，实现回南天窗户智能开关。\r\n4. 智能防夹功能。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7964", "论文题目": "窗帘的智能开关设计研究", "论文类型": "实验研究", "论文内容": "一般窗户有两层窗帘组成，一层为不透光的厚帘，一层为透明薄纱。\r\n通过研究，实现下面场景的智能控制：\r\n1. 屋内无人时，打开两层窗帘，透风透气。\r\n2. 白天有人时，打开厚帘，关上薄纱，透光不透人。\r\n3. 午休时，定时关上厚帘；午休到，厚帘定时打开，并与闹钟唤醒联动。\r\n4. 晚上开灯时，关上厚帘；关灯后，厚帘打开，关上薄纱。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7968", "论文题目": "空调的智能控制研究", "论文类型": "实验研究", "论文内容": "1. 通过温度传感器，实现对空调的智能开关控制。\r\n2. 通过光线传感器，对白天或黑夜（睡眠环境下），实现空调风量和输出功率的智能控制。\r\n3. 通过湿度传感器，实现回南天智能除湿功能。\r\n4. 可远程操控，定时开关。", "出题教师": "谭植元", "所属系": "电子信息工程系"}, {"topic_id": "7985", "论文题目": "智能加药控制系统的设计", "论文类型": "工程设计", "论文内容": "设计一款基于单片机的加药控制系统，该系统能够分时、分量对加药的剂量进行控制。具体要求如下：\r\n1.通过监控药液的重量来控制加药的剂量；\r\n2.能够通过触摸屏或者APP进行分时、分量控制。\r\n3.当药液液面低于阈值时能够自动进行补液。\r\n4.分析液面高度与控制精度之间关系，并进行修正。", "出题教师": "李培森", "所属系": "电子信息工程系"}, {"topic_id": "7986", "论文题目": "基于单片机的太阳能追踪系统开发", "论文类型": "工程设计", "论文内容": "设计一款基于单片机的太阳能追踪系统，该系统能够自动驱动太阳能板指向太阳，以获取最大的光照。具体要求如下：\r\n1. 通过检测是否下雨决定是否开启追踪系统；\r\n2. 该能够控制太阳能板上下及左右转动；\r\n3. 能够采用两种形式实现自动追踪，覆盖阴天和晴天。", "出题教师": "李培森", "所属系": "电子信息工程系"}, {"topic_id": "7987", "论文题目": "基于单片机的车灯智能控制系统", "论文类型": "工程设计", "论文内容": "设计一种汽车灯光智能控制系统，该系统能够依据不同外部条件实现对灯光的智能控制。具体要求如下：\r\n1. 根据外部光照和对向车辆的距离实现远近灯的智能切换。\r\n2. 根据车辆行驶方向和地形调节前照灯的角度。", "出题教师": "李培森", "所属系": "电子信息工程系"}, {"topic_id": "7988", "论文题目": "面向智能移动机器人的室内建图与自动导航系统设计", "论文类型": "工程设计", "论文内容": "1、实现机器人的ROS系统和底盘控制\r\n2、实现自定义的地图建模\r\n3、完成机器人的自动导航\r\n4、其他辅助完成系统的功能", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7989", "论文题目": "基于视觉水果识别的称重系统设计", "论文类型": "工程设计", "论文内容": "设计一种基于视觉水果识别的称重系统，该系统能够正确识别出水果名称，并进行称重计算。具体要求如下：\r\n1.能够正确识别10种以上常见水果，其中至少包括4种相似水果。\r\n2.能够自动称重，并将重量及价格在屏幕上显示出来。", "出题教师": "李培森", "所属系": "电子信息工程系"}, {"topic_id": "7990", "论文题目": "基于树莓派的语音识别与交互系统设计", "论文类型": "工程设计", "论文内容": "1、360度等效拾音：确保系统能够在任意方向准确捕捉音频信号，得到干净的音频数据。\r\n2、离线命令词识别：利用离线命令词识别模型，实现无需联网的本地命令词识别，识别模型部署在本地确保数据隐私性与系统响应速度。\r\n3、TTS文字转语音功能：把需要播报的文本文字通过接口发送给语音合成引擎，语音合成引擎识别处理后将生成相应的音频文件.wav格式，下载到树莓派终端调用扬声器对音频文件进行播放。\r\n4、语音交互功能：用户可就天气、新闻、网络搜索、健康等知识与远程大数据模型进行语音交互，基本满足日常交流互动。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7991", "论文题目": "智能机器人QT界面控制终端设计", "论文类型": "工程设计", "论文内容": "1.有简约清晰的界面设计：利用Qt Creator设计控制终端界面初步的设计框架与功能按钮\r\n2.能够有效收发通信控制：采用串口通讯实现信号的收发与展示\r\n3.包含控制终端基本交互功能：实现不同功能按钮功能代码\r\n4.界面能够完成屏幕的自适应：美化界面设计，实时显示远程终端的位置以及全局视图。\r\n5.要求功能完整、界面美观、信号收发迅速", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7992", "论文题目": "面向学校的节能路灯系统", "论文类型": "工程设计", "论文内容": "功能说明：\r\n1.太阳能充蓄电\r\n2.路灯状态自检\r\n自主设置亮灭的时间段\r\n定期自检\r\n异常报告：位置\r\n3.通信模块\r\n区域内路灯联网，进行实时操控，统一开关指定区域路灯等 \r\n4.检测行人（灯随人亮灭）\r\n在路灯节能时间行人出行，路灯自动检测行人，自动开启部分路灯。\r\n\r\n预期成果：路灯能实现范围联网，可以规定时间亮灭、范围亮灭，在规定范围内路灯能自动切换电源以供节能和正常使用。在夜间路灯能及时检测路人，达到正常照明需求，保证安全。能检测自身工作状态，及时上报故障位置，合理利用社会资源，不造成人力浪费。\r\n\r\n完成课题所需的基础知识与能力要求：\r\n熟悉单片机电路设计和编程应用、光电检测电路、太阳能模块等电路设计基本原理等", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7993", "论文题目": "基于ESP32的电机旋钮控制器", "论文类型": "工程设计", "论文内容": "1、具有多状态的力反馈模式：棘轮模式（转动电机的每个限定角度带有阻力），顺滑模式（通过接受电机转动的方向给予一个同向轻微的扭力），用户自定义模式（使用户客制化设置电机每个限定角度带有的阻力和限定角度的大小）等等\r\n2能控制PC/手机：通过无线通信与PC/手机互通，连接后旋钮可以控制电脑鼠标操作、实现按键映射、亮度大小、音量大小等控制。\r\n3、电脑上可自行定义旋钮的电机属性等\r\n4、能接入米家生态系统\r\n5、预期成果：提供一款体积小，功能强大的旋钮为大家实现某一操作提供一种另类的方式，改善用户使用体验。\r\n所需基础和能力：电路设计、PCB绘制、元器件焊接、FOC基本原理（Field-Oriented Control磁场定向控制）、esp-idf物联网开发框架、arduino 、python等", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7994", "论文题目": "基于ESP32的智能桌面时钟", "论文类型": "工程设计", "论文内容": "1、传统时钟功能\r\n2时间校准功能\r\n3、天气预报功能\r\n4、基于文心一言API的语音交互与备忘录创建功能\r\n5、APP控制与管理功能：\r\n\r\n预期成果\r\n1、完成ESP32智能桌面时钟的硬件设计与组装。\r\n2、实现上述所有功能模块的开发与集成。\r\n3、提供详细的用户手册和文档，包括硬件设计图、软件代码说明等。\r\n4、通过实际测试验证时钟的稳定性和可靠性。\r\n基础和能力要求：\r\n1、熟练掌握ESP32开发板及其周边组件的选型与连接。\r\n2、具备良好的C/C++编程能力和Arduino IDE等开发环境的使用经验。\r\n3、熟悉网络通信协议和API集成技术。\r\n4、了解文心一言API的基本原理和使用方法。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7995", "论文题目": "智能健康监测眼镜", "论文类型": "工程设计", "论文内容": "1.功能说明：实时监测用户头颈部的状态和用眼状态数据，对颈椎活动和视距进行追踪和分析，当监测到用户颈椎活动或视距异常时，系统将发出语音或连接手机小程序提醒，引导用户采取正确的姿势，减少健康风险。\r\n2.STM32主要用于对传感器数据的获取、信号的滤波、数据的预处理以及对整个终端系统的控制。\r\n3.ESP32主要用于WIFI和蓝牙的通信以及音频信号的处理。\r\n4.实现STM32与ESP32间通信，将采集到的数据通过wifi和蓝牙上传至云端以及微信小程序。\r\n5.微信小程序采用uniapp+unicloud开发\r\n\r\n完成课题所需的基础知识与能力要求：\r\nSTM32+多传感器+esp32+小程序+云端间信息传输处理\r\n电路图设计，pcb布线，FPC设计以及焊接", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7996", "论文题目": "基于stm32的物流送货小车", "论文类型": "工程设计", "论文内容": "（1）功能说明\r\n1.需要实现按照特定路线行走进行物流送货（借助红外寻迹）\r\n2.需要实现运输途中避开障碍（超声波测距判断转弯自动避障）\r\n3.需要实现软件控制小车（蓝牙控制模块）,人工控制小车行走路线\r\n4.需要实现紧急制动报警（蜂鸣器借助距离判断，中断实现制动加报警），以此面对突发状况\r\n5.需要实现联网（云平台），将小车本身的状态（（单个轮子以及整体）速度、报警次数等）上传至云平台，借此实现对小车状况的熟悉，方便及时对小车维护\r\n（2）预期成果\r\n在物流仓库按照特定路线寻迹，并进行自动避障的物流送货小车，且在某些特定需求时，可以进行人工控制小车，并能实现紧急制动报警与联网将小车状态上传至云平台，在云平台可以了解到小车本身状态来进行合理维护\r\n所需基础知识：stm32单片机的中断、串口、pwm、GPIO等知识；对电路原理的基本熟悉；one net等开源云平台的熟悉；", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7997", "论文题目": "基于物联网的智能家居控制系统设计与实现", "论文类型": "工程设计", "论文内容": "功能要求：\r\n1.远程控制：用户可以通过手机应用或者网站远程控制家居设备，实现灯光、空调、窗帘等设备的开关、调节和定时功能。\r\n\r\n2.智能化家电控制：系统可以根据房间的温湿度、光照等，自动化地调节窗帘、灯光等电气设备\r\n\r\n3.智能语音控制：通过语音识别技术，用户可以通过语音指令控制家居设备，提高用户的使用便利性。\r\n\r\n4.智能化场景切换：系统可以根据用户的设定，自动进行场景切换，如“回家模式”即自动开启门锁、调节室内温度等。\r\n\r\n5.烟雾报警：当系统检测到烟雾指标异常，向用户发送报警请求。\r\n\r\n6.系统联动场景：系统可以实现家居设备之间的联动，如当检测到用户离家时，自动关闭所有设备；当有人进入家门时，自动开启灯光和空调等", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7998", "论文题目": "基于stm32的智能充电控制系统", "论文类型": "工程设计", "论文内容": "设计一个有充电保护（充满后降低充电电流），显示剩余电量和充电状态以及远程提示的充电控制器。\r\n\r\n功能要求：\r\n1.有启动和停止开关\r\n2.自动切换恒压恒流充电\r\n3.显示电量、充电时间长、功率消耗等\r\n4.反馈当前充电状态，并能远程提示等", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "7999", "论文题目": "基于物联网的中草药种植信息监测系统", "论文类型": "工程设计", "论文内容": "（1）中草药种植过程需要监测的信息调研\r\n（2）确定需要监测的相关物理参数\r\n（3）设计监测终端，实现相关检测参数的检测\r\n（4）完成监测数据的存储、统计和分析", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8000", "论文题目": "游泳馆水质监测系统设计", "论文类型": "工程设计", "论文内容": "针对游泳馆的泳池,设计一种基于STM32单片机、ESP8266无线模块和MQTT传输协议的实时水质监测系统，自行设计android手机APP作为接收客户端,来实现远程水质监测。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8001", "论文题目": "基于RT-Thread的智慧农业大棚监控系统设计", "论文类型": "工程设计", "论文内容": "设计一套基于RT-Thread的智慧农业大棚监控系统，应用 RT-Thread 操作系统，设备端采集大棚内的温湿度、光照度，并通过Wi-Fi模块及 MQTT 协议实现与 OneNET平台的数据交互，满足智慧农业大棚的监控需求，实现对设备端进行实时远程监控的目的，有效提高农业种植的产量。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8002", "论文题目": "智能垃圾分拣车", "论文类型": "工程设计", "论文内容": "以视频处理模块和超声波等辅助部件,设计出以四轴机械臂,机械爪以及舵机为机械部件并辅以神经网络深度学习识别模型的智能垃圾分拣小车.在实现垃圾分拣一体化的同时,还部分解决了人工垃圾分拣的缺陷.", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8003", "论文题目": "基于STM32的家庭医生健康管理平台的设计与实现", "论文类型": "工程设计", "论文内容": "利用生理信号采集模块结合STM32F103 微处理器及设计算法，实现了心率、血氧饱和度、血压和血糖生理数据计算。计算出的生理数据均在合理误差范围之内，医生可以根据患者的生理数据为患者进行病情诊断，为其调整用药量或者用药种类。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8004", "论文题目": "空巢老人守护系统的设计与实现", "论文类型": "工程设计", "论文内容": "当下人口老龄化的程度越来越严重，随着社会和科学技术不断的发展，空巢老人的安全监护需求增加，运用嵌入式系统和物联网技术对空巢老人进行守护，能够了解空巢老人生活情况并及时处理突发状况，可以保障空巢老人的生命安全。面对空巢老人监护问题，采用 嵌入式技术结合传感器、无线通信以及目标检测算法，实现了一个对空巢老人环境监测、生理监测以及危险行为检测于一体的守护系统。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8005", "论文题目": "小型实验室人脸识别管理系统的设计与实现", "论文类型": "工程设计", "论文内容": "应用在小型实验室的人员管理场合，实现（1）门禁功能（2）人员管理（3）人脸录入与识别以及数据管理（4）数据存储和分析以及形成报表", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8006", "论文题目": "实验室空气质量监测系统的设计与实现", "论文类型": "工程设计", "论文内容": "利用传感器对室内的温湿度、照明、消防系统、漏水、漏油、空调、新风机、空气质量等进行实时监测，一旦实验室的环境条件如温度、湿度、照明度、空气净化度等出现异常，中央处理系统便会在第一时间自动发出指令，系统进行多重报警，如语音报警、声光报警和远程报警等，并通过控制安置在实验室中的各种智能开关，解决一切的安全隐患问题。", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8007", "论文题目": "基于stm32的智能取货小车", "论文类型": "工程设计", "论文内容": "本课题的预期成品效果能够自动将货物取出并且送达指定位置，并且拥有语音提示功能。\r\n（1）系统模块 ：控制和协调\r\n（2）循迹模块：按规定路线行进  \r\n（3）避障模块：避开障碍物     \r\n（4）机械臂模块：实现物品抓取和投放  \r\n（5）返航模块：回到出发点", "出题教师": "何永玲", "所属系": "电子信息工程系"}, {"topic_id": "8009", "论文题目": "基于STM32跟随小车系统设计", "论文类型": "工程设计", "论文内容": "设计一种基于STM32 的跟随小车，该小车能够实现智能跟随、人机交互、智能避障等功能。具体要求如下：\r\n1. 系统能够识别主人的位置，并自动控制小车跟随主人移动，保持一定的安全距离。\r\n2. 提供直观、友好的人机交互界面，方便控制小车、查询信息等。\r\n3. 遇见障碍物时，能够主动停止或转弯避免碰撞等安全隐患。", "出题教师": "李培森", "所属系": "电子信息工程系"}, {"topic_id": "8026", "论文题目": "基于STM32的智能畜牧业养殖系统", "论文类型": "工程设计", "论文内容": "本论文主要分析智能畜牧业养殖在国内外的发展现状，给出智能畜牧养殖管理系统的方案。主要包含数据采集节点的软硬件设计、数据中转站的软硬件设计、服务器的设计、客户端软件的设计及牲畜模式识别算法的设计。其中数据采集节点包含畜牧环境采集节点和牲畜活体数据采集节点两部分，分别用于采集畜牧环境信息和牲畜活体数据；畜牧环境采集节点包含微控制器模块、气象传感器模块、GPS北斗双模定位模块、电源模块；牲畜活体数据采集节点包括RFID电子耳标和智能项环。数据中转站设计包含嵌入式模块MT7688、4G模块、RFID读卡器模块、Lo Ra接收装置等。", "出题教师": "向征", "所属系": "电子信息工程系"}, {"topic_id": "8033", "论文题目": "智能水杯的设计", "论文类型": "工程设计", "论文内容": "要求设计一个智能水杯，可准确显示水温，水位高度等，如温度过低，可加热，温度过高可加热到适宜温度，可语音提示，水温过高，开盖有报警等功能，温度等信息可在显示屏上显示，也可在手机上显示。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8034", "论文题目": "电子密码锁的设计", "论文类型": "工程设计", "论文内容": "要求至少设计两种智能密码模式，数字和字母必须间隔，指纹或人脸识别模式二选一，要有数字，字母的输入，删除，更改等，输错三次即报警或锁定等功能，密码成功可开锁，要有相应的开锁动作等简单的机械装置。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8035", "论文题目": "多功能智能小车的设计", "论文类型": "工程设计", "论文内容": "设计一个多功能智能小车，可避障，可按指定路径行走，可进行清扫，可在手机上遥控，可以语音提示，音乐播放等功能。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8036", "论文题目": "多功能数字时钟的设计", "论文类型": "工程设计", "论文内容": "要有时间模块，断电重新上电后也能正确显示时间\r\n要有时间，日期等的调节按钮，注意2月份，大小月份等的日期显示，显示温湿度等功能。\r\n设置3-4个按键，可以调整加或减，确定，复位等。\r\n有语音播报，闹铃，整点报时等功能。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8037", "论文题目": "智能清扫小车的设计", "论文类型": "工程设计", "论文内容": "设计的清扫小车可自行探测地面情况，可自动清扫，也可根据用户的清洁要求和喜好进行清扫，清扫过程中，注意设计路线的优化等。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8039", "论文题目": "智能音乐播放器的设计", "论文类型": "工程设计", "论文内容": "设计任务\r\n（1） 可播放多首音乐，且通过按键来选择播放的音乐\r\n（2） 选择音乐时，音乐名称在LCD上显示\r\n（3） 音乐播放种类跨度要大一些，播放声音要清晰\r\n（4） 可快速选歌 ，切歌等\r\n（5） 如能增加图片，视频的播放就更好，如完成困难，可增加录音，播放功能。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8040", "论文题目": "多功能广告屏的设计", "论文类型": "工程设计", "论文内容": "可显示文字和图形，显示文字和图形应清晰，稳定，文字可以静止，也可以左右循环移动。\r\n可实时输入汉字，字母等。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8043", "论文题目": "物联网蔬菜大棚监测系统的设计", "论文类型": "工程设计", "论文内容": "设计的系统要求能够监测土壤的温度，湿度，光照强度等参数，显示在屏幕上，并实时传输到管理者手机，用于实时监测，并采取相应的措施，自动开启加热，散热，洒水等措施，保证大棚养殖的环境等。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8044", "论文题目": "家庭安防报警器的设计", "论文类型": "工程设计", "论文内容": "设计一个家庭安防报警器，包括烟雾报警，防盗，防煤气泄漏等，采用多个传感器，从而实现防盗，防火，防煤气泄漏的作用。\r\n一旦发生危险，马上通知用户，并提醒其采用相关措施。\r\n用户也可在手机上实时查看安全数据。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8045", "论文题目": "智能健康手环的设计", "论文类型": "工程设计", "论文内容": "面上智能手环功能多种多样，如监测步数、里程、卡路里消耗、睡眠质量等用户健康数据。很少有针对老年人身体健康监测的手环，尤其是独居老人，健康手环变得尤为重要。改系统要求针对老年人设计一款智能手环，如有健康指数等的异常或超标，及时传递给联系人，尽量避免危险的发生。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8047", "论文题目": "智能电子血压仪的设计", "论文类型": "工程设计", "论文内容": "设计一款人体智能血压仪，能准确测量并记录用户一段时间的血压，并进行数据分析，对比，提醒等功能\r\n如数据有异常，可提醒用户，并进行健康建议，急救等功能。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8048", "论文题目": "倒车雷达系统的设计", "论文类型": "工程设计", "论文内容": "设计一个倒车雷达系统，能够进行准确测距，报警等功能。需要多个传感器测距，请合理布局。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8055", "论文题目": "智能风扇设计", "论文类型": "工程设计", "论文内容": "设计一个智能风扇，可无极调速，根据温度调节风速大小，如没人自动关闭等，可在手机控制。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8069", "论文题目": "智能家居控制系统的设计", "论文类型": "工程设计", "论文内容": "设计一个智能家居系统，功能可包括智能照明系统，智能安防系统，语音控制系统，智能门窗系统等。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8070", "论文题目": "路灯自动控制系统的设计", "论文类型": "工程设计", "论文内容": "路灯自动控制系统是一种专门用于管理和控制公共道路上的路灯的技术系统， 通过监测和分析路况、环境光照和时间等因素，对路灯的亮度进行智能调节和管理。 路灯控制系统可以实现自动化的照明管理，根据不同时间段和实际需求，灵活地调整路灯的亮度，既可以提供良好的道路照明效果，确保行人和车辆的安全，同时也能够节约能源、降低维护成本。", "出题教师": "陈华艳", "所属系": "电子信息工程系"}, {"topic_id": "8088", "论文题目": "基于STM32+FreeRTOS智能手表设计与实现", "论文类型": "工程设计", "论文内容": "设计一款类似市面流通的智能手表，具有智能手表的绝大部分功能：可测血氧血压，睡眠监测记步，测温湿度，时钟，万年历等等功能。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8091", "论文题目": "基于home assistant的智能家居控制系统的设计与实现", "论文类型": "工程设计", "论文内容": "1.\t**功能要求**:\r\n- 使用 Home Assistant 平台实现智能家居控制。\r\n- 可实现温湿度， 雨滴，烟雾，煤气浓度，光照信息等的采集，并通过  WiFi 或蜂窝系统将数据无线传输至HA主控中心，部分模拟传感信息需要进行数模转化。\r\n- 通过HA主控中心实现自动化控制， 根据传感信息对步电机控制实现开窗关窗功能，同时实现烟雾报警和邮件提醒。\r\n- 设计具有摄像头功能的智能音箱，实现语音UI交互，可查询传感器数据、天气信息，以及与机器人智能聊天。\r\n- 实现远程查询与监控，包括网页端以及移动app。\r\n- 实现灯光报警、邮件报警、推送通知功能，并完成传感器数据、灯光及人脸的实时监控。\r\n- 改进CNN 人脸识别算法，以提高人脸识别准确率（选）。\r\n\r\n2.\t**性能要求**:\r\n\r\n- 搭建实验平台进行系统功能测试，检测主控单元、智能音响及用户界面等功能的正常运行。\r\n- 实现环境检测、语音控制灯光、智能音箱、实时监控及报警信息发送功能。\r\n- 系统支持超过 10 人同时通过 Web 端和移动端远程管理，语音识别在安静环境中的准确率达不低于90%，灯光模块按钮的成功率为 99.9%，人脸识别的正确率不低于 95%\r\n- 整个智能家居系统可通过语音指令实现设备联", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8093", "论文题目": "智能语音控制垃圾桶的设计与实现", "论文类型": "工程设计", "论文内容": "**功能要求**\r\n\r\n1. 通过语音控制垃圾桶开关。\r\n2. 具备语音播报功能，播报内容：开，关，垃圾桶满提示，异常提醒。\r\n\r\n**技术要求**\r\n\r\n1. 建议采用ESP32/ESP2866或STM32等处理器。\r\n2. 优先采用具有操作系统的软件解决方案，编程语言不限，可采用C, Ardoino以及MicroPython\r\n3. 设计中应包含语音识别模块，语音播报模块以及电机控制模块\r\n4. 系统基本保护功能，如果看门狗，异常报警。\r\n5. **选做：**设计并实现简单地APP，实现手机APP远程（WIFI或蓝牙）控制。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8094", "论文题目": "基于人脸识别的存储柜设计与实现", "论文类型": "工程设计", "论文内容": "**功能要求**\r\n\r\n1. 人脸学习以及存储管理\r\n2. 可实现人脸匹配，如果匹配，则打开柜门\r\n3. 长时间检测不到正常人脸后，自动关门（选做）\r\n4. 必要的报警功能，如：未关门等。\r\n\r\n**技术要求**\r\n\r\n1. 推荐使用集成模组ESP-CAM 或 ESP-EYE\r\n2. 优先采用具有操作系统的软件解决方案，编程语言不限，可采用C, Ardoino以及MicroPython\r\n3. 系统基本保护功能，如果看门狗，异常报警。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8095", "论文题目": "智能门锁的设计与实现", "论文类型": "工程设计", "论文内容": "**功能要求**\r\n\r\n1. 可实现三种种开锁方式：指纹开锁、密码开锁、RFID/NFC开锁。任选两种开锁方式\r\n2. 指纹录入与存储，可管理多个指纹，包括录入，修改，删除等操作\r\n3. 密码录入与存储，可管理多个密码，包括录入，修改，删除等操作。\r\n4. RFID/NFC卡片管理，可管理多个卡片，包括录入，修改，删除等操作。\r\n5. 带有液晶显示屏，默认进入待机关闭状态，主要作为指纹以及密码操作的UI界面\r\n6. 具备语音提示功能\r\n\r\n**技术要求**\r\n\r\n1. 建议采用ESP32/ESP2866或STM32等处理器。\r\n2. 优先采用具有操作系统的软件解决方案，编程语言不限，可采用C, Ardoino以及MicroPython\r\n3. 系统基本保护功能，如果看门狗，异常报警。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8096", "论文题目": "蓝牙网关的设计与实现", "论文类型": "工程设计", "论文内容": "功能要求\r\n\r\n1. 网关包含一路WIFI和至少两路蓝牙MASTER。\r\n2. 实现蓝牙设备与网关的配对连接\r\n3. 网关通过WiFi连接云端IOT服务，如阿里云IOT或机智云等。\r\n4. 设计简单的APP，通过手机APP操作在云端管理蓝牙节点设备，如果设备添加，删除等操作\r\n5. 设计简单的蓝牙侧空节点，带有一路LED灯和温湿度检测\r\n6. 将温湿度发布到IOT云端，手机端APP订阅该服务，在APP上显示实时温湿度。\r\n通过手机APP发布开灯指令到云端，蓝牙节点订阅该服务并实现实时开关灯。\r\n\r\n技术要求\r\n\r\n1. 蓝牙网关推荐使用ESP32/STM32等MCU，或树莓派。蓝牙节点建议采用ESP32\r\n2. 优先采用具有操作系统的软件解决方案，编程语言不限，可采用C, Ardoino以及MicroPython\r\n3. 系统基本保护功能，如果看门狗，异常报警。\r\n4. 协议建议使用MQTT", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8097", "论文题目": "基于LORA的土壤湿度检测传输系统", "论文类型": "工程设计", "论文内容": "**功能要求**\r\n\r\n1. 采用Lora点对点透传模式或lorawan模式\r\n2. 系统包含一个基站节点，N个检测节点（实际实现中只需要一个即可），检测节点分时轮询方式与基站节点通信。\r\n3. 设计轮询访问控制协议，并在基站节点和检测节点分别实现。\r\n4. 检测节点要求\r\n5. 配置外接土壤湿度传感器，实时检测土壤湿度并通过Lora透传至基站节点\r\n采用低功耗设计\r\n采用电池供电\r\n6. 基站节点要求\r\n7. 管理N个检测节点\r\n接收来自节点的湿度数据\r\n将收到的信息显示在设备显示屏\r\n设计联网功能（通过WiFi或蜂窝通信），将数据发布到服务器或IOT云端。（选做）\r\n8. 两个节点的lora通信模块可提供使用，无需重新购买。\r\n\r\n**技术要求**\r\n\r\n1. 采集节点尽量遵循低功耗设计，MCU在满足条件情况下选择ULPBench分数高的。建议使用轻量级操作系统，采用C编程。\r\n2. 基站节点强调性能，可采用ARM-CORTEX-A系统处理器，也可采用STM32F/ESP32系列。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8098", "论文题目": "基于RFID的人员计数与门禁系统", "论文类型": "工程设计", "论文内容": "**基本要求**\r\n\r\n1. 通过RFID刷卡实时检测对应人员是否刷卡。\r\n2. 对已刷卡人员进行实时计数，并在液晶屏显示累计人数。\r\n3. 对已刷卡人员通过电机控制门禁闸门开关，并带有防夹设计。\r\n4. 设计人员累计数清零功能，可通过按键或触屏实现。\r\n5. 对于RFID的要求：采用高频加密卡，卡片存储区需记录姓名，ID等相关信息参数\r\n6. 液晶显示及存储要求：\r\n    1. 显示实时累计人数\r\n    2. 显示当前刷卡人员相关信息，如姓名等。并要求定时清除。\r\n    3. 可存储不少于100人的相关信息\r\n\r\n**选做**\r\n\r\n1. 远程同步刷卡记录数据至云端或手机端。\r\n    1. 若同步到手机端：设计并实现简单地APP，实现实时数据上传到手机APP，采用蓝牙实现。\r\n    2. 若同步到云端：实现IOT平台搭建，实现检测数据实时同步至IOT云端平台。推荐使用商业平台，如：阿里云，机智云等。采用WiFi实现。\r\n2. 远程清零现场刷卡系统累计人数。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8099", "论文题目": "便携式游戏掌机设计", "论文类型": "工程设计", "论文内容": "1. 音乐播放功能，支持MP3 , WAV格式等。可显示歌词。\r\n2. 图片浏览，支持bmp, jpg格式，支持图片放大缩小。\r\n3. 文本浏览，支持TXT格式\r\n4. 支持nes格式游戏格式。\r\n5. 硬件采用低功耗设计\r\n6. 软件采用基于操作系统以及相关GUI设计。建议采用UCOSii以及STemWin实现。\r\n7. 也可以采用模拟器的方式运行，操作系统不特别指定，Linux，Android，以及专用系统可以任选其一。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8100", "论文题目": "手机快充设备的设计与实现", "论文类型": "工程设计", "论文内容": "基本**要求**\r\n\r\n1. 支持双向 PD3.0 快充等多种协议的移动电源 SOC；\r\n2. 支持QC2.0/QC3.0 输出快充协议；\r\n3. 支持FCP ，AFC, SFCP 等输入/输出快充协议；\r\n4. 电池端充电电流最高可达5.0A；\r\n5. 自适应充电电流调节；\r\n6. 支持4.20V、4.35V、4.40V、4.50V电池；\r\n7. 输入过压、欠压保护，输出过流、过压、短路保护，电池过充、过放、过流保护，IC过温保护，充放电电池温度NTC保护；\r\n8. 输出接口支持TYPE-C\r\n\r\n**选做**\r\n\r\n1. 增加AC-DC变换，实现完整的充电适配器设计。\r\n2. 增加液晶显示，显示实时电量，充放电状态。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8101", "论文题目": "智能花盆的设计与实现", "论文类型": "工程设计", "论文内容": "### 设计要求\r\n\r\n制作一个可以检测空气温湿度以及土壤湿度的智能花盆。\r\n\r\n1. 各种参数检测准确，可自行添加其余与盆栽相关的参数测量功能。需要有提示功能，告知用户目前花盆的各种情况，需要带一个简单的屏幕交互界面。\r\n2. 采用ESP32S、ESP8266或STM32单片机，\r\n3. 具备无线WiFi联网功能，可通过中间服务器（如 MQTT BROKER）与手机客户端进行数据交互。\r\n4. 手机客户端可以是微信小程序或者Native APP\r\n5. 手机客户端提供基本人机交互和数据展示。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8102", "论文题目": "网络时钟和气象站的设计与实现", "论文类型": "工程设计", "论文内容": "### 设计要求\r\n\r\n制作一个能联网确定时间并且提供当前城市日平均温度的便携式闹钟。\r\n\r\n1. 时间与当前城市日平均温度从网络获取并且准确，有屏幕进行显示。\r\n2. 不用对时永远准确，可预报未来三天的最低/高气温，可显示室内实时温度。\r\n3. 采用ESP32、ESP8266或STM32单片机，\r\n4. 电池供电\r\n5. 采用WIFI方式联网\r\n6. 低功耗设计", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8103", "论文题目": "基于ESP32的家庭视频监控系统的设计与实现", "论文类型": "工程设计", "论文内容": "### **基本要求**\r\n\r\n1. 使用ESP32模组联网，串流摄像头实时视频。\r\n2. 制作简单手机APP，小程序或网页, 通过互联网远程串流摄像头实时视频。\r\n3. 远程控制功能。能通过网络实时控制监控摄像头开关机以及重启。\r\n4. 视频录像本地本地存储\r\n5. 在线拍照转存到手机。\r\n6. 身份认证，以获取访问摄像头以及实时视频的权限。\r\n\r\n### **选做**\r\n\r\n1. 可语音唤醒（接入小爱同学控制）。\r\n2. 摄像头连接至IOT云端实现设备管理（米家）。\r\n3. 人脸识别能力，当检测到陌生人或物时发出语音报警同时报警到手机APP或云端。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8104", "论文题目": "LoRa单点网关的设计与实现", "论文类型": "工程设计", "论文内容": "### 设计要求：\r\n\r\n设计并实现一款LoRa单点网关系统，要求如下：\r\n\r\n1. 系统包含三部分\r\n    - lora监测节点：采集温湿度，气压等数据并通过lora点对点发送到接收端。\r\n    - lora网关：接受来自监测端的数据并将其通过网络传输的iot平台\r\n    - MQTT 代理，作为IOT的云平台\r\n2. 网关设备具备Wi-Fi接入网络的能力\r\n3. 网关设备支持mqtt协议，将接收到的监测数据发布到iot平台。\r\n4. iot平台订阅并接受数据并将其显示在客户端。\r\n5. 也可采用其他协议：如，http，coap等", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8105", "论文题目": "基于ESP32的智能健康手表", "论文类型": "工程设计", "论文内容": "### 设计要求：\r\n\r\n设计一款智能健康手表，并实现其原型：具体要求：\r\n\r\n1. 通过NTP(Network Time Protocol)显示时间\r\n2. 使用陀螺仪监测运动情况\r\n3. 测试佩戴者的实时心率BPM，血氧饱和度\r\n4. 睡眠监测\r\n5. 配置oled屏，设计手表UI，并且在显示屏中显示相关的测量数据。\r\n6. 电池供电\r\n\r\n**选做**\r\n\r\n1. 增加BLE低功耗蓝牙连接能力\r\n2. 制作简单的手机APP，通过蓝牙通信将健康数据同步到APP中。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8106", "论文题目": "宠物自动喂食装置的设计与实现", "论文类型": "工程设计", "论文内容": "### 设计要求：\r\n\r\n设计一款宠物自动喂食装置，并实现其原型：具体要求：\r\n\r\n1. 设计手机APP或小程序实现远程控制宠物喂食装置。\r\n2. 定义应用指令协议，投喂装置根据定义的指令实现相应的操纵PWM波控制舵机旋转的动作。\r\n3. 通过指令实现自动、定时、定量地喂投。\r\n4. 手机app或小程序可以查看喂食装置的实施状态。\r\n5. 手机app或小程序通过iot平台与喂食装置通信，协议采用mqtt。\r\n6. 联网方式采用wifi。\r\n7. 建议采用esp32或stm32+esp8266实现。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8107", "论文题目": "基于NB-IOT电量采集系统", "论文类型": "工程设计", "论文内容": "### 设计要求\r\n\r\n1. 设计一款基于NB-IOT电量采集装置及监测系统，主要实现以下功能：\r\n2. 采集用电设备的：\r\n    - 有功功率、\r\n    - 电流\r\n    - 电压有效值\r\n    - 用电量\r\n3. 采集数据通过NB-IOT的通信方式与物联网平台进行交互。\r\n4. IOT平台可采用公有IOT平台，如ONE-NET。私有IOT平台，如我系自建平台，也可以采用收费平台，如阿里，腾讯等。\r\n5. IOT平台可实时监控数据。建议采用MQTT协议\r\n6. 采集通信设备采用低功耗设计\r\n7. 电参数通过NB-IoT上传到IOT平台，可以在后台实时监控数据。\r\n8. 后台发送指令控制电参数是否上传（可以拓展继电器控制）。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8108", "论文题目": "网络电子相册的设计与实现", "论文类型": "工程设计", "论文内容": "### 设计要求\r\n\r\n设计一款带有联网功能的电子相册，并实现其原型，主要功能包括：\r\n\r\n1. 相片存储在本地系统SD卡，或者网络存储。\r\n2. 本地SD下相片导入方式可直接拷入或由网络提取\r\n3. 电子相册系统获取相片的方式：\r\n    - 本地读取\r\n    - 网络读取\r\n4. 可设置相片显示的轮转方式。\r\n5. 可联网调取实时天气并显示在相册的屏幕。\r\n6. 使用手势识别切换相册，左右手势切换相册和天气，上下手势切换图片\r\n7. 调取bing WALLPAPER或者USPLASH图片作为动态背景墙纸（选做）\r\n8. 建议采用ESP32 MCU。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8109", "论文题目": "智能温控婴儿床的设计", "论文类型": "工程设计", "论文内容": "### 设计要求\r\n\r\n设计一款智能温控婴儿床的原型系统，实现以下功能：\r\n\r\n1. 实时检测床内温度\r\n2. 检测婴儿状态\r\n3. 配置RTC实时时间\r\n4. 温控系统：电机PWM按键三档调速，自动开关加热器。使用控制继电器控制加热。\r\n5. 通过OLED屏幕显示窗内实时数据\r\n6. 发送实时温度以及床内数据到手机终端（通过蓝牙或WiFi）。\r\n7. 手机可远程设置窗内温度，实时调节温度", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8135", "论文题目": "人脸识别方法的分析与研究", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、人脸图像的采集与分析\r\n2、图像处理的基本方法\r\n3、图像的二值化\r\n4、C＋＋程序设计和图像处理的知识和技巧。\r\n　以人脸图像为研究对象，通过对人脸图像的二值化等处理，建立人脸识别系统模型，并以C＋＋等程序设计工具实现人脸比对的基本功能。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，加强学生生物图像处理的基本技术能力的培养，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n1、掌握人脸图像分析的基本方法\r\n2、以C＋＋等程序设计方法，实现人脸分析比对的基本工具。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8136", "论文题目": "颅脑图像识别的分析与研究", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、颅脑CT，MRI等图像的成像特点和应用\r\n2、基于CT，MRI等图像的颅脑疾病诊断与分析\r\n3、图像识别基本方法\r\n4、医学图像诊断的模型\r\n5、C＋＋程序设计和图像处理的知识的技巧。\r\n　以颅脑CT，MRI等医学图像为研究对象，研究颅脑CT，MRI等图像的特点，建立一种基本相关性分析的颅脑MRI图像的诊断模型。首先选择合适的颅脑CT，MRI等图像二值化方法及其它预处理方法，再提取经处理后颅脑CT，MRI等图像的特征数据，建立相关的颅脑MRI图像数据库，并以C＋＋等程序设计工具实现颅脑CT，MRI等图像的自动诊断。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，加强学生医学图像处理与分析的基本技术能力的培养，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n1、掌握颅脑CT，MRI等图像自动识别诊断的基本方法和作用\r\n2、熟悉颅脑MRI图像的特点和分析方法\r\n3、以C＋＋等程序设计方法，实现颅脑CT，MRI等图像识别诊断的基本工具。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8137", "论文题目": "共享单车智能管理系统的分析与设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n2、共享单车管理的基本方法\r\n2、运用传感器采集共享单车数据的方法\r\n3、共享单车数据处理的基本方法和应用\r\n　研究共享单车的智能管理方法，通过运用不同的传感器，动态采集共享单车的实时状态，并对其进行监控和管理，分析研究共享单车智能管理系统的实现方法。提高学生综合应用单片机和传感器等各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握共享单车智能管理系统的分析和设计方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8138", "论文题目": "甲酫检测仪的分析与设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、单片机系统的基本知识\r\n2、甲酫浓度的检测方法\r\n　以甲酫测试仪为研究对象，通过对甲酫浓度测试仪的分析与研究，设计基于单片机的便携式甲酫浓度的检测系统。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握单片机检测甲酫浓度的分析和设计方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8139", "论文题目": "基于单片机的公交地铁屏蔽门的分析与设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、单片机系统的基本知识\r\n2、公交地铁屏蔽门控制和监测的方法\r\n　以公交地铁屏蔽门为研究对象，通过对公交地铁屏蔽门系统的分析与研究，设计基于单片机的公交地铁屏蔽门控制系统。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握单片机控制系统的分析和设计基本方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8141", "论文题目": "智能水表的分析与设计研究", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、智能水表的相关概念和知识\r\n2、数据采集与处理的方法\r\n3、GPS及蓝牙通讯系统原理\r\n　以智能水表为研究对象，通过对智能水表的分析，研究智能水表的相关功能和基本原理，分析智能水表系统的设计与实现方法。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握智能水表系统的分析和设计基本方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8142", "论文题目": "智能手表的分析与设计研究", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、智能手表的基本原理\r\n2、智能手表的设计方\r\n　以智能手表为研究对象，通过对智能手表的分析，研究智能手表的基本原理，提出基于单片机的智能手表的设计方法。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握智能手表的分析和设计基本方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8144", "论文题目": "基于MQTT的植物监测系统的设计与实现", "论文类型": "工程设计", "论文内容": "### 功能要求\r\n\r\n1. 环境监测：\r\n实时监测土壤湿度、温度、PH值，环境光照强度，空气温湿度等环境参数。\r\n通过传感器采集数据，并将数据传输到微控制器进行处理。\r\n2. 数据存储与处理：\r\n将采集到的数据存储在IOT云端， 采用mqtt协议。\r\n可以对历史数据进行分析，数据可视化，以便生成报告或进行趋势分析。\r\n3. 用户界面：\r\n提供用户友好的界面APP显示植物的生长的环境数据。\r\n4. 报警功能：\r\n当环境参数超出设定范围时，系统能够发出警报（如声音报警或LED闪烁）。\r\n支持通过手机应用或网络发送警报信息（如短信或推送通知）。\r\n5. 自动浇水系统：\r\n根据土壤湿度传感器的数据，自动控制水泵进行浇水，保持植物适宜的水分。\r\n6. 远程监控：\r\n通过手机app或WEB客户端，可以实时查看数据并发送指令\r\n\r\n### 性能要求\r\n\r\n1. 采样频率：\r\n确保传感器数据的采样频率能够满足实时监测需求，如每分钟采样一次。\r\n2. 数据精度：\r\n确保传感器的测量精度符合实际应用要求，例如土壤湿度精度±2%RH，温度精度±0.5°C。\r\n3. 功耗管理：\r\n系统应具有低功耗设计，能够长时间运行，支持电池供电情况下至少在一个月内无需充电。\r\n具备休眠模式在没有活动时降低功耗。\r\n4. 响应时间：\r\n系统对外部环境的变化应能快速响应，报警时间延迟不超过5秒。\r\n5. 通信稳定性：\r\n无线通信需保证信号稳定，数据传输延迟不超过1秒，确保数据及时更新。\r\n6. 操作温度范围：\r\n系统应能够在-10°C至60°C的温度范围内稳定运行，适应多种环境条件。", "出题教师": "张利民", "所属系": "电子信息工程系"}, {"topic_id": "8153", "论文题目": "基于单片机的停车场车位及车辆状态监控系统的设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、单片机系统的基本知识\r\n2、车位及车辆状态监控方法\r\n　以停车场车位及车辆状态监控为研究对象，通过对停车场车位及车辆状态监控的分析与研究，设计基于单片机的停车场车位及车辆状态监控系统，要求实时统计空闲车位数量，对停车场内车辆的状态(自燃，警报器等特征)进行实时监控。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握单片机对停车场车位及车辆状态监控系统的分析和设计方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8154", "论文题目": "指纹识别系统的分析和研究", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、指纹图像的采集与分析\r\n2、图像处理的基本方法\r\n3、图像的二值化\r\n4、C＋＋程序设计和图像处理的知识的技巧。\r\n　以指纹图像为研究对象，通过对指纹图像的二值化处理，建立基于像素分析的指纹比对系统模型，并以C＋＋等程序设计工具实现虹膜比对的基本功能。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，加强学生生物图像处理的基本技术能力的培养，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n1、掌握基于像素的指纹图像分析的基本方法\r\n2、以C＋＋等程序设计方法，实现指纹分析比对的基本工具。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8157", "论文题目": "基于微信小程序和二维码识别的医院预约就诊系统的分析与设计", "论文类型": "实验研究", "论文内容": "综合运用微信小程序开发，二维码技术，图像识别，C＋＋、Matlab等语言，研究二维码识别在医院预约就诊微信小程序中的基本原理和应用，设计易用便捷的医院预约就诊系统。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8158", "论文题目": "智能物流柜系统的分析与设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、单片机系统的基本知识\r\n2、智能物流柜系统的基本原理\r\n　以智能物流柜系统为研究对象，通过智能物流柜状态进行监控，实时掌握快件收发状态，设计基于单片机的物流柜智能管理系统。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握单片机智能物流柜系统的分析和设计方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8161", "论文题目": "智能灯光控制系统的分析与设计", "论文类型": "实验研究", "论文内容": "应具备的知识：\r\n1、单片机系统的基本知识\r\n2、智能灯光控制系统的基本原理\r\n　以智能物流柜灯光控制系统为研究对象，通过蓝牙，WIFI，远程等方法对灯光进行实时控制，达到根据不同需求进行灯光组合控制的目的，设计语音，感应等的智能灯光控制系统。提高学生综合应用各种计算机技术及相关应用领域基本知识的能力，增强学生应聘相关工作岗位的信心和能力。\r\n目标：\r\n掌握智能灯光控制系统的分析和设计方法", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8162", "论文题目": "手机无线充电技术的分析与研究", "论文类型": "实验研究", "论文内容": "综合运单片机及嵌入式系统的相关知识，电磁感应技术，自动控制技术等，分析手机无线充电技术的基本原理，研究设计可行的手机无线充电方法。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8167", "论文题目": "基于微信小程序的网上超市系统的分析与设计", "论文类型": "实验研究", "论文内容": "综合运用微信小程序开发，二维码技术，数据库技术，研究微信超市系统的基本原理和应用，设计易用便捷的网上超市系统。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8168", "论文题目": "电动车电源管理系统的设计与研究", "论文类型": "实验研究", "论文内容": "通过综合应用单片机，无线传输技术，蓝牙技术，C语言等软件开发工具，研究电动车电源温度，电量等状态的管理的原理和功能，设计具有一定研究意义和应用价值的电动车电源管理系统。", "出题教师": "王宁", "所属系": "电子信息工程系"}, {"topic_id": "8177", "论文题目": "基于STM32单片机的电机驱动设计与实现", "论文类型": "工程设计", "论文内容": "由单片机产生的转速,转向和制动的控制信号,经由逻辑电路进行运算对应于电机的特定工作状态,运算结果作为电机驱动电路H桥四个开关管的控制信号,从而实现直流电机的调速,正反转或停止等控制.利用光电传感器实时检测直流电机的转速,按照预先设定的参数调节速度,形成闭环控制。通过语音播报最终步进电机的旋转的步数和方向，并在液晶屏上显示结果。\r\n备注：以上提到的技术都不强制使用，只要实现相关功能即可", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8178", "论文题目": "基于STM32单片机的步进电机驱动设计与实现", "论文类型": "工程设计", "论文内容": "要求：\r\nA、希望通过手机或按键控制旋转角度和控制方向。\r\nB、通过语音播报最终步进电机的旋转的步数和方向，并在液晶屏上显示结果。并把数据上传到手机。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8179", "论文题目": "点阵式汉字电子显示屏设计与实现", "论文类型": "工程设计", "论文内容": "希望能通过手机随时修改点阵屏上显示的汉字，汉字能左右滚动，也可以上下滚动，可以固定显示。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8180", "论文题目": "远程抄表系统的设计与实现", "论文类型": "工程设计", "论文内容": "希望能检测用户的电池情况，可以远程读数，并记录到本地。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8181", "论文题目": "智能化数字闹钟设计与实现", "论文类型": "工程设计", "论文内容": "请查一下“波比熊T6智能闹钟”的功能，完成类似功能即可。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8182", "论文题目": "便携式气象监测器设计与实现", "论文类型": "工程设计", "论文内容": "设计并制作一个便携式气象检测仪（包括测量装置和接收装置，通过无线方式通信）。要求能对风向、风速、温度、湿度、气压等气象数据进行采集、存储、显示功能，并实现数据的无线传输通信。\r\n系统测量的技术指标如下：\r\n温度测量范围:-40~50℃，温度精度:±0.2℃\r\n湿度测量范围:09%RH~100%RH，湿度精度:±1%RH\r\n气压测量范围:0~1(MPa)，气压精度:0.25%FS\r\n风速测量范围:0.4~75m/s，风速精度:±0.17m/s\r\n风向测量范围:360°，风向精度:精确到16个风向(东、西、南、北、东南、西南、东北、西北)\r\n该系统测量范围和精度符合一般气象仪的要求,且具备便携式的特点,能够在实际中加以应用。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8183", "论文题目": "书写坐姿提醒器设计与实现", "论文类型": "工程设计", "论文内容": "本题目要求设计一个书写坐姿提醒器，其作用作用是监测书写者头部位置，在书写者头部位置过低时会发出声光报警。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8184", "论文题目": "基于单片机的智能快速充电器设计与实现", "论文类型": "工程设计", "论文内容": "本文介绍了一种基于单片机的智能快速充电器的设计方法.根据充电电池取样电压和电流状态信息,单片机产生合适的PWM信号,控制BUCK变换器工作,实现充电高效控制.快速充电过程中,充电电池的电压和电流取样值波动较大,为了消除这种影响,采用软件检测处理,及时确定充电结束时间,避免过充.\r\n\r\n备注：以上提到的技术都不强制使用，只要实现相关功能即可", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8185", "论文题目": "基于视听觉转换的室内导盲系统设计与实现", "论文类型": "工程设计", "论文内容": "针对基于视听觉转换的室内导盲系统设计与实现，可以参考以下信息：\r\n系统设计\r\n硬件平台\r\n处理器：采用STM32或其他芯片作为中央处理器。\r\n操作系统：搭载Linux系统。\r\n\r\n功能模块：包括双目采集、GPS定位、语音播报、GSM短信、语音通话和无线传输。\r\n\r\n软件平台\r\n\r\n智能识别：利用深度学习算法在远程云服务器上对目标场景进行智能识别。\r\n\r\n导航与引导：通过语音实时对盲人的行走进行准确引导。\r\n\r\n物品归类：帮助盲人进行简易物品归类。\r\n\r\n系统实现\r\n\r\n双目采集\r\n\r\n采用双目摄像头进行实时视频采集，通过图像处理技术提取有用信息。\r\n\r\n配备深度感知模块，能够识别障碍物和识别路径。\r\n\r\nGPS定位与导航\r\n\r\n系统通过GPS模块获取位置信息，结合地图数据进行路径规划，为盲人提供准确的导航。\r\n\r\n具备避障功能，能够自动规避障碍物，确保安全的行走路线。\r\n\r\n语音与通讯\r\n\r\n系统支持语音通话和GSM短信，确保与外界进行通讯联系。\r\n\r\n配备音响信号，通过环境声音辅助引导盲人。\r\n\r\n物品识别\r\n\r\n利用计算机视觉和机器学习技术，对物品进行识别和分类。\r\n\r\n通过训练有素的模型，帮助盲人识别和避开障碍物。\r\n此题有一定难度，请谨慎选择！！！", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8186", "论文题目": "基于单片机的物流机器人设计与实现", "论文类型": "工程设计", "论文内容": "具有一定的路线规划能力，能应用于一些特定的场合，如餐馆配送菜、酒店配送快递、仓库配送物流等等。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8188", "论文题目": "快递取件扫描系统的设计与实现", "论文类型": "工程设计", "论文内容": "设计一个类似菜鸟驿站取件系统，可以实现自助取件，自助记录取件情况，并能根据是否有可取快递而开菜鸟驿站的大门。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8189", "论文题目": "智能分类垃圾系统的设计与实现", "论文类型": "工程设计", "论文内容": "要求：设计能适应中国国情的垃圾分类系统。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8190", "论文题目": "校园签到系统设计与实现", "论文类型": "软件开发", "论文内容": "开发一款可以在手机和电脑上应用的校园签到系统，可以使用手势签到、可以扫码签到、可以签到码签到等方法签到。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8191", "论文题目": "基于神经网络的人脸识别系统的设计和实现", "论文类型": "实验研究", "论文内容": "算法研究，可以比较精准地识别人脸", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8192", "论文题目": "智能饮水机系统设计与实现", "论文类型": "工程设计", "论文内容": "设计一款智能化的饮水机，可以提示水量、饮水次数、自动加热等等人性化功能。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8193", "论文题目": "物联网智能仓储系统的设计与实现", "论文类型": "工程设计", "论文内容": "参考资料如下，仅供参考。\r\n\r\n物联网智能仓储系统的设计与实现是一个复杂的过程，涉及多个技术领域和系统组件。以下是一个详细的设计与实现框架：\r\n1. 系统架构\r\n智能仓储系统的架构基于物联网技术，通常包括以下几个关键部分：\r\n硬件层：包括传感器、执行设备（如自动化货架、机器人）、存储设备等。\r\n软件层：包括数据采集和分析软件、控制算法、用户界面等。\r\n通信层：用于连接各个硬件和软件组件，确保实时数据传输和操作协同。\r\n2. 系统功能\r\n智能仓储系统通过这些组件实现以下主要功能：\r\n信息感知：利用传感器监测环境条件、库存水平和设备状态。\r\n自动化操作：通过自动化货架和机器人执行各种仓储操作。\r\n智能管理：使用智能软件和数据分析技术进行库存管理、订单处理、拣货优化等。\r\n实时监控：通过无线网络和大数据技术实现实时监控，提高运营效率和准确性。\r\n3. 技术融合\r\n物联网技术与云计算、大数据、人工智能等技术融合，形成了一个适应其发展的技术生态。具体来说：\r\n物联网与大数据：通过大数据分析，可以优化库存管理，提高预测准确性。\r\n物联网与人工智能：通过智能算法和机器学习技术，提高作业效率和准确性。\r\n物联网与云计算：利用云计算技术，可以扩展存储和处理能力，实现弹性扩展和高效管理。\r\n4. 系统设计原则\r\n智能仓储系统的设计应遵循以下原则：\r\n实时性：采用高速无线网络技术，确保所有操作和管理具有实时性。\r\n自动化：减少人工操作，提高自动化水平，降低劳动成本。\r\n集成性：将环境监控、设备控制、作业调度等集成在一起，实现统一管理和控制。\r\n安全性：确保数据的安全性和完整性，使用内部局域网和访问控制机制。\r\n5. 实施步骤\r\n智能仓储系统的实施步骤包括：\r\n需求分析：明确系统需求和目标。\r\n架构设计：设计系统架构，包括硬件、软件、通信组件。\r\n设备选择与部署：选择合适的传感器、执行设备和存储设备，并进行部署。\r\n软件开发与测试：开发数据采集、分析、控制和用户界面软件，并进行测试。\r\n系统集成与测试：将各个组件集成在一起，进行系统测试和优化。\r\n运行与维护：系统上线运行，并进行持续的维护和优化。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8194", "论文题目": "停车场智能导引系统的设计与实现", "论文类型": "工程设计", "论文内容": "能监测统计出入车流量情况，可以在手机上容易得到停车场内空闲车位。其他功能可以任意发挥。", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8195", "论文题目": "宠物箱温度湿度监控系统设计与实现", "论文类型": "工程设计", "论文内容": "监控并可调节宠物箱的温湿度", "出题教师": "余华芳", "所属系": "电子信息工程系"}, {"topic_id": "8196", "论文题目": "智能导盲手杖设计与实现", "论文类型": "工程设计", "论文内容": "能很好地监测路面情况并语音告知盲人。", "出题教师": "余华芳", "所属系": "电子信息工程系"}]
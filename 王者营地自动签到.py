# coding=utf-8
import requests
import time

userId = "344164551"
timestamp = '1676873844224'
openid = "363DA6614682785A8222E7A46EE940E7"
msdkEncodeParam = "A5FA160A3597D7347A5CA87F13C4F1388CCD4A14D04C3BA77D30DE23E512CDF3A60DD19A4AD9D4FC7A73E76364299F6A50B11696B5B305378C034F112EC6983E0C4C0B33FF8BBAD7182B2E2C8636F91C6C1AF4B52B8453E596815CDCC58F40567DF7132F23198CE42DA511A60260417E9A26B3C2047C133FAE2ABC8C88B114B75ECCE484E5FCEF7784759B531AAD2B02753DCF451FE7A91296186FCF203CA24FC6D88242D23180C5362589DB7100930C"
sig = '1e3cde532e007a7eb1ebca442eee2a94'
appid = "1105200115"
version = "3.1.96a"
token = "4EMnVEn3"

headers = {  # 任务奖励一键领取和每日签到共用Headers
    "Content-Type": "application/json",
    "timestamp": timestamp,
    "userId": userId,
    "algorithm": "v2",
    "openid": openid,
    "encode": "2",
    "source": "smoba_zhushou",
    "msdkEncodeParam": msdkEncodeParam,
    "sig": sig,
    "appid": appid,
    "version": version
}


# 任务列表
def tasklist():
    url = "https://kohcamp.qq.com/operation/action/tasklist"
    response = requests.post(url, headers=headers)
    b = [i.get('taskId') for i in response.json()['data']['taskList']]
    return b


# 获取文章id
def newstask():
    url = "https://kohcamp.qq.com/info/listinfov2"
    data = {
        "page": 0,
        "channelId": 25818
    }
    headers = {
        "token": token,
        "userId": userId
    }
    response = requests.post(url, headers=headers, json=data)
    a = []
    for i in response.json()['data']['list']:
        if i.get('infoContent'):
            a.append(i.get('infoContent').get('infoId'))
    return a[-1]


# 点赞
def addlike():
    url = "https://ssl.kohsocialapp.qq.com:10001/user/addlike"
    data = {
        "iInfoId": newstask(),
        "token": token,
        "userId": userId,
        "like": 1
    }
    response = requests.post(url, headers=None, data=data)
    return response.json()


def Browsingrecord():  # 浏览战绩
    url = "https://ssl.kohsocialapp.qq.com:10001/play/gettaskconditiondata"
    data = {
        "userId": "344164551",
        "roleId": "283161462",
        "openid": "363DA6614682785A8222E7A46EE940E7",
        "source": "smoba_zhushou",
        "sig": "0851b45ba992a5db641dd16cc0784764",
        "algorithm": "v2",
        "encode": "2",
        "timestamp": "1675873046509",
        "appid": "1105200115",
        "version": "3.1.96a",
        "gameId": "20001",
        "appVersion": "2036814104",
        "gameOpenid": "90404412019BD78D6A377834E425CAFC",
        "areaId": "1",
        "serverId": "1173",
        "platid": "1",
        "cClientVersionName": "6.81.401",
        "msdkEncodeParam": "7248FCE07CB4C3700D12DA58DE15F9F16EF1C3D1B93FE4C21C8FC3BEEA63135692B43D2590E7ACE39BA70693A345E992C2CA55BFED8DDFBA0D3D7B6F23879B752E681781B04589AAFBCE1FFE1258557CAD8FFBBD7BC12ECC6C5931ECFC8EFADB0A6FCB53CFE110CC4DDBAFA044D35CD615DF7A21373B5AB2289A420AA9CBBA7A8515AF8F3097F962E6F70B868AD0979477BE05DEBA49F2101A5242B8CC6F6E651D62F3937A6E7E2DD0232893ABC86E0E",
        "cSystem": "android",
        "h5Get": "1",
        "msdkToken": "eFKzeNXN",
        "type": "6",
        "tabName": "战绩",
        "viewOtherBattle": "1"
    }
    response = requests.post(url, headers=None, data=data)
    return response.json()


def Startgame():  # 启动游戏
    data = {
        "type": "2",
        "token": token,
        "userId": userId
    }
    response = requests.post("https://ssl.kohsocialapp.qq.com:10001/play/gettaskconditiondata", data=data, headers=None)
    return response.json()


def Sharecontent():  # 分享内容
    url = "https://ssl.kohsocialapp.qq.com:10001/play/gettaskconditiondata"
    data = {
        "type": "1",
        "token": token,
        "userId": userId
    }
    response = requests.post(url, headers=None, data=data)
    return response.json()


def Dailybrowse():  # 每日浏览
    url = "https://ssl.kohsocialapp.qq.com:10001/game/detailinfov3"
    data = {
        "iInfoId": newstask(),
        "gameId": "20001",
        "token": token,
        "userId": userId
    }
    response = requests.post(url, headers=None, data=data)
    return response.json()


def messagepush(checkin, summary):  # 消息推送
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": checkin,
        "summary": summary,
        "contentType": 1,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"]
    }
    headers = {
        "Content-Type": "application/json"
    }
    requests.post(url, json=data, headers=headers)


def signin():  # 每日签到
    url = "https://kohcamp.qq.com/operation/action/signin"
    data = {"roleId": "885499875"}
    response = requests.post(url, headers=headers, json=data)
    return response.json()


def rewardtask():  # 任务奖励一键领取
    url = "https://kohcamp.qq.com/operation/action/rewardtask"
    data = {"taskIds": tasklist(),
            "roleId": "885499875"
            }
    response = requests.post(url, json=data, headers=headers)
    return response.json()


def main():
    tasks = [
        ('王者营地签到', signin),
        ('王者营地任务每日浏览', Dailybrowse),
        ('王者营地任务分享内容', Sharecontent),
        ('王者营地启动游戏', Startgame),
        ('王者营地浏览战绩', Browsingrecord),
        ('王者营地点赞', addlike),
        ('王者营地任务奖励一键领取', rewardtask),
    ]
    for task_name, task_func in tasks:
        try:
            result = task_func()
            print(result)
            if result['returnCode'] != 0:
                messagepush(result, f'{task_name}出错')
        except Exception as e:
            messagepush('如题', f'{task_name}出错: {str(e)}')
        time.sleep(1)


main()

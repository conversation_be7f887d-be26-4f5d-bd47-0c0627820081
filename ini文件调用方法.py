"""INI文件是一种文本文件格式，通常用于存储配置数据。INI文件通常包含多个节(section)，
每个节包含多个键值对(key-value pairs)。Python中可以使用ConfigParser模块来读取和写入INI文件。"""
# 当使用ConfigParser模块来读取INI文件时，需要使用ConfigParser类的对象。以下是一个例子：

import configparser

# 创建ConfigParser对象
config = configparser.ConfigParser()

# 读取INI文件
config.read('example.ini')
'''
config.read('example.ini')中的参数是INI文件的相对路径或绝对路径。

如果INI文件与脚本文件位于同一目录中，则可以直接使用文件名：

config.read('example.ini')


如果INI文件位于脚本文件的上级目录中，则可以使用相对路径：

config.read('../example.ini')


如果INI文件位于脚本文件的某个子目录中，则可以使用相对路径和目录名：

config.read('config/example.ini')


还可以使用绝对路径来读取INI文件：

config.read('/path/to/example.ini')


需要确保指定的路径是正确的，否则ConfigParser将无法读取文件.
'''
# 获取配置值
name = config.get('personal_info', 'name')
age = config.getint('personal_info', 'age')
city = config.get('personal_info', 'city')
email = config.get('contact_info', 'email')
phone = config.get('contact_info', 'phone')

# 在上面的例子中，读取出来的值是字符串，除了使用getint方法获取的整数类型的值。get方法返回一个字符串，它将在INI文件中以文本形式出现。
# 如果需要将值作为其他数据类型使用，例如整数、浮点数或布尔值，则需要进行类型转换。例如，在上面的示例中，我们使用getint方法从personal_info部分获取age，
# 它将以整数类型返回。


# 输出配置值
print(f"Name: {name}")
print(f"Age: {age}")
print(f"City: {city}")
print(f"Email: {email}")
print(f"Phone: {phone}")

# 假设example.ini文件的内容如下：
'''
[personal_info]
name = John Doe
age = 35
city = New York

[contact_info]
email = <EMAIL>
phone = ************
'''

# 在这个例子中，我们创建了一个ConfigParser对象，然后使用它的read方法读取了example.ini文件。
# 我们可以使用get方法和getint方法从personal_info和contact_info节中获取相应的配置值，然后将它们打印出来.

# ConfigParser提供了一些方法来修改INI文件并保存更改。例如，可以使用set方法来设置一个新的键值对或修改：

config.set('section_name', 'key_name', 'value')

# 可以使用remove_option方法删除一个键值对：

config.remove_option('section_name', 'key_name')

# 可以使用remove_section方法删除一个节：

config.remove_section('section_name')

# 最后，可以使用write方法将更改写入INI文件：

with open('example.ini', 'w') as configfile:
    config.write(configfile)

# 如果INI文件不存在，则将创建一个新文件并写入配置。如果INI文件已存在，则将覆盖其中的所有内容。

# 注意，写入INI文件时，ConfigParser将使用和读取时相同的格式和样式。如果您要使用不同的格式或样式，
# 请使用标准Python文件I / O操作来打开文件并按照所需的方式编写内容.

# 需要注意的是，使用write()方法写入INI文件时，会将原文件中的内容覆盖掉。如果希望在原文件中追加内容，
# 可以将打开文件的模式改为追加模式，即使用'a'模式，例如：

with open('example.ini', 'a') as configfile:
    config.write(configfile)

# 在这种情况下，write()方法将追加内容到INI文件的末尾，而不是覆盖原有内容.

# 在 ConfigParser 中，可以使用 set() 方法添加键值对，但是如果没有至少一个节，无法添加.
# 如果INI文件中没有任何节，则必须先添加至少一个节，然后才能添加键值对。可以使用 add_section(section) 方法添加新的节。
# 以下是一个示例，展示如何在INI文件中添加节和键值对：
# 在新节中添加一个键值对
config.set('section1', 'key1', 'value1')
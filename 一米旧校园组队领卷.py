import requests
import json
from time import sleep
from random import shuffle

interface_parameters = ["20231222102048456438529322824734","20231218112359421337113721298204"]

num = 0

headers = {
    'Host': '1mapp.cloudyee.com',
    'Content-Type': 'application/json'
}


def get_access_token(userid):
    json_data = {
        'app_id': '1mapp',
        'app_secret': 'f55737ea50afe698a55a8e9980b22668',
        'scope': 'app',
        'ds-oauth2-uid': userid,
        'response_type': 'token',
        'state': 'oauth2',
        'redirect_uri': 'doshare',
    }

    response = requests.post('https://1mapp.cloudyee.com/oauth2/emauthorize', headers=headers, json=json_data)
    return response.json()['access_token']


def get_new_interface_parameters(interface_parameters, access_token):
    url = 'https://1mapp.cloudyee.com/oauth2/resources'

    data = {
        "interface_code": "foundingTeamGrabActivity",
        "interface_parameters": f'{interface_parameters}',
        "access_token": access_token
    }

    response = requests.post(url, headers=headers, json=data)
    new_interface_parameters = interface_parameters
    new_interface_parameters[1] = response.json()['Result']
    return new_interface_parameters


def jionTeam(interface_parameters, access_token):
    url = 'https://1mapp.cloudyee.com/oauth2/resources'

    data = {
        'interface_code': 'jionTeamGrabActivity',
        'interface_parameters': f'{interface_parameters}',
        'access_token': access_token
    }

    response = requests.post(url, headers=None, json=data)
    return response.json()['RetCode']


def get_reward(interface_parameters, access_token):
    url = "https://1mapp.cloudyee.com/oauth2/resources"

    payload = {
        "interface_code": "grabOrgPackActivityByActivityId",
        "interface_parameters": f'{interface_parameters}',
        "access_token": access_token
    }

    response = requests.post(url, json=payload, headers=None)
    if response.json()['RetCode'] == '000000':
        print('获得优惠卷：', response.json()['Result']['Amount'])


# 读取 223yimi.json 文件
with open('223yimi.json', 'r') as f:
    data = json.load(f)
userids = data['ck1']
shuffle(userids)
# 1是大号2是小号
lst = [
    "20231218112359421337113721298204",  # 大号
    "20220323162016185150603950918001",  # 小号
    "20220225101203502398999269914901",  # 黄嘉铖
    # "20210911113314003689597494276001",  # 陈惠民
    # "20220625094453891585759405487001",  # 张浩锐
    # "20210903180604595326932883516802",  # 郑境荣
    # "20210904231840631341587302309302",  # 张书诚
    "20231218164321461405549823021003",  # 朱宇森1
    "20231217165943349063773311315703",  # 朱宇森2
]

# 获取前两个元素和后面的元素
first_two = lst[:2]
rest = lst[2:]

# 打乱后面的元素
shuffle(rest)

# 组合成新的列表
my_userids = first_two + rest

is_break = 0
del interface_parameters[1]
interface_parameters_copy = interface_parameters[:]
for my_userid in my_userids:
    i = 0
    interface_parameters = interface_parameters_copy[:]
    for count in range(2):
        i += 1
        print(f'\033[31m正在为用户 \033[32m【{my_userid}】\033[31m 进行第 \033[32m【{i}】\033[31m次助力…………\033[0m')
        my_access_token = get_access_token(my_userid)
        interface_parameters.append(my_userid)
        print(interface_parameters)
        new_interface_parameters = get_new_interface_parameters(interface_parameters, my_access_token)
        new_interface_parameters_copy = new_interface_parameters[:]
        if '！' in new_interface_parameters[1]:
            print('\033[33m' + new_interface_parameters[1] + '\033[0m')

            interface_parameters = interface_parameters_copy[:]
            shuffle(userids)
            continue

        new_interface_parameters.append(my_userid)
        print('开始助力…………')
        for userid in userids:
            try:
                access_token = get_access_token(userid)
                new_interface_parameters.insert(1, userid)
                Code = jionTeam(new_interface_parameters, access_token)
                if Code == '100004':
                    print('助力人助力达到上限……跳过')
                if Code == '100005':
                    print('助力人今日已助力过你……')
                if Code == '100006':
                    num = 3
                    break
                if Code == '000000':
                    num += 1
                    print('当前助力人数:', num)
                if num == 3:
                    break
                del new_interface_parameters[1]
                sleep(0.1)
            except (json.decoder.JSONDecodeError) as e:
                print(f'出错了：{e}')
        if num == 3:
            print('助力完成,开始领取奖励…………')
            sleep(0.1)
            get_reward(new_interface_parameters_copy[1:2], my_access_token)
            num = 0
            interface_parameters = interface_parameters_copy[:]
            shuffle(userids)

{"change_date": "2024-05-22 00:43:12", "1": {"url": "http://146.190.107.54", "is_register": true, "is_login": true, "models": {}}, "2": {"url": "http://138.68.133.230", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "3": {"url": "https://143.198.147.186", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-1106:petruzzo-photography:wp-test-01:97xDFTf9", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-wp-01:99ROL9ga", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ4Ub3", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7Oa1:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7boO:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ71WD:ckpt-step-100", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bL7bO:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bLYxo:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bMgWI", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-1106:petruzzo-photography:wp-test-01:97xDFTf9", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-wp-01:99ROL9ga", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ4Ub3", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7Oa1:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7boO:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ71WD:ckpt-step-100", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bL7bO:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bLYxo:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bMgWI", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "4": {"url": "http://146.190.108.20", "is_register": true, "is_login": true, "models": {}}, "5": {"url": "http://170.64.130.116", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4", "gpt-3.5-turbo-16k", "gpt-4-0314", "gpt-4-0613"], "gptPlugins": ["gpt-4-0613"]}}, "6": {"url": "http://172.104.213.13", "is_register": true, "is_login": true, "models": {"Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b:nitro", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "7": {"url": "http://64.23.235.78", "is_register": true, "is_login": true, "models": {}}, "8": {"url": "http://165.227.79.221", "is_register": true, "is_login": true, "models": {}}, "9": {"url": "https://164.92.255.36", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "snowflake/snowflake-arctic-instruct", "mistralai/mixtral-8x7b-instruct:nitro", "open-orca/mistral-7b-openorca", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "10": {"url": "http://147.182.249.230", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"], "assistant": ["gpt-4", "gpt-4-0613"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"]}}, "11": {"url": "http://174.138.48.24", "is_register": true, "is_login": true, "models": {}}, "12": {"url": "http://159.89.185.74", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "13": {"url": "http://167.99.96.187", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-70b-instruct", "snowflake/snowflake-arctic-instruct", "mistralai/mixtral-8x7b-instruct:nitro", "open-orca/mistral-7b-openorca", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "meta-llama/llama-3-8b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "14": {"url": "http://164.92.169.50", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "15": {"url": "http://207.154.222.41", "is_register": true, "is_login": true, "models": {}}, "16": {"url": "http://167.99.200.145", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "assistants": ["gpt-4-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4-turbo", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "17": {"url": "http://165.22.233.202", "is_register": true, "is_login": true, "models": {}}, "18": {"url": "http://104.131.76.169", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "19": {"url": "http://143.198.141.249", "is_register": true, "is_login": true, "models": {}}, "20": {"url": "http://137.184.244.251", "is_register": true, "is_login": true, "models": {}}, "21": {"url": "http://167.99.49.239", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "22": {"url": "https://209.38.226.24", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"], "anthropic": ["claude-1", "claude-instant-1", "claude-2"]}}, "23": {"url": "https://167.99.49.239", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "24": {"url": "http://138.68.106.106", "is_register": true, "is_login": true, "models": {}}, "25": {"url": "https://66.228.57.170", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4-turbo-preview", "gpt-3.5-turbo", "gpt-4", "dall-e-3"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-1106", "gpt-4-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914", "gpt-4", "gpt-3.5-turbo-instruct", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview"]}}, "26": {"url": "http://64.23.171.33", "is_register": true, "is_login": true, "models": {}}, "27": {"url": "https://ofkc2ida.rpcld.net", "is_register": true, "is_login": true, "models": {}}, "28": {"url": "https://chat.orzkz.com", "is_register": true, "is_login": true, "models": {"gptPlugins": ["gpt-4-turbo"], "azureOpenAI": ["gpt-4-turbo"]}}, "29": {"url": "https://gpt.teh.me", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "30": {"url": "https://newai.sq0715.com", "is_register": true, "is_login": true, "models": {}}, "31": {"url": "https://e09rria0.rpcld.app", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-turbo-2024-04-09", "gpt-4-0125-preview", "gpt-4-0613", "gpt-4-turbo", "gpt-4-vision-preview", "gpt-3.5-turbo", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-0613:personal::7vZqIsWe", "ft:gpt-3.5-turbo-0613:personal::7zXotK7o", "ft:gpt-3.5-turbo-0613:personal::7zansPNv", "ft:gpt-3.5-turbo-0613:personal::7zrKaNV1", "ft:gpt-3.5-turbo-0613:personal::80Izyfts", "ft:gpt-3.5-turbo-0613:personal::80dpGZem", "ft:gpt-3.5-turbo-0613:personal::83Avm0G4", "ft:gpt-3.5-turbo-0613:personal::85HqmybV", "ft:gpt-3.5-turbo-0613:personal::861uxiTP", "ft:gpt-3.5-turbo-0613:personal::8IgoIcsG", "ft:gpt-3.5-turbo-0613:personal::8PFpg7bC", "ft:gpt-3.5-turbo-0613:personal::8SYTHZQD", "ft:gpt-3.5-turbo-0613:personal::8SYeSbO5", "ft:gpt-3.5-turbo-0613:personal::8V1vnRlI", "ft:gpt-3.5-turbo-0613:personal::8W66BU5I", "ft:gpt-3.5-turbo-0613:personal::8Z6Rei3P", "ft:gpt-3.5-turbo-0613:personal::8Z6PE8HK", "ft:gpt-3.5-turbo-0613:personal::8Z6PIItv", "ft:gpt-3.5-turbo-0613:personal::8YdL58ZX", "ft:gpt-3.5-turbo-0613:personal::8YdLr8Op", "ft:gpt-3.5-turbo-0613:personal::8YdLsKfZ", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-turbo-2024-04-09", "gpt-4-0125-preview", "gpt-4-0613", "gpt-4-turbo", "gpt-4-vision-preview", "gpt-3.5-turbo", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-0613:personal::7vZqIsWe", "ft:gpt-3.5-turbo-0613:personal::7zXotK7o", "ft:gpt-3.5-turbo-0613:personal::7zansPNv", "ft:gpt-3.5-turbo-0613:personal::7zrKaNV1", "ft:gpt-3.5-turbo-0613:personal::80Izyfts", "ft:gpt-3.5-turbo-0613:personal::80dpGZem", "ft:gpt-3.5-turbo-0613:personal::83Avm0G4", "ft:gpt-3.5-turbo-0613:personal::85HqmybV", "ft:gpt-3.5-turbo-0613:personal::861uxiTP", "ft:gpt-3.5-turbo-0613:personal::8IgoIcsG", "ft:gpt-3.5-turbo-0613:personal::8PFpg7bC", "ft:gpt-3.5-turbo-0613:personal::8SYTHZQD", "ft:gpt-3.5-turbo-0613:personal::8SYeSbO5", "ft:gpt-3.5-turbo-0613:personal::8V1vnRlI", "ft:gpt-3.5-turbo-0613:personal::8W66BU5I", "ft:gpt-3.5-turbo-0613:personal::8Z6Rei3P", "ft:gpt-3.5-turbo-0613:personal::8Z6PE8HK", "ft:gpt-3.5-turbo-0613:personal::8Z6PIItv", "ft:gpt-3.5-turbo-0613:personal::8YdL58ZX", "ft:gpt-3.5-turbo-0613:personal::8YdLr8Op", "ft:gpt-3.5-turbo-0613:personal::8YdLsKfZ", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic1": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "32": {"url": "https://ai.ty0.icu", "is_register": true, "is_login": true, "models": {}}, "33": {"url": "https://ai.sposito.fr", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "34": {"url": "https://ai.swiftlaunch.co", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0125", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "ft:gpt-3.5-turbo-0125:swift-launch::8yMwaE18", "ft:gpt-3.5-turbo-0125:swift-launch::8yOs8Oro", "ft:gpt-3.5-turbo-0613:swift-launch::958Pbrnc", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "35": {"url": "https://chat.teresita.casa", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "36": {"url": "https://cc.luouse.site", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "llama3-70b-8192"], "gptPlugins": ["360GPT_S2_V9", "babbage-002", "chatglm_lite", "chatglm_pro", "chatglm_std", "chatglm_turbo", "ChatPro", "ChatStd", "claude-2", "claude-2.0", "claude-2.1", "claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-instant-1.2", "dall-e-2", "dall-e-3", "davinci-002", "embedding-bert-512-v1", "Embedding-V1", "embedding_s1_v1", "ERNIE-Bot", "ERNIE-Bot-4", "ERNIE-Bot-8K", "ERNIE-Bot-turbo", "ERNIE-Speed", "gemini-1.0-pro-001", "gemini-1.0-pro-vision-001", "glm-3-turbo", "glm-4", "glm-4v", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-32k-0314", "gpt-4-32k-0613", "gpt-4-turbo-preview", "gpt-4-vision-preview", "hun<PERSON>", "mistral-7b-instruct", "mixtral-8x7b-instruct", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "moonshot-v1-128k", "moonshot-v1-32k", "moonshot-v1-8k", "PaLM-2", "qwen-max", "qwen-max-longcontext", "qwen-plus", "qwen-turbo", "semantic_similarity_s1_v1", "sonar-medium-chat", "sonar-medium-online", "sonar-small-chat", "sonar-small-online", "SparkDesk", "SparkDesk-v1.1", "SparkDesk-v2.1", "SparkDesk-v3.1", "SparkDesk-v3.5", "swap_face", "text-ada-001", "text-babbage-001", "text-curie-001", "text-davinci-002", "text-davinci-003", "text-davinci-edit-001", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-embedding-v1", "text-moderation-latest", "text-moderation-stable", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1", "yi-34b-chat-0205", "yi-34b-chat-200k", "yi-vl-plus"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "37": {"url": "http://cc.luouse.site", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "llama3-70b-8192"], "gptPlugins": ["360GPT_S2_V9", "babbage-002", "chatglm_lite", "chatglm_pro", "chatglm_std", "chatglm_turbo", "ChatPro", "ChatStd", "claude-2", "claude-2.0", "claude-2.1", "claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-instant-1.2", "dall-e-2", "dall-e-3", "davinci-002", "embedding-bert-512-v1", "Embedding-V1", "embedding_s1_v1", "ERNIE-Bot", "ERNIE-Bot-4", "ERNIE-Bot-8K", "ERNIE-Bot-turbo", "ERNIE-Speed", "gemini-1.0-pro-001", "gemini-1.0-pro-vision-001", "glm-3-turbo", "glm-4", "glm-4v", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-32k-0314", "gpt-4-32k-0613", "gpt-4-turbo-preview", "gpt-4-vision-preview", "hun<PERSON>", "mistral-7b-instruct", "mixtral-8x7b-instruct", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "moonshot-v1-128k", "moonshot-v1-32k", "moonshot-v1-8k", "PaLM-2", "qwen-max", "qwen-max-longcontext", "qwen-plus", "qwen-turbo", "semantic_similarity_s1_v1", "sonar-medium-chat", "sonar-medium-online", "sonar-small-chat", "sonar-small-online", "SparkDesk", "SparkDesk-v1.1", "SparkDesk-v2.1", "SparkDesk-v3.1", "SparkDesk-v3.5", "swap_face", "text-ada-001", "text-babbage-001", "text-curie-001", "text-davinci-002", "text-davinci-003", "text-davinci-edit-001", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-embedding-v1", "text-moderation-latest", "text-moderation-stable", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1", "yi-34b-chat-0205", "yi-34b-chat-200k", "yi-vl-plus"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "38": {"url": "https://193.122.145.241", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "llama3-70b-8192"], "gptPlugins": ["360GPT_S2_V9", "babbage-002", "chatglm_lite", "chatglm_pro", "chatglm_std", "chatglm_turbo", "ChatPro", "ChatStd", "claude-2", "claude-2.0", "claude-2.1", "claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-instant-1.2", "dall-e-2", "dall-e-3", "davinci-002", "embedding-bert-512-v1", "Embedding-V1", "embedding_s1_v1", "ERNIE-Bot", "ERNIE-Bot-4", "ERNIE-Bot-8K", "ERNIE-Bot-turbo", "ERNIE-Speed", "gemini-1.0-pro-001", "gemini-1.0-pro-vision-001", "glm-3-turbo", "glm-4", "glm-4v", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-32k-0314", "gpt-4-32k-0613", "gpt-4-turbo-preview", "gpt-4-vision-preview", "hun<PERSON>", "mistral-7b-instruct", "mixtral-8x7b-instruct", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "moonshot-v1-128k", "moonshot-v1-32k", "moonshot-v1-8k", "PaLM-2", "qwen-max", "qwen-max-longcontext", "qwen-plus", "qwen-turbo", "semantic_similarity_s1_v1", "sonar-medium-chat", "sonar-medium-online", "sonar-small-chat", "sonar-small-online", "SparkDesk", "SparkDesk-v1.1", "SparkDesk-v2.1", "SparkDesk-v3.1", "SparkDesk-v3.5", "swap_face", "text-ada-001", "text-babbage-001", "text-curie-001", "text-davinci-002", "text-davinci-003", "text-davinci-edit-001", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-embedding-v1", "text-moderation-latest", "text-moderation-stable", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1", "yi-34b-chat-0205", "yi-34b-chat-200k", "yi-vl-plus"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "39": {"url": "https://47.238.239.223", "is_register": true, "is_login": true, "models": {}}, "40": {"url": "http://8.217.250.134:3080", "is_register": true, "is_login": true, "models": {}}, "41": {"url": "https://librechat-librechat.hf.space", "is_register": true, "is_login": true, "models": {"DEEPNIGHT": ["gpt-35-turbo", "gpt-35-turbo-16k", "gpt-4-turbo"]}}, "42": {"url": "http://20.106.189.2:3080", "is_register": true, "is_login": true, "models": {}}, "43": {"url": "http://20.83.101.94", "is_register": true, "is_login": true, "models": {"bingAI": ["BingAI", "Sydney"]}}, "44": {"url": "https://20.230.182.202", "is_register": true, "is_login": true, "models": {"bingAI": ["BingAI", "Sydney"]}}, "45": {"url": "http://47.238.239.223:8080", "is_register": true, "is_login": true, "models": {}}, "46": {"url": "http://93.183.93.91:3080", "is_register": true, "is_login": true, "models": {"OpenRouter": ["perplexity/llama-3-sonar-large-32k-online", "microsoft/wizardlm-2-8x22b", "openai/gpt-4o"]}}, "47": {"url": "http://34.147.184.121", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"], "gptPlugins": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "48": {"url": "http://8.136.106.76:3080", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview"]}}, "49": {"url": "http://162.254.25.148:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-instruct", "gpt-4-0613", "text-davinci-003", "gpt-4-0314"], "gptPlugins": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "50": {"url": "http://217.142.240.46:3080", "is_register": true, "is_login": true, "models": {}}, "51": {"url": "http://89.116.220.221:3080", "is_register": true, "is_login": true, "models": {}}, "52": {"url": "http://114.55.130.246:3080", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4o", "gpt-all"], "gptPlugins": ["claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "dall-e-2", "dall-e-3", "deepseek-chat", "deepseek-coder", "DeepSeek-V2", "gemini-1.5-flash-latest", "gemini-1.5-pro", "gemini-pro", "gemini-pro-vision", "gpt-3.5-net", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0125-preview-tc", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-all", "gpt-4-gizmo-*", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4o", "gpt-4o-2024-05-13", "gpt-4o-n", "kokomi", "llama3-70b", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "net-gpt-3.5-turbo", "search-gpts-chat", "suno-v3", "swap_face", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1"]}}, "53": {"url": "http://45.63.31.23", "is_register": true, "is_login": true, "models": {"anthropic": ["claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1"]}}, "54": {"url": "http://144.24.166.2:3080", "is_register": true, "is_login": true, "models": {"groq": ["gemma-7b-it", "llama2-70b-4096", "llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768"]}}, "55": {"url": "http://149.104.28.132:3080", "is_register": true, "is_login": true, "models": {}}, "56": {"url": "http://46.101.11.77", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "57": {"url": "http://89.117.150.241:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "58": {"url": "http://52.220.237.60:3080", "is_register": true, "is_login": true, "models": {}}, "59": {"url": "https://chat.checkersoftware.ca", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o-2024-05-13", "gpt-3.5-turbo-16k", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-3.5-turbo-16k", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "60": {"url": "https://5.78.42.231", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o-2024-05-13", "gpt-3.5-turbo-16k", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-3.5-turbo-16k", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "61": {"url": "http://89.117.77.131:3080", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "Proxy": ["gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-4", "gpt-4-0613", "gpt-4-0314", "gpt-4-32k", "gpt-4-32k-0314", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "text-embedding-ada-002"]}}, "62": {"url": "https://chat.ddaodan.cn", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-4", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "63": {"url": "https://212.50.250.176", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-4", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "64": {"url": "http://74.48.24.238:3080", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-32k", "gpt-4-turbo-preview", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large", "text-curie-001", "text-babbage-001", "text-ada-001", "text-davinci-002", "text-davinci-003", "dall-e-2", "dall-e-3", "whisper-1", "tts-1", "tts-1-hd", "claude-3-haiku-20240307", "claude-3-sonnet-20240229", "claude-3-opus-20240229", "qwen-turbo", "qwen-plus", "qwen-max", "qwen-max-longcontext", "SparkDesk-v3.5", "gemini-pro", "gemini-1.5-pro", "gemini-pro-vision", "claude-3-sonnet-20240229", "claude-3-opus-20240229", "claude-3-haiku-20240307", "command", "command-nightly", "command-light", "command-light-nightly", "command-r", "command-r-plus", "moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k", "gemma-7b-it", "llama2-70b-4096", "mixtral-8x7b-32768", "llama3-8b-8192", "llama3-70b-8192", "deepseek-chat", "deepseek-coder", "gpt-4o", "gpt-4-1106-vision-preview", "gpt-4o-2024-05-13", "gemini-1.5-flash"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-32k", "gpt-4-turbo-preview", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large", "text-curie-001", "text-babbage-001", "text-ada-001", "text-davinci-002", "text-davinci-003", "dall-e-2", "dall-e-3", "whisper-1", "tts-1", "tts-1-hd", "claude-3-haiku-20240307", "claude-3-sonnet-20240229", "claude-3-opus-20240229", "qwen-turbo", "qwen-plus", "qwen-max", "qwen-max-longcontext", "SparkDesk-v3.5", "gemini-pro", "gemini-1.5-pro", "gemini-pro-vision", "claude-3-sonnet-20240229", "claude-3-opus-20240229", "claude-3-haiku-20240307", "command", "command-nightly", "command-light", "command-light-nightly", "command-r", "command-r-plus", "moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k", "gemma-7b-it", "llama2-70b-4096", "mixtral-8x7b-32768", "llama3-8b-8192", "llama3-70b-8192", "deepseek-chat", "deepseek-coder", "gpt-4o", "gpt-4-1106-vision-preview", "gpt-4o-2024-05-13", "gemini-1.5-flash"]}}, "65": {"url": "http://20.5.240.200:3080", "is_register": true, "is_login": true, "models": {}}, "66": {"url": "http://15.204.245.100:3080", "is_register": true, "is_login": true, "models": {}}, "67": {"url": "http://138.201.48.160:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-32k-0314", "gpt-3.5-turbo-16k-0613", "gpt-4-1106-vision-preview", "gpt-4-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0314", "gpt-4-turbo-2024-04-09", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-4-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-32k-0314", "gpt-3.5-turbo-16k-0613", "gpt-4-1106-vision-preview", "gpt-4-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0314", "gpt-4-turbo-2024-04-09", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-4-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "68": {"url": "https://54.176.181.249", "is_register": true, "is_login": true, "models": {"Finflux": ["anthropic/claude-3-sonnet", "openai/gpt-4-turbo", "google/gemini-pro-1.5", "openai/gpt-3.5-turbo-0125"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b:nitro", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "69": {"url": "https://18.162.45.224", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "gpt-4-32k", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "70": {"url": "http://5.181.51.30:9002", "is_register": true, "is_login": true, "models": {}}, "71": {"url": "https://gpt.share.vc", "is_register": true, "is_login": true, "models": {"1440": ["1440"], "openAI1": ["gpt-4o", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-0613:sharelabs::7rpGBBP4", "ft:gpt-3.5-turbo-0613:sharelabs::7sYH8HFQ", "ft:gpt-3.5-turbo-0613:sharelabs::7sYch3Xa", "ft:gpt-3.5-turbo-0613:sharelabs::7ukXDibu", "ft:gpt-3.5-turbo-0613:sharelabs::7uxka5BZ", "ft:gpt-3.5-turbo-0613:sharelabs::7vALdRem", "ft:gpt-3.5-turbo-0613:sharelabs::7vicVgBE", "ft:gpt-3.5-turbo-0613:sharelabs::7xUOynxi", "ft:gpt-3.5-turbo-0613:sharelabs::84rG2Ld9", "ft:gpt-3.5-turbo-1106:sharelabs:phil:8S0DuTKl", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "Spree": ["Spree"], "Feno": ["<PERSON><PERSON>"], "Instill": ["Instill"], "Shareland": ["Shareland"], "groq": ["gemma-7b-it", "llama2-70b-4096", "llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "Perplexity": ["sonar-medium-online", "sonar-small-online", "sonar-medium-chat", "sonar-small-chat"], "together.ai": ["togethercomputer/llama-2-70b-chat", "togethercomputer/alpaca-7b", "togethercomputer/llama-2-13b-chat", "togethercomputer/llama-2-7b-chat", "teknium/OpenHermes-2p5-Mistral-7B", "lmsys/vicuna-13b-v1.5-16k", "NousResearch/Nous-Capybara-7B-V1p9", "NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO", "NousResearch/Nous-Hermes-2-Mixtral-8x7B-SFT", "NousResearch/Nous-Hermes-Llama2-70b", "NousResearch/Nous-Hermes-llama-2-7b", "NousResearch/Nous-Hermes-Llama2-13b", "NousResearch/Nous-Hermes-2-Yi-34B", "openchat/openchat-3.5-1210", "Open-Orca/Mistral-7B-OpenOrca", "togethercomputer/Qwen-7B-Chat", "snorkelai/Snorkel-Mistral-PairRM-DPO", "togethercomputer/falcon-40b-instruct", "togethercomputer/falcon-7b-instruct", "togethercomputer/GPT-NeoXT-Chat-Base-20B", "togethercomputer/Llama-2-7B-32K-Instruct", "togethercomputer/Pythia-Chat-Base-7B-v0.16", "togethercomputer/RedPajama-INCITE-Chat-3B-v1", "togethercomputer/RedPajama-INCITE-7B-Chat", "togethercomputer/StripedHyena-Nous-7B", "Undi95/ReMM-SLERP-L2-13B", "Undi95/Toppy-M-7B", "WizardLM/WizardLM-13B-V1.2", "garage-bAInd/Platypus2-70B-instruct", "mistralai/Mistral-7B-Instruct-v0.1", "mistralai/Mistral-7B-Instruct-v0.2", "mistralai/Mixtral-8x7B-Instruct-v0.1", "teknium/OpenHermes-2-Mistral-7B", "upstage/SOLAR-10.7B-Instruct-v1.0", "zero-one-ai/Yi-34B-<PERSON><PERSON>", "Austism/chronos-hermes-13b", "DiscoResearch/DiscoLM-mixtral-8x7b-v2", "Gryphe/MythoMax-L2-13b", "lmsys/vicuna-13b-v1.5", "lmsys/vicuna-7b-v1.5", "codellama/CodeLlama-13b-Instruct-hf", "codellama/CodeLlama-34b-Instruct-hf", "codellama/CodeLlama-70b-Instruct-hf", "codellama/CodeLlama-7b-Instruct-hf"]}}, "72": {"url": "http://***************", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-instruct", "gpt-4-0613", "text-davinci-003", "gpt-4-0314"], "gptPlugins": ["gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "73": {"url": "https://23.224.174.238", "is_register": true, "is_login": true, "models": {}}, "74": {"url": "http://54.237.100.49", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-4o"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "75": {"url": "https://74.48.44.133", "is_register": true, "is_login": true, "models": {}}, "76": {"url": "https://159.69.221.63", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-instant-1-100k"], "Cohere": ["Cohere Command R+"], "Meta": ["Meta Llama 3 70B"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "Sven Heim IT": ["Meta-Llama-3-8B-Instruct.Q5_K_M"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4"]}}, "77": {"url": "https://chat.sysee.cn", "is_register": true, "is_login": true, "models": {}}, "78": {"url": "https://henrikchat.de", "is_register": true, "is_login": true, "models": {}}, "79": {"url": "https://51.75.73.178", "is_register": true, "is_login": true, "models": {}}, "80": {"url": "https://74.48.107.40", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "81": {"url": "https://gpt.raojialong.love", "is_register": true, "is_login": true, "models": {}}, "82": {"url": "https://www.mamanempires.com", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "83": {"url": "https://mamanempires.com", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "84": {"url": "https://165.22.42.172", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "85": {"url": "http://163.172.52.180:2096", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "jetmoe/jetmoe-8b-chat:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b:nitro", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "jetmoe/jetmoe-8b-chat", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "86": {"url": "https://gamerai.livewire.group", "is_register": true, "is_login": true, "models": {"assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"]}}, "87": {"url": "https://3.24.165.23", "is_register": true, "is_login": true, "models": {"assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"]}}, "88": {"url": "http://143.42.34.42:3333", "is_register": true, "is_login": true, "models": {"assistants": ["gpt-4o", "gpt-4-turbo-preview"], "openAI2": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "89": {"url": "https://chat.fanetz.com", "is_register": true, "is_login": true, "models": {}}, "90": {"url": "http://107.175.111.43:81", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "lynn/soliloquy-l3", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "gryphe/mythomax-l2-13b", "meta-llama/llama-2-13b-chat", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-70b-instruct", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "meta-llama/llama-3-8b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "91": {"url": "https://91.107.135.152", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "92": {"url": "https://103.170.232.165", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo", "gpt-4", "gpt-4-vision-preview"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-0125-preview", "gpt-4-0613", "gpt-4-turbo", "gpt-4-vision-preview", "gpt-3.5-turbo", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "93": {"url": "http://92.119.127.55:3080", "is_register": true, "is_login": true, "models": {"NagaAI": ["gpt-4o", "gpt-4o-2024-05-13", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "gpt-4-1106-vision-preview", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4", "gpt-4-0613", "gemini-1.5-pro-latest", "llama-3-70b-instruct", "llama-3-8b-instruct", "mixtral-8x22b-instruct", "command-r-plus", "command-r", "mistral-large", "mistral-large-2402", "mistral-next", "mistral-small", "mistral-small-2402", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-opus", "claude-3-opus-20240229", "claude-3-sonnet", "claude-3-sonnet-20240229", "claude-3-haiku", "claude-3-haiku-20240307", "claude-2.1", "claude-instant", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b-instruct", "mixtral-8x7b-instruct", "stable-diffusion-3", "midjourney", "dall-e-3", "playground-v2.5", "sdxl", "kandinsky-3.1", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large", "m2m100-1.2b", "xtts-v2", "bark", "google-tts-1"]}}, "94": {"url": "http://62.171.137.122:3080", "is_register": true, "is_login": true, "models": {}}, "95": {"url": "http://83.229.121.81", "is_register": true, "is_login": true, "models": {}}, "96": {"url": "http://109.107.190.209:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gemini-1.5-pro-latest", "gemini-1.5-flash-latest", "llama-3-70b-instruct", "llama-3-8b-instruct", "mixtral-8x22b-instruct", "command-r-plus", "command-r", "mistral-large", "mistral-large-2402", "mistral-next", "mistral-small", "mistral-small-2402", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-haiku", "claude-3-haiku-20240307", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b-instruct", "mixtral-8x7b-instruct", "playground-v2.5", "sdxl", "kandinsky-3.1", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large", "m2m100-1.2b", "google-tts-1"], "bingAI": ["BingAI", "Sydney"], "chatGPTBrowser": ["text-davinci-002-render-sha"], "gptPlugins": ["gemini-1.5-pro-latest", "gemini-1.5-flash-latest", "llama-3-70b-instruct", "llama-3-8b-instruct", "mixtral-8x22b-instruct", "command-r-plus", "command-r", "mistral-large", "mistral-large-2402", "mistral-next", "mistral-small", "mistral-small-2402", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-haiku", "claude-3-haiku-20240307", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b-instruct", "mixtral-8x7b-instruct", "playground-v2.5", "sdxl", "kandinsky-3.1", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large", "m2m100-1.2b", "google-tts-1"], "anthropic": ["claude-1", "claude-instant-1", "claude-2"]}}, "97": {"url": "http://188.165.209.51:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "Ollama": ["llama3", "llama2", "mistral", "codellama", "dolphin-mixtral", "mistral-openorca"]}}, "98": {"url": "http://207.154.222.41:3080", "is_register": true, "is_login": true, "models": {}}, "99": {"url": "https://chat.wonk.ai", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-instant-1-100k"], "Cohere": ["Cohere Command R+"], "Meta": ["Meta Llama 3 70B"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "Sven Heim IT": ["Meta-Llama-3-8B-Instruct.Q5_K_M"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4"]}}, "100": {"url": "http://74.48.81.85:3080", "is_register": true, "is_login": true}, "101": {"url": "http://45.138.71.234:3081", "is_register": true, "is_login": true, "models": {}}, "102": {"url": "https://susie.ottodeng.com", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-4-turbo"], "gptPlugins": ["gpt-4-turbo"]}}, "103": {"url": "https://20.2.8.69", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-4-turbo"], "gptPlugins": ["gpt-4-turbo"]}}, "104": {"url": "http://13.64.193.225:3080", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-4"], "gptPlugins": ["gpt-4"]}}, "105": {"url": "http://116.211.228.182:3080", "is_register": true, "is_login": true, "models": {}}, "106": {"url": "https://librechat.endpoints.q-gcp-ps1-p2-pd-baioniq-23-02.cloud.goog", "is_register": true, "is_login": true, "models": {}}, "107": {"url": "https://app.neuralinx.ai", "is_register": true, "is_login": true, "models": {}}, "108": {"url": "https://redhorse.cloud", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "109": {"url": "https://www.redhorse.cloud", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "110": {"url": "http://redhorse.cloud", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "111": {"url": "http://www.redhorse.cloud", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "112": {"url": "https://chat.personalfinancialai.com", "is_register": true, "is_login": true, "models": {"Mistral": []}}, "113": {"url": "https://13.92.119.188", "is_register": true, "is_login": true, "models": {"Mistral": []}}, "114": {"url": "http://138.68.113.96", "is_register": true, "is_login": true, "models": {}}, "115": {"url": "https://ny2.rfarelay.xyz", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "Proxy": ["gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-4", "gpt-4-0613", "gpt-4-0314", "gpt-4-32k", "gpt-4-32k-0314", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "text-embedding-ada-002"]}}, "116": {"url": "https://4.190.127.69", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-35-turbo"], "ntt-tsuzumi": ["v1-7b-instruct-ct2"]}}, "117": {"url": "https://chat.veryclever.ai", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"], "anthropic": ["claude-1", "claude-instant-1", "claude-2"]}}, "118": {"url": "https://www.librechat.cloud", "is_register": true, "is_login": true, "models": {}}, "119": {"url": "http://74.208.106.176:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "gptPlugins": ["text-davinci-003", "gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914"]}}, "120": {"url": "http://3.122.170.230", "is_register": true, "is_login": true, "models": {}}, "121": {"url": "http://213.170.133.176", "is_register": true, "is_login": true, "models": {}}, "122": {"url": "http://82.33.251.180:3080", "is_register": true, "is_login": true, "models": {}}, "123": {"url": "https://aiwort.com", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "124": {"url": "https://152.67.212.191", "is_register": true, "is_login": true, "models": {"GPT": ["gpt4-turbo-offline", "gpt4-turbo-online", "gpt4-offline", "gpt4-online", "gpt3.5-online"]}}, "125": {"url": "http://46.4.69.184:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["mistralai_Mistral-7B-v0.1", "mixtral-8x7b-v0.1.Q4_K_M.gguf"], "gptPlugins": ["mistralai_Mistral-7B-v0.1", "mixtral-8x7b-v0.1.Q4_K_M.gguf"]}}, "126": {"url": "http://173.249.1.65", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0613", "gpt-4-0125-preview", "gpt-3.5-turbo-0301", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-3.5-turbo-0613", "gpt-4-0125-preview", "gpt-3.5-turbo-0301", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "127": {"url": "http://120.77.78.46", "is_register": true, "is_login": true, "models": {}}, "128": {"url": "https://lb.xhalo.top", "is_register": true, "is_login": true, "models": {"openAI2": [], "bingAI": ["BingAI", "Sydney"], "chatGPTBrowser": ["text-davinci-002-render-sha"], "gptPlugins": []}}, "129": {"url": "https://4.194.103.119", "is_register": true, "is_login": true, "models": {"gptPlugins": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768", "gemma-7b-it"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "assistants": ["gpt-4"]}}, "130": {"url": "http://141.98.168.163", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-3.5-turbo-16k-0613", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-3.5-turbo-16k-0613", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "lynn/soliloquy-l3", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b", "meta-llama/llama-2-13b-chat", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-70b-instruct", "mistralai/mixtral-8x7b-instruct:nitro", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "meta-llama/llama-3-8b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "131": {"url": "http://165.227.179.87", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "databricks/dbrx-instruct", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "132": {"url": "http://180.184.181.166:10002", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-4-vision-preview", "gpt-4-turbo-preview"], "gptPlugins": ["gpt-4-turbo-preview", "gpt-3.5-turbo-0125"]}}, "133": {"url": "https://ai.try-chatgpt.fun", "is_register": true, "is_login": true, "models": {}}, "134": {"url": "http://ai.try-chatgpt.fun", "is_register": true, "is_login": true, "models": {}}, "135": {"url": "https://chat.kemuri.top", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-4o", "claude-3-opus", "gemini-1.0-ultra", "gemini-1.5-pro", "mixtral-8x7b", "lzlv-70b", "airoboros", "gemma-7b", "llama-3-70b", "codellama-34b", "codellama-70b", "gorilla-openfunctions-v2", "zephyr-orpo", "dbrx-instruct", "wizardlm-2-8x22b", "openchat-latest", "deepseek-coder-latest"], "gptPlugins": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "136": {"url": "https://141.147.17.160", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-4o", "claude-3-opus", "gemini-1.0-ultra", "gemini-1.5-pro", "mixtral-8x7b", "lzlv-70b", "airoboros", "gemma-7b", "llama-3-70b", "codellama-34b", "codellama-70b", "gorilla-openfunctions-v2", "zephyr-orpo", "dbrx-instruct", "wizardlm-2-8x22b", "openchat-latest", "deepseek-coder-latest"], "gptPlugins": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "137": {"url": "http://52.231.185.81:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b:nitro", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "138": {"url": "https://34.128.145.75", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "139": {"url": "http://103.74.174.152:3080", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-instruct", "gpt-4-0613", "text-davinci-003", "gpt-4-0314"], "gptPlugins": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "mistralai/mixtral-8x7b-instruct", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "140": {"url": "https://chatgpt.gomonta.com", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo", "gpt-4"], "assistant": ["gpt-4"], "gptPlugins": ["gpt-4"]}}, "141": {"url": "http://198.46.226.118:3080", "is_register": true, "is_login": true, "models": {}}, "142": {"url": "https://camaro.tokyo", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "143": {"url": "https://121.82.111.169", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "144": {"url": "https://lumechat.com", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-turbo-2024-04-09", "gpt-4-0613", "gpt-4-turbo", "gpt-4-vision-preview", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-turbo-2024-04-09", "gpt-4-0613", "gpt-4-turbo", "gpt-4-vision-preview", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"]}}, "145": {"url": "https://libre.williampetruzzo.com", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-1106:petruzzo-photography:wp-test-01:97xDFTf9", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-wp-01:99ROL9ga", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ4Ub3", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7Oa1:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7boO:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ71WD:ckpt-step-100", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bL7bO:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bLYxo:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bMgWI", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-1106:petruzzo-photography:wp-test-01:97xDFTf9", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-wp-01:99ROL9ga", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ4Ub3", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7Oa1:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ7boO:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography::9EfZ71WD:ckpt-step-100", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bL7bO:ckpt-step-80", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bLYxo:ckpt-step-90", "ft:gpt-3.5-turbo-1106:petruzzo-photography:pzo-blog-v1:9F8bMgWI", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "146": {"url": "http://101.201.44.23", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-16k-0613", "dall-e-3", "text-embedding-3-large", "dall-e-2", "whisper-1", "tts-1-hd-1106", "tts-1-hd", "gpt-3.5-turbo-0125", "text-embedding-3-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914", "tts-1", "davinci-002", "gpt-3.5-turbo-instruct", "babbage-002", "tts-1-1106", "text-embedding-ada-002"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "dall-e-3", "text-embedding-3-large", "dall-e-2", "whisper-1", "tts-1-hd-1106", "tts-1-hd", "gpt-3.5-turbo-0125", "text-embedding-3-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914", "tts-1", "davinci-002", "gpt-3.5-turbo-instruct", "babbage-002", "tts-1-1106", "text-embedding-ada-002"]}}, "147": {"url": "https://magic.ninomae.cn", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-turbo-preview", "lmsys-gpt-4o", "lmsys-gpt-4-turbo", "long-gpt-4", "long-gpt-4-1106-preview", "long-gpt-4-turbo", "chat-pro-gpt-3.5-turbo", "chat-pro-gpt-4-1106-preview", "chat-pro-gpt-4-pro-max", "chat-pro-google-chat-bison", "chat-pro-google-text-bison", "chat-pro-google-codechat-bison", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "gemini-1.5-pro-preview", "im-a-good-gpt2-chatbot", "im-also-a-good-gpt2-chatbot", "llama-3-sonar-large-32k-online", "llama-3-sonar-small-32k-online", "llama-3-sonar-large-32k-chat", "llama-3-sonar-small-32k-chat", "dbrx-instruct", "llama-3-8b-instruct", "llama-3-70b-instruct", "codellama-70b-instruct", "mistral-7b-instruct", "llava-v1.5-7b", "llava-v1.6-34b", "mixtral-8x7b-instruct", "mixtral-8x22b-instruct", "mistral-medium", "gemma-2b-it", "gemma-7b-it"]}}, "148": {"url": "http://107.173.179.13:8080", "is_register": true, "is_login": true, "models": {"openAI": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"], "gptPlugins": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "149": {"url": "https://leteasyai.cn", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"]}}, "150": {"url": "https://www.leteasyai.cn", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"]}}, "151": {"url": "https://49.51.202.45", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4-1106-preview", "gpt-4-1106-vision-preview"]}}, "152": {"url": "http://159.223.93.149", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106"]}}, "153": {"url": "http://114.115.175.108:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k-0613", "dall-e-3", "text-embedding-3-large", "dall-e-2", "whisper-1", "tts-1-hd-1106", "tts-1-hd", "gpt-3.5-turbo-0125", "text-embedding-3-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914", "tts-1", "davinci-002", "gpt-3.5-turbo-instruct", "babbage-002", "tts-1-1106", "text-embedding-ada-002"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "dall-e-3", "text-embedding-3-large", "dall-e-2", "whisper-1", "tts-1-hd-1106", "tts-1-hd", "gpt-3.5-turbo-0125", "text-embedding-3-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct-0914", "tts-1", "davinci-002", "gpt-3.5-turbo-instruct", "babbage-002", "tts-1-1106", "text-embedding-ada-002"]}}, "154": {"url": "http://47.251.63.244", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0613", "gpt-4", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914"], "gptPlugins": ["gpt-4-0613", "gpt-4", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914"]}}, "155": {"url": "http://5.181.51.30:3080", "is_register": true, "is_login": true, "models": {}}, "156": {"url": "http://34.143.129.133", "is_register": true, "is_login": true, "models": {}}, "157": {"url": "http://54.151.191.184", "is_register": true, "is_login": true, "models": {}}, "158": {"url": "http://64.225.13.132:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "159": {"url": "https://chat.libre.techtanx.com", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-instruct", "gpt-4-0613", "text-davinci-003", "gpt-4-0314"], "gptPlugins": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "160": {"url": "http://91.244.197.122:3080", "is_register": true, "is_login": true, "models": {}}, "161": {"url": "http://138.68.133.230:3080", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "162": {"url": "http://116.202.215.49:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-0613:personal::8GbWUtxM", "ft:gpt-3.5-turbo-0613:personal::8Gcnl04A", "ft:gpt-3.5-turbo-0613:personal::8Gdh77ju", "ft:gpt-3.5-turbo-0613:personal::8GntJsgY", "ft:gpt-3.5-turbo-0613:personal::8GqkG4oA", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "ft:gpt-3.5-turbo-0613:personal::8GbWUtxM", "ft:gpt-3.5-turbo-0613:personal::8Gcnl04A", "ft:gpt-3.5-turbo-0613:personal::8Gdh77ju", "ft:gpt-3.5-turbo-0613:personal::8GntJsgY", "ft:gpt-3.5-turbo-0613:personal::8GqkG4oA", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "163": {"url": "http://5.45.110.139:3080", "is_register": true, "is_login": true, "models": {}}, "164": {"url": "http://142.171.105.174:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview"], "assistants": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview"], "gptPlugins": ["gpt-4", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"]}}, "165": {"url": "http://157.90.112.85:3081", "is_register": true, "is_login": true, "models": {}}, "166": {"url": "http://138.2.89.68:3081", "is_register": true, "is_login": true, "models": {}}, "167": {"url": "http://8.222.243.194", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "xwin-lm/xwin-lm-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-70b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "01-ai/yi-34b-200k", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "168": {"url": "http://173.249.1.65:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0613", "gpt-4-0125-preview", "gpt-3.5-turbo-0301", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-3.5-turbo-0613", "gpt-4-0125-preview", "gpt-3.5-turbo-0301", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "169": {"url": "http://167.99.234.34", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4o"], "assistants": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301"], "Mistral": ["open-mistral-7b", "mistral-tiny-2312", "mistral-tiny", "open-mixtral-8x7b", "open-mixtral-8x22b", "open-mixtral-8x22b-2404", "mistral-small-2312", "mistral-small", "mistral-small-2402", "mistral-small-latest", "mistral-medium-latest", "mistral-medium-2312", "mistral-medium", "mistral-large-latest", "mistral-large-2402", "mistral-embed"], "OpenRouter": ["databricks/dbrx-instruct", "sophosympatheia/midnight-rose-70b", "cohere/command", "nousresearch/nous-capybara-34b", "openrouter/auto", "nousresearch/nous-capybara-7b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "google/gemma-7b-it:free", "alpindale/goliath-120b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "gryphe/mythomist-7b", "openrouter/cinematika-7b", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "mistralai/mixtral-8x7b-instruct", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/sonar-small-online", "perplexity/llama-3-sonar-large-32k-online", "mistralai/mistral-large", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-70b", "jondurbin/airoboros-l2-70b", "migtissera/synthia-70b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "pygmalionai/mythalion-13b", "undi95/remm-slerp-l2-13b", "xwin-lm/xwin-lm-70b", "gryphe/mythomax-l2-13b-8k", "undi95/toppy-m-7b", "alpindale/goliath-120b", "lizpreciatior/lzlv-70b-fp16-hf", "neversleep/noromaid-20b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "cognitivecomputations/dolphin-mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-instant-v1", "anthropic/claude-instant-v1-100k", "mancer/weaver", "open-orca/mistral-7b-openorca", "gryphe/mythomax-l2-13b"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "170": {"url": "http://**************:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "mistral-large", "mistral-next", "mistral-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-opus", "claude-3-sonnet", "claude-2", "claude-instant", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b", "mixtral-8x7b", "midjourney", "dall-e-3", "playground-v2.5", "sdxl", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large-v3", "whisper-1", "m2m100-1.2b", "xtts-v2", "bark", "google-tts-1"], "gptPlugins": ["gpt-4", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "mistral-large", "mistral-next", "mistral-small", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-opus", "claude-3-sonnet", "claude-2", "claude-instant", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b", "mixtral-8x7b", "midjourney", "dall-e-3", "playground-v2.5", "sdxl", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large-v3", "whisper-1", "m2m100-1.2b", "xtts-v2", "bark", "google-tts-1"]}}, "171": {"url": "http://89.232.185.141", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "172": {"url": "http://18.219.8.21", "is_register": true, "is_login": true, "models": {}}, "173": {"url": "http://52.59.150.176", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0125", "gpt-4-0613", "gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4-0125-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0125", "gpt-4-0613", "gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4-0125-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "174": {"url": "http://140.83.82.111:3080", "is_register": true, "is_login": true, "models": {"openAI": [], "gptPlugins": []}}, "175": {"url": "https://43.163.235.97", "is_register": true, "is_login": true, "models": {}}, "176": {"url": "http://8.134.190.15:3080", "is_register": true, "is_login": true, "models": {"chatGPTBrowser": ["text-davinci-002-render-sha"]}}, "177": {"url": "https://www.gpteams.co", "is_register": true, "is_login": true, "models": {}}, "178": {"url": "https://gpteams.co", "is_register": true, "is_login": true, "models": {}}, "179": {"url": "https://152.42.233.155", "is_register": true, "is_login": true, "models": {}}, "180": {"url": "http://158.180.234.220:3080", "is_register": true, "is_login": true, "models": {}}, "181": {"url": "http://89.168.74.155:3080", "is_register": true, "is_login": true, "models": {"openAI": [], "gptPlugins": []}}, "182": {"url": "http://142.171.119.77", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview"], "assistants": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview"], "gptPlugins": ["gpt-4", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"]}}, "183": {"url": "https://axionz.net", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "chatGPTBrowser": ["text-davinci-002-render-sha", "gpt-4"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "184": {"url": "https://www.axionz.net", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "chatGPTBrowser": ["text-davinci-002-render-sha", "gpt-4"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "185": {"url": "https://140.99.243.187", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "chatGPTBrowser": ["text-davinci-002-render-sha", "gpt-4"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "186": {"url": "https://chat.naga.ac", "is_register": true, "is_login": true, "models": {}}, "187": {"url": "http://216.48.187.57:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613"]}}, "188": {"url": "http://103.117.20.175:3080", "is_register": true, "is_login": true, "models": {}}, "189": {"url": "https://r1.openlinuxwyfzcm.shop", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview"], "chatGPTBrowser": ["text-da<PERSON><PERSON>", "gpt-3.5-turbo", "gpt-3.5-turbo-16K", "gpt-4-1106-preview", "gpt-4"], "gptPlugins": ["gpt-3.5-turbo"]}}, "190": {"url": "https://107.173.114.36", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview"], "chatGPTBrowser": ["text-da<PERSON><PERSON>", "gpt-3.5-turbo", "gpt-3.5-turbo-16K", "gpt-4-1106-preview", "gpt-4"], "gptPlugins": ["gpt-3.5-turbo"]}}, "191": {"url": "http://199.126.81.252:3080", "is_register": true, "is_login": true, "models": {}}, "192": {"url": "http://49.13.53.128:3080", "is_register": true, "is_login": true, "models": {}}, "193": {"url": "http://145.239.6.41:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0613", "gpt-4", "text-davinci-003", "gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-4-0613", "gpt-4", "text-davinci-003", "gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k"]}}, "194": {"url": "https://librechat.chaton.fun", "is_register": true, "is_login": true, "models": {}}, "195": {"url": "http://103.116.247.126:3080", "is_register": true, "is_login": true, "models": {}}, "196": {"url": "http://129.211.0.77:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "whisper-1", "davinci-002", "gpt-3.5-turbo", "dall-e-2", "tts-1-hd-1106", "tts-1-hd", "text-embedding-3-large", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "tts-1", "dall-e-3", "gpt-3.5-turbo-1106", "babbage-002", "tts-1-1106", "text-embedding-3-small", "text-embedding-ada-002", "gpt-3.5-turbo-0125"]}}, "197": {"url": "https://ask.aatihra.ch", "is_register": true, "is_login": true, "models": {}}, "198": {"url": "http://38.114.103.39:3080", "is_register": true, "is_login": true, "models": {}}, "199": {"url": "http://5.75.241.26", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"]}}, "200": {"url": "https://ai.fie.rs", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-vision-preview", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-vision-preview", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-0613", "gpt-4-turbo-preview", "gpt-4", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "201": {"url": "http://137.184.12.95:3080", "is_register": true, "is_login": true, "models": {}}, "202": {"url": "https://test.nova315.com", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "ft:gpt-3.5-turbo-0125:personal::8xwp5wte", "ft:gpt-3.5-turbo-0125:personal::8zhestFe", "ft:gpt-3.5-turbo-0125:personal::90tDFegO", "ft:gpt-3.5-turbo-0125:personal::90fX5SFs", "ft:gpt-3.5-turbo-0613:personal::90pYlX2P", "ft:gpt-3.5-turbo-1106:personal::90pgaD39", "ft:gpt-3.5-turbo-1106:personal::90qndsZk", "ft:gpt-3.5-turbo-0613:personal::90r<PERSON><PERSON>b", "ft:gpt-3.5-turbo-0125:personal::92a5np2a", "ft:gpt-3.5-turbo-0125:personal::92aWyY95", "ft:gpt-3.5-turbo-0125:personal::92hKiwf1", "ft:gpt-3.5-turbo-0125:personal::9EzqBXg8", "ft:gpt-3.5-turbo-0125:personal::9EzqBFyf:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal::9EzqBuzM:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVAY9D:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVBsC3:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVBFHH", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxFwmA:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxGDzY:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxGpFi", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mLahq:ckpt-step-232", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mM5zg:ckpt-step-261", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mMyVd", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX48ID:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX5Gai:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX5TdU", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8SIwm:ckpt-step-264", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8SwAs:ckpt-step-297", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8S0PZ", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgJCwk", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgIZku:ckpt-step-231", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgJLx0:ckpt-step-264", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRkGPa:ckpt-step-272", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRkT6w:ckpt-step-306", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRlR6g", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpGQDr", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpG5ZY:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpGRv3:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUk4sp:ckpt-step-312", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUkOfa:ckpt-step-351", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUkOFs", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKwxT:ckpt-step-312", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKNmE:ckpt-step-351", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKatZ", "ft:gpt-3.5-turbo-0125:personal::9K06CV13:ckpt-step-144", "ft:gpt-3.5-turbo-0125:personal::9K06DYze:ckpt-step-162", "ft:gpt-3.5-turbo-0125:personal::9K06DrsI", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhDlTM:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhDRUr:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhELmS"], "gptPlugins": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4o-2024-05-13", "ft:gpt-3.5-turbo-0125:personal::8xwp5wte", "ft:gpt-3.5-turbo-0125:personal::8zhestFe", "ft:gpt-3.5-turbo-0125:personal::90tDFegO", "ft:gpt-3.5-turbo-0125:personal::90fX5SFs", "ft:gpt-3.5-turbo-0613:personal::90pYlX2P", "ft:gpt-3.5-turbo-1106:personal::90pgaD39", "ft:gpt-3.5-turbo-1106:personal::90qndsZk", "ft:gpt-3.5-turbo-0613:personal::90r<PERSON><PERSON>b", "ft:gpt-3.5-turbo-0125:personal::92a5np2a", "ft:gpt-3.5-turbo-0125:personal::92aWyY95", "ft:gpt-3.5-turbo-0125:personal::92hKiwf1", "ft:gpt-3.5-turbo-0125:personal::9EzqBXg8", "ft:gpt-3.5-turbo-0125:personal::9EzqBFyf:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal::9EzqBuzM:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVAY9D:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVBsC3:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0508:9MdVBFHH", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxFwmA:ckpt-step-80", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxGDzY:ckpt-step-90", "ft:gpt-3.5-turbo-0125:personal:c0427:9ITxGpFi", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mLahq:ckpt-step-232", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mM5zg:ckpt-step-261", "ft:gpt-3.5-turbo-0125:personal:c0504:9L1mMyVd", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX48ID:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX5Gai:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0506v2:9LvX5TdU", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8SIwm:ckpt-step-264", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8SwAs:ckpt-step-297", "ft:gpt-3.5-turbo-0125:personal:c0505v3:9LM8S0PZ", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgJCwk", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgIZku:ckpt-step-231", "ft:gpt-3.5-turbo-0125:personal:c0505v5:9LMgJLx0:ckpt-step-264", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRkGPa:ckpt-step-272", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRkT6w:ckpt-step-306", "ft:gpt-3.5-turbo-0125:personal:c0505v6:9LYRlR6g", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpGQDr", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpG5ZY:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0505v7:9LYpGRv3:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUk4sp:ckpt-step-312", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUkOfa:ckpt-step-351", "ft:gpt-3.5-turbo-0125:personal:c0501:9JtUkOFs", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKwxT:ckpt-step-312", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKNmE:ckpt-step-351", "ft:gpt-3.5-turbo-0125:personal:c0501:9JuiKatZ", "ft:gpt-3.5-turbo-0125:personal::9K06CV13:ckpt-step-144", "ft:gpt-3.5-turbo-0125:personal::9K06DYze:ckpt-step-162", "ft:gpt-3.5-turbo-0125:personal::9K06DrsI", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhDlTM:ckpt-step-216", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhDRUr:ckpt-step-243", "ft:gpt-3.5-turbo-0125:personal:c0505v8:9LZhELmS"]}}, "203": {"url": "https://clintonllm.net", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "snowflake/snowflake-arctic-instruct", "mistralai/mixtral-8x7b-instruct:nitro", "open-orca/mistral-7b-openorca", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "204": {"url": "https://song.lloring.com", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"]}}, "205": {"url": "http://72.14.189.174:3080", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": [], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "206": {"url": "http://23.165.40.62:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-4-turbo"], "anthropic": ["claude-3-haiku-20240307"], "gptPlugins": ["gpt-4-turbo-preview", "gpt-3.5-turbo"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "microsoft/wizardlm-2-8x22b", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "207": {"url": "http://18.162.45.224:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "gpt-4-32k", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "208": {"url": "https://sb.luouse.site", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "llama3-70b-8192"], "gptPlugins": ["360GPT_S2_V9", "babbage-002", "chatglm_lite", "chatglm_pro", "chatglm_std", "chatglm_turbo", "ChatPro", "ChatStd", "claude-2", "claude-2.0", "claude-2.1", "claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-instant-1.2", "dall-e-2", "dall-e-3", "davinci-002", "embedding-bert-512-v1", "Embedding-V1", "embedding_s1_v1", "ERNIE-Bot", "ERNIE-Bot-4", "ERNIE-Bot-8K", "ERNIE-Bot-turbo", "ERNIE-Speed", "gemini-1.0-pro-001", "gemini-1.0-pro-vision-001", "glm-3-turbo", "glm-4", "glm-4v", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-32k-0314", "gpt-4-32k-0613", "gpt-4-turbo-preview", "gpt-4-vision-preview", "hun<PERSON>", "mistral-7b-instruct", "mixtral-8x7b-instruct", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "moonshot-v1-128k", "moonshot-v1-32k", "moonshot-v1-8k", "PaLM-2", "qwen-max", "qwen-max-longcontext", "qwen-plus", "qwen-turbo", "semantic_similarity_s1_v1", "sonar-medium-chat", "sonar-medium-online", "sonar-small-chat", "sonar-small-online", "SparkDesk", "SparkDesk-v1.1", "SparkDesk-v2.1", "SparkDesk-v3.1", "SparkDesk-v3.5", "swap_face", "text-ada-001", "text-babbage-001", "text-curie-001", "text-davinci-002", "text-davinci-003", "text-davinci-edit-001", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-embedding-v1", "text-moderation-latest", "text-moderation-stable", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1", "yi-34b-chat-0205", "yi-34b-chat-200k", "yi-vl-plus"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "209": {"url": "http://142.171.183.76:3080", "is_register": true, "is_login": true, "models": {}}, "210": {"url": "http://142.171.183.84:3080", "is_register": true, "is_login": true, "models": {}}, "211": {"url": "http://52.197.186.89:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4"]}}, "212": {"url": "http://203.55.176.213:3080", "is_register": true, "is_login": true, "models": {}}, "213": {"url": "http://104.128.94.89:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "214": {"url": "https://ai.kugua.cn", "is_register": true, "is_login": true, "models": {}}, "215": {"url": "http://ai.kugua.cn", "is_register": true, "is_login": true, "models": {}}, "216": {"url": "http://88.198.56.93:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-3.5-turbo-16k-0613", "whisper-1", "davinci-002", "gpt-3.5-turbo", "dall-e-2", "tts-1-hd-1106", "tts-1-hd", "gpt-4-0613", "gpt-4", "text-embedding-3-large", "gpt-4-1106-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-4-0125-preview", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "tts-1", "dall-e-3", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "babbage-002", "tts-1-1106", "gpt-4-vision-preview", "text-embedding-3-small", "text-embedding-ada-002", "gpt-3.5-turbo-0125"], "anthropic": ["claude-1", "claude-instant-1.2", "claude-3-haiku-20240307"], "bingAI": ["BingAI", "Sydney"], "ShuttleAI": ["shuttle-instant", "shuttle-turbo", "shuttle-1", "shuttle-tools", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-vision-preview", "gpt-4-32k", "gpt-4-32k-0613", "gpt-4-0613", "gpt-4", "gpt-4-bing-turbo", "gpt-4-bing", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "chatgpt", "claude-3-opus", "claude-3-sonnet", "claude-3-haiku-200k", "claude-3-haiku", "claude-2.1", "claude-2.0", "claude-instant-100k", "claude-instant", "gemini-1.5-pro", "gemini-pro", "gemini-pro-vision", "palm-2", "mistral-large", "mistral-next", "mistral-medium", "mistral-small", "mistral-7b", "mixtral-8x7b", "dolphin-mixtral-8x7b", "chronos-hermes-13b", "pplx-70b-online", "pplx-70b-chat", "pplx-7b-online", "pplx-7b-chat", "nous-capybara-34b", "yi-34b-chat", "starcoder-16b", "starcoder-7b", "deepseek-coder", "deepseek-chat", "airoboros-2-70b", "airoboros-70b", "falcon-180b", "phind-34b", "phind-code-llama-v2-34b", "code-llama-70b", "code-llama-34b", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "llama-summarize", "llava-13b", "midjourney", "dall-e-3", "dall-e-3-premium", "dall-e-2", "sdxl", "sdxl-turbo", "sdxl-emoji", "dreamshaper-xl", "juggernaut-xl", "dynavision-xl", "realism-engine-xl", "sdxl-inpainting", "turbovision-xl", "kandinsky-2.2", "kandinsky-2", "stable-diffusion 2.1", "stable-diffusion 1.5", "playground-v2.5", "dreamshaper-v8", "rev-animated", "anything-v5", "absolutereality-v1.8.1", "realisticvision-v5", "timeless-v1", "portrait-v1", "analog", "anything-v3", "anything-v4", "abyssorangemix", "deliberate", "dreamlike-v1", "dreamlike-v2", "dreamshaper-5", "dreamshaper-6", "<PERSON><PERSON>rethvividmix", "lyriel-v15", "lyriel-v16", "mechamix", "meinamix", "realisticvs-v14", "realisticvs-v20", "riffusion", "sd-v14", "sd-v15", "sbp", "theallysmix", "openjourney", "icbinp", "latent-consistency-model", "deepfloyd-if", "material-diffusion", "eleven-labs", "eleven-labs-2", "eleven-labs-999", "speechify", "tts-1", "tts-1-hd", "whisper-large", "whisper-1", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-moderation-latest", "text-moderation-stable", "insult-1", "joke-1", "py-minify-1", "search-google", "search-ddg"], "Ollama": ["mistral", "mixtral", "llama2:13b", "llava:13b", "notux", "nous-hermes2-mixtral"]}}, "217": {"url": "http://193.122.145.241:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "llama3-70b-8192"], "gptPlugins": ["360GPT_S2_V9", "babbage-002", "chatglm_lite", "chatglm_pro", "chatglm_std", "chatglm_turbo", "ChatPro", "ChatStd", "claude-2", "claude-2.0", "claude-2.1", "claude-3-haiku-20240307", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-instant-1.2", "dall-e-2", "dall-e-3", "davinci-002", "embedding-bert-512-v1", "Embedding-V1", "embedding_s1_v1", "ERNIE-Bot", "ERNIE-Bot-4", "ERNIE-Bot-8K", "ERNIE-Bot-turbo", "ERNIE-Speed", "gemini-1.0-pro-001", "gemini-1.0-pro-vision-001", "glm-3-turbo", "glm-4", "glm-4v", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct", "gpt-4", "gpt-4-0125-preview", "gpt-4-0314", "gpt-4-0613", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-32k-0314", "gpt-4-32k-0613", "gpt-4-turbo-preview", "gpt-4-vision-preview", "hun<PERSON>", "mistral-7b-instruct", "mixtral-8x7b-instruct", "mj_blend", "mj_custom_zoom", "mj_describe", "mj_high_variation", "mj_imagine", "mj_inpaint", "mj_low_variation", "mj_modal", "mj_pan", "mj_reroll", "mj_shorten", "mj_upscale", "mj_variation", "mj_zoom", "moonshot-v1-128k", "moonshot-v1-32k", "moonshot-v1-8k", "PaLM-2", "qwen-max", "qwen-max-longcontext", "qwen-plus", "qwen-turbo", "semantic_similarity_s1_v1", "sonar-medium-chat", "sonar-medium-online", "sonar-small-chat", "sonar-small-online", "SparkDesk", "SparkDesk-v1.1", "SparkDesk-v2.1", "SparkDesk-v3.1", "SparkDesk-v3.5", "swap_face", "text-ada-001", "text-babbage-001", "text-curie-001", "text-davinci-002", "text-davinci-003", "text-davinci-edit-001", "text-embedding-3-large", "text-embedding-3-small", "text-embedding-ada-002", "text-embedding-v1", "text-moderation-latest", "text-moderation-stable", "tts-1", "tts-1-1106", "tts-1-hd", "tts-1-hd-1106", "whisper-1", "yi-34b-chat-0205", "yi-34b-chat-200k", "yi-vl-plus"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "218": {"url": "http://183.6.116.72:3116", "is_register": true, "is_login": true, "models": {}}, "219": {"url": "https://135.181.82.202", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]}}, "220": {"url": "https://chat.encasaxo.com", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]}}, "221": {"url": "http://154.40.54.93:3080", "is_register": true, "is_login": true, "models": {}}, "222": {"url": "https://128.140.118.120", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["open-mistral-7b", "mistral-tiny-2312", "mistral-tiny", "open-mixtral-8x7b", "mistral-small-2312", "mistral-small", "mistral-small-2402", "mistral-small-latest", "mistral-medium-latest", "mistral-medium-2312", "mistral-medium", "mistral-large-latest", "mistral-large-2402", "mistral-embed"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "223": {"url": "http://54.238.120.171:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-4"]}}, "224": {"url": "http://202.61.250.21:1080", "is_register": true, "is_login": true, "models": {}}, "225": {"url": "http://43.163.236.33:3080", "is_register": true, "is_login": true, "models": {}}, "226": {"url": "http://195.201.31.141:3080", "is_register": true, "is_login": true, "models": {}}, "227": {"url": "http://3.23.0.81:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0125-preview"], "assistants": ["gpt-4-0125-preview"], "gptPlugins": ["gpt-4-0125-preview"], "anthropic": ["claude-1", "claude-instant-1", "claude-2", "claude-3-opus-20240229"]}}, "228": {"url": "http://144.21.62.242:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-instruct", "gpt-4-0613", "text-davinci-003", "gpt-4-0314"], "gptPlugins": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "229": {"url": "http://172.93.219.59:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", " gpt-3.5"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"], "Github Copilot": ["gpt-4", "gpt-3.5-turbo"], "Coze GPT-4": ["gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"]}}, "230": {"url": "http://154.23.248.222", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"]}}, "231": {"url": "http://169.229.156.242:3080", "is_register": true, "is_login": true, "models": {}}, "232": {"url": "http://47.76.72.199:3080", "is_register": true, "is_login": true, "models": {}}, "233": {"url": "http://65.21.79.51:3080", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-4", "gpt-3.5-turbo", "gpt-4-1106-preview"], "gptPlugins": ["gpt-4", "gpt-3.5-turbo", "gpt-4-1106-preview"]}}, "234": {"url": "http://1.32.228.15:3080", "is_register": true, "is_login": true, "models": {}}, "235": {"url": "https://analogai.in", "is_register": true, "is_login": true, "models": {"gptPlugins": ["gpt-3.5-turbo", "gpt-4-turbo", "gpt-4-vision"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it", "llama3-70b-8192"], "Anthropic": ["claude-3-sonnet", "claude-3-opus"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4-turbo", "gpt-4-vision"], "assistants": ["gpt-4-turbo"]}}, "236": {"url": "https://libre.mn.cyou", "is_register": true, "is_login": true, "models": {}}, "237": {"url": "https://libre.meinan.life", "is_register": true, "is_login": true, "models": {}}, "238": {"url": "http://128.199.72.246:880", "is_register": true, "is_login": true, "models": {}}, "239": {"url": "http://107.172.62.32:3080", "is_register": true, "is_login": true, "models": {}}, "240": {"url": "https://chat.aiwelink.top", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-4", "gpt-4-1106-preview", "gpt-4-32k", "gpt-4-all", "gpt-3.5-turbo-16k", "gpt-3.5-turbo"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-4", "gpt-4-1106-preview", "gpt-4-32k", "gpt-3.5-turbo-16k", "gpt-3.5-turbo"]}}, "241": {"url": "http://47.100.179.87:3080", "is_register": true, "is_login": true, "models": {"openAI": [], "gptPlugins": []}}, "242": {"url": "http://150.136.33.117:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-4", "gpt-4-0314", "gpt-4-0613", "gpt-4-32k", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4o", "gpt-4o-2024-05-13", "gpt-4-vision-preview", "claude-instant", "claude-2", "claude-3", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "palm-2", "gemini-pro", "gemini-1.5-pro", "gemini-pro-vision", "gemma-7b", "llama-2-7b", "llama-2-13b", "llama-2-70b", "qwen-72b", "vicuna-7b", "vicuna-13b", "openchat-7b", "mythomist-7b", "chatglm-2-6b", "mistral-7b", "mistral-7b-instruct", "mixtral-8x7b", "mistral-next", "mistral-small", "mistral-medium", "mistral-large", "mythomax-13b", "goliath-120b", "llama-3-8b", "llama-3-70b", "llamaguard-7b", "rocket-3b", "toppy-7b", "falcon-180b", "falcon-180b", "sim-simi", "sim-simi-en", "sim-simi-ph", "sim-simi-ar", "sim-simi-es", "sim-simi-id", "sim-simi-bn", "dall-e-3", "kandinsky-2.2", "kandinsky-3", "playground-2.5", "stable-diffusion-1.4", "stable-diffusion-1.5", "stable-diffusion-3", "sdxl", "guofeng-3", "dreamshaper-6", "dreamshaper-7", "dreamshaper-8", "dreamshaper-xl", "absolute-reality-1.6", "absolute-reality-1.8", "rev-animated-1.2.2", "lyriel-1.6", "toonyou-beta6", "timeless", "abyss-orange-mix-3", "openjourney-4", "meinamix-9", "mechamix-10", "meinamix-11", "counterfeit-3", "dalcefo-4", "realistic-vision", "realistic-vision-2", "realistic-vision-4", "realistic-vision-5", "portraitplus", "pastelmix-anime", "juggernaut-aftermath", "juggernaut-xl-4.5", "anything-3", "anything-4.5", "anything-5", "icbinp", "cute<PERSON><PERSON><PERSON>-adorable", "breakdomain-m2150", "breakdomain-i2428", "indigofurrymix-7.5-hybrid", "cyberrealistic-3.3", "dreamlike-anime", "dreamlike-diffusion", "dreamlike-photoreal", "lofi-4", "midjourney", "nijijourney", "tts-1-alloy", "tts-1-echo", "tts-1-fable", "tts-1-nova", "tts-1-onyx", "tts-1-shimmer", "google-tts", "google-translate"], "gptPlugins": ["gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-4", "gpt-4-0314", "gpt-4-0613", "gpt-4-32k", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4o", "gpt-4o-2024-05-13", "gpt-4-vision-preview", "claude-instant", "claude-2", "claude-3", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "palm-2", "gemini-pro", "gemini-1.5-pro", "gemini-pro-vision", "gemma-7b", "llama-2-7b", "llama-2-13b", "llama-2-70b", "qwen-72b", "vicuna-7b", "vicuna-13b", "openchat-7b", "mythomist-7b", "chatglm-2-6b", "mistral-7b", "mistral-7b-instruct", "mixtral-8x7b", "mistral-next", "mistral-small", "mistral-medium", "mistral-large", "mythomax-13b", "goliath-120b", "llama-3-8b", "llama-3-70b", "llamaguard-7b", "rocket-3b", "toppy-7b", "falcon-180b", "falcon-180b", "sim-simi", "sim-simi-en", "sim-simi-ph", "sim-simi-ar", "sim-simi-es", "sim-simi-id", "sim-simi-bn", "dall-e-3", "kandinsky-2.2", "kandinsky-3", "playground-2.5", "stable-diffusion-1.4", "stable-diffusion-1.5", "stable-diffusion-3", "sdxl", "guofeng-3", "dreamshaper-6", "dreamshaper-7", "dreamshaper-8", "dreamshaper-xl", "absolute-reality-1.6", "absolute-reality-1.8", "rev-animated-1.2.2", "lyriel-1.6", "toonyou-beta6", "timeless", "abyss-orange-mix-3", "openjourney-4", "meinamix-9", "mechamix-10", "meinamix-11", "counterfeit-3", "dalcefo-4", "realistic-vision", "realistic-vision-2", "realistic-vision-4", "realistic-vision-5", "portraitplus", "pastelmix-anime", "juggernaut-aftermath", "juggernaut-xl-4.5", "anything-3", "anything-4.5", "anything-5", "icbinp", "cute<PERSON><PERSON><PERSON>-adorable", "breakdomain-m2150", "breakdomain-i2428", "indigofurrymix-7.5-hybrid", "cyberrealistic-3.3", "dreamlike-anime", "dreamlike-diffusion", "dreamlike-photoreal", "lofi-4", "midjourney", "nijijourney", "tts-1-alloy", "tts-1-echo", "tts-1-fable", "tts-1-nova", "tts-1-onyx", "tts-1-shimmer", "google-tts", "google-translate"]}}, "243": {"url": "http://132.226.232.191:3080", "is_register": true, "is_login": true, "models": {"openAI": [], "gptPlugins": []}}, "244": {"url": "http://125.212.226.160:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613"], "gptPlugins": ["gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613"]}}, "245": {"url": "http://74.48.96.35:3080", "is_register": true, "is_login": true, "models": {}}, "246": {"url": "http://128.199.72.246:3080", "is_register": true, "is_login": true, "models": {}}, "247": {"url": "https://www.alexanderpichler.name", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["open-mistral-7b", "mistral-tiny-2312", "mistral-tiny", "open-mixtral-8x7b", "mistral-small-2312", "mistral-small", "mistral-small-2402", "mistral-small-latest", "mistral-medium-latest", "mistral-medium-2312", "mistral-medium", "mistral-large-latest", "mistral-large-2402", "mistral-embed"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "248": {"url": "http://39.106.136.125:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4"], "gptPlugins": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-4"]}}, "249": {"url": "http://206.217.142.170:3080", "is_register": true, "is_login": true, "models": {}}, "250": {"url": "http://202.61.250.21:3080", "is_register": true, "is_login": true, "models": {}}, "251": {"url": "https://chat.863691.xyz", "is_register": true, "is_login": true, "models": {"GPT": ["gpt4-turbo-offline", "gpt4-turbo-online", "gpt4-offline", "gpt4-online", "gpt3.5-online"]}}, "252": {"url": "http://193.123.230.130:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4", "gpt-3.5-turbo"]}}, "253": {"url": "http://107.173.171.160:3080", "is_register": true, "is_login": true, "models": {}}, "254": {"url": "http://49.13.19.207:3080", "is_register": true, "is_login": true, "models": {"HenAI Proxy": ["gpt-3.5-turbo-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-turbo", "gpt-4o", "gpt-4o-2024-05-13", "gpt-4-vision-preview", "claude-1.3", "claude-2", "claude-2.1", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "claude-instant-1", "claude-instant-1.1", "claude-instant-1.2", "mixtral-8x7b-instruct", "mistral-tiny", "mistral-small", "mistral-medium", "mistral-large", "mistral-7b-instruct", "gemma-7b", "gemma-2b-it", "chronos-hermes-13b", "llama-2-7b", "llama-2-13b", "llama-2-70b", "llama-3-8b", "llama-3-70b", "openchat-3.5", "phind-codellama-34b", "llava-1.5", "lzlv-70b", "airoboros-70b", "airoboros-70b-gpt4", "codellama-34b-instruct", "codellama-70b-instruct", "starcoder2-15b", "dolphin-2.6-mixtral-8x7b", "sonar-small-chat", "sonar-medium-chat", "sonar-small-online", "sonar-medium-online", "dall-e-3", "stable-diffusion-3", "sdxl", "pastel-mix-anime", "absolute-reality-v1.6", "absolute-reality-v1.8.1", "anything-v3", "anything-v4.5", "anything-v5", "meinamix", "deliberate-v2", "deliberate-v3", "rev-animated", "dreamshaper-6", "dreamshaper-7", "dreamshaper-8", "realistic-vision-v1.4", "realistic-vision-v2", "realistic-vision-v4", "realistic-vision-v5", "openjourney-v4", "am-i-real-v4.1", "analog-v1", "abyssorangemix-v3", "blazing-drive-v10g", "cetusmix-version35", "counterfeit-v3.0", "cyberrealistic-v3.3", "dalcefo-v4", "dreamlike-anime-v1", "dreamlike-diffusion-v1", "dreamlike-photoreal-v2", "edge-of-realism-eor-v2.0", "eimis-anime-diffusion-v1.0", "elld<PERSON><PERSON>-vivid", "epicrealism-natural-sin-rc1", "juggernaut-aftermath", "lofi-v4", "lyriel-v1.6", "majicmix-realistic-v4", "mechamix-v1.0", "meinamix-meina-v9", "neverending-dream-v1.22", "protogen-x3.4", "redshift-diffusion-v1.0", "rundiffusion-fx-2.5d-v1.0", "rundiffusion-fx-photorealistic-v1.0", "sd-v1.4", "sd-v1.5", "timeless-v1", "toonyou-beta-6", "text-moderation-stable", "text-moderation-latest"], "FreesedAI Proxy": ["gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4", "gpt-4o", "gpt-4-0613", "gpt-4-32k", "gemini-1.5-pro-latest", "gemini-pro", "claude-instant", "claude-instant-1.1", "claude-1.2", "claude-1", "claude-2.1", "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", "mixtral-8x7b", "mistral-small", "mistral-medium", "mistral-large", "llama-2-7b", "llama-2-13b", "llama-2-70b", "llama-3-8b", "llama-3-70b", "codellama-70b", "codellama-34b", "codellama-13b", "codellama-7b", "mistral-7b", "hermes-2-pro-mistral-7b", "mixtral-8x22b-finetuned", "nous-hermes-2-mixtral-8x7b-dpo", "mixtral-8x22b-instruct", "qwen1.5-32b", "hermes-2-pro-llama-3-8b", "gemma-7b", "gemma-2b", "gemma", "mixtral", "llama-3", "llama-2-uncensored", "codellama", "wizardlm2", "qwen-7b", "dolphin-mixtral", "command", "command-r", "command-r-plus", "pplx-7b-online", "pplx-70b-online", "jetmoe-8b-chat", "cinematika-7b", "toppy-m-7b", "mythomist-7b", "openchat-7b", "nous-capybara-7b", "bing-creative", "bing-balanced", "bing-precise", "bing-creative-offline", "bing-balanced-offline", "bing-precise-offline", "dall-e-3", "dall-e-2", "midjourney"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "databricks/dbrx-instruct", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "255": {"url": "http://168.119.19.248:3080", "is_register": true, "is_login": true, "models": {"openAI": ["mistral", "yarn-mistral", "mixtral", "llava", "b<PERSON><PERSON><PERSON>", "sqlcoder"], "gptPlugins": ["sqlcoder", "mistral", "b<PERSON><PERSON><PERSON>", "gpt-4-vision-preview", "gpt-4", "yarn-mistral", "mixtral", "llava", "ollama/bakllava", "ollama/llava", "ollama/mistral", "ollama/mixtral", "ollama/sqlcoder", "ollama/yarn-mistral"]}}, "256": {"url": "http://20.127.42.201:3080", "is_register": true, "is_login": true, "models": {"Mistral": [], "together.ai": ["meta-llama/Llama-2-70b-chat-hf", "mistralai/Mixtral-8x7B-Instruct-v0.1", "togethercomputer/RedPajama-INCITE-Chat-3B-v1", "togethercomputer/llama-2-13b-chat"], "FlowiseAI": ["default-model"]}}, "257": {"url": "http://65.108.147.68:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo"], "gptPlugins": ["gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo"]}}, "258": {"url": "http://120.24.41.119:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-4-0125-preview", "gpt-3.5-turbo-1106", "gpt-4", "gpt-4-turbo-preview", "gpt-4-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-1106-preview"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-instruct", "gpt-4-0125-preview", "gpt-3.5-turbo-1106", "gpt-4", "gpt-4-turbo-preview", "gpt-4-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-1106-preview"]}}, "259": {"url": "https://chat.fuckopenai.fun", "is_register": true, "is_login": true, "models": {}}, "260": {"url": "http://37.44.215.5:3080", "is_register": true, "is_login": true, "models": {}}, "261": {"url": "http://130.61.218.15:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gemini-1.5-pro-latest", "llama-3-70b-chat", "llama-3-8b-chat", "mixtral-8x22b-instruct", "command-r-plus", "command-r", "mistral-large", "mistral-large-2402", "mistral-next", "mistral-small", "mistral-small-2402", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-haiku", "claude-3-haiku-20240307", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b-instruct", "mixtral-8x7b-instruct", "playground-v2.5", "sdxl", "kandinsky-3.1", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large-v3", "whisper-1", "m2m100-1.2b", "google-tts-1"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gemini-1.5-pro-latest", "llama-3-70b-chat", "llama-3-8b-chat", "mixtral-8x22b-instruct", "command-r-plus", "command-r", "mistral-large", "mistral-large-2402", "mistral-next", "mistral-small", "mistral-small-2402", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613", "claude-3-haiku", "claude-3-haiku-20240307", "gemini-pro", "gemini-pro-vision", "llama-2-70b-chat", "llama-2-13b-chat", "llama-2-7b-chat", "mistral-7b-instruct", "mixtral-8x7b-instruct", "playground-v2.5", "sdxl", "kandinsky-3.1", "kandinsky-3", "kandinsky-2.2", "kandinsky-2", "text-embedding-3-small", "text-embedding-3-large", "text-embedding-ada-002", "bge-large-en-v1.5", "text-moderation-latest", "text-moderation-stable", "whisper-large-v3", "whisper-1", "m2m100-1.2b", "google-tts-1"]}}, "262": {"url": "http://47.109.109.6:13000", "is_register": true, "is_login": true, "models": {"Mistral": [], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "nousresearch/nous-hermes-llama2-13b", "meta-llama/codellama-34b-instruct", "phind/phind-codellama-34b", "intel/neural-chat-7b", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "gryphe/mythomax-l2-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "codellama/codellama-70b-instruct", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "undi95/toppy-m-7b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "openchat/openchat-7b:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r"]}}, "263": {"url": "http://103.247.28.242:3080", "is_register": true, "is_login": true, "models": {}}, "264": {"url": "https://chat.rootiest.dev", "is_register": true, "is_login": true, "models": {"bingAI": ["BingAI", "Sydney"]}}, "265": {"url": "http://20.249.84.227:3080", "is_register": true, "is_login": true, "models": {"openAI": [], "bingAI": ["BingAI", "Sydney"], "chatGPTBrowser": ["text-davinci-002-render-sha"], "gptPlugins": []}}, "266": {"url": "http://107.175.111.43:3080", "is_register": true, "is_login": true, "models": {"groq": ["llama3-70b-8192", "llama3-8b-8192", "llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "lynn/soliloquy-l3", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "gryphe/mythomax-l2-13b", "meta-llama/llama-2-13b-chat", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-70b-instruct", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "meta-llama/llama-3-8b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "microsoft/wizardlm-2-8x22b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "267": {"url": "https://convo.red-naxela.com", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-0125", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613"], "gptPlugins": ["gpt-4-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4", "gpt-4-1106-vision-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-1106", "gpt-4-vision-preview", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["open-mistral-7b", "mistral-tiny-2312", "mistral-tiny", "open-mixtral-8x7b", "mistral-small-2312", "mistral-small", "mistral-small-2402", "mistral-small-latest", "mistral-medium-latest", "mistral-medium-2312", "mistral-medium", "mistral-large-latest", "mistral-large-2402", "mistral-embed"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "migtissera/synthia-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "268": {"url": "http://170.64.149.86:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "269": {"url": "http://148.135.5.56:3080", "is_register": true, "is_login": true, "models": {}}, "270": {"url": "http://8.222.243.194:3080", "is_register": true, "is_login": true, "models": {"groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "jebcarter/psyfighter-13b", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "haotian-liu/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "meta-llama/llama-2-13b-chat", "xwin-lm/xwin-lm-70b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "undi95/remm-slerp-l2-13b:extended", "mancer/weaver", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "teknium/openhermes-2-mistral-7b", "teknium/openhermes-2.5-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "open-orca/mistral-7b-openorca", "huggingfaceh4/zephyr-7b-beta", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "perplexity/pplx-70b-online", "perplexity/pplx-7b-online", "perplexity/pplx-7b-chat", "perplexity/pplx-70b-chat", "perplexity/sonar-small-chat", "perplexity/sonar-medium-chat", "perplexity/sonar-small-online", "perplexity/sonar-medium-online", "fireworks/mixtral-8x22b-instruct-preview", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-70b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "gryphe/mythomax-l2-13b", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "01-ai/yi-34b-200k", "mistralai/mixtral-8x7b-instruct", "cognitivecomputations/dolphin-mixtral-8x7b", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "databricks/dbrx-instruct", "huggingfaceh4/zephyr-orpo-141b-a35b", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "anthropic/claude-2", "anthropic/claude-2.1", "anthropic/claude-2.0", "anthropic/claude-instant-1", "anthropic/claude-instant-1.2", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "anthropic/claude-2:beta", "anthropic/claude-2.1:beta", "anthropic/claude-2.0:beta", "anthropic/claude-instant-1:beta", "mistralai/mixtral-8x22b", "huggingfaceh4/zephyr-7b-beta:free", "mistralai/mixtral-8x7b-instruct:nitro", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "databricks/dbrx-instruct:nitro", "undi95/toppy-m-7b:nitro", "gryphe/mythomax-l2-13b:extended", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "271": {"url": "http://169.48.92.161:3080", "is_register": true, "is_login": true, "models": {"Nitro": ["./models/llama-2-7b-model.gguf"]}}, "272": {"url": "https://librechat-loheagn.fly.dev", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-turbo"], "gptPlugins": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "databricks/dbrx-instruct", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "neversleep/llama-3-lumimaid-70b", "cognitivecomputations/dolphin-mixtral-8x7b", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "gryphe/mythomax-l2-13b:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "273": {"url": "http://107.173.114.36:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-turbo-preview"], "chatGPTBrowser": ["text-da<PERSON><PERSON>", "gpt-3.5-turbo", "gpt-3.5-turbo-16K", "gpt-4-1106-preview", "gpt-4"], "gptPlugins": ["gpt-3.5-turbo"]}}, "274": {"url": "http://108.162.131.202:3080", "is_register": true, "is_login": true, "models": {"azureOpenAI": ["gpt-4", "gpt-35-turbo"]}}, "275": {"url": "http://47.94.132.149:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"]}}, "276": {"url": "https://chat.764566.xyz", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "gpt-3.5-turbo-16k"], "bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4o", "gpt-4", "gpt-3.5-turbo"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.0", "claude-2.1", "claude-instant-1.2"]}}, "277": {"url": "http://18.183.241.51:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"], "chatGPTBrowser": ["text-davinci-002-render-sha", "gpt-4"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301", "text-davinci-003"]}}, "278": {"url": "http://140.99.243.187:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0301", "text-davinci-003", "gpt-4", "gpt-4-0314", "gpt-4-0613"], "chatGPTBrowser": ["text-davinci-002-render-sha", "gpt-4"], "gptPlugins": ["gpt-4", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-16k", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-0301"]}}, "279": {"url": "http://206.81.24.43:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-turbo-preview", "gpt-3.5-turbo"], "gptPlugins": ["gpt-4-turbo-preview", "gpt-3.5-turbo"]}}, "280": {"url": "http://154.7.179.25:3080", "is_register": true, "is_login": true, "models": {}}, "281": {"url": "http://librechat.chenhaiyun.online", "is_register": true, "is_login": true, "models": {}}, "282": {"url": "http://101.6.64.199:3001", "is_register": true, "is_login": true, "models": {}}, "283": {"url": "http://167.71.56.211", "is_register": true, "is_login": true, "models": {}}, "284": {"url": "http://104.236.3.47", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "assistants": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "285": {"url": "https://newchat.excellentops.net", "is_register": true, "is_login": true, "models": {"gptPlugins": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768", "gemma-7b-it"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "assistants": ["gpt-4"]}}, "286": {"url": "https://netsense-d.excellentops.com", "is_register": true, "is_login": true, "models": {"gptPlugins": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "groq": ["llama3-70b-8192", "llama3-8b-8192", "mixtral-8x7b-32768", "gemma-7b-it"], "azureOpenAI": ["gpt-3.5-turbo", "gpt-4-turbo-preview", "gpt-4-vision-preview", "gpt-4-1106-preview", "gpt-4-turbo", "gpt-4"], "assistants": ["gpt-4"]}}, "287": {"url": "http://64.227.143.141", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "288": {"url": "http://209.38.186.37", "is_register": true, "is_login": true, "models": {}}, "289": {"url": "http://157.230.121.172", "is_register": true, "is_login": true, "models": {}}, "290": {"url": "http://209.97.149.103", "is_register": true, "is_login": true, "models": {}}, "291": {"url": "https://home5.cmds.top", "is_register": true, "is_login": true, "models": {}}, "292": {"url": "https://38.23.149.123", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "293": {"url": "http://173.197.148.66:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4-0125-preview", "gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4-turbo-preview", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-test-shared", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4-0125-preview", "gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4-turbo-preview", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-test-shared", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "294": {"url": "http://52.231.185.81", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-4o", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-2024-05-13", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "groq": ["llama2-70b-4096", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["mistral-tiny", "mistral-small", "mistral-medium"], "OpenRouter": ["openrouter/auto", "nousresearch/nous-capybara-7b:free", "mistralai/mistral-7b-instruct:free", "openchat/openchat-7b:free", "gryphe/mythomist-7b:free", "undi95/toppy-m-7b:free", "openrouter/cinematika-7b:free", "google/gemma-7b-it:free", "meta-llama/llama-3-8b-instruct:free", "koboldai/psyfighter-13b-2", "intel/neural-chat-7b", "gryphe/mythomax-l2-13b:nitro", "pygmalionai/mythalion-13b", "xwin-lm/xwin-lm-70b", "alpindale/goliath-120b", "neversleep/noromaid-20b", "gryphe/mythomist-7b", "sophosympatheia/midnight-rose-70b", "sao10k/fimbulvetr-11b-v2", "neversleep/llama-3-lumimaid-8b", "undi95/remm-slerp-l2-13b:extended", "gryphe/mythomax-l2-13b:extended", "meta-llama/llama-3-8b-instruct:extended", "neversleep/llama-3-lumimaid-8b:extended", "mancer/weaver", "nousresearch/nous-capybara-7b", "meta-llama/codellama-34b-instruct", "codellama/codellama-70b-instruct", "phind/phind-codellama-34b", "open-orca/mistral-7b-openorca", "teknium/openhermes-2-mistral-7b", "undi95/remm-slerp-l2-13b", "openrouter/cinematika-7b", "01-ai/yi-34b-chat", "01-ai/yi-34b", "01-ai/yi-6b", "togethercomputer/stripedhyena-nous-7b", "togethercomputer/stripedhyena-hessian-7b", "mistralai/mixtral-8x7b", "nousresearch/nous-hermes-yi-34b", "nousresearch/nous-hermes-2-mixtral-8x7b-sft", "nousresearch/nous-hermes-2-mistral-7b-dpo", "meta-llama/llama-3-8b", "meta-llama/llama-3-70b", "meta-llama/llama-guard-2-8b", "allenai/olmo-7b-instruct", "snowflake/snowflake-arctic-instruct", "qwen/qwen-110b-chat", "qwen/qwen-32b-chat", "qwen/qwen-14b-chat", "qwen/qwen-7b-chat", "qwen/qwen-4b-chat", "mistralai/mixtral-8x7b-instruct:nitro", "openai/gpt-3.5-turbo", "openai/gpt-3.5-turbo-0125", "openai/gpt-3.5-turbo-1106", "openai/gpt-3.5-turbo-0613", "openai/gpt-3.5-turbo-0301", "openai/gpt-3.5-turbo-16k", "openai/gpt-4o", "openai/gpt-4o-2024-05-13", "openai/gpt-4-turbo", "openai/gpt-4-turbo-preview", "openai/gpt-4-1106-preview", "openai/gpt-4", "openai/gpt-4-0314", "openai/gpt-4-32k", "openai/gpt-4-32k-0314", "openai/gpt-4-vision-preview", "openai/gpt-3.5-turbo-instruct", "google/palm-2-chat-bison", "google/palm-2-codechat-bison", "google/palm-2-chat-bison-32k", "google/palm-2-codechat-bison-32k", "google/gemini-pro", "google/gemini-pro-vision", "google/gemini-pro-1.5", "google/gemini-flash-1.5", "perplexity/llama-3-sonar-small-32k-chat", "perplexity/llama-3-sonar-small-32k-online", "perplexity/llama-3-sonar-large-32k-chat", "perplexity/llama-3-sonar-large-32k-online", "fireworks/firellava-13b", "anthropic/claude-3-opus", "anthropic/claude-3-sonnet", "anthropic/claude-3-haiku", "anthropic/claude-2", "anthropic/claude-2.0", "anthropic/claude-2.1", "anthropic/claude-instant-1", "anthropic/claude-3-opus:beta", "anthropic/claude-3-sonnet:beta", "anthropic/claude-3-haiku:beta", "anthropic/claude-2:beta", "anthropic/claude-2.0:beta", "anthropic/claude-2.1:beta", "anthropic/claude-instant-1:beta", "meta-llama/llama-2-13b-chat", "meta-llama/llama-2-70b-chat", "nousresearch/nous-hermes-llama2-13b", "nousresearch/nous-capybara-34b", "jondurbin/airoboros-l2-70b", "jondurbin/bagel-34b", "austism/chronos-hermes-13b", "mistral<PERSON>/mistral-7b-instruct", "teknium/openhermes-2.5-mistral-7b", "gryphe/mythomax-l2-13b", "huggingfaceh4/zephyr-7b-beta", "openchat/openchat-7b", "undi95/toppy-m-7b", "lizpreciatior/lzlv-70b-fp16-hf", "jebcarter/psyfighter-13b", "mistralai/mixtral-8x7b-instruct", "neversleep/noromaid-mixtral-8x7b-instruct", "nousresearch/nous-hermes-2-mixtral-8x7b-dpo", "rwkv/rwkv-5-world-3b", "recursal/rwkv-5-3b-ai-town", "recursal/eagle-7b", "google/gemma-7b-it", "meta-llama/llama-3-8b-instruct", "meta-llama/llama-3-70b-instruct", "microsoft/wizardlm-2-8x22b", "microsoft/wizardlm-2-7b", "mistralai/mixtral-8x22b", "mistralai/mixtral-8x22b-instruct", "lynn/soliloquy-l3", "cognitivecomputations/dolphin-mixtral-8x7b", "databricks/dbrx-instruct", "liuhaotian/llava-yi-34b", "qwen/qwen-72b-chat", "deepseek/deepseek-chat", "deepseek/deepseek-coder", "anthropic/claude-1", "anthropic/claude-1.2", "anthropic/claude-instant-1.0", "anthropic/claude-instant-1.1", "huggingfaceh4/zephyr-7b-beta:free", "meta-llama/llama-2-70b-chat:nitro", "mistralai/mistral-7b-instruct:nitro", "google/gemma-7b-it:nitro", "undi95/toppy-m-7b:nitro", "meta-llama/llama-3-8b-instruct:nitro", "meta-llama/llama-3-70b-instruct:nitro", "liuhaotian/llava-13b", "nousresearch/nous-hermes-2-vision-7b", "mistralai/mistral-tiny", "mistralai/mistral-small", "mistralai/mistral-medium", "mistralai/mistral-large", "cohere/command", "cohere/command-r", "cohere/command-r-plus"]}}, "295": {"url": "http://129.226.208.105:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "gpt-4o-2024-05-13"], "assistants": ["gpt-3.5-turbo", "gpt-4-turbo", "gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "gpt-4o-2024-05-13"], "gptPlugins": ["gpt-4", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4o-2024-05-13"], "together.ai": ["google/gemma-7b-it", "mistralai/Mistral-7B-Instruct-v0.1", "mistralai/Mistral-7B-Instruct-v0.2", "mistralai/Mixtral-8x7B-Instruct-v0.1", "mistralai/Mixtral-8x22B-Instruct-v0.1", "deepseek-ai/deepseek-coder-33b-instruct"], "groq": ["llama3-8b-8192", "llama3-70b-8192", "mixtral-8x7b-32768", "gemma-7b-it"], "Mistral": ["open-mistral-7b", "open-mixtral-8x7b", "mistral-small-latest", "mistral-medium-latest", "mistral-large-latest"]}}, "296": {"url": "http://167.99.190.10", "is_register": true, "is_login": true, "models": {}}, "297": {"url": "http://172.233.186.167:3080", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "assistants": ["gpt-3.5-turbo-0125", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-16k", "gpt-3.5-turbo", "gpt-4", "gpt-4-0314", "gpt-4-32k-0314", "gpt-4-0613", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-1106", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview"], "gptPlugins": ["gpt-4", "gpt-4-turbo-preview", "gpt-4-0125-preview", "gpt-4-1106-preview", "gpt-4-0613", "gpt-3.5-turbo", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0613"]}}, "298": {"url": "http://144.76.113.219:3080", "is_register": true, "is_login": true, "models": {}}, "299": {"url": "https://34.130.148.162", "is_register": true, "is_login": true, "models": {}}, "300": {"url": "http://84.143.149.211", "is_register": true, "is_login": true, "models": {}}, "301": {"url": "http://165.227.232.114", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-0125", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo", "gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-0125-preview", "gpt-4-vision-preview", "gpt-4-turbo-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "302": {"url": "http://143.198.5.126", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-4o", "gpt-3.5-turbo-0125", "gpt-3.5-turbo-0301", "gpt-3.5-turbo", "gpt-4", "gpt-4-0613", "gpt-4-vision-preview", "gpt-3.5-turbo-0613", "gpt-3.5-turbo-16k-0613", "gpt-4-0125-preview", "gpt-4-turbo-preview", "gpt-4-1106-preview", "gpt-3.5-turbo-1106", "gpt-3.5-turbo-instruct", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-16k"], "gptPlugins": ["gpt-4-0125-preview", "gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4-turbo-preview", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-4o-test-shared", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "303": {"url": "http://139.59.49.147", "is_register": true, "is_login": true, "models": {"openAI": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"], "gptPlugins": ["gpt-3.5-turbo-16k", "gpt-4o-2024-05-13", "gpt-4o", "gpt-4", "gpt-4-0613", "gpt-3.5-turbo-1106", "gpt-4-1106-preview", "gpt-3.5-turbo-0301", "gpt-4-0125-preview", "gpt-4-turbo-2024-04-09", "gpt-4-turbo-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4-32k-0314", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-4-0314", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "304": {"url": "http://104.236.8.159", "is_register": true, "is_login": true, "models": {}}, "305": {"url": "http://143.198.96.225", "is_register": true, "is_login": true, "models": {}}, "306": {"url": "https://45.33.72.239", "is_register": true, "is_login": true, "models": {}}, "307": {"url": "http://149.28.30.157", "is_register": true, "is_login": true, "models": {}}, "308": {"url": "https://159.69.127.245", "is_register": true, "is_login": true, "models": {"openAI1": ["gpt-4o", "gpt-4", "gpt-4-turbo"], "gptPlugins": ["gpt-4o-2024-05-13", "gpt-4o", "gpt-3.5-turbo-16k", "gpt-4", "gpt-4-0613", "gpt-4-turbo-2024-04-09", "gpt-3.5-turbo-1106", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-3.5-turbo-0301", "gpt-4-1106-preview", "gpt-4-0125-preview", "gpt-3.5-turbo-0125", "gpt-4-turbo-preview", "gpt-3.5-turbo-0613", "gpt-4-1106-vision-preview", "gpt-4-vision-preview", "gpt-3.5-turbo-16k-0613", "gpt-3.5-turbo-instruct-0914", "gpt-3.5-turbo-instruct"]}}, "309": {"url": "http://93.183.93.91", "is_register": true, "is_login": true, "models": {"OpenRouter": ["microsoft/wizardlm-2-8x22b", "perplexity/llama-3-sonar-large-32k-online", "openai/gpt-4o"]}}, "310": {"url": "https://fmihkyt.com", "is_register": true, "is_login": true, "models": {"openAI2": ["gpt-3.5-turbo-0125", "gpt-4o"], "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2", "claude-1.2", "claude-1", "claude-1-100k", "claude-instant-1", "claude-instant-1-100k"]}}, "311": {"url": "https://chat.doublemine.me:2096", "is_register": true, "is_login": true, "models": {"bingAI": ["BingAI", "Sydney"], "gptPlugins": ["gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "gpt-3.5-turbo", "gpt-4-32k"], "azureOpenAI": ["gpt-4-turbo-2024-04-09", "gpt-4-vision-preview", "gpt-3.5-turbo", "gpt-4-32k"]}}, "312": {"url": "http://www.targate.xyz:3080", "is_register": true, "is_login": true, "models": {}}}
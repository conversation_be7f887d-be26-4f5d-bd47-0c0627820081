import requests
import datetime
from functools import lru_cache

# 登录
url = "https://wapi.http.linkudp.com/index/users/login_do"
body = {
    'phone': '19975357677',
    'password': '1234qwer',
    'remember': '0'
}
headers = {
    "Accept": "text/html, */*; q=0.01",
    "Accept-Encoding": 'gzip, deflate, br',
    "Accept-Language": 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Length': '48',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Host': 'wapi.http.linkudp.com',
    "Origin": 'https://www.zmhttp.com',
    "Referer": 'https://www.zmhttp.com/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'cross-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
}
response = requests.post(url, json=body, headers=headers)
result = response.json()
print(result['ret_data'])

# 获取用户信息
url = "https://wapi.http.linkudp.com/index/users/user_info"
body = {

}
headers = {
    "Accept": "text/html, */*; q=0.01",
    "Accept-Encoding": 'gzip, deflate, br',
    "Accept-Language": 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Length': '0',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Host': 'wapi.http.linkudp.com',
    "Origin": 'https://www.zmhttp.com',
    "Referer": 'https://www.zmhttp.com/ucenter/?first_time=0',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'cross-site',
    'session-id': result['ret_data'],
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
}
requests.post(url, json=body, headers=headers)

# 自动领取每日免费ip
url = "https://wapi.http.linkudp.com/index/users/get_day_free_pack"
body = {
    'geetest_challenge': '',
    'geetest_validate': '',
    'geetest_seccode': ''
}
headers = {
    "Accept": "text/html, */*; q=0.01",
    "Accept-Encoding": 'gzip, deflate, br',
    "Accept-Language": 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Content-Length': '53',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Host': 'wapi.http.linkudp.com',
    "Origin": 'https://www.zmhttp.com',
    "Referer": 'https://www.zmhttp.com/ucenter/?first_time=0',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'cross-site',
    'session-id': result['ret_data'],
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36'
}
response = requests.post(url, json=body, headers=headers)
print(response.json()['msg'])

# 获取接口
url = 'https://owapi.http.linkudp.com/index/api/new_get_ips'
data = {  # key-value pairs
    'num': 20,
    'package_id': 259445,
    'type': 2,
    'pro_id': '',
    'port_type': 1,
    'city_id': '',
    'yys': 0,
    'time_show': 'true',
    'city_show': 'true',
    'yys_show': 'true',
    'manyregions': '',
    'region_type': 2,
    'line_break': 6,
    'special_break': '',
    'port_bit': 4,
    'm_repeat': 1,
    'pack_type': 'pack',
    'long_city': ''
}
response = requests.post(url, json=data, headers=headers)
i = response.json()['ret_data']
print(f'直连IP:{i["link"]}\n独享IP:{i["link2"]}\n隧道IP:{i["link3"]}')


# 筛选最优ip
@lru_cache(maxsize=None)
def get_ip_data():
    url = i["link"]
    response = requests.post(url, json={}, headers={})
    ip_data = response.json()
    return ip_data


def get_excellent_ip(ip_data):
    date1 = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    d1 = datetime.datetime.strptime(date1, '%Y-%m-%d %H:%M:%S')
    # 使用列表推导式和max函数代替循环和排序
    member = [datetime.datetime.strptime(new_data['data'][geshu]['expire_time'], '%Y-%m-%d %H:%M:%S') - d1 for geshu in
              range(len(ip_data['data']))]
    max_time = max(member)
    # 使用生成器表达式和join函数代替循环和print函数
    excellent_ip_port = "\n".join(
        f"{new_data['data'][geshu]['ip']}:{new_data['data'][geshu]['port']}" for geshu in range(len(ip_data['data'])) if
        member[geshu] == max_time)
    return max_time.seconds // 60, excellent_ip_port


new_data = get_ip_data()
if new_data['code'] == 0:
    print("获取芝麻代理ip成功")
else:
    print("获取ip失败")
max_minutes, excellent_ip_port = get_excellent_ip(new_data)
print(f'可用最长时间:{max_minutes}分钟')
print("符合最优时间的ip及其端口：")
print(excellent_ip_port)

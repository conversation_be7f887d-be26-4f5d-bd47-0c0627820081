"""
该程序用于从网站quake.360.net中扫描LibreChat网站，实现注册、登录、获取不用key的可用模型
360搜索关键字："LibreChat"
F12选择 Fetch/XHR 选择名为quake_service的请求复制响应存入input_data.json里面
然后运行该程序
2024.5.20
"openAI1"可以用
"openAI2"不可以用
"openAI3"备用，响应太慢
"""
import requests
import json
import warnings
from urllib3.exceptions import InsecureRequestWarning
from datetime import datetime
import os
from urllib.parse import urlparse, urlunparse

PROXY_URL = 'http://127.0.0.1:10809'
os.environ['HTTP_PROXY'] = PROXY_URL
os.environ['HTTPS_PROXY'] = PROXY_URL
warnings.simplefilter('ignore', InsecureRequestWarning)
# 绿色的 ANSI 转义码
GREEN = '\033[92m'
# 重置颜色的 ANSI 转义码
RESET = '\033[0m'
# 勾字符
check_mark = "✔"

# 读取现有的 LibreChat.json 文件内容
try:
    with open('LibreChat.json', 'r', encoding='utf-8') as file:
        existing_data = json.load(file)
        existing_list = [existing_data[i].get('url') for i in existing_data if 'url' in existing_data[i] and existing_data[i].get('url')]
        num_old = len(existing_data)
        num = len(existing_data)
except FileNotFoundError:
    existing_data = {}
    existing_list = []
    num_old = 0
    num = 0

is_register = False
token = ''

headers = {
    'Accept': 'application/json, text/plain, */*',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0',
}
json_data1 = {
    'name': 'ty we',
    'username': '<EMAIL>',
    'email': '<EMAIL>',
    'password': '12345qwert',
    'confirm_password': '12345qwert',
}
json_data2 = {
    'email': '<EMAIL>',
    'password': '12345qwert',
}

websites = []
processed_websites = {}
def normalize_url(url):
    if not url.startswith(('http://', 'https://')):
        url = 'http://' + url
    return url
def get_base_url_with_port(url):
    parsed_url = urlparse(url)
    # 保留协议、主机名和端口
    if parsed_url.port:
        base_url = f"{parsed_url.hostname}:{parsed_url.port}"
    else:
        base_url = f"{parsed_url.hostname}"
    return base_url
# 读取文件内容
with open('C:\\Users\\<USER>\\自用程序\\fofa爬虫\\LibreChat.txt', 'r') as file:
    for line in file:
        website = line.strip()
        website = normalize_url(website)
        base_url = get_base_url_with_port(website)
        # 检查是否需要替换 https 为 http
        if base_url in processed_websites:
            if website.startswith('http://') and processed_websites[base_url].startswith('https://'):
                # 替换为 http://
                websites.remove(processed_websites[base_url])
                websites.append(website)
                processed_websites[base_url] = website
            elif website.startswith('http://') and processed_websites[base_url].startswith('http://'):
                # 如果已经是 http://，则跳过
                continue
            elif website.startswith('https://') and processed_websites[base_url].startswith('http://'):
                # 如果已经是 http://，则跳过 https://
                continue
        else:
            # 记录新网址
            processed_websites[base_url] = website
            websites.append(website)

n = 0
existing_domains = {link.split("//")[1] for link in existing_list}  # 提前提取已存在的域名，使用集合提高查找效率

for link in websites:
    n += 1
    print(f"正在访问第{n}个链接")
    domain = link.split("//")[1]
    if domain in existing_domains:
        print("该网站已记录，跳过")
        continue
    existing_domains.add(domain)  # 将新域名添加到集合中
    try:
        response = requests.get(f'{link}/api/config', headers=headers, verify=False, timeout=5, allow_redirects=False)
        # 检查是否有重定向
        if response.status_code in [301, 302, 303, 307, 308]:
            print(link)
            print("重定向URL:", response.headers['Location'])
            base_url = response.headers['Location'].split("/api")[0]
            link = base_url
            response = requests.get(response.headers['Location'], verify=False, timeout=5)
        try:
            test_data = response.json()
        except ValueError as e:
            print(link)
            print("config没有json数据:", "\033[91m{}\033[0m".format(e))
            continue
        if test_data.get('registrationEnabled', False):
            print(link)
            my_dict = {"url": link}
            response = requests.post(f'{link}/api/auth/register', headers=headers, json=json_data1, verify=False)
            if response.status_code == 404:
                print("被反将一军，该网站不能注册")
                print(response.text)
                continue
            print(response.status_code, response.json())
            if 'user' in response.json():
                print(f"网站注册成功：{link}")
                my_dict['is_register'] = True
                is_register = True
            if 'message' in response.json():
                if response.json()['message'] == 'Something went wrong':
                    print(f"该网站注册过了：{link}")
                    my_dict['is_register'] = True
                    is_register = True
                if response.json()['message'] == 'Too many accounts created, please try again after 60 minutes':
                    print(f"触发网站60分钟注册限制")
                    my_dict['is_register'] = True
                    is_register = True
                if response.json()['message'] == 'Registration from this domain is not allowed.':
                    print(f"该网站注册地方不在这：{link}  跳过")
                    my_dict['is_register'] = False
                    continue
            if is_register:
                my_dict['is_login'] = False
                response = requests.post(f'{link}/api/auth/login', headers=headers, json=json_data2, verify=False)
                if response.status_code == 200:
                    if 'token' in response.json():
                        print("登录成功", f"{GREEN}{check_mark}{RESET}"*3)
                        my_dict['is_login'] = True
                        token = f'Bearer {response.json()["token"]}'
                    else:
                        print("登录失败")
                headers1 = {
                    'Accept': 'application/json, text/plain, */*',
                    'Authorization': token,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0',
                }
                response = requests.get(f'{link}/api/models', headers=headers1, verify=False)
                if response.status_code == 200:
                    print("获取模型列表成功")
                    model = response.json()
                    my_dict['models'] = {}
                else:
                    print("获取模型列表失败，跳过")
                    continue
                response = requests.get(f'{link}/api/endpoints', headers=headers, verify=False)
                if response.status_code == 200:
                    data = response.json()
                    for key, value in data.items():
                        if isinstance(value, dict) and "userProvide" in value and not value["userProvide"]:
                            print(f"{key}")
                            key_list = model.get(key, [])
                            my_dict['models'][key] = key_list
                            for item in key_list:
                                print(f"  - {item}")
                if my_dict['is_login']:
                    num += 1
                    existing_data.update({str(num): my_dict})
                    existing_data['change_date'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    # 将更新后的数据写回 LibreChat.json 文件
                    with open('LibreChat.json', 'w', encoding='utf-8') as file:
                        json.dump(existing_data, file, ensure_ascii=False, indent=4)
    except requests.exceptions.ProxyError as e:
        print(f"代理错误：{link}, 错误信息：{e}")
    except requests.exceptions.ReadTimeout:
        print(f"访问超时：{link}, 跳过此链接")
    except requests.exceptions.RequestException as e:
        print(f"请求遇到错误：{e}, {link}")


print(f"数据已成功写入 LibreChat.json 文件，增加了{num-num_old}个数据")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏常量定义模块
定义五子棋游戏的所有常量、配置参数和枚举值

包含：
- 游戏基础常量（棋盘大小、棋子类型）
- 游戏状态枚举
- GUI配置参数
- 颜色配置
- AI配置参数
"""

# ==================== 游戏基础常量 ====================

# 棋盘配置
BOARD_SIZE = 15         # 棋盘大小（15x15）
WIN_COUNT = 5           # 获胜所需连子数量

# 棋子类型
EMPTY_CELL = 0          # 空位
BLACK_PIECE = 1         # 黑棋
WHITE_PIECE = 2         # 白棋

# 玩家类型
HUMAN_PLAYER = 'human'  # 人类玩家
AI_PLAYER = 'ai'        # AI玩家

# ==================== 游戏状态枚举 ====================

# 游戏状态
GAME_ONGOING = 0        # 游戏进行中
GAME_BLACK_WIN = 1      # 黑棋获胜
GAME_WHITE_WIN = 2      # 白棋获胜
GAME_DRAW = 3           # 平局

# 游戏模式
MODE_HUMAN_VS_AI = 'human_vs_ai'        # 人机对战
MODE_HUMAN_VS_HUMAN = 'human_vs_human'  # 人人对战
MODE_AI_VS_AI = 'ai_vs_ai'              # 机机对战

# ==================== GUI配置参数 ====================

# 棋盘显示配置
CELL_SIZE = 30          # 格子大小（像素）
BOARD_MARGIN = 50       # 棋盘边距（像素）
PIECE_RADIUS = 12       # 棋子半径（像素）
LINE_WIDTH = 1          # 网格线宽度

# 窗口配置
WINDOW_TITLE = '五子棋游戏'
WINDOW_MIN_WIDTH = 600
WINDOW_MIN_HEIGHT = 700

# 控制面板配置
CONTROL_PANEL_WIDTH = 200
BUTTON_WIDTH = 15
BUTTON_HEIGHT = 2
BUTTON_PADDING = 10

# ==================== 颜色配置 ====================

# 棋盘颜色
BOARD_COLOR = '#DEB887'         # 棋盘背景色（浅棕色）
LINE_COLOR = '#8B4513'          # 网格线颜色（深棕色）
BORDER_COLOR = '#654321'        # 边框颜色

# 棋子颜色
BLACK_COLOR = '#000000'         # 黑棋颜色
WHITE_COLOR = '#FFFFFF'         # 白棋颜色
BLACK_BORDER = '#333333'        # 黑棋边框
WHITE_BORDER = '#CCCCCC'        # 白棋边框

# 高亮颜色
LAST_MOVE_COLOR = '#FF0000'     # 最后一步高亮色（红色）
HOVER_COLOR = '#FFFF00'         # 鼠标悬停色（黄色）
WINNING_LINE_COLOR = '#00FF00'  # 获胜连线色（绿色）

# 界面颜色
BACKGROUND_COLOR = '#F5F5DC'    # 主界面背景色（米色）
BUTTON_COLOR = '#E6E6FA'        # 按钮颜色（淡紫色）
BUTTON_ACTIVE_COLOR = '#D8BFD8' # 按钮激活色
TEXT_COLOR = '#000000'          # 文字颜色

# ==================== AI配置参数 ====================

# AI难度级别
AI_EASY = 'easy'        # 简单
AI_MEDIUM = 'medium'    # 中等
AI_HARD = 'hard'        # 困难

# AI算法参数
AI_SEARCH_DEPTH = {
    AI_EASY: 2,         # 简单AI搜索深度
    AI_MEDIUM: 4,       # 中等AI搜索深度
    AI_HARD: 6          # 困难AI搜索深度
}

AI_THINK_TIME = {
    AI_EASY: 0.5,       # 简单AI思考时间（秒）
    AI_MEDIUM: 1.0,     # 中等AI思考时间（秒）
    AI_HARD: 2.0        # 困难AI思考时间（秒）
}

# ==================== 方向向量 ====================

# 八个方向的向量（用于检查连子）
DIRECTIONS = [
    (0, 1),   # 水平向右
    (1, 0),   # 垂直向下
    (1, 1),   # 右下对角
    (1, -1),  # 左下对角
    (0, -1),  # 水平向左
    (-1, 0),  # 垂直向上
    (-1, -1), # 左上对角
    (-1, 1)   # 右上对角
]

# 四个主要方向（用于胜负判断）
MAIN_DIRECTIONS = [
    (0, 1),   # 水平
    (1, 0),   # 垂直
    (1, 1),   # 右下对角
    (1, -1)   # 左下对角
]

# ==================== 消息常量 ====================

# 游戏消息
MSG_GAME_START = "游戏开始！黑棋先行。"
MSG_BLACK_WIN = "黑棋获胜！"
MSG_WHITE_WIN = "白棋获胜！"
MSG_GAME_DRAW = "平局！"
MSG_INVALID_MOVE = "无效落子位置！"
MSG_AI_THINKING = "AI思考中..."

# 按钮文本
BTN_NEW_GAME = "新游戏"
BTN_UNDO = "悔棋"
BTN_RESET = "重置"
BTN_SETTINGS = "设置"
BTN_EXIT = "退出"

# 菜单文本
MENU_GAME_MODE = "游戏模式"
MENU_AI_DIFFICULTY = "AI难度"
MENU_HELP = "帮助"
MENU_ABOUT = "关于"

# ==================== 配置验证 ====================

def validate_constants():
    """验证常量配置的合理性"""
    assert BOARD_SIZE > 0, "棋盘大小必须大于0"
    assert WIN_COUNT <= BOARD_SIZE, "获胜连子数不能超过棋盘大小"
    assert CELL_SIZE > 0, "格子大小必须大于0"
    assert PIECE_RADIUS > 0, "棋子半径必须大于0"
    assert PIECE_RADIUS < CELL_SIZE // 2, "棋子半径不能超过格子大小的一半"

    print("✅ 常量配置验证通过")

# 在模块导入时自动验证
if __name__ == "__main__":
    validate_constants()

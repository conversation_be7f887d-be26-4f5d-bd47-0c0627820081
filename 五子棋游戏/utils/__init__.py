"""
工具模块
包含常量定义和工具函数
"""

# 导出所有常量
from .constants import *

__all__ = [
    # 游戏基础常量
    'BOARD_SIZE', 'WIN_COUNT', 'EMPTY_CELL', 'BLACK_PIECE', 'WHITE_PIECE',
    'HUM<PERSON>_PLAYER', 'AI_PLAYER',

    # 游戏状态
    'GAME_ONGOING', 'GAME_BLACK_WIN', 'GAME_WHITE_WIN', 'GAME_DRAW',
    'MODE_HUMAN_VS_AI', 'MODE_HUMAN_VS_HUMAN', 'MODE_AI_VS_AI',

    # GUI配置
    'CELL_SIZE', 'BOARD_MARGIN', 'PIECE_RADIUS', 'LINE_WIDTH',
    'WINDOW_TITLE', 'WINDOW_MIN_WIDTH', 'WINDOW_MIN_HEIGHT',
    'CONTROL_PANEL_WIDTH', 'BUTTON_WIDTH', 'BUTTON_HEIGHT', 'BUTTON_PADDING',

    # 颜色配置
    'BOARD_COLOR', 'LINE_COLOR', 'BORDER_COLOR',
    'BLACK_COLOR', 'WHITE_COLOR', 'BLACK_BORDER', 'WHITE_BORDER',
    'LAST_MOVE_COLOR', 'HOVER_COLOR', 'WINNING_LINE_COLOR',
    'BACKGROUND_COLOR', 'BUTTON_COLOR', 'BUTTON_ACTIVE_COLOR', 'TEXT_COLOR',

    # AI配置
    'AI_EASY', 'AI_MEDIUM', 'AI_HARD',
    'AI_SEARCH_DEPTH', 'AI_THINK_TIME',

    # 方向向量
    'DIRECTIONS', 'MAIN_DIRECTIONS',

    # 消息常量
    'MSG_GAME_START', 'MSG_BLACK_WIN', 'MSG_WHITE_WIN', 'MSG_GAME_DRAW',
    'MSG_INVALID_MOVE', 'MSG_AI_THINKING',
    'BTN_NEW_GAME', 'BTN_UNDO', 'BTN_RESET', 'BTN_SETTINGS', 'BTN_EXIT',
    'MENU_GAME_MODE', 'MENU_AI_DIFFICULTY', 'MENU_HELP', 'MENU_ABOUT',

    # 工具函数
    'validate_constants'
]

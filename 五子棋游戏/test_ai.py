#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI算法测试脚本
验证GomokuAI类的各项功能
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.ai import GomokuAI, create_ai, get_ai_difficulties, get_difficulty_description
from game.board import GameBoard
from utils.constants import BLACK_PIECE, WHITE_PIECE, AI_EASY, AI_MEDIUM, AI_HARD


def test_ai_creation():
    """测试AI创建"""
    print("🧪 测试AI创建...")
    
    # 测试不同难度的AI创建
    for difficulty in [AI_EASY, AI_MEDIUM, AI_HARD]:
        ai = create_ai(difficulty)
        assert ai.difficulty == difficulty, f"AI难度应为{difficulty}"
        assert ai.max_depth > 0, "搜索深度应大于0"
        assert ai.think_time > 0, "思考时间应大于0"
    
    # 测试无效难度
    try:
        GomokuA<PERSON>("invalid")
        assert False, "应该抛出异常"
    except ValueError:
        pass  # 预期的异常
    
    print("✅ AI创建测试通过")


def test_ai_basic_moves():
    """测试AI基本落子"""
    print("🧪 测试AI基本落子...")
    
    ai = create_ai(AI_EASY)
    board = GameBoard()
    
    # 空棋盘时AI应选择中心位置附近
    move = ai.get_best_move(board, BLACK_PIECE)
    assert move is not None, "AI应能选择落子位置"
    assert isinstance(move, tuple), "落子应为元组"
    assert len(move) == 2, "落子应有两个坐标"
    
    row, col = move
    assert board.is_valid_move(row, col), "AI选择的位置应有效"
    
    # 验证AI选择的位置在中心区域附近
    center = board.size // 2
    distance = abs(row - center) + abs(col - center)
    assert distance <= 3, "首步应在中心区域附近"
    
    print("✅ AI基本落子测试通过")


def test_ai_winning_detection():
    """测试AI获胜检测"""
    print("🧪 测试AI获胜检测...")
    
    ai = create_ai(AI_MEDIUM)
    board = GameBoard()
    
    # 设置一个即将获胜的局面（4子连珠）
    for col in range(4):
        board.make_move(7, 7 + col, BLACK_PIECE)
    
    # AI应该选择获胜位置
    move = ai.get_best_move(board, BLACK_PIECE)
    assert move is not None, "AI应能找到获胜位置"
    
    # 验证这确实是获胜位置
    row, col = move
    board.make_move(row, col, BLACK_PIECE)
    assert board.check_line_win(row, col, BLACK_PIECE), "AI选择的位置应能获胜"
    
    print("✅ AI获胜检测测试通过")


def test_ai_defense():
    """测试AI防守能力"""
    print("🧪 测试AI防守能力...")
    
    ai = create_ai(AI_MEDIUM)
    board = GameBoard()
    
    # 设置对手即将获胜的局面
    for col in range(4):
        board.make_move(7, 7 + col, WHITE_PIECE)
    
    # AI应该阻止对手获胜
    move = ai.get_best_move(board, BLACK_PIECE)
    assert move is not None, "AI应能选择防守位置"
    
    # 验证AI选择了阻止位置
    expected_defense_positions = [(7, 11), (7, 6)]  # 两端的阻止位置
    assert move in expected_defense_positions, f"AI应选择防守位置，实际选择了{move}"
    
    print("✅ AI防守能力测试通过")


def test_ai_difficulty_differences():
    """测试不同难度AI的差异"""
    print("🧪 测试不同难度AI的差异...")
    
    board = GameBoard()
    # 设置一个复杂局面
    moves = [(7, 7), (8, 8), (7, 8), (8, 7), (6, 9)]
    for i, (row, col) in enumerate(moves):
        piece = BLACK_PIECE if i % 2 == 0 else WHITE_PIECE
        board.make_move(row, col, piece)
    
    # 测试不同难度的AI
    results = {}
    for difficulty in [AI_EASY, AI_MEDIUM, AI_HARD]:
        ai = create_ai(difficulty)
        start_time = time.time()
        move = ai.get_best_move(board, BLACK_PIECE)
        think_time = time.time() - start_time
        
        results[difficulty] = {
            'move': move,
            'time': think_time,
            'nodes': ai.nodes_evaluated
        }
    
    # 验证难度差异
    assert results[AI_EASY]['time'] <= results[AI_MEDIUM]['time'], "简单AI应比中等AI快"
    assert results[AI_MEDIUM]['time'] <= results[AI_HARD]['time'], "中等AI应比困难AI快"
    
    print("✅ AI难度差异测试通过")


def test_ai_statistics():
    """测试AI统计功能"""
    print("🧪 测试AI统计功能...")
    
    ai = create_ai(AI_MEDIUM)
    board = GameBoard()
    
    # 执行几次AI决策
    for _ in range(3):
        move = ai.get_best_move(board, BLACK_PIECE)
        if move:
            board.make_move(move[0], move[1], BLACK_PIECE)
    
    # 获取统计信息
    stats = ai.get_statistics()
    assert 'difficulty' in stats, "统计应包含难度信息"
    assert 'nodes_evaluated' in stats, "统计应包含评估节点数"
    assert stats['nodes_evaluated'] > 0, "应有节点被评估"
    
    # 测试缓存
    initial_cache_size = stats['cache_size']
    ai.get_best_move(board, BLACK_PIECE)  # 再次执行
    new_stats = ai.get_statistics()
    
    # 缓存应该增长或命中
    assert (new_stats['cache_size'] >= initial_cache_size or 
            new_stats['cache_hits'] > stats['cache_hits']), "缓存应该工作"
    
    print("✅ AI统计功能测试通过")


def test_ai_utility_functions():
    """测试AI工具函数"""
    print("🧪 测试AI工具函数...")
    
    # 测试难度列表
    difficulties = get_ai_difficulties()
    assert AI_EASY in difficulties, "应包含简单难度"
    assert AI_MEDIUM in difficulties, "应包含中等难度"
    assert AI_HARD in difficulties, "应包含困难难度"
    
    # 测试难度描述
    for difficulty in difficulties:
        description = get_difficulty_description(difficulty)
        assert isinstance(description, str), "描述应为字符串"
        assert len(description) > 0, "描述不应为空"
    
    # 测试无效难度描述
    invalid_desc = get_difficulty_description("invalid")
    assert "未知" in invalid_desc, "无效难度应返回未知描述"
    
    print("✅ AI工具函数测试通过")


def test_ai_performance():
    """测试AI性能"""
    print("🧪 测试AI性能...")
    
    board = GameBoard()
    
    # 测试简单AI的性能
    ai = create_ai(AI_EASY)
    start_time = time.time()
    move = ai.get_best_move(board, BLACK_PIECE)
    elapsed_time = time.time() - start_time
    
    assert move is not None, "AI应能选择位置"
    assert elapsed_time < 5.0, "简单AI应在5秒内完成"
    
    # 测试AI设置难度
    assert ai.set_difficulty(AI_HARD), "设置难度应成功"
    assert ai.difficulty == AI_HARD, "难度应更新"
    
    print("✅ AI性能测试通过")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始AI算法功能测试...")
    print("=" * 50)
    
    try:
        test_ai_creation()
        test_ai_basic_moves()
        test_ai_winning_detection()
        test_ai_defense()
        test_ai_difficulty_differences()
        test_ai_statistics()
        test_ai_utility_functions()
        test_ai_performance()
        
        print("=" * 50)
        print("🎉 所有AI测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()

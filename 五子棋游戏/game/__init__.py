"""
游戏逻辑模块
包含棋盘、玩家、AI算法和游戏控制逻辑
"""

# 导出主要类
from .board import GameBoard, create_empty_board, is_valid_board_size
from .player import (
    Player, HumanPlayer, AIPlayer,
    create_human_player, create_ai_player, create_player_pair,
    create_human_vs_human, create_ai_vs_ai,
    get_opponent_piece_type, is_valid_piece_type, get_piece_type_name,
    get_difficulty_levels, get_difficulty_display_names
)
from .ai import (
    GomokuAI, create_ai, get_ai_difficulties,
    get_difficulty_description, benchmark_ai
)
from .game_logic import (
    GameLogic, GameState, create_game_logic,
    get_available_game_modes, get_game_mode_description, validate_game_setup
)

__all__ = [
    # 棋盘相关
    'GameBoard',
    'create_empty_board',
    'is_valid_board_size',

    # 玩家相关
    'Player',
    'HumanPlayer',
    'AIPlayer',
    'create_human_player',
    'create_ai_player',
    'create_player_pair',
    'create_human_vs_human',
    'create_ai_vs_ai',
    'get_opponent_piece_type',
    'is_valid_piece_type',
    'get_piece_type_name',
    'get_difficulty_levels',
    'get_difficulty_display_names',

    # AI相关
    'GomokuAI',
    'create_ai',
    'get_ai_difficulties',
    'get_difficulty_description',
    'benchmark_ai',

    # 游戏逻辑
    'GameLogic',
    'GameState',
    'create_game_logic',
    'get_available_game_modes',
    'get_game_mode_description',
    'validate_game_setup'
]

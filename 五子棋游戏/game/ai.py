#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI算法模块
实现五子棋AI算法，包括简单规则AI和评估函数

包含：
- GomokuAI类：五子棋AI核心算法
- 防守优先策略：阻止对手获胜
- 攻击策略：寻找获胜机会
- 位置评估算法：评估落子价值
- 不同难度级别：简单、中等、困难
"""

import random
import time
from typing import Tuple, List, Optional, TYPE_CHECKING
import sys
import os

# 添加父目录到路径以导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.constants import (
    BOARD_SIZE, WIN_COUNT, EMPTY_CELL, BLACK_PIECE, WHITE_PIECE,
    MAIN_DIRECTIONS, AI_EASY, AI_MEDIUM, AI_HARD,
    AI_SEARCH_DEPTH, AI_THINK_TIME
)

# 避免循环导入
if TYPE_CHECKING:
    from .board import GameBoard


class GomokuAI:
    """
    五子棋AI算法类

    实现不同难度级别的AI算法，包括防守优先策略、攻击策略和位置评估
    """

    def __init__(self, difficulty: str = AI_EASY):
        """
        初始化AI算法

        Args:
            difficulty: AI难度级别 (AI_EASY, AI_MEDIUM, AI_HARD)
        """
        if difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            raise ValueError(f"无效的AI难度: {difficulty}")

        self.difficulty = difficulty
        self.max_depth = AI_SEARCH_DEPTH.get(difficulty, 2)
        self.think_time = AI_THINK_TIME.get(difficulty, 0.5)

        # 评估权重配置
        self.weights = self._get_evaluation_weights()

        # 统计信息
        self.nodes_evaluated = 0
        self.cache_hits = 0
        self.position_cache = {}  # 位置评估缓存

    def _get_evaluation_weights(self) -> dict:
        """
        根据难度获取评估权重

        Returns:
            dict: 评估权重配置
        """
        if self.difficulty == AI_EASY:
            return {
                'win': 10000,      # 获胜
                'block_win': 5000, # 阻止对手获胜
                'four': 1000,      # 四子连珠
                'block_four': 500, # 阻止对手四子
                'three': 100,      # 三子连珠
                'block_three': 50, # 阻止对手三子
                'two': 10,         # 二子连珠
                'center': 5        # 中心位置奖励
            }
        elif self.difficulty == AI_MEDIUM:
            return {
                'win': 50000,
                'block_win': 25000,
                'four': 5000,
                'block_four': 2500,
                'three': 500,
                'block_three': 250,
                'two': 50,
                'center': 10
            }
        else:  # AI_HARD
            return {
                'win': 100000,
                'block_win': 50000,
                'four': 10000,
                'block_four': 5000,
                'three': 1000,
                'block_three': 500,
                'two': 100,
                'center': 20
            }

    def get_best_move(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        获取最佳落子位置

        Args:
            board: 当前棋盘状态
            player_type: 当前玩家类型

        Returns:
            Optional[Tuple[int, int]]: 最佳落子位置
        """
        start_time = time.time()
        self.nodes_evaluated = 0
        self.cache_hits = 0

        try:
            if self.difficulty == AI_EASY:
                move = self._simple_strategy(board, player_type)
            else:
                move = self._advanced_strategy(board, player_type)

            # 确保返回有效位置
            if move and board.is_valid_move(move[0], move[1]):
                return move
            else:
                # 如果算法失败，随机选择
                return self._get_random_move(board)

        except Exception as e:
            print(f"AI算法出错: {e}")
            return self._get_random_move(board)
        finally:
            elapsed_time = time.time() - start_time
            if elapsed_time < self.think_time:
                time.sleep(self.think_time - elapsed_time)

    def _simple_strategy(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        简单策略：防守优先 + 基础攻击

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 选择的位置
        """
        opponent_type = WHITE_PIECE if player_type == BLACK_PIECE else BLACK_PIECE

        # 1. 检查是否可以直接获胜
        win_move = self._find_winning_move(board, player_type)
        if win_move:
            return win_move

        # 2. 检查是否需要阻止对手获胜
        block_move = self._find_winning_move(board, opponent_type)
        if block_move:
            return block_move

        # 3. 寻找最佳攻击位置
        attack_move = self._find_best_attack_move(board, player_type)
        if attack_move:
            return attack_move

        # 4. 选择评估值最高的位置
        return self._find_best_position(board, player_type)

    def _advanced_strategy(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        高级策略：使用更复杂的评估和搜索

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 选择的位置
        """
        opponent_type = WHITE_PIECE if player_type == BLACK_PIECE else BLACK_PIECE

        # 1. 检查直接获胜
        win_move = self._find_winning_move(board, player_type)
        if win_move:
            return win_move

        # 2. 检查阻止对手获胜
        block_move = self._find_winning_move(board, opponent_type)
        if block_move:
            return block_move

        # 3. 使用minimax算法搜索最佳位置
        if self.difficulty == AI_HARD:
            return self._minimax_search(board, player_type)
        else:
            return self._evaluate_all_positions(board, player_type)

    def _find_winning_move(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        寻找能够直接获胜的位置

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 获胜位置，如果没有则返回None
        """
        for row in range(board.size):
            for col in range(board.size):
                if board.is_valid_move(row, col):
                    # 模拟落子
                    board.make_move(row, col, player_type)

                    # 检查是否获胜
                    if board.check_line_win(row, col, player_type):
                        board.undo_move()  # 撤销模拟落子
                        return (row, col)

                    board.undo_move()  # 撤销模拟落子

        return None

    def _find_best_attack_move(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        寻找最佳攻击位置（形成三子或四子连珠）

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 最佳攻击位置
        """
        best_move = None
        best_score = -1

        for row in range(board.size):
            for col in range(board.size):
                if board.is_valid_move(row, col):
                    score = self._evaluate_attack_position(board, row, col, player_type)
                    if score > best_score:
                        best_score = score
                        best_move = (row, col)

        return best_move if best_score > 0 else None

    def _evaluate_attack_position(self, board: 'GameBoard', row: int, col: int, player_type: int) -> int:
        """
        评估攻击位置的价值

        Args:
            board: 棋盘状态
            row: 行坐标
            col: 列坐标
            player_type: 玩家类型

        Returns:
            int: 位置评估分数
        """
        score = 0

        # 检查四个方向的连子情况
        for direction in MAIN_DIRECTIONS:
            dr, dc = direction

            # 计算该方向的连子数
            consecutive = self._count_consecutive_in_direction(board, row, col, dr, dc, player_type)

            # 根据连子数给分
            if consecutive >= 4:
                score += self.weights['four']
            elif consecutive >= 3:
                score += self.weights['three']
            elif consecutive >= 2:
                score += self.weights['two']

        # 中心位置奖励
        center = board.size // 2
        distance_to_center = abs(row - center) + abs(col - center)
        score += max(0, self.weights['center'] - distance_to_center)

        return score

    def _count_consecutive_in_direction(self, board: 'GameBoard', row: int, col: int,
                                      dr: int, dc: int, player_type: int) -> int:
        """
        计算指定方向上的连续棋子数（包括模拟落子）

        Args:
            board: 棋盘状态
            row: 起始行
            col: 起始列
            dr: 行方向增量
            dc: 列方向增量
            player_type: 玩家类型

        Returns:
            int: 连续棋子数
        """
        # 模拟在该位置落子
        board.make_move(row, col, player_type)

        # 计算正方向连子数
        positive_count = board.count_consecutive_pieces(row, col, (dr, dc), player_type)
        # 计算负方向连子数
        negative_count = board.count_consecutive_pieces(row, col, (-dr, -dc), player_type)

        # 撤销模拟落子
        board.undo_move()

        # 总连子数 = 1（当前位置）+ 正方向 + 负方向
        return 1 + positive_count + negative_count

    def _find_best_position(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        寻找评估值最高的位置

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 最佳位置
        """
        best_move = None
        best_score = float('-inf')

        # 获取候选位置（减少搜索空间）
        candidates = self._get_candidate_positions(board)

        for row, col in candidates:
            if board.is_valid_move(row, col):
                score = self._evaluate_position(board, row, col, player_type)
                if score > best_score:
                    best_score = score
                    best_move = (row, col)

        return best_move

    def _get_candidate_positions(self, board: 'GameBoard') -> List[Tuple[int, int]]:
        """
        获取候选位置（已有棋子周围的空位）

        Args:
            board: 棋盘状态

        Returns:
            List[Tuple[int, int]]: 候选位置列表
        """
        candidates = set()

        # 如果棋盘为空，返回中心位置
        if board.get_move_count() == 0:
            center = board.size // 2
            return [(center, center)]

        # 寻找已有棋子周围的空位
        for row in range(board.size):
            for col in range(board.size):
                if board.get_piece(row, col) != EMPTY_CELL:
                    # 检查周围8个方向的空位
                    for dr in [-1, 0, 1]:
                        for dc in [-1, 0, 1]:
                            if dr == 0 and dc == 0:
                                continue
                            new_row, new_col = row + dr, col + dc
                            if (board.is_valid_position(new_row, new_col) and
                                board.is_empty_position(new_row, new_col)):
                                candidates.add((new_row, new_col))

        return list(candidates) if candidates else board.get_empty_positions()[:20]

    def _evaluate_position(self, board: 'GameBoard', row: int, col: int, player_type: int) -> int:
        """
        评估某个位置的价值

        Args:
            board: 棋盘状态
            row: 行坐标
            col: 列坐标
            player_type: 玩家类型

        Returns:
            int: 位置评估分数
        """
        self.nodes_evaluated += 1

        # 检查缓存
        cache_key = (tuple(map(tuple, board.board)), row, col, player_type)
        if cache_key in self.position_cache:
            self.cache_hits += 1
            return self.position_cache[cache_key]

        opponent_type = WHITE_PIECE if player_type == BLACK_PIECE else BLACK_PIECE

        # 计算自己的攻击价值
        attack_score = self._calculate_pattern_score(board, row, col, player_type, True)

        # 计算防守价值（阻止对手）
        defense_score = self._calculate_pattern_score(board, row, col, opponent_type, False)

        # 位置价值（中心位置更有价值）
        center = board.size // 2
        distance_to_center = abs(row - center) + abs(col - center)
        position_score = max(0, self.weights['center'] - distance_to_center)

        total_score = attack_score + defense_score + position_score

        # 缓存结果
        self.position_cache[cache_key] = total_score

        return total_score

    def _calculate_pattern_score(self, board: 'GameBoard', row: int, col: int,
                               player_type: int, is_attack: bool) -> int:
        """
        计算模式分数（连子模式的价值）

        Args:
            board: 棋盘状态
            row: 行坐标
            col: 列坐标
            player_type: 玩家类型
            is_attack: 是否为攻击模式

        Returns:
            int: 模式分数
        """
        score = 0

        # 模拟落子
        board.make_move(row, col, player_type)

        try:
            # 检查是否直接获胜
            if board.check_line_win(row, col, player_type):
                score += self.weights['win'] if is_attack else self.weights['block_win']
            else:
                # 检查各个方向的连子模式
                for direction in MAIN_DIRECTIONS:
                    dr, dc = direction
                    consecutive = board.count_consecutive_pieces(row, col, direction, player_type)
                    consecutive += board.count_consecutive_pieces(row, col, (-dr, -dc), player_type)
                    consecutive += 1  # 加上当前位置

                    # 根据连子数给分
                    if consecutive >= 4:
                        weight_key = 'four' if is_attack else 'block_four'
                        score += self.weights[weight_key]
                    elif consecutive >= 3:
                        weight_key = 'three' if is_attack else 'block_three'
                        score += self.weights[weight_key]
                    elif consecutive >= 2:
                        score += self.weights['two']

        finally:
            # 撤销模拟落子
            board.undo_move()

        return score

    def _evaluate_all_positions(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        评估所有候选位置并返回最佳位置

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 最佳位置
        """
        candidates = self._get_candidate_positions(board)
        best_move = None
        best_score = float('-inf')

        for row, col in candidates:
            if board.is_valid_move(row, col):
                score = self._evaluate_position(board, row, col, player_type)
                if score > best_score:
                    best_score = score
                    best_move = (row, col)

        return best_move

    def _minimax_search(self, board: 'GameBoard', player_type: int) -> Optional[Tuple[int, int]]:
        """
        使用Minimax算法搜索最佳位置

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            Optional[Tuple[int, int]]: 最佳位置
        """
        candidates = self._get_candidate_positions(board)
        best_move = None
        best_score = float('-inf')

        for row, col in candidates[:10]:  # 限制搜索范围
            if board.is_valid_move(row, col):
                board.make_move(row, col, player_type)
                score = self._minimax(board, self.max_depth - 1, False, player_type,
                                    float('-inf'), float('inf'))
                board.undo_move()

                if score > best_score:
                    best_score = score
                    best_move = (row, col)

        return best_move

    def _minimax(self, board: 'GameBoard', depth: int, is_maximizing: bool,
                player_type: int, alpha: float, beta: float) -> float:
        """
        Minimax算法实现（带Alpha-Beta剪枝）

        Args:
            board: 棋盘状态
            depth: 搜索深度
            is_maximizing: 是否为最大化玩家
            player_type: 当前玩家类型
            alpha: Alpha值
            beta: Beta值

        Returns:
            float: 评估分数
        """
        self.nodes_evaluated += 1

        # 终止条件
        if depth == 0 or board.is_board_full():
            return self._evaluate_board_state(board, player_type)

        current_player = player_type if is_maximizing else (
            WHITE_PIECE if player_type == BLACK_PIECE else BLACK_PIECE
        )

        candidates = self._get_candidate_positions(board)[:8]  # 进一步限制搜索

        if is_maximizing:
            max_eval = float('-inf')
            for row, col in candidates:
                if board.is_valid_move(row, col):
                    board.make_move(row, col, current_player)
                    eval_score = self._minimax(board, depth - 1, False, player_type, alpha, beta)
                    board.undo_move()

                    max_eval = max(max_eval, eval_score)
                    alpha = max(alpha, eval_score)
                    if beta <= alpha:
                        break  # Alpha-Beta剪枝
            return max_eval
        else:
            min_eval = float('inf')
            for row, col in candidates:
                if board.is_valid_move(row, col):
                    board.make_move(row, col, current_player)
                    eval_score = self._minimax(board, depth - 1, True, player_type, alpha, beta)
                    board.undo_move()

                    min_eval = min(min_eval, eval_score)
                    beta = min(beta, eval_score)
                    if beta <= alpha:
                        break  # Alpha-Beta剪枝
            return min_eval

    def _evaluate_board_state(self, board: 'GameBoard', player_type: int) -> float:
        """
        评估整个棋盘状态

        Args:
            board: 棋盘状态
            player_type: 玩家类型

        Returns:
            float: 棋盘评估分数
        """
        opponent_type = WHITE_PIECE if player_type == BLACK_PIECE else BLACK_PIECE

        my_score = 0
        opponent_score = 0

        # 评估所有已下的棋子
        for row in range(board.size):
            for col in range(board.size):
                piece = board.get_piece(row, col)
                if piece == player_type:
                    my_score += self._evaluate_piece_value(board, row, col, player_type)
                elif piece == opponent_type:
                    opponent_score += self._evaluate_piece_value(board, row, col, opponent_type)

        return my_score - opponent_score

    def _evaluate_piece_value(self, board: 'GameBoard', row: int, col: int, player_type: int) -> int:
        """
        评估单个棋子的价值

        Args:
            board: 棋盘状态
            row: 行坐标
            col: 列坐标
            player_type: 玩家类型

        Returns:
            int: 棋子价值
        """
        value = 0

        for direction in MAIN_DIRECTIONS:
            dr, dc = direction
            consecutive = board.count_consecutive_pieces(row, col, direction, player_type)
            consecutive += board.count_consecutive_pieces(row, col, (-dr, -dc), player_type)
            consecutive += 1  # 加上当前位置

            if consecutive >= 4:
                value += 1000
            elif consecutive >= 3:
                value += 100
            elif consecutive >= 2:
                value += 10

        return value

    def _get_random_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        随机选择一个有效位置

        Args:
            board: 棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 随机位置
        """
        empty_positions = board.get_empty_positions()
        if empty_positions:
            return random.choice(empty_positions)
        return None

    def clear_cache(self) -> None:
        """清除位置评估缓存"""
        self.position_cache.clear()
        self.cache_hits = 0

    def get_statistics(self) -> dict:
        """
        获取AI统计信息

        Returns:
            dict: 统计信息
        """
        return {
            'difficulty': self.difficulty,
            'max_depth': self.max_depth,
            'nodes_evaluated': self.nodes_evaluated,
            'cache_hits': self.cache_hits,
            'cache_size': len(self.position_cache)
        }

    def set_difficulty(self, difficulty: str) -> bool:
        """
        设置AI难度

        Args:
            difficulty: 新的难度级别

        Returns:
            bool: 设置是否成功
        """
        if difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            return False

        self.difficulty = difficulty
        self.max_depth = AI_SEARCH_DEPTH.get(difficulty, 2)
        self.think_time = AI_THINK_TIME.get(difficulty, 0.5)
        self.weights = self._get_evaluation_weights()
        self.clear_cache()  # 清除缓存，因为评估权重已改变

        return True

    def __str__(self) -> str:
        """字符串表示"""
        return f"GomokuAI(difficulty={self.difficulty}, depth={self.max_depth})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"GomokuAI(difficulty='{self.difficulty}', max_depth={self.max_depth}, "
                f"nodes_evaluated={self.nodes_evaluated})")


# 工具函数

def create_ai(difficulty: str = AI_EASY) -> GomokuAI:
    """
    创建AI实例

    Args:
        difficulty: AI难度级别

    Returns:
        GomokuAI: AI实例
    """
    return GomokuAI(difficulty)


def get_ai_difficulties() -> List[str]:
    """
    获取所有可用的AI难度级别

    Returns:
        List[str]: 难度级别列表
    """
    return [AI_EASY, AI_MEDIUM, AI_HARD]


def get_difficulty_description(difficulty: str) -> str:
    """
    获取难度描述

    Args:
        difficulty: 难度级别

    Returns:
        str: 难度描述
    """
    descriptions = {
        AI_EASY: "简单 - 基础策略，适合新手",
        AI_MEDIUM: "中等 - 平衡策略，有一定挑战性",
        AI_HARD: "困难 - 高级算法，具有挑战性"
    }
    return descriptions.get(difficulty, "未知难度")


def benchmark_ai(board: 'GameBoard', difficulty: str, iterations: int = 10) -> dict:
    """
    AI性能基准测试

    Args:
        board: 测试棋盘
        difficulty: AI难度
        iterations: 测试次数

    Returns:
        dict: 性能统计
    """
    ai = create_ai(difficulty)
    total_time = 0
    total_nodes = 0

    for _ in range(iterations):
        start_time = time.time()
        ai.get_best_move(board, BLACK_PIECE)
        total_time += time.time() - start_time
        total_nodes += ai.nodes_evaluated
        ai.nodes_evaluated = 0

    return {
        'difficulty': difficulty,
        'iterations': iterations,
        'average_time': total_time / iterations,
        'average_nodes': total_nodes / iterations,
        'total_time': total_time
    }

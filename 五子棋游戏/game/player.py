#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家类模块
实现Player基类和HumanPlayer、AIPlayer子类
管理玩家信息、游戏状态和玩家行为

包含：
- Player基类：玩家抽象接口
- HumanPlayer子类：人类玩家实现
- AIPlayer子类：AI玩家实现
- 玩家信息管理和统计
"""

from abc import ABC, abstractmethod
from typing import Tuple, Optional, TYPE_CHECKING
import sys
import os

# 添加父目录到路径以导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.constants import (
    BLACK_PIECE, WHITE_PIECE, HUMAN_PLAYER, AI_PLAYER,
    AI_EASY, AI_MEDIUM, AI_HARD
)

# 避免循环导入
if TYPE_CHECKING:
    from .board import GameBoard


class Player(ABC):
    """
    玩家抽象基类

    定义所有玩家类型的通用接口和属性
    """

    def __init__(self, name: str, piece_type: int, player_type: str):
        """
        初始化玩家

        Args:
            name: 玩家名称
            piece_type: 棋子类型 (BLACK_PIECE 或 WHITE_PIECE)
            player_type: 玩家类型 (HUMAN_PLAYER 或 AI_PLAYER)
        """
        if piece_type not in [BLACK_PIECE, WHITE_PIECE]:
            raise ValueError(f"无效的棋子类型: {piece_type}")

        if player_type not in [HUMAN_PLAYER, AI_PLAYER]:
            raise ValueError(f"无效的玩家类型: {player_type}")

        self.name = name
        self.piece_type = piece_type
        self.player_type = player_type
        self.wins = 0  # 胜利次数
        self.losses = 0  # 失败次数
        self.draws = 0  # 平局次数
        self.total_moves = 0  # 总落子数
        self.thinking_time = 0.0  # 思考时间（秒）

    @abstractmethod
    def get_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        获取玩家的下一步落子位置

        Args:
            board: 当前棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 落子位置 (row, col)，如果无法获取则返回None
        """
        pass

    def add_win(self) -> None:
        """记录一次胜利"""
        self.wins += 1

    def add_loss(self) -> None:
        """记录一次失败"""
        self.losses += 1

    def add_draw(self) -> None:
        """记录一次平局"""
        self.draws += 1

    def add_move(self) -> None:
        """记录一次落子"""
        self.total_moves += 1

    def get_total_games(self) -> int:
        """获取总游戏数"""
        return self.wins + self.losses + self.draws

    def get_win_rate(self) -> float:
        """
        获取胜率

        Returns:
            float: 胜率 (0.0 - 1.0)
        """
        total_games = self.get_total_games()
        return self.wins / total_games if total_games > 0 else 0.0

    def reset_stats(self) -> None:
        """重置统计数据"""
        self.wins = 0
        self.losses = 0
        self.draws = 0
        self.total_moves = 0
        self.thinking_time = 0.0

    def get_piece_symbol(self) -> str:
        """
        获取棋子符号

        Returns:
            str: 棋子符号
        """
        return "●" if self.piece_type == BLACK_PIECE else "○"

    def get_piece_name(self) -> str:
        """
        获取棋子名称

        Returns:
            str: 棋子名称
        """
        return "黑棋" if self.piece_type == BLACK_PIECE else "白棋"

    def __str__(self) -> str:
        """字符串表示"""
        return f"{self.name}({self.get_piece_name()})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"{self.__class__.__name__}(name='{self.name}', "
                f"piece_type={self.piece_type}, wins={self.wins})")


class HumanPlayer(Player):
    """
    人类玩家类

    通过GUI界面接收用户输入的落子位置
    """

    def __init__(self, name: str = "玩家", piece_type: int = BLACK_PIECE):
        """
        初始化人类玩家

        Args:
            name: 玩家名称
            piece_type: 棋子类型
        """
        super().__init__(name, piece_type, HUMAN_PLAYER)
        self.pending_move: Optional[Tuple[int, int]] = None  # 等待GUI输入的落子位置
        self.is_move_ready = False  # 是否有可用的落子

    def get_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        获取人类玩家的落子位置

        Args:
            board: 当前棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 落子位置，如果还未选择则返回None
        """
        if self.is_move_ready and self.pending_move is not None:
            move = self.pending_move
            self.clear_move()  # 清除已使用的落子
            return move
        return None

    def set_move(self, row: int, col: int) -> bool:
        """
        设置玩家选择的落子位置

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 设置是否成功
        """
        if row < 0 or col < 0:
            return False

        self.pending_move = (row, col)
        self.is_move_ready = True
        return True

    def clear_move(self) -> None:
        """清除当前的落子选择"""
        self.pending_move = None
        self.is_move_ready = False

    def has_move_ready(self) -> bool:
        """
        检查是否有可用的落子

        Returns:
            bool: 是否有可用落子
        """
        return self.is_move_ready and self.pending_move is not None


class AIPlayer(Player):
    """
    AI玩家类

    使用算法自动选择最佳落子位置
    """

    def __init__(self, name: str = "电脑", piece_type: int = WHITE_PIECE,
                 difficulty: str = AI_EASY):
        """
        初始化AI玩家

        Args:
            name: AI名称
            piece_type: 棋子类型
            difficulty: 难度级别 (AI_EASY, AI_MEDIUM, AI_HARD)
        """
        super().__init__(name, piece_type, AI_PLAYER)

        if difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            raise ValueError(f"无效的AI难度: {difficulty}")

        self.difficulty = difficulty
        self.search_depth = self._get_search_depth()
        self.think_time = self._get_think_time()
        self.last_move_time = 0.0  # 上次思考时间

    def _get_search_depth(self) -> int:
        """
        根据难度获取搜索深度

        Returns:
            int: 搜索深度
        """
        depth_map = {
            AI_EASY: 2,
            AI_MEDIUM: 4,
            AI_HARD: 6
        }
        return depth_map[self.difficulty]

    def _get_think_time(self) -> float:
        """
        根据难度获取思考时间

        Returns:
            float: 思考时间（秒）
        """
        time_map = {
            AI_EASY: 0.5,
            AI_MEDIUM: 1.0,
            AI_HARD: 2.0
        }
        return time_map[self.difficulty]

    def get_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        获取AI的落子位置

        Args:
            board: 当前棋盘状态

        Returns:
            Optional[Tuple[int, int]]: AI选择的落子位置
        """
        import time
        start_time = time.time()

        try:
            # 获取最佳落子位置
            move = self._calculate_best_move(board)

            # 记录思考时间
            self.last_move_time = time.time() - start_time
            self.thinking_time += self.last_move_time

            return move

        except Exception as e:
            print(f"AI计算落子时出错: {e}")
            # 如果AI计算失败，随机选择一个有效位置
            return self._get_random_move(board)

    def _calculate_best_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        计算最佳落子位置（使用GomokuAI算法）

        Args:
            board: 棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 最佳落子位置
        """
        try:
            # 导入AI算法（延迟导入避免循环依赖）
            from .ai import GomokuAI

            # 创建AI实例
            ai = GomokuAI(self.difficulty)

            # 获取最佳落子位置
            return ai.get_best_move(board, self.piece_type)

        except ImportError:
            # 如果AI模块不可用，使用简单策略
            return self._simple_fallback_strategy(board)

    def _simple_fallback_strategy(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        简单的后备策略（当AI模块不可用时）

        Args:
            board: 棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 落子位置
        """
        # 简单策略：优先选择中心区域的空位
        center = board.size // 2

        # 首先尝试中心位置
        if board.is_valid_move(center, center):
            return (center, center)

        # 然后尝试中心周围的位置
        for radius in range(1, min(center + 1, 4)):
            for dr in range(-radius, radius + 1):
                for dc in range(-radius, radius + 1):
                    if abs(dr) == radius or abs(dc) == radius:  # 只检查边界
                        row, col = center + dr, center + dc
                        if board.is_valid_move(row, col):
                            return (row, col)

        # 如果中心区域都被占用，随机选择
        return self._get_random_move(board)

    def _get_random_move(self, board: 'GameBoard') -> Optional[Tuple[int, int]]:
        """
        随机选择一个有效的落子位置

        Args:
            board: 棋盘状态

        Returns:
            Optional[Tuple[int, int]]: 随机落子位置
        """
        import random

        empty_positions = board.get_empty_positions()
        if empty_positions:
            return random.choice(empty_positions)
        return None

    def set_difficulty(self, difficulty: str) -> bool:
        """
        设置AI难度

        Args:
            difficulty: 新的难度级别

        Returns:
            bool: 设置是否成功
        """
        if difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            return False

        self.difficulty = difficulty
        self.search_depth = self._get_search_depth()
        self.think_time = self._get_think_time()
        return True

    def get_difficulty_name(self) -> str:
        """
        获取难度名称

        Returns:
            str: 难度名称
        """
        name_map = {
            AI_EASY: "简单",
            AI_MEDIUM: "中等",
            AI_HARD: "困难"
        }
        return name_map[self.difficulty]

    def get_last_move_time(self) -> float:
        """
        获取上次落子的思考时间

        Returns:
            float: 思考时间（秒）
        """
        return self.last_move_time

    def get_average_move_time(self) -> float:
        """
        获取平均落子时间

        Returns:
            float: 平均时间（秒）
        """
        return self.thinking_time / self.total_moves if self.total_moves > 0 else 0.0


# 工具函数和玩家工厂方法

def create_human_player(name: str = "玩家", piece_type: int = BLACK_PIECE) -> HumanPlayer:
    """
    创建人类玩家

    Args:
        name: 玩家名称
        piece_type: 棋子类型

    Returns:
        HumanPlayer: 人类玩家实例
    """
    return HumanPlayer(name, piece_type)


def create_ai_player(name: str = "电脑", piece_type: int = WHITE_PIECE,
                     difficulty: str = AI_EASY) -> AIPlayer:
    """
    创建AI玩家

    Args:
        name: AI名称
        piece_type: 棋子类型
        difficulty: 难度级别

    Returns:
        AIPlayer: AI玩家实例
    """
    return AIPlayer(name, piece_type, difficulty)


def create_player_pair(human_name: str = "玩家", ai_name: str = "电脑",
                      ai_difficulty: str = AI_EASY,
                      human_first: bool = True) -> Tuple[Player, Player]:
    """
    创建人机对战的玩家对

    Args:
        human_name: 人类玩家名称
        ai_name: AI玩家名称
        ai_difficulty: AI难度
        human_first: 人类是否先手（黑棋）

    Returns:
        Tuple[Player, Player]: (先手玩家, 后手玩家)
    """
    if human_first:
        human = create_human_player(human_name, BLACK_PIECE)
        ai = create_ai_player(ai_name, WHITE_PIECE, ai_difficulty)
        return human, ai
    else:
        ai = create_ai_player(ai_name, BLACK_PIECE, ai_difficulty)
        human = create_human_player(human_name, WHITE_PIECE)
        return ai, human


def create_human_vs_human(player1_name: str = "玩家1",
                         player2_name: str = "玩家2") -> Tuple[HumanPlayer, HumanPlayer]:
    """
    创建人人对战的玩家对

    Args:
        player1_name: 玩家1名称（黑棋）
        player2_name: 玩家2名称（白棋）

    Returns:
        Tuple[HumanPlayer, HumanPlayer]: (玩家1, 玩家2)
    """
    player1 = create_human_player(player1_name, BLACK_PIECE)
    player2 = create_human_player(player2_name, WHITE_PIECE)
    return player1, player2


def create_ai_vs_ai(ai1_name: str = "电脑1", ai2_name: str = "电脑2",
                   ai1_difficulty: str = AI_MEDIUM,
                   ai2_difficulty: str = AI_MEDIUM) -> Tuple[AIPlayer, AIPlayer]:
    """
    创建AI对战的玩家对

    Args:
        ai1_name: AI1名称（黑棋）
        ai2_name: AI2名称（白棋）
        ai1_difficulty: AI1难度
        ai2_difficulty: AI2难度

    Returns:
        Tuple[AIPlayer, AIPlayer]: (AI1, AI2)
    """
    ai1 = create_ai_player(ai1_name, BLACK_PIECE, ai1_difficulty)
    ai2 = create_ai_player(ai2_name, WHITE_PIECE, ai2_difficulty)
    return ai1, ai2


def get_opponent_piece_type(piece_type: int) -> int:
    """
    获取对手的棋子类型

    Args:
        piece_type: 当前棋子类型

    Returns:
        int: 对手棋子类型
    """
    return WHITE_PIECE if piece_type == BLACK_PIECE else BLACK_PIECE


def is_valid_piece_type(piece_type: int) -> bool:
    """
    验证棋子类型是否有效

    Args:
        piece_type: 棋子类型

    Returns:
        bool: 是否有效
    """
    return piece_type in [BLACK_PIECE, WHITE_PIECE]


def get_piece_type_name(piece_type: int) -> str:
    """
    获取棋子类型名称

    Args:
        piece_type: 棋子类型

    Returns:
        str: 棋子名称
    """
    if piece_type == BLACK_PIECE:
        return "黑棋"
    elif piece_type == WHITE_PIECE:
        return "白棋"
    else:
        return "未知"


def get_difficulty_levels() -> list:
    """
    获取所有可用的AI难度级别

    Returns:
        list: 难度级别列表
    """
    return [AI_EASY, AI_MEDIUM, AI_HARD]


def get_difficulty_display_names() -> dict:
    """
    获取难度级别的显示名称

    Returns:
        dict: 难度级别到显示名称的映射
    """
    return {
        AI_EASY: "简单",
        AI_MEDIUM: "中等",
        AI_HARD: "困难"
    }

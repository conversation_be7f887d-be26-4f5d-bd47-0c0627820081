#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏逻辑控制器模块
协调棋盘、玩家和AI之间的交互，管理游戏流程

包含：
- GameLogic类：游戏核心控制器
- 游戏流程控制和回合管理
- 胜负判断和游戏状态管理
- 玩家交互和AI集成
- 悔棋和重置功能
"""

import time
from typing import List, Optional, Tuple, Callable, Any
from enum import Enum
import sys
import os

# 添加父目录到路径以导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.constants import (
    BOARD_SIZE, BLACK_PIECE, WHITE_PIECE, EMPTY_CELL,
    GAME_ONGOING, GAME_BLACK_WIN, GAME_WHITE_WIN, GAME_DRAW,
    MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI,
    HUMAN_PLAYER, AI_PLAYER, AI_EASY, AI_MEDIUM, AI_HARD,
    MSG_GAME_START, MSG_BLACK_WIN, MSG_WHITE_WIN, MSG_GAME_DRAW, MSG_INVALID_MOVE
)

from .board import GameBoard
from .player import Player, HumanPlayer, AIPlayer, create_player_pair, create_human_vs_human, create_ai_vs_ai
from .ai import GomokuAI


class GameState(Enum):
    """游戏状态枚举"""
    NOT_STARTED = "not_started"    # 未开始
    ONGOING = "ongoing"            # 进行中
    PAUSED = "paused"              # 暂停
    FINISHED = "finished"          # 已结束
    WAITING_FOR_PLAYER = "waiting" # 等待玩家输入


class GameLogic:
    """
    游戏逻辑控制器

    作为MVC模式中的Controller，协调Model（棋盘、玩家）和View（GUI）之间的交互
    管理游戏的完整生命周期和所有业务逻辑
    """

    def __init__(self):
        """初始化游戏逻辑控制器"""
        # 核心组件
        self.board = GameBoard()
        self.players: List[Player] = []

        # 游戏状态
        self.game_state = GameState.NOT_STARTED
        self.current_player_index = 0
        self.game_result = GAME_ONGOING
        self.winner: Optional[Player] = None

        # 游戏配置
        self.game_mode = MODE_HUMAN_VS_AI
        self.ai_difficulty = AI_EASY

        # 游戏历史和统计
        self.move_history: List[dict] = []  # 详细的移动历史
        self.game_start_time: Optional[float] = None
        self.total_game_time = 0.0

        # 事件回调
        self.on_move_made: Optional[Callable] = None
        self.on_game_end: Optional[Callable] = None
        self.on_player_change: Optional[Callable] = None
        self.on_game_state_change: Optional[Callable] = None

        # AI相关
        self.ai_thinking = False
        self.last_ai_move_time = 0.0

    def setup_game(self, game_mode: str, player1_name: str = "玩家1",
                   player2_name: str = "玩家2", ai_difficulty: str = AI_EASY,
                   human_first: bool = True) -> bool:
        """
        设置游戏模式和玩家

        Args:
            game_mode: 游戏模式 (MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI)
            player1_name: 玩家1名称
            player2_name: 玩家2名称
            ai_difficulty: AI难度级别
            human_first: 人类是否先手（仅在人机对战时有效）

        Returns:
            bool: 设置是否成功
        """
        try:
            self.game_mode = game_mode
            self.ai_difficulty = ai_difficulty

            # 根据游戏模式创建玩家
            if game_mode == MODE_HUMAN_VS_AI:
                self.players = list(create_player_pair(
                    player1_name, player2_name, ai_difficulty, human_first
                ))
            elif game_mode == MODE_HUMAN_VS_HUMAN:
                self.players = list(create_human_vs_human(player1_name, player2_name))
            elif game_mode == MODE_AI_VS_AI:
                self.players = list(create_ai_vs_ai(
                    player1_name, player2_name, ai_difficulty, ai_difficulty
                ))
            else:
                raise ValueError(f"无效的游戏模式: {game_mode}")

            # 重置游戏状态
            self.reset_game()
            self._change_game_state(GameState.NOT_STARTED)

            return True

        except Exception as e:
            print(f"设置游戏失败: {e}")
            return False

    def start_game(self) -> bool:
        """
        开始游戏

        Returns:
            bool: 开始是否成功
        """
        if not self.players or len(self.players) != 2:
            return False

        if self.game_state != GameState.NOT_STARTED:
            return False

        # 初始化游戏
        self.board.reset()
        self.current_player_index = 0
        self.game_result = GAME_ONGOING
        self.winner = None
        self.move_history.clear()
        self.game_start_time = time.time()

        # 重置玩家状态
        for player in self.players:
            if isinstance(player, HumanPlayer):
                player.clear_move()

        self._change_game_state(GameState.ONGOING)
        self._trigger_player_change()

        return True

    def make_move(self, row: int, col: int) -> bool:
        """
        执行落子操作

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 落子是否成功
        """
        if self.game_state != GameState.ONGOING:
            return False

        current_player = self.get_current_player()
        if not current_player:
            return False

        # 验证落子有效性
        if not self.board.is_valid_move(row, col):
            return False

        # 如果当前玩家是人类玩家，验证这是他们选择的位置
        if isinstance(current_player, HumanPlayer):
            if not current_player.has_move_ready():
                return False
            player_move = current_player.get_move(self.board)
            if player_move != (row, col):
                return False

        # 执行落子
        success = self.board.make_move(row, col, current_player.piece_type)
        if not success:
            return False

        # 记录移动历史
        move_info = {
            'player': current_player.name,
            'piece_type': current_player.piece_type,
            'position': (row, col),
            'timestamp': time.time(),
            'move_number': len(self.move_history) + 1
        }
        self.move_history.append(move_info)

        # 更新玩家统计
        current_player.add_move()

        # 检查游戏结果
        self.game_result = self.board.check_winner(row, col)

        # 触发移动事件
        self._trigger_move_made(row, col, current_player)

        # 处理游戏结果
        if self.game_result != GAME_ONGOING:
            self._handle_game_end()
        else:
            # 切换到下一个玩家
            self._switch_to_next_player()

        return True

    def process_ai_move(self) -> bool:
        """
        处理AI玩家的落子

        Returns:
            bool: AI落子是否成功
        """
        if self.game_state != GameState.ONGOING:
            return False

        current_player = self.get_current_player()
        if not isinstance(current_player, AIPlayer):
            return False

        if self.ai_thinking:
            return False

        try:
            self.ai_thinking = True
            start_time = time.time()

            # 获取AI的落子位置
            move = current_player.get_move(self.board)

            self.last_ai_move_time = time.time() - start_time

            if move:
                row, col = move
                return self.make_move(row, col)
            else:
                print("AI无法选择有效位置")
                return False

        except Exception as e:
            print(f"AI落子出错: {e}")
            return False
        finally:
            self.ai_thinking = False

    def set_human_move(self, row: int, col: int) -> bool:
        """
        设置人类玩家的落子位置

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 设置是否成功
        """
        current_player = self.get_current_player()
        if not isinstance(current_player, HumanPlayer):
            return False

        if not self.board.is_valid_move(row, col):
            return False

        return current_player.set_move(row, col)

    def undo_last_move(self) -> bool:
        """
        悔棋功能

        Returns:
            bool: 悔棋是否成功
        """
        if self.game_state != GameState.ONGOING:
            return False

        if not self.move_history:
            return False

        # 撤销棋盘上的落子
        if not self.board.undo_move():
            return False

        # 移除历史记录
        last_move = self.move_history.pop()

        # 切换回上一个玩家
        self.current_player_index = (self.current_player_index - 1) % 2

        # 如果是人类玩家，清除其待定移动
        current_player = self.get_current_player()
        if isinstance(current_player, HumanPlayer):
            current_player.clear_move()

        # 重置游戏结果
        self.game_result = GAME_ONGOING
        self.winner = None

        # 如果游戏状态是结束，改回进行中
        if self.game_state == GameState.FINISHED:
            self._change_game_state(GameState.ONGOING)

        self._trigger_player_change()

        return True

    def reset_game(self) -> None:
        """重置游戏到初始状态"""
        self.board.reset()
        self.current_player_index = 0
        self.game_result = GAME_ONGOING
        self.winner = None
        self.move_history.clear()
        self.game_start_time = None
        self.total_game_time = 0.0
        self.ai_thinking = False
        self.last_ai_move_time = 0.0

        # 重置玩家状态
        for player in self.players:
            if isinstance(player, HumanPlayer):
                player.clear_move()

        self._change_game_state(GameState.NOT_STARTED)

    def pause_game(self) -> bool:
        """
        暂停游戏

        Returns:
            bool: 暂停是否成功
        """
        if self.game_state == GameState.ONGOING:
            self._change_game_state(GameState.PAUSED)
            return True
        return False

    def resume_game(self) -> bool:
        """
        恢复游戏

        Returns:
            bool: 恢复是否成功
        """
        if self.game_state == GameState.PAUSED:
            self._change_game_state(GameState.ONGOING)
            return True
        return False

    def get_current_player(self) -> Optional[Player]:
        """
        获取当前玩家

        Returns:
            Optional[Player]: 当前玩家，如果没有则返回None
        """
        if not self.players or self.current_player_index >= len(self.players):
            return None
        return self.players[self.current_player_index]

    def get_opponent_player(self) -> Optional[Player]:
        """
        获取对手玩家

        Returns:
            Optional[Player]: 对手玩家，如果没有则返回None
        """
        if not self.players or len(self.players) != 2:
            return None
        opponent_index = (self.current_player_index + 1) % 2
        return self.players[opponent_index]

    def is_current_player_ai(self) -> bool:
        """
        检查当前玩家是否为AI

        Returns:
            bool: 当前玩家是否为AI
        """
        current_player = self.get_current_player()
        return isinstance(current_player, AIPlayer)

    def is_current_player_human(self) -> bool:
        """
        检查当前玩家是否为人类

        Returns:
            bool: 当前玩家是否为人类
        """
        current_player = self.get_current_player()
        return isinstance(current_player, HumanPlayer)

    def can_undo(self) -> bool:
        """
        检查是否可以悔棋

        Returns:
            bool: 是否可以悔棋
        """
        return (self.game_state == GameState.ONGOING and
                len(self.move_history) > 0 and
                not self.ai_thinking)

    def can_make_move(self, row: int, col: int) -> bool:
        """
        检查是否可以在指定位置落子

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 是否可以落子
        """
        return (self.game_state == GameState.ONGOING and
                self.board.is_valid_move(row, col) and
                not self.ai_thinking)

    def get_game_info(self) -> dict:
        """
        获取游戏信息

        Returns:
            dict: 游戏信息字典
        """
        current_player = self.get_current_player()

        info = {
            'game_state': self.game_state.value,
            'game_mode': self.game_mode,
            'current_player': current_player.name if current_player else None,
            'current_piece_type': current_player.piece_type if current_player else None,
            'move_count': len(self.move_history),
            'game_result': self.game_result,
            'winner': self.winner.name if self.winner else None,
            'can_undo': self.can_undo(),
            'ai_thinking': self.ai_thinking,
            'last_move': self.board.get_last_move(),
            'board_size': self.board.size
        }

        # 添加时间信息
        if self.game_start_time:
            if self.game_state == GameState.FINISHED:
                info['game_duration'] = self.total_game_time
            else:
                info['game_duration'] = time.time() - self.game_start_time
        else:
            info['game_duration'] = 0.0

        # 添加玩家信息
        info['players'] = []
        for i, player in enumerate(self.players):
            player_info = {
                'name': player.name,
                'piece_type': player.piece_type,
                'player_type': player.player_type,
                'is_current': i == self.current_player_index,
                'wins': player.wins,
                'losses': player.losses,
                'draws': player.draws
            }

            if isinstance(player, AIPlayer):
                player_info['difficulty'] = player.difficulty
                player_info['last_move_time'] = player.get_last_move_time()

            info['players'].append(player_info)

        return info

    def get_move_history(self) -> List[dict]:
        """
        获取移动历史

        Returns:
            List[dict]: 移动历史列表
        """
        return self.move_history.copy()

    def get_game_statistics(self) -> dict:
        """
        获取游戏统计信息

        Returns:
            dict: 统计信息
        """
        stats = {
            'total_moves': len(self.move_history),
            'game_duration': self.total_game_time if self.game_state == GameState.FINISHED
                           else (time.time() - self.game_start_time if self.game_start_time else 0),
            'average_move_time': 0.0,
            'players_stats': []
        }

        # 计算平均移动时间
        if len(self.move_history) > 1:
            total_time = 0.0
            for i in range(1, len(self.move_history)):
                total_time += self.move_history[i]['timestamp'] - self.move_history[i-1]['timestamp']
            stats['average_move_time'] = total_time / (len(self.move_history) - 1)

        # 玩家统计
        for player in self.players:
            player_stats = {
                'name': player.name,
                'total_games': player.get_total_games(),
                'win_rate': player.get_win_rate(),
                'total_moves': player.total_moves
            }

            if isinstance(player, AIPlayer):
                player_stats['average_think_time'] = player.get_average_move_time()
                player_stats['difficulty'] = player.difficulty

            stats['players_stats'].append(player_stats)

        return stats

    # 内部方法

    def _switch_to_next_player(self) -> None:
        """切换到下一个玩家"""
        self.current_player_index = (self.current_player_index + 1) % 2
        self._trigger_player_change()

    def _handle_game_end(self) -> None:
        """处理游戏结束"""
        if self.game_start_time:
            self.total_game_time = time.time() - self.game_start_time

        # 确定获胜者
        if self.game_result == GAME_BLACK_WIN:
            self.winner = self._get_player_by_piece_type(BLACK_PIECE)
        elif self.game_result == GAME_WHITE_WIN:
            self.winner = self._get_player_by_piece_type(WHITE_PIECE)
        else:
            self.winner = None  # 平局

        # 更新玩家统计
        for player in self.players:
            if self.winner == player:
                player.add_win()
            elif self.winner is None:
                player.add_draw()
            else:
                player.add_loss()

        self._change_game_state(GameState.FINISHED)
        self._trigger_game_end()

    def _get_player_by_piece_type(self, piece_type: int) -> Optional[Player]:
        """根据棋子类型获取玩家"""
        for player in self.players:
            if player.piece_type == piece_type:
                return player
        return None

    def _change_game_state(self, new_state: GameState) -> None:
        """改变游戏状态并触发事件"""
        old_state = self.game_state
        self.game_state = new_state
        self._trigger_game_state_change(old_state, new_state)

    # 事件触发方法

    def _trigger_move_made(self, row: int, col: int, player: Player) -> None:
        """触发落子事件"""
        if self.on_move_made:
            try:
                self.on_move_made(row, col, player, self.board.copy_board())
            except Exception as e:
                print(f"落子事件回调出错: {e}")

    def _trigger_game_end(self) -> None:
        """触发游戏结束事件"""
        if self.on_game_end:
            try:
                self.on_game_end(self.game_result, self.winner, self.get_game_statistics())
            except Exception as e:
                print(f"游戏结束事件回调出错: {e}")

    def _trigger_player_change(self) -> None:
        """触发玩家切换事件"""
        if self.on_player_change:
            try:
                current_player = self.get_current_player()
                self.on_player_change(current_player, self.current_player_index)
            except Exception as e:
                print(f"玩家切换事件回调出错: {e}")

    def _trigger_game_state_change(self, old_state: GameState, new_state: GameState) -> None:
        """触发游戏状态改变事件"""
        if self.on_game_state_change:
            try:
                self.on_game_state_change(old_state.value, new_state.value)
            except Exception as e:
                print(f"游戏状态改变事件回调出错: {e}")

    # 事件回调设置方法

    def set_move_callback(self, callback: Callable) -> None:
        """设置落子事件回调"""
        self.on_move_made = callback

    def set_game_end_callback(self, callback: Callable) -> None:
        """设置游戏结束事件回调"""
        self.on_game_end = callback

    def set_player_change_callback(self, callback: Callable) -> None:
        """设置玩家切换事件回调"""
        self.on_player_change = callback

    def set_game_state_change_callback(self, callback: Callable) -> None:
        """设置游戏状态改变事件回调"""
        self.on_game_state_change = callback

    # 配置方法

    def set_ai_difficulty(self, difficulty: str) -> bool:
        """
        设置AI难度

        Args:
            difficulty: 新的难度级别

        Returns:
            bool: 设置是否成功
        """
        if difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            return False

        self.ai_difficulty = difficulty

        # 更新现有AI玩家的难度
        for player in self.players:
            if isinstance(player, AIPlayer):
                player.set_difficulty(difficulty)

        return True

    def get_valid_moves(self) -> List[Tuple[int, int]]:
        """
        获取所有有效的落子位置

        Returns:
            List[Tuple[int, int]]: 有效位置列表
        """
        return self.board.get_empty_positions()

    def get_board_state(self) -> List[List[int]]:
        """
        获取棋盘状态

        Returns:
            List[List[int]]: 棋盘状态的深拷贝
        """
        return self.board.copy_board()

    def get_result_message(self) -> str:
        """
        获取游戏结果消息

        Returns:
            str: 结果消息
        """
        if self.game_result == GAME_BLACK_WIN:
            return MSG_BLACK_WIN
        elif self.game_result == GAME_WHITE_WIN:
            return MSG_WHITE_WIN
        elif self.game_result == GAME_DRAW:
            return MSG_GAME_DRAW
        else:
            return MSG_GAME_START

    def __str__(self) -> str:
        """字符串表示"""
        return (f"GameLogic(state={self.game_state.value}, "
                f"mode={self.game_mode}, moves={len(self.move_history)})")

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"GameLogic(game_state={self.game_state}, game_mode='{self.game_mode}', "
                f"current_player_index={self.current_player_index}, "
                f"move_count={len(self.move_history)})")


# 工具函数

def create_game_logic() -> GameLogic:
    """
    创建游戏逻辑实例

    Returns:
        GameLogic: 游戏逻辑实例
    """
    return GameLogic()


def get_available_game_modes() -> List[str]:
    """
    获取可用的游戏模式

    Returns:
        List[str]: 游戏模式列表
    """
    return [MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI]


def get_game_mode_description(mode: str) -> str:
    """
    获取游戏模式描述

    Args:
        mode: 游戏模式

    Returns:
        str: 模式描述
    """
    descriptions = {
        MODE_HUMAN_VS_AI: "人机对战 - 与AI对手进行游戏",
        MODE_HUMAN_VS_HUMAN: "人人对战 - 两个人类玩家对战",
        MODE_AI_VS_AI: "AI对战 - 观看两个AI对战"
    }
    return descriptions.get(mode, "未知模式")


def validate_game_setup(game_mode: str, ai_difficulty: str = None) -> bool:
    """
    验证游戏设置的有效性

    Args:
        game_mode: 游戏模式
        ai_difficulty: AI难度（可选）

    Returns:
        bool: 设置是否有效
    """
    if game_mode not in get_available_game_modes():
        return False

    if game_mode in [MODE_HUMAN_VS_AI, MODE_AI_VS_AI]:
        if ai_difficulty and ai_difficulty not in [AI_EASY, AI_MEDIUM, AI_HARD]:
            return False

    return True

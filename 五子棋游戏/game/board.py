#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
棋盘类模块
管理15x15棋盘的状态、落子操作、胜负判断等核心功能

包含：
- GameBoard类：棋盘核心逻辑
- 棋盘初始化和重置
- 落子验证和执行
- 五子连珠检测算法
- 悔棋功能
- 棋盘状态查询
"""

from typing import List, Tuple, Optional
import sys
import os

# 添加父目录到路径以导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.constants import (
    BOARD_SIZE, WIN_COUNT, EMPTY_CELL, BLACK_PIECE, WHITE_PIECE,
    MAIN_DIRECTIONS, GAME_ONGOING, GAME_BLACK_WIN, GAME_WHITE_WIN, GAME_DRAW
)


class GameBoard:
    """
    五子棋棋盘类

    管理棋盘状态、落子操作、胜负判断等核心功能
    """

    def __init__(self, size: int = BOARD_SIZE):
        """
        初始化棋盘

        Args:
            size: 棋盘大小，默认为15x15
        """
        self.size = size
        self.board = [[EMPTY_CELL for _ in range(size)] for _ in range(size)]
        self.move_history: List[Tuple[int, int, int]] = []  # 落子历史 (row, col, player)
        self.last_move: Optional[Tuple[int, int]] = None  # 最后一步位置

    def is_valid_position(self, row: int, col: int) -> bool:
        """
        检查位置是否在棋盘范围内

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 位置是否有效
        """
        return 0 <= row < self.size and 0 <= col < self.size

    def is_empty_position(self, row: int, col: int) -> bool:
        """
        检查位置是否为空

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 位置是否为空
        """
        if not self.is_valid_position(row, col):
            return False
        return self.board[row][col] == EMPTY_CELL

    def is_valid_move(self, row: int, col: int) -> bool:
        """
        验证落子位置是否有效

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 落子是否有效
        """
        return self.is_valid_position(row, col) and self.is_empty_position(row, col)

    def make_move(self, row: int, col: int, player: int) -> bool:
        """
        执行落子操作

        Args:
            row: 行坐标
            col: 列坐标
            player: 玩家类型 (BLACK_PIECE 或 WHITE_PIECE)

        Returns:
            bool: 落子是否成功
        """
        if not self.is_valid_move(row, col):
            return False

        if player not in [BLACK_PIECE, WHITE_PIECE]:
            return False

        # 执行落子
        self.board[row][col] = player
        self.move_history.append((row, col, player))
        self.last_move = (row, col)

        return True

    def undo_move(self) -> bool:
        """
        悔棋操作，撤销最后一步

        Returns:
            bool: 悔棋是否成功
        """
        if not self.move_history:
            return False

        # 获取最后一步
        last_row, last_col, _ = self.move_history.pop()

        # 清空该位置
        self.board[last_row][last_col] = EMPTY_CELL

        # 更新最后一步位置
        if self.move_history:
            self.last_move = (self.move_history[-1][0], self.move_history[-1][1])
        else:
            self.last_move = None

        return True

    def get_piece(self, row: int, col: int) -> int:
        """
        获取指定位置的棋子

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            int: 棋子类型 (EMPTY_CELL, BLACK_PIECE, WHITE_PIECE)
        """
        if not self.is_valid_position(row, col):
            return EMPTY_CELL
        return self.board[row][col]

    def count_consecutive_pieces(self, row: int, col: int, direction: Tuple[int, int], player: int) -> int:
        """
        在指定方向上计算连续棋子数量

        Args:
            row: 起始行坐标
            col: 起始列坐标
            direction: 方向向量 (row_delta, col_delta)
            player: 玩家类型

        Returns:
            int: 连续棋子数量
        """
        count = 0
        dr, dc = direction
        current_row, current_col = row + dr, col + dc

        while (self.is_valid_position(current_row, current_col) and
               self.board[current_row][current_col] == player):
            count += 1
            current_row += dr
            current_col += dc

        return count

    def check_line_win(self, row: int, col: int, player: int) -> bool:
        """
        检查从指定位置是否形成五子连珠

        Args:
            row: 行坐标
            col: 列坐标
            player: 玩家类型

        Returns:
            bool: 是否获胜
        """
        if self.board[row][col] != player:
            return False

        # 检查四个主要方向
        for direction in MAIN_DIRECTIONS:
            dr, dc = direction

            # 计算正方向和反方向的连续棋子数
            positive_count = self.count_consecutive_pieces(row, col, direction, player)
            negative_count = self.count_consecutive_pieces(row, col, (-dr, -dc), player)

            # 加上当前位置的棋子，总数为 1 + positive_count + negative_count
            total_count = 1 + positive_count + negative_count

            if total_count >= WIN_COUNT:
                return True

        return False

    def check_winner(self, row: int, col: int) -> int:
        """
        检查是否有玩家获胜

        Args:
            row: 最后落子的行坐标
            col: 最后落子的列坐标

        Returns:
            int: 游戏状态 (GAME_ONGOING, GAME_BLACK_WIN, GAME_WHITE_WIN, GAME_DRAW)
        """
        if not self.is_valid_position(row, col):
            return GAME_ONGOING

        player = self.board[row][col]
        if player == EMPTY_CELL:
            return GAME_ONGOING

        # 检查是否获胜
        if self.check_line_win(row, col, player):
            return GAME_BLACK_WIN if player == BLACK_PIECE else GAME_WHITE_WIN

        # 检查是否平局（棋盘已满）
        if self.is_board_full():
            return GAME_DRAW

        return GAME_ONGOING

    def is_board_full(self) -> bool:
        """
        检查棋盘是否已满

        Returns:
            bool: 棋盘是否已满
        """
        for row in range(self.size):
            for col in range(self.size):
                if self.board[row][col] == EMPTY_CELL:
                    return False
        return True

    def get_empty_positions(self) -> List[Tuple[int, int]]:
        """
        获取所有空位置

        Returns:
            List[Tuple[int, int]]: 空位置列表 [(row, col), ...]
        """
        empty_positions = []
        for row in range(self.size):
            for col in range(self.size):
                if self.board[row][col] == EMPTY_CELL:
                    empty_positions.append((row, col))
        return empty_positions

    def get_move_count(self) -> int:
        """
        获取已下棋子数量

        Returns:
            int: 棋子数量
        """
        return len(self.move_history)

    def get_last_move(self) -> Optional[Tuple[int, int]]:
        """
        获取最后一步位置

        Returns:
            Optional[Tuple[int, int]]: 最后一步位置，如果没有则返回None
        """
        return self.last_move

    def reset(self) -> None:
        """
        重置棋盘到初始状态
        """
        # 完全重新创建棋盘数组，确保所有位置都是空的
        self.board = [[EMPTY_CELL for _ in range(self.size)] for _ in range(self.size)]

        # 清空移动历史
        self.move_history.clear()

        # 重置最后一步位置
        self.last_move = None

        # 验证棋盘确实已清空
        self._verify_board_empty()

    def copy_board(self) -> List[List[int]]:
        """
        复制当前棋盘状态

        Returns:
            List[List[int]]: 棋盘状态的深拷贝
        """
        return [row[:] for row in self.board]

    def set_board(self, board_state: List[List[int]]) -> bool:
        """
        设置棋盘状态（用于测试或恢复）

        Args:
            board_state: 棋盘状态

        Returns:
            bool: 设置是否成功
        """
        if len(board_state) != self.size or any(len(row) != self.size for row in board_state):
            return False

        self.board = [row[:] for row in board_state]
        # 重新构建移动历史（简化版本）
        self.move_history.clear()
        self.last_move = None

        return True

    def _verify_board_empty(self) -> None:
        """
        验证棋盘是否确实为空

        这是一个内部方法，用于调试和确保棋盘重置的正确性
        """
        for row in range(self.size):
            for col in range(self.size):
                if self.board[row][col] != EMPTY_CELL:
                    # 如果发现非空位置，强制清空
                    print(f"警告：发现位置 ({row}, {col}) 未正确清空，强制清空")
                    self.board[row][col] = EMPTY_CELL

    def get_board_string(self) -> str:
        """
        获取棋盘的字符串表示（用于调试）

        Returns:
            str: 棋盘字符串
        """
        symbols = {EMPTY_CELL: '.', BLACK_PIECE: '●', WHITE_PIECE: '○'}
        lines = []

        # 添加列号
        header = '  ' + ''.join(f'{i:2}' for i in range(self.size))
        lines.append(header)

        for row in range(self.size):
            line = f'{row:2}' + ''.join(f' {symbols[self.board[row][col]]}' for col in range(self.size))
            lines.append(line)

        return '\n'.join(lines)

    def __str__(self) -> str:
        """
        字符串表示

        Returns:
            str: 棋盘信息
        """
        return f"GameBoard({self.size}x{self.size}, moves: {len(self.move_history)})"

    def __repr__(self) -> str:
        """
        详细字符串表示

        Returns:
            str: 详细信息
        """
        return (f"GameBoard(size={self.size}, moves={len(self.move_history)}, "
                f"last_move={self.last_move})")


# 工具函数
def create_empty_board(size: int = BOARD_SIZE) -> GameBoard:
    """
    创建空棋盘

    Args:
        size: 棋盘大小

    Returns:
        GameBoard: 新的空棋盘
    """
    return GameBoard(size)


def is_valid_board_size(size: int) -> bool:
    """
    验证棋盘大小是否有效

    Args:
        size: 棋盘大小

    Returns:
        bool: 大小是否有效
    """
    return 5 <= size <= 25  # 合理的棋盘大小范围

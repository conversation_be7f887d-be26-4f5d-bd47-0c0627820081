# 五子棋游戏

## 项目简介

这是一个使用Python tkinter开发的五子棋游戏，支持人机对战，具有图形界面和智能AI对手。

## 功能特性

### 🎮 游戏功能
- **人机对战**: 支持玩家与AI对战
- **图形界面**: 使用tkinter创建美观的游戏界面
- **智能AI**: 实现防守优先和攻击策略的AI算法
- **悔棋功能**: 支持撤销上一步操作
- **游戏重置**: 随时重新开始游戏

### 🏗️ 技术特性
- **面向对象设计**: 清晰的模块分离和类设计
- **MVC架构**: 分离游戏逻辑、界面和控制器
- **可扩展性**: 易于添加新功能和改进AI算法

## 项目结构

```
五子棋游戏/
├── main.py              # 主程序入口
├── game/                # 游戏逻辑模块
│   ├── __init__.py
│   ├── board.py         # 棋盘类
│   ├── player.py        # 玩家类
│   ├── ai.py           # AI算法类
│   └── game_logic.py   # 游戏逻辑类
├── gui/                 # 图形界面模块
│   ├── __init__.py
│   ├── game_window.py  # 主窗口类
│   └── components.py   # GUI组件类
├── utils/               # 工具模块
│   ├── __init__.py
│   └── constants.py    # 常量定义
├── README.md           # 项目说明
└── requirements.txt    # 依赖包
```

## 安装和运行

### 1. 环境要求
- Python 3.6+
- tkinter（通常随Python安装）

### 2. 安装依赖
```bash
cd 五子棋游戏
pip install -r requirements.txt
```

### 3. 运行游戏
```bash
python main.py
```

## 游戏规则

1. **目标**: 在15x15的棋盘上，率先形成五子连珠（横、竖、斜任意方向）的玩家获胜
2. **落子**: 黑棋先行，双方轮流落子
3. **胜负**: 形成五子连珠即获胜，棋盘下满为平局

## 游戏模式

### 🤖 人机对战
- 与智能AI对手进行游戏
- 三种AI难度级别：简单、中等、困难
- AI使用Minimax算法和Alpha-Beta剪枝优化

### 👥 人人对战
- 两个人类玩家轮流对战
- 支持悔棋功能
- 实时显示当前玩家和游戏统计

### 🤖🤖 AI对战
- 观看两个AI对手自动对战
- 可设置不同难度的AI进行对战
- 适合学习AI策略和算法演示

## 操作说明

### 基本操作
- **落子**: 点击棋盘交叉点进行落子
- **悔棋**: 点击"悔棋"按钮撤销上一步
- **重置**: 点击"重置"按钮重新开始当前游戏
- **新游戏**: 点击"新游戏"按钮开始新的游戏

### 快速开始
- **人机对战**: 点击右侧"人机对战"按钮快速开始
- **人人对战**: 点击右侧"人人对战"按钮快速开始
- **AI对战**: 点击右侧"AI对战"按钮观看AI对战

### 游戏设置
- 点击"设置"按钮可以调整AI难度和游戏模式
- 支持运行时切换设置并重新开始游戏

## 开发说明

### 代码规范
- 使用snake_case命名方法和变量
- 使用PascalCase命名类
- 提供详细的中文注释
- 遵循PEP 8代码风格

### 模块说明
- **game模块**: 包含游戏核心逻辑，独立于界面
  - `board.py`: 棋盘状态管理和胜负判断
  - `player.py`: 玩家抽象和人类/AI玩家实现
  - `ai.py`: AI算法实现，包含Minimax和评估函数
  - `game_logic.py`: 游戏流程控制和状态管理
- **gui模块**: 负责图形界面显示和用户交互
  - `game_window.py`: 主游戏窗口和事件处理
  - `components.py`: GUI基础组件（棋盘、按钮、状态栏等）
- **utils模块**: 提供常量定义和工具函数
  - `constants.py`: 游戏常量、配置参数和枚举值

### 技术架构
- **设计模式**: MVC架构、工厂模式、观察者模式
- **AI算法**: Minimax搜索 + Alpha-Beta剪枝
- **界面框架**: Python tkinter标准库
- **多线程**: AI计算使用后台线程，避免界面阻塞

### 性能特性
- **智能搜索**: AI使用候选位置筛选减少搜索空间
- **缓存优化**: 位置评估结果缓存提高效率
- **响应式界面**: 多线程处理确保界面流畅
- **内存管理**: 合理的对象生命周期管理

## 故障排除

### 常见问题

**Q: 游戏无法启动，提示模块导入错误**
A: 确保在项目根目录（包含main.py的目录）运行游戏：
```bash
cd 五子棋游戏
python main.py
```

**Q: tkinter模块不存在**
A: 在某些Linux发行版中需要单独安装tkinter：
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
```

**Q: AI思考时间过长**
A: 可以在设置中调整AI难度为"简单"，或者修改`utils/constants.py`中的`AI_THINK_TIME`参数。

**Q: 界面显示异常或字体问题**
A: 这通常是系统字体配置问题，程序会自动使用系统默认字体。

### 调试模式
如果遇到问题，可以在命令行中运行游戏查看详细错误信息：
```bash
python main.py
```

## 扩展开发

### 添加新功能
1. **新的AI算法**: 在`game/ai.py`中实现新的评估函数
2. **界面主题**: 在`utils/constants.py`中修改颜色配置
3. **游戏模式**: 在`game/game_logic.py`中添加新的游戏模式
4. **统计功能**: 扩展`game/player.py`中的统计系统

### 代码贡献
1. 遵循现有的代码风格和命名规范
2. 添加详细的中文注释
3. 确保新功能有对应的测试
4. 更新相关文档

## 许可证

本项目仅供学习和娱乐使用。

## 作者

AI Assistant - 2025年

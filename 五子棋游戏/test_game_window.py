#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主游戏窗口测试脚本
验证GameWindow类的各项功能
"""

import sys
import os
import tkinter as tk
import threading
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.game_window import GameWindow, create_game_window
from game.game_logic import GameState
from utils.constants import MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI, AI_EASY


class GameWindowTester:
    """游戏窗口测试器"""
    
    def __init__(self):
        self.test_results = []
        
    def run_tests(self):
        """运行所有测试"""
        print("🚀 开始主游戏窗口功能测试...")
        print("=" * 50)
        
        try:
            self.test_window_creation()
            self.test_game_initialization()
            self.test_ui_components()
            self.test_game_modes()
            self.test_event_handling()
            
            print("=" * 50)
            print("🎉 所有主窗口测试通过！")
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_window_creation(self):
        """测试窗口创建"""
        print("🧪 测试窗口创建...")
        
        # 创建游戏窗口
        game_window = create_game_window()
        assert game_window is not None, "游戏窗口应成功创建"
        assert isinstance(game_window, GameWindow), "应创建GameWindow实例"
        
        # 检查主要组件
        assert game_window.root is not None, "主窗口应存在"
        assert game_window.board_canvas is not None, "棋盘画布应存在"
        assert game_window.game_controls is not None, "游戏控制面板应存在"
        assert game_window.status_bar is not None, "状态栏应存在"
        assert game_window.player_info is not None, "玩家信息面板应存在"
        
        # 关闭窗口
        game_window.root.destroy()
        
        print("✅ 窗口创建测试通过")
    
    def test_game_initialization(self):
        """测试游戏初始化"""
        print("🧪 测试游戏初始化...")
        
        game_window = create_game_window()
        
        # 检查游戏逻辑初始化
        assert game_window.game_logic is not None, "游戏逻辑应初始化"
        assert game_window.game_logic.game_state == GameState.NOT_STARTED, "初始状态应为未开始"
        
        # 检查默认设置
        game_info = game_window.game_logic.get_game_info()
        assert game_info['game_mode'] == MODE_HUMAN_VS_AI, "默认模式应为人机对战"
        
        game_window.root.destroy()
        
        print("✅ 游戏初始化测试通过")
    
    def test_ui_components(self):
        """测试UI组件"""
        print("🧪 测试UI组件...")
        
        game_window = create_game_window()
        
        # 测试棋盘组件
        assert game_window.board_canvas.size == 15, "棋盘大小应为15x15"
        assert game_window.board_canvas.get_canvas() is not None, "棋盘画布应存在"
        
        # 测试控制面板
        assert game_window.game_controls.get_frame() is not None, "控制面板框架应存在"
        
        # 测试状态栏
        assert game_window.status_bar.get_frame() is not None, "状态栏框架应存在"
        
        # 测试玩家信息
        assert game_window.player_info.get_frame() is not None, "玩家信息框架应存在"
        
        game_window.root.destroy()
        
        print("✅ UI组件测试通过")
    
    def test_game_modes(self):
        """测试游戏模式"""
        print("🧪 测试游戏模式...")
        
        game_window = create_game_window()
        
        # 测试人机对战模式
        game_window.quick_start_game(MODE_HUMAN_VS_AI)
        assert game_window.game_logic.game_state == GameState.ONGOING, "游戏应开始"
        assert len(game_window.game_logic.players) == 2, "应有两个玩家"
        
        # 重置游戏
        game_window.game_logic.reset_game()
        
        # 测试人人对战模式
        game_window.quick_start_game(MODE_HUMAN_VS_HUMAN)
        assert game_window.game_logic.game_state == GameState.ONGOING, "游戏应开始"
        
        # 重置游戏
        game_window.game_logic.reset_game()
        
        # 测试AI对战模式
        game_window.quick_start_game(MODE_AI_VS_AI)
        assert game_window.game_logic.game_state == GameState.ONGOING, "游戏应开始"
        
        game_window.root.destroy()
        
        print("✅ 游戏模式测试通过")
    
    def test_event_handling(self):
        """测试事件处理"""
        print("🧪 测试事件处理...")
        
        game_window = create_game_window()
        
        # 开始人人对战游戏
        game_window.quick_start_game(MODE_HUMAN_VS_HUMAN)
        
        # 模拟棋盘点击
        initial_move_count = game_window.game_logic.get_game_info()['move_count']
        
        # 模拟点击事件
        game_window.on_board_click(7, 7)
        
        # 检查是否成功落子
        new_move_count = game_window.game_logic.get_game_info()['move_count']
        assert new_move_count == initial_move_count + 1, "应成功落子"
        
        # 测试悔棋功能
        if game_window.game_logic.can_undo():
            game_window.on_undo()
            undo_move_count = game_window.game_logic.get_game_info()['move_count']
            assert undo_move_count == initial_move_count, "悔棋应成功"
        
        game_window.root.destroy()
        
        print("✅ 事件处理测试通过")
    
    def test_ai_integration(self):
        """测试AI集成（简单测试）"""
        print("🧪 测试AI集成...")
        
        game_window = create_game_window()
        
        # 开始人机对战
        game_window.quick_start_game(MODE_HUMAN_VS_AI)
        
        # 人类玩家落子
        game_window.on_board_click(7, 7)
        
        # 等待AI思考（短暂延迟）
        time.sleep(0.1)
        
        # 检查游戏状态
        game_info = game_window.game_logic.get_game_info()
        assert game_info['move_count'] >= 1, "应至少有一步落子"
        
        game_window.root.destroy()
        
        print("✅ AI集成测试通过")


def run_automated_tests():
    """运行自动化测试"""
    tester = GameWindowTester()
    return tester.run_tests()


def run_interactive_test():
    """运行交互式测试"""
    print("🎮 启动交互式游戏窗口测试...")
    print("请手动测试以下功能：")
    print("1. 点击棋盘落子")
    print("2. 尝试不同游戏模式")
    print("3. 使用悔棋和重置功能")
    print("4. 测试设置对话框")
    print("5. 关闭窗口")
    print("-" * 30)
    
    try:
        game_window = create_game_window()
        game_window.run()
        print("✅ 交互式测试完成")
        return True
    except Exception as e:
        print(f"❌ 交互式测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("选择测试模式：")
    print("1. 自动化测试")
    print("2. 交互式测试")
    print("3. 两者都运行")
    
    try:
        choice = input("请输入选择 (1/2/3): ").strip()
        
        if choice == "1":
            return run_automated_tests()
        elif choice == "2":
            return run_interactive_test()
        elif choice == "3":
            success1 = run_automated_tests()
            if success1:
                print("\n" + "="*50)
                print("自动化测试完成，现在启动交互式测试...")
                success2 = run_interactive_test()
                return success1 and success2
            return False
        else:
            print("无效选择，运行自动化测试...")
            return run_automated_tests()
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        return False
    except Exception as e:
        print(f"测试运行出错: {e}")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 所有测试完成！")
    else:
        print("\n❌ 测试失败！")
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
五子棋游戏主程序
功能：
1. 人机对战五子棋游戏
2. 图形界面操作
3. 智能AI对手
4. 悔棋和重置功能

作者：AI Assistant
创建时间：2025年
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.game_window import create_game_window


def main():
    """主函数"""
    try:
        print("🎮 五子棋游戏启动中...")

        # 创建并启动游戏窗口
        game_window = create_game_window()
        print("✅ 游戏窗口创建成功")

        # 运行游戏
        game_window.run()

        print("👋 游戏结束，感谢游玩！")

    except KeyboardInterrupt:
        print("\n⚠️ 游戏被用户中断")
    except Exception as e:
        print(f"\n❌ 游戏运行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

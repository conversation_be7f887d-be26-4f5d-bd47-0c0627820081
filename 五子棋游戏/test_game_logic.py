#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏逻辑控制器测试脚本
验证GameLogic类的各项功能
"""

import sys
import os
import time

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.game_logic import (
    GameLogic, GameState, create_game_logic,
    get_available_game_modes, get_game_mode_description, validate_game_setup
)
from utils.constants import (
    MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI,
    BLACK_PIECE, WHITE_PIECE, AI_EASY, AI_MEDIUM,
    GAME_ONGOING, GAME_BLACK_WIN
)


def test_game_logic_creation():
    """测试游戏逻辑创建"""
    print("🧪 测试游戏逻辑创建...")
    
    # 测试创建
    game = create_game_logic()
    assert isinstance(game, GameLogic), "应创建GameLogic实例"
    assert game.game_state == GameState.NOT_STARTED, "初始状态应为未开始"
    assert len(game.players) == 0, "初始玩家列表应为空"
    assert game.game_result == GAME_ONGOING, "初始游戏结果应为进行中"
    
    print("✅ 游戏逻辑创建测试通过")


def test_game_setup():
    """测试游戏设置"""
    print("🧪 测试游戏设置...")
    
    game = create_game_logic()
    
    # 测试人机对战设置
    success = game.setup_game(MODE_HUMAN_VS_AI, "玩家", "电脑", AI_EASY, True)
    assert success, "人机对战设置应成功"
    assert len(game.players) == 2, "应有两个玩家"
    assert game.players[0].piece_type == BLACK_PIECE, "玩家1应为黑棋"
    assert game.players[1].piece_type == WHITE_PIECE, "玩家2应为白棋"
    
    # 测试人人对战设置
    success = game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    assert success, "人人对战设置应成功"
    assert len(game.players) == 2, "应有两个玩家"
    
    # 测试AI对战设置
    success = game.setup_game(MODE_AI_VS_AI, "AI1", "AI2", AI_MEDIUM)
    assert success, "AI对战设置应成功"
    assert len(game.players) == 2, "应有两个玩家"
    
    print("✅ 游戏设置测试通过")


def test_game_flow():
    """测试游戏流程"""
    print("🧪 测试游戏流程...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    
    # 测试开始游戏
    success = game.start_game()
    assert success, "开始游戏应成功"
    assert game.game_state == GameState.ONGOING, "游戏状态应为进行中"
    assert game.current_player_index == 0, "应从第一个玩家开始"
    
    # 测试人类玩家落子
    current_player = game.get_current_player()
    assert current_player is not None, "应有当前玩家"
    
    # 设置人类玩家的落子
    success = game.set_human_move(7, 7)
    assert success, "设置人类落子应成功"
    
    # 执行落子
    success = game.make_move(7, 7)
    assert success, "落子应成功"
    assert len(game.move_history) == 1, "移动历史应有一条记录"
    assert game.current_player_index == 1, "应切换到下一个玩家"
    
    print("✅ 游戏流程测试通过")


def test_move_validation():
    """测试落子验证"""
    print("🧪 测试落子验证...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    game.start_game()
    
    # 测试有效落子
    assert game.can_make_move(7, 7), "中心位置应可落子"
    
    # 测试无效位置
    assert not game.can_make_move(-1, 0), "边界外位置不应可落子"
    assert not game.can_make_move(15, 15), "边界外位置不应可落子"
    
    # 执行一步落子
    game.set_human_move(7, 7)
    game.make_move(7, 7)
    
    # 测试重复位置
    assert not game.can_make_move(7, 7), "已占用位置不应可落子"
    
    print("✅ 落子验证测试通过")


def test_undo_functionality():
    """测试悔棋功能"""
    print("🧪 测试悔棋功能...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    game.start_game()
    
    # 初始状态不能悔棋
    assert not game.can_undo(), "初始状态不应能悔棋"
    
    # 执行几步落子
    moves = [(7, 7), (8, 8), (7, 8)]
    for i, (row, col) in enumerate(moves):
        game.set_human_move(row, col)
        game.make_move(row, col)
    
    assert len(game.move_history) == 3, "应有3步移动历史"
    assert game.can_undo(), "应能悔棋"
    
    # 测试悔棋
    success = game.undo_last_move()
    assert success, "悔棋应成功"
    assert len(game.move_history) == 2, "移动历史应减少"
    assert game.board.is_empty_position(7, 8), "悔棋位置应为空"
    
    print("✅ 悔棋功能测试通过")


def test_ai_integration():
    """测试AI集成"""
    print("🧪 测试AI集成...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_AI, "玩家", "电脑", AI_EASY, True)
    game.start_game()
    
    # 人类玩家先手
    assert game.is_current_player_human(), "当前应为人类玩家"
    
    # 人类落子
    game.set_human_move(7, 7)
    game.make_move(7, 7)
    
    # 现在应该是AI的回合
    assert game.is_current_player_ai(), "当前应为AI玩家"
    
    # 处理AI落子
    success = game.process_ai_move()
    assert success, "AI落子应成功"
    assert len(game.move_history) == 2, "应有两步移动历史"
    
    print("✅ AI集成测试通过")


def test_game_end_detection():
    """测试游戏结束检测"""
    print("🧪 测试游戏结束检测...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    game.start_game()
    
    # 设置一个获胜局面（黑棋水平五子）
    black_moves = [(7, 7), (7, 9), (7, 11)]
    white_moves = [(8, 7), (8, 8)]
    
    for i in range(3):
        # 黑棋落子
        game.set_human_move(black_moves[i][0], black_moves[i][1])
        game.make_move(black_moves[i][0], black_moves[i][1])
        
        if i < 2:  # 白棋落子（除了最后一轮）
            game.set_human_move(white_moves[i][0], white_moves[i][1])
            game.make_move(white_moves[i][0], white_moves[i][1])
    
    # 再下两步让黑棋获胜
    game.set_human_move(7, 13)
    game.make_move(7, 13)
    
    game.set_human_move(8, 9)  # 白棋随意落子
    game.make_move(8, 9)
    
    game.set_human_move(7, 15)  # 黑棋完成五子连珠
    game.make_move(7, 15)
    
    # 检查游戏是否结束
    assert game.game_state == GameState.FINISHED, "游戏应已结束"
    assert game.game_result == GAME_BLACK_WIN, "黑棋应获胜"
    assert game.winner is not None, "应有获胜者"
    
    print("✅ 游戏结束检测测试通过")


def test_game_statistics():
    """测试游戏统计"""
    print("🧪 测试游戏统计...")
    
    game = create_game_logic()
    game.setup_game(MODE_HUMAN_VS_HUMAN, "玩家1", "玩家2")
    game.start_game()
    
    # 执行几步落子
    moves = [(7, 7), (8, 8), (7, 8), (8, 7)]
    for row, col in moves:
        game.set_human_move(row, col)
        game.make_move(row, col)
    
    # 获取游戏信息
    info = game.get_game_info()
    assert 'game_state' in info, "游戏信息应包含状态"
    assert 'move_count' in info, "游戏信息应包含移动数"
    assert info['move_count'] == 4, "移动数应正确"
    
    # 获取统计信息
    stats = game.get_game_statistics()
    assert 'total_moves' in stats, "统计应包含总移动数"
    assert stats['total_moves'] == 4, "总移动数应正确"
    
    print("✅ 游戏统计测试通过")


def test_utility_functions():
    """测试工具函数"""
    print("🧪 测试工具函数...")
    
    # 测试游戏模式
    modes = get_available_game_modes()
    assert MODE_HUMAN_VS_AI in modes, "应包含人机对战模式"
    assert MODE_HUMAN_VS_HUMAN in modes, "应包含人人对战模式"
    assert MODE_AI_VS_AI in modes, "应包含AI对战模式"
    
    # 测试模式描述
    for mode in modes:
        description = get_game_mode_description(mode)
        assert isinstance(description, str), "描述应为字符串"
        assert len(description) > 0, "描述不应为空"
    
    # 测试设置验证
    assert validate_game_setup(MODE_HUMAN_VS_AI, AI_EASY), "有效设置应通过验证"
    assert not validate_game_setup("invalid_mode"), "无效模式应验证失败"
    
    print("✅ 工具函数测试通过")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始游戏逻辑控制器功能测试...")
    print("=" * 50)
    
    try:
        test_game_logic_creation()
        test_game_setup()
        test_game_flow()
        test_move_validation()
        test_undo_functionality()
        test_ai_integration()
        test_game_end_detection()
        test_game_statistics()
        test_utility_functions()
        
        print("=" * 50)
        print("🎉 所有游戏逻辑测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()

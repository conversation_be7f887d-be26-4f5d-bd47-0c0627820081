#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件模块
实现GUI基础组件，包括棋盘绘制、棋子绘制、按钮组件等

包含：
- BoardCanvas类：棋盘画布组件
- GameControls类：游戏控制面板
- StatusBar类：状态栏组件
- PlayerInfo类：玩家信息显示
- 棋盘绘制和棋子绘制功能
- 鼠标交互和事件处理
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
from typing import Callable, Optional, Tuple, Dict, Any
import math
import sys
import os

# 添加父目录到路径以导入utils模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.constants import (
    BOARD_SIZE, CELL_SIZE, BOARD_MARGIN, PIECE_RADIUS, LINE_WIDTH,
    BOARD_COLOR, LINE_COLOR, BORDER_COLOR,
    BLACK_COLOR, WHITE_COLOR, BLACK_BORDER, WHITE_BORDER,
    LAST_MOVE_COLOR, HOVER_COLOR, WINNING_LINE_COLOR,
    BACKGROUND_COLOR, BUTTON_COLOR, BUTTON_ACTIVE_COLOR, TEXT_COLOR,
    BUTTON_WIDTH, BUTTON_HEIGHT, BUTTON_PADDING,
    BLACK_PIECE, WHITE_PIECE, EMPTY_CELL,
    BTN_NEW_GAME, BTN_UNDO, BTN_RESET, BTN_SETTINGS, BTN_EXIT,
    AI_EASY, AI_MEDIUM, AI_HARD
)


class BoardCanvas:
    """
    棋盘画布组件

    负责绘制棋盘、棋子和相关的视觉效果
    """

    def __init__(self, parent, size: int = BOARD_SIZE, cell_size: int = CELL_SIZE):
        """
        初始化棋盘画布

        Args:
            parent: 父容器
            size: 棋盘大小
            cell_size: 格子大小
        """
        self.parent = parent
        self.size = size
        self.cell_size = cell_size
        self.margin = BOARD_MARGIN

        # 计算画布大小
        self.canvas_size = self.size * self.cell_size + 2 * self.margin

        # 创建画布
        self.canvas = tk.Canvas(
            parent,
            width=self.canvas_size,
            height=self.canvas_size,
            bg=BOARD_COLOR,
            highlightthickness=2,
            highlightbackground=BORDER_COLOR
        )

        # 存储绘制对象
        self.pieces: Dict[Tuple[int, int], int] = {}  # 棋子对象ID
        self.grid_lines: list = []  # 网格线ID
        self.last_move_highlight: Optional[int] = None  # 最后一步高亮
        self.hover_highlight: Optional[int] = None  # 鼠标悬停高亮

        # 事件回调
        self.on_click: Optional[Callable] = None
        self.on_hover: Optional[Callable] = None

        # 初始化绘制
        self.draw_board()
        self.setup_events()

    def draw_board(self) -> None:
        """绘制棋盘网格"""
        # 清除现有网格线
        for line_id in self.grid_lines:
            self.canvas.delete(line_id)
        self.grid_lines.clear()

        # 绘制网格线
        for i in range(self.size):
            # 计算线条位置
            pos = self.margin + i * self.cell_size

            # 绘制垂直线
            line_id = self.canvas.create_line(
                pos, self.margin,
                pos, self.canvas_size - self.margin,
                fill=LINE_COLOR,
                width=LINE_WIDTH
            )
            self.grid_lines.append(line_id)

            # 绘制水平线
            line_id = self.canvas.create_line(
                self.margin, pos,
                self.canvas_size - self.margin, pos,
                fill=LINE_COLOR,
                width=LINE_WIDTH
            )
            self.grid_lines.append(line_id)

        # 绘制天元点（中心点和四个角点）
        self._draw_star_points()

    def _draw_star_points(self) -> None:
        """绘制天元点"""
        center = self.size // 2
        radius = 3

        # 中心天元点
        self._draw_star_point(center, center, radius)

        # 四个角的天元点（如果棋盘足够大）
        if self.size >= 15:
            offset = 3
            positions = [
                (offset, offset),
                (offset, self.size - 1 - offset),
                (self.size - 1 - offset, offset),
                (self.size - 1 - offset, self.size - 1 - offset)
            ]

            for row, col in positions:
                self._draw_star_point(row, col, radius)

    def _draw_star_point(self, row: int, col: int, radius: int) -> None:
        """绘制单个天元点"""
        x, y = self._get_pixel_position(row, col)
        self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            fill=LINE_COLOR,
            outline=LINE_COLOR
        )

    def draw_piece(self, row: int, col: int, piece_type: int) -> bool:
        """
        绘制棋子

        Args:
            row: 行坐标
            col: 列坐标
            piece_type: 棋子类型

        Returns:
            bool: 绘制是否成功
        """
        if not self._is_valid_position(row, col):
            return False

        if (row, col) in self.pieces:
            return False  # 位置已有棋子

        x, y = self._get_pixel_position(row, col)

        # 选择颜色
        if piece_type == BLACK_PIECE:
            fill_color = BLACK_COLOR
            outline_color = BLACK_BORDER
        elif piece_type == WHITE_PIECE:
            fill_color = WHITE_COLOR
            outline_color = WHITE_BORDER
        else:
            return False

        # 绘制棋子
        piece_id = self.canvas.create_oval(
            x - PIECE_RADIUS, y - PIECE_RADIUS,
            x + PIECE_RADIUS, y + PIECE_RADIUS,
            fill=fill_color,
            outline=outline_color,
            width=2
        )

        # 添加立体效果
        if piece_type == BLACK_PIECE:
            # 黑棋添加高光
            highlight_id = self.canvas.create_oval(
                x - PIECE_RADIUS + 3, y - PIECE_RADIUS + 3,
                x - PIECE_RADIUS + 7, y - PIECE_RADIUS + 7,
                fill='#333333',
                outline='#333333'
            )
        else:
            # 白棋添加阴影
            shadow_id = self.canvas.create_oval(
                x - PIECE_RADIUS + 2, y - PIECE_RADIUS + 2,
                x + PIECE_RADIUS, y + PIECE_RADIUS,
                fill='#DDDDDD',
                outline='#DDDDDD'
            )

        self.pieces[(row, col)] = piece_id
        return True

    def remove_piece(self, row: int, col: int) -> bool:
        """
        移除棋子

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 移除是否成功
        """
        if (row, col) not in self.pieces:
            return False

        piece_id = self.pieces.pop((row, col))
        self.canvas.delete(piece_id)
        return True

    def clear_board(self) -> None:
        """清空所有棋子"""
        # 删除所有棋子
        for piece_id in self.pieces.values():
            self.canvas.delete(piece_id)
        self.pieces.clear()

        # 清除所有高亮
        self.clear_highlights()

        # 强制刷新画布显示
        self.canvas.update_idletasks()
        self.canvas.update()

    def highlight_last_move(self, row: int, col: int) -> None:
        """
        高亮最后一步

        Args:
            row: 行坐标
            col: 列坐标
        """
        # 清除之前的高亮
        if self.last_move_highlight:
            self.canvas.delete(self.last_move_highlight)

        if not self._is_valid_position(row, col):
            return

        x, y = self._get_pixel_position(row, col)

        # 绘制高亮圆圈
        self.last_move_highlight = self.canvas.create_oval(
            x - PIECE_RADIUS - 3, y - PIECE_RADIUS - 3,
            x + PIECE_RADIUS + 3, y + PIECE_RADIUS + 3,
            outline=LAST_MOVE_COLOR,
            width=3,
            fill=''
        )

    def show_hover_effect(self, row: int, col: int) -> None:
        """
        显示鼠标悬停效果

        Args:
            row: 行坐标
            col: 列坐标
        """
        # 清除之前的悬停效果
        if self.hover_highlight:
            self.canvas.delete(self.hover_highlight)
            self.hover_highlight = None

        if not self._is_valid_position(row, col) or (row, col) in self.pieces:
            return

        x, y = self._get_pixel_position(row, col)

        # 绘制悬停提示
        self.hover_highlight = self.canvas.create_oval(
            x - PIECE_RADIUS // 2, y - PIECE_RADIUS // 2,
            x + PIECE_RADIUS // 2, y + PIECE_RADIUS // 2,
            outline=HOVER_COLOR,
            width=2,
            fill=HOVER_COLOR
        )

    def clear_hover_effect(self) -> None:
        """清除悬停效果"""
        if self.hover_highlight:
            self.canvas.delete(self.hover_highlight)
            self.hover_highlight = None

    def clear_highlights(self) -> None:
        """清除所有高亮效果"""
        if self.last_move_highlight:
            self.canvas.delete(self.last_move_highlight)
            self.last_move_highlight = None
        self.clear_hover_effect()

    def get_position_from_click(self, x: int, y: int) -> Optional[Tuple[int, int]]:
        """
        将鼠标点击坐标转换为棋盘位置

        Args:
            x: 鼠标X坐标
            y: 鼠标Y坐标

        Returns:
            Optional[Tuple[int, int]]: 棋盘位置，如果无效则返回None
        """
        # 转换为棋盘坐标
        col = round((x - self.margin) / self.cell_size)
        row = round((y - self.margin) / self.cell_size)

        if self._is_valid_position(row, col):
            return (row, col)
        return None

    def setup_events(self) -> None:
        """设置事件绑定"""
        self.canvas.bind('<Button-1>', self._on_canvas_click)
        self.canvas.bind('<Motion>', self._on_canvas_motion)
        self.canvas.bind('<Leave>', self._on_canvas_leave)

    def set_click_callback(self, callback: Callable) -> None:
        """设置点击回调"""
        self.on_click = callback

    def set_hover_callback(self, callback: Callable) -> None:
        """设置悬停回调"""
        self.on_hover = callback

    def _on_canvas_click(self, event) -> None:
        """处理画布点击事件"""
        position = self.get_position_from_click(event.x, event.y)
        if position and self.on_click:
            self.on_click(position[0], position[1])

    def _on_canvas_motion(self, event) -> None:
        """处理鼠标移动事件"""
        position = self.get_position_from_click(event.x, event.y)
        if position:
            self.show_hover_effect(position[0], position[1])
            if self.on_hover:
                self.on_hover(position[0], position[1])
        else:
            self.clear_hover_effect()

    def _on_canvas_leave(self, event) -> None:
        """处理鼠标离开事件"""
        self.clear_hover_effect()

    def _get_pixel_position(self, row: int, col: int) -> Tuple[int, int]:
        """
        获取棋盘位置对应的像素坐标

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            Tuple[int, int]: 像素坐标 (x, y)
        """
        x = self.margin + col * self.cell_size
        y = self.margin + row * self.cell_size
        return (x, y)

    def _is_valid_position(self, row: int, col: int) -> bool:
        """
        检查位置是否有效

        Args:
            row: 行坐标
            col: 列坐标

        Returns:
            bool: 位置是否有效
        """
        return 0 <= row < self.size and 0 <= col < self.size

    def resize(self, new_cell_size: int) -> None:
        """
        调整棋盘大小

        Args:
            new_cell_size: 新的格子大小
        """
        self.cell_size = new_cell_size
        self.canvas_size = self.size * self.cell_size + 2 * self.margin

        self.canvas.config(width=self.canvas_size, height=self.canvas_size)
        self.clear_board()
        self.draw_board()

    def get_canvas(self) -> tk.Canvas:
        """获取画布对象"""
        return self.canvas


class GameControls:
    """
    游戏控制面板组件

    包含游戏控制按钮和设置选项
    """

    def __init__(self, parent):
        """
        初始化游戏控制面板

        Args:
            parent: 父容器
        """
        self.parent = parent

        # 创建主框架
        self.frame = tk.Frame(parent, bg=BACKGROUND_COLOR)

        # 按钮回调
        self.on_new_game: Optional[Callable] = None
        self.on_undo: Optional[Callable] = None
        self.on_reset: Optional[Callable] = None
        self.on_settings: Optional[Callable] = None
        self.on_exit: Optional[Callable] = None

        # 创建控件
        self.create_buttons()
        self.create_settings()

    def create_buttons(self) -> None:
        """创建控制按钮"""
        # 按钮样式配置
        button_style = {
            'width': BUTTON_WIDTH,
            'height': BUTTON_HEIGHT,
            'bg': BUTTON_COLOR,
            'fg': TEXT_COLOR,
            'font': ('Arial', 10, 'bold'),
            'relief': 'raised',
            'bd': 2
        }

        # 创建按钮框架
        button_frame = tk.Frame(self.frame, bg=BACKGROUND_COLOR)
        button_frame.pack(fill=tk.X, padx=BUTTON_PADDING, pady=BUTTON_PADDING)

        # 新游戏按钮
        self.new_game_btn = tk.Button(
            button_frame,
            text=BTN_NEW_GAME,
            command=self._on_new_game_click,
            **button_style
        )
        self.new_game_btn.pack(fill=tk.X, pady=2)

        # 悔棋按钮
        self.undo_btn = tk.Button(
            button_frame,
            text=BTN_UNDO,
            command=self._on_undo_click,
            **button_style
        )
        self.undo_btn.pack(fill=tk.X, pady=2)

        # 重置按钮
        self.reset_btn = tk.Button(
            button_frame,
            text=BTN_RESET,
            command=self._on_reset_click,
            **button_style
        )
        self.reset_btn.pack(fill=tk.X, pady=2)

        # 设置按钮
        self.settings_btn = tk.Button(
            button_frame,
            text=BTN_SETTINGS,
            command=self._on_settings_click,
            **button_style
        )
        self.settings_btn.pack(fill=tk.X, pady=2)

        # 退出按钮
        self.exit_btn = tk.Button(
            button_frame,
            text=BTN_EXIT,
            command=self._on_exit_click,
            **button_style
        )
        self.exit_btn.pack(fill=tk.X, pady=2)

    def create_settings(self) -> None:
        """创建设置面板"""
        # 设置框架
        settings_frame = tk.LabelFrame(
            self.frame,
            text="游戏设置",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 10, 'bold')
        )
        settings_frame.pack(fill=tk.X, padx=BUTTON_PADDING, pady=BUTTON_PADDING)

        # AI难度设置
        tk.Label(
            settings_frame,
            text="AI难度:",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 9)
        ).pack(anchor=tk.W, padx=5, pady=2)

        self.difficulty_var = tk.StringVar(value=AI_EASY)
        self.difficulty_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.difficulty_var,
            values=[AI_EASY, AI_MEDIUM, AI_HARD],
            state='readonly',
            width=15
        )
        self.difficulty_combo.pack(fill=tk.X, padx=5, pady=2)

        # 游戏模式设置
        tk.Label(
            settings_frame,
            text="游戏模式:",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 9)
        ).pack(anchor=tk.W, padx=5, pady=2)

        self.mode_var = tk.StringVar(value="人机对战")
        self.mode_combo = ttk.Combobox(
            settings_frame,
            textvariable=self.mode_var,
            values=["人机对战", "人人对战", "AI对战"],
            state='readonly',
            width=15
        )
        self.mode_combo.pack(fill=tk.X, padx=5, pady=2)

    def set_button_callbacks(self, new_game=None, undo=None, reset=None,
                           settings=None, exit_game=None) -> None:
        """设置按钮回调函数"""
        if new_game:
            self.on_new_game = new_game
        if undo:
            self.on_undo = undo
        if reset:
            self.on_reset = reset
        if settings:
            self.on_settings = settings
        if exit_game:
            self.on_exit = exit_game

    def set_button_state(self, button_name: str, enabled: bool) -> None:
        """
        设置按钮状态

        Args:
            button_name: 按钮名称
            enabled: 是否启用
        """
        state = tk.NORMAL if enabled else tk.DISABLED

        if button_name == 'undo':
            self.undo_btn.config(state=state)
        elif button_name == 'reset':
            self.reset_btn.config(state=state)
        elif button_name == 'new_game':
            self.new_game_btn.config(state=state)

    def get_ai_difficulty(self) -> str:
        """获取选择的AI难度"""
        return self.difficulty_var.get()

    def get_game_mode(self) -> str:
        """获取选择的游戏模式"""
        return self.mode_var.get()

    def set_ai_difficulty(self, difficulty: str) -> None:
        """设置AI难度"""
        self.difficulty_var.set(difficulty)

    def set_game_mode(self, mode: str) -> None:
        """设置游戏模式"""
        self.mode_var.set(mode)

    # 按钮事件处理
    def _on_new_game_click(self) -> None:
        if self.on_new_game:
            self.on_new_game()

    def _on_undo_click(self) -> None:
        if self.on_undo:
            self.on_undo()

    def _on_reset_click(self) -> None:
        if self.on_reset:
            self.on_reset()

    def _on_settings_click(self) -> None:
        if self.on_settings:
            self.on_settings()

    def _on_exit_click(self) -> None:
        if self.on_exit:
            self.on_exit()

    def get_frame(self) -> tk.Frame:
        """获取主框架"""
        return self.frame


class StatusBar:
    """
    状态栏组件

    显示游戏状态信息
    """

    def __init__(self, parent):
        """
        初始化状态栏

        Args:
            parent: 父容器
        """
        self.frame = tk.Frame(parent, bg=BACKGROUND_COLOR, relief=tk.SUNKEN, bd=1)

        # 状态标签
        self.status_label = tk.Label(
            self.frame,
            text="欢迎来到五子棋游戏",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 10),
            anchor=tk.W
        )
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        # 时间标签
        self.time_label = tk.Label(
            self.frame,
            text="00:00",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 10),
            anchor=tk.E
        )
        self.time_label.pack(side=tk.RIGHT, padx=5)

    def set_status(self, message: str) -> None:
        """设置状态消息"""
        self.status_label.config(text=message)

    def set_time(self, time_str: str) -> None:
        """设置时间显示"""
        self.time_label.config(text=time_str)

    def get_frame(self) -> tk.Frame:
        """获取状态栏框架"""
        return self.frame


class PlayerInfo:
    """
    玩家信息显示组件
    """

    def __init__(self, parent):
        """
        初始化玩家信息面板

        Args:
            parent: 父容器
        """
        self.frame = tk.LabelFrame(
            parent,
            text="玩家信息",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 10, 'bold')
        )

        # 当前玩家显示
        self.current_player_frame = tk.Frame(self.frame, bg=BACKGROUND_COLOR)
        self.current_player_frame.pack(fill=tk.X, padx=5, pady=5)

        tk.Label(
            self.current_player_frame,
            text="当前玩家:",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 9)
        ).pack(side=tk.LEFT)

        self.current_player_label = tk.Label(
            self.current_player_frame,
            text="玩家1 (●)",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 9, 'bold')
        )
        self.current_player_label.pack(side=tk.LEFT, padx=(10, 0))

        # 游戏统计
        self.stats_frame = tk.Frame(self.frame, bg=BACKGROUND_COLOR)
        self.stats_frame.pack(fill=tk.X, padx=5, pady=5)

        self.move_count_label = tk.Label(
            self.stats_frame,
            text="步数: 0",
            bg=BACKGROUND_COLOR,
            fg=TEXT_COLOR,
            font=('Arial', 9)
        )
        self.move_count_label.pack(anchor=tk.W)

    def set_current_player(self, player_name: str, piece_symbol: str) -> None:
        """
        设置当前玩家

        Args:
            player_name: 玩家名称
            piece_symbol: 棋子符号
        """
        self.current_player_label.config(text=f"{player_name} ({piece_symbol})")

    def set_move_count(self, count: int) -> None:
        """设置步数"""
        self.move_count_label.config(text=f"步数: {count}")

    def get_frame(self) -> tk.LabelFrame:
        """获取玩家信息框架"""
        return self.frame


# 工具函数和组件工厂方法

def create_board_canvas(parent, size: int = BOARD_SIZE, cell_size: int = CELL_SIZE) -> BoardCanvas:
    """
    创建棋盘画布

    Args:
        parent: 父容器
        size: 棋盘大小
        cell_size: 格子大小

    Returns:
        BoardCanvas: 棋盘画布实例
    """
    return BoardCanvas(parent, size, cell_size)


def create_game_controls(parent) -> GameControls:
    """
    创建游戏控制面板

    Args:
        parent: 父容器

    Returns:
        GameControls: 游戏控制面板实例
    """
    return GameControls(parent)


def create_status_bar(parent) -> StatusBar:
    """
    创建状态栏

    Args:
        parent: 父容器

    Returns:
        StatusBar: 状态栏实例
    """
    return StatusBar(parent)


def create_player_info(parent) -> PlayerInfo:
    """
    创建玩家信息面板

    Args:
        parent: 父容器

    Returns:
        PlayerInfo: 玩家信息面板实例
    """
    return PlayerInfo(parent)


def show_message_box(title: str, message: str, msg_type: str = "info") -> None:
    """
    显示消息框

    Args:
        title: 标题
        message: 消息内容
        msg_type: 消息类型 ('info', 'warning', 'error', 'question')
    """
    if msg_type == "info":
        messagebox.showinfo(title, message)
    elif msg_type == "warning":
        messagebox.showwarning(title, message)
    elif msg_type == "error":
        messagebox.showerror(title, message)
    elif msg_type == "question":
        return messagebox.askyesno(title, message)


def show_game_result_dialog(winner_name: str, game_stats: dict) -> bool:
    """
    显示游戏结果对话框

    Args:
        winner_name: 获胜者名称
        game_stats: 游戏统计信息

    Returns:
        bool: 是否选择重新开始
    """
    if winner_name:
        message = f"🎉 恭喜 {winner_name} 获胜！\n\n"
    else:
        message = "🤝 游戏平局！\n\n"

    message += f"总步数: {game_stats.get('total_moves', 0)}\n"
    message += f"游戏时长: {game_stats.get('game_duration', 0):.1f}秒\n\n"
    message += "是否重新开始游戏？"

    return messagebox.askyesno("游戏结束", message)


def show_settings_dialog(parent, current_difficulty: str, current_mode: str) -> Optional[dict]:
    """
    显示设置对话框

    Args:
        parent: 父窗口
        current_difficulty: 当前AI难度
        current_mode: 当前游戏模式

    Returns:
        Optional[dict]: 新的设置，如果取消则返回None
    """
    dialog = tk.Toplevel(parent)
    dialog.title("游戏设置")
    dialog.geometry("300x200")
    dialog.resizable(False, False)
    dialog.transient(parent)
    dialog.grab_set()

    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")

    result = {}

    # AI难度设置
    tk.Label(dialog, text="AI难度:", font=('Arial', 10)).pack(pady=5)
    difficulty_var = tk.StringVar(value=current_difficulty)
    difficulty_combo = ttk.Combobox(
        dialog,
        textvariable=difficulty_var,
        values=[AI_EASY, AI_MEDIUM, AI_HARD],
        state='readonly'
    )
    difficulty_combo.pack(pady=5)

    # 游戏模式设置
    tk.Label(dialog, text="游戏模式:", font=('Arial', 10)).pack(pady=5)
    mode_var = tk.StringVar(value=current_mode)
    mode_combo = ttk.Combobox(
        dialog,
        textvariable=mode_var,
        values=["人机对战", "人人对战", "AI对战"],
        state='readonly'
    )
    mode_combo.pack(pady=5)

    # 按钮框架
    button_frame = tk.Frame(dialog)
    button_frame.pack(pady=20)

    def on_ok():
        result['difficulty'] = difficulty_var.get()
        result['mode'] = mode_var.get()
        dialog.destroy()

    def on_cancel():
        dialog.destroy()

    tk.Button(button_frame, text="确定", command=on_ok, width=10).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="取消", command=on_cancel, width=10).pack(side=tk.LEFT, padx=5)

    dialog.wait_window()
    return result if result else None


def format_time(seconds: float) -> str:
    """
    格式化时间显示

    Args:
        seconds: 秒数

    Returns:
        str: 格式化的时间字符串 (MM:SS)
    """
    minutes = int(seconds // 60)
    seconds = int(seconds % 60)
    return f"{minutes:02d}:{seconds:02d}"


def get_piece_symbol(piece_type: int) -> str:
    """
    获取棋子符号

    Args:
        piece_type: 棋子类型

    Returns:
        str: 棋子符号
    """
    if piece_type == BLACK_PIECE:
        return "●"
    elif piece_type == WHITE_PIECE:
        return "○"
    else:
        return "?"


def validate_canvas_size(size: int, cell_size: int) -> bool:
    """
    验证画布大小是否合理

    Args:
        size: 棋盘大小
        cell_size: 格子大小

    Returns:
        bool: 大小是否合理
    """
    canvas_size = size * cell_size + 2 * BOARD_MARGIN
    return 200 <= canvas_size <= 1000  # 合理的画布大小范围

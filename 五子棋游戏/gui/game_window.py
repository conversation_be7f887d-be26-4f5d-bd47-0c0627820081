#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主游戏窗口模块
实现GameWindow主窗口类，整合所有GUI组件和游戏逻辑

包含：
- GameWindow类：主游戏窗口
- 完整的界面布局和组件集成
- 用户交互事件处理
- 游戏流程控制和状态管理
- AI自动对战处理
- 游戏结果显示和统计
"""

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
from typing import Optional
import sys
import os

# 添加父目录到路径以导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui.components import (
    BoardCanvas, GameControls, StatusBar, PlayerInfo,
    create_board_canvas, create_game_controls, create_status_bar, create_player_info,
    show_message_box, show_game_result_dialog, show_settings_dialog,
    format_time, get_piece_symbol
)
from game.game_logic import GameLogic, GameState, create_game_logic
from game.player import HumanPlayer, AIPlayer
from utils.constants import (
    WINDOW_TITLE, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT,
    BACKGROUND_COLOR, BOARD_SIZE, CELL_SIZE,
    MODE_HUMAN_VS_AI, MODE_HUMAN_VS_HUMAN, MODE_AI_VS_AI,
    AI_EASY, AI_MEDIUM, AI_HARD,
    BLACK_PIECE, WHITE_PIECE,
    MSG_GAME_START, MSG_AI_THINKING
)


class GameWindow:
    """
    主游戏窗口类

    整合所有GUI组件和游戏逻辑，作为整个应用的主界面控制器
    """

    def __init__(self):
        """初始化主游戏窗口"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.configure(bg=BACKGROUND_COLOR)
        self.root.resizable(False, False)

        # 游戏逻辑
        self.game_logic = create_game_logic()

        # GUI组件
        self.board_canvas: Optional[BoardCanvas] = None
        self.game_controls: Optional[GameControls] = None
        self.status_bar: Optional[StatusBar] = None
        self.player_info: Optional[PlayerInfo] = None

        # 游戏状态
        self.ai_thread: Optional[threading.Thread] = None
        self.game_timer_running = False
        self.start_time = 0.0

        # 初始化界面
        self.setup_ui()
        self.setup_events()
        self.setup_game_logic_callbacks()

        # 设置窗口属性
        self.center_window()
        self.setup_window_properties()

        # 初始化游戏
        self.initialize_game()

    def setup_ui(self) -> None:
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg=BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建游戏区域框架
        game_frame = tk.Frame(main_frame, bg=BACKGROUND_COLOR)
        game_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧：棋盘区域
        board_frame = tk.Frame(game_frame, bg=BACKGROUND_COLOR)
        board_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建棋盘
        self.board_canvas = create_board_canvas(board_frame, BOARD_SIZE, CELL_SIZE)
        self.board_canvas.get_canvas().pack()

        # 右侧：控制面板
        control_frame = tk.Frame(game_frame, bg=BACKGROUND_COLOR, width=250)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(15, 0))
        control_frame.pack_propagate(False)

        # 创建控制组件
        self.create_control_panel(control_frame)

        # 底部：状态栏
        self.status_bar = create_status_bar(self.root)
        self.status_bar.get_frame().pack(side=tk.BOTTOM, fill=tk.X)

    def create_control_panel(self, parent) -> None:
        """创建控制面板"""
        # 玩家信息面板
        self.player_info = create_player_info(parent)
        self.player_info.get_frame().pack(fill=tk.X, pady=(0, 10))

        # 游戏控制面板
        self.game_controls = create_game_controls(parent)
        self.game_controls.get_frame().pack(fill=tk.X, pady=(0, 10))

        # 创建游戏模式选择面板
        self.create_game_mode_panel(parent)

        # 创建统计信息面板
        self.create_stats_panel(parent)

    def create_game_mode_panel(self, parent) -> None:
        """创建游戏模式选择面板"""
        mode_frame = tk.LabelFrame(
            parent,
            text="快速开始",
            bg=BACKGROUND_COLOR,
            fg='black',
            font=('Arial', 10, 'bold')
        )
        mode_frame.pack(fill=tk.X, pady=(0, 10))

        # 游戏模式按钮
        tk.Button(
            mode_frame,
            text="人机对战",
            command=lambda: self.quick_start_game(MODE_HUMAN_VS_AI),
            bg='#E6E6FA',
            font=('Arial', 9),
            width=20
        ).pack(fill=tk.X, padx=5, pady=2)

        tk.Button(
            mode_frame,
            text="人人对战",
            command=lambda: self.quick_start_game(MODE_HUMAN_VS_HUMAN),
            bg='#E6E6FA',
            font=('Arial', 9),
            width=20
        ).pack(fill=tk.X, padx=5, pady=2)

        tk.Button(
            mode_frame,
            text="AI对战",
            command=lambda: self.quick_start_game(MODE_AI_VS_AI),
            bg='#E6E6FA',
            font=('Arial', 9),
            width=20
        ).pack(fill=tk.X, padx=5, pady=2)

    def create_stats_panel(self, parent) -> None:
        """创建统计信息面板"""
        self.stats_frame = tk.LabelFrame(
            parent,
            text="游戏统计",
            bg=BACKGROUND_COLOR,
            fg='black',
            font=('Arial', 10, 'bold')
        )
        self.stats_frame.pack(fill=tk.X, pady=(0, 10))

        # 游戏时间
        self.time_label = tk.Label(
            self.stats_frame,
            text="游戏时间: 00:00",
            bg=BACKGROUND_COLOR,
            font=('Arial', 9)
        )
        self.time_label.pack(anchor=tk.W, padx=5, pady=2)

        # 游戏模式
        self.mode_label = tk.Label(
            self.stats_frame,
            text="模式: 未开始",
            bg=BACKGROUND_COLOR,
            font=('Arial', 9)
        )
        self.mode_label.pack(anchor=tk.W, padx=5, pady=2)

    def setup_events(self) -> None:
        """设置事件绑定"""
        # 棋盘点击事件
        self.board_canvas.set_click_callback(self.on_board_click)

        # 游戏控制按钮事件
        self.game_controls.set_button_callbacks(
            new_game=self.on_new_game,
            undo=self.on_undo,
            reset=self.on_reset,
            settings=self.on_settings,
            exit_game=self.on_exit
        )

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

    def setup_game_logic_callbacks(self) -> None:
        """设置游戏逻辑回调"""
        self.game_logic.set_move_callback(self.on_move_made)
        self.game_logic.set_game_end_callback(self.on_game_end)
        self.game_logic.set_player_change_callback(self.on_player_change)
        self.game_logic.set_game_state_change_callback(self.on_game_state_change)

    def setup_window_properties(self) -> None:
        """设置窗口属性"""
        # 设置最小尺寸
        self.root.minsize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)

        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap('icon.ico')  # 可以添加图标文件
            pass
        except:
            pass

    def center_window(self) -> None:
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"+{x}+{y}")

    def initialize_game(self) -> None:
        """初始化游戏"""
        # 设置默认游戏模式
        self.game_logic.setup_game(MODE_HUMAN_VS_AI, "玩家", "电脑", AI_EASY, True)

        # 更新界面
        self.update_display()
        self.status_bar.set_status("欢迎来到五子棋游戏！选择游戏模式开始游戏。")

    # 事件处理方法

    def on_board_click(self, row: int, col: int) -> None:
        """处理棋盘点击事件"""
        if self.game_logic.game_state != GameState.ONGOING:
            return

        # 检查是否可以落子
        if not self.game_logic.can_make_move(row, col):
            return

        # 检查当前玩家是否为人类
        if not self.game_logic.is_current_player_human():
            return

        # 设置人类玩家的落子
        if self.game_logic.set_human_move(row, col):
            # 执行落子
            if self.game_logic.make_move(row, col):
                # 如果游戏继续且下一个玩家是AI，启动AI思考
                if (self.game_logic.game_state == GameState.ONGOING and
                    self.game_logic.is_current_player_ai()):
                    self.start_ai_thinking()

    def on_new_game(self) -> None:
        """处理新游戏按钮"""
        if self.game_logic.game_state == GameState.ONGOING:
            if not messagebox.askyesno("确认", "当前游戏正在进行中，确定要开始新游戏吗？"):
                return

        # 显示设置对话框
        current_difficulty = self.game_controls.get_ai_difficulty()
        current_mode = self.game_controls.get_game_mode()

        settings = show_settings_dialog(self.root, current_difficulty, current_mode)
        if settings:
            self.start_new_game_with_settings(settings)

    def on_undo(self) -> None:
        """处理悔棋按钮"""
        if self.game_logic.can_undo():
            if self.game_logic.undo_last_move():
                self.update_display()
                self.status_bar.set_status("已悔棋")

    def on_reset(self) -> None:
        """处理重置按钮"""
        if messagebox.askyesno("确认", "确定要重置游戏吗？"):
            # 停止计时器
            self.stop_game_timer()

            # 重置游戏逻辑
            self.game_logic.reset_game()

            # 清空棋盘显示
            self.board_canvas.clear_board()

            # 更新界面显示
            self.update_display()

            # 更新状态
            self.status_bar.set_status("游戏已重置")

    def on_settings(self) -> None:
        """处理设置按钮"""
        current_difficulty = self.game_controls.get_ai_difficulty()
        current_mode = self.game_controls.get_game_mode()

        settings = show_settings_dialog(self.root, current_difficulty, current_mode)
        if settings:
            # 更新设置
            self.game_controls.set_ai_difficulty(settings['difficulty'])
            self.game_controls.set_game_mode(settings['mode'])

            # 如果游戏正在进行，询问是否应用新设置
            if self.game_logic.game_state == GameState.ONGOING:
                if messagebox.askyesno("应用设置", "是否立即应用新设置并重新开始游戏？"):
                    self.start_new_game_with_settings(settings)
            else:
                # 应用AI难度设置
                self.game_logic.set_ai_difficulty(settings['difficulty'])

    def on_exit(self) -> None:
        """处理退出按钮"""
        self.on_window_close()

    def on_window_close(self) -> None:
        """处理窗口关闭事件"""
        if self.game_logic.game_state == GameState.ONGOING:
            if not messagebox.askyesno("确认退出", "游戏正在进行中，确定要退出吗？"):
                return

        # 停止AI线程
        if self.ai_thread and self.ai_thread.is_alive():
            # 这里可以添加线程停止逻辑
            pass

        self.stop_game_timer()
        self.root.quit()
        self.root.destroy()

    # 游戏逻辑回调方法

    def on_move_made(self, row: int, col: int, player, board_state) -> None:
        """处理落子事件"""
        # 在棋盘上绘制棋子
        self.board_canvas.draw_piece(row, col, player.piece_type)

        # 高亮最后一步
        self.board_canvas.highlight_last_move(row, col)

        # 更新显示
        self.update_display()

        # 更新状态
        self.status_bar.set_status(f"{player.name} 在 ({row+1}, {col+1}) 落子")

    def on_game_end(self, game_result: int, winner, game_stats: dict) -> None:
        """处理游戏结束事件"""
        self.stop_game_timer()

        # 显示游戏结果
        winner_name = winner.name if winner else None
        if show_game_result_dialog(winner_name, game_stats):
            # 用户选择重新开始 - 确保棋盘被清空
            self.board_canvas.clear_board()
            self.quick_start_game(self.game_logic.game_mode)

        self.update_display()

    def on_player_change(self, current_player, player_index: int) -> None:
        """处理玩家切换事件"""
        self.update_display()

        if current_player:
            piece_symbol = get_piece_symbol(current_player.piece_type)
            self.status_bar.set_status(f"轮到 {current_player.name} ({piece_symbol}) 落子")

            # 如果是AI玩家，启动AI思考
            if isinstance(current_player, AIPlayer):
                self.start_ai_thinking()

    def on_game_state_change(self, old_state: str, new_state: str) -> None:
        """处理游戏状态改变事件"""
        if new_state == GameState.ONGOING.value:
            self.start_game_timer()
        elif new_state == GameState.FINISHED.value:
            self.stop_game_timer()

        self.update_display()

    # AI处理方法

    def start_ai_thinking(self) -> None:
        """启动AI思考"""
        if self.ai_thread and self.ai_thread.is_alive():
            return  # AI已在思考中

        self.status_bar.set_status(MSG_AI_THINKING)

        # 在新线程中运行AI
        self.ai_thread = threading.Thread(target=self.ai_thinking_worker, daemon=True)
        self.ai_thread.start()

    def ai_thinking_worker(self) -> None:
        """AI思考工作线程"""
        try:
            # 处理AI落子
            success = self.game_logic.process_ai_move()

            # 在主线程中更新界面
            self.root.after(0, self.on_ai_move_complete, success)

        except Exception as e:
            print(f"AI思考出错: {e}")
            self.root.after(0, self.on_ai_move_complete, False)

    def on_ai_move_complete(self, success: bool) -> None:
        """AI落子完成回调"""
        if not success:
            self.status_bar.set_status("AI落子失败")

        # 如果游戏继续且下一个玩家也是AI，继续AI思考
        if (self.game_logic.game_state == GameState.ONGOING and
            self.game_logic.is_current_player_ai()):
            # 短暂延迟后继续下一个AI的思考
            self.root.after(500, self.start_ai_thinking)

    # 游戏控制方法

    def quick_start_game(self, mode: str) -> None:
        """快速开始游戏"""
        # 停止当前游戏和计时器
        if self.game_logic.game_state == GameState.ONGOING:
            self.game_logic.reset_game()
        self.stop_game_timer()

        # 立即清空棋盘（在设置游戏之前）
        self.board_canvas.clear_board()

        # 设置游戏模式
        difficulty = self.game_controls.get_ai_difficulty()

        # 根据模式设置玩家
        if mode == MODE_HUMAN_VS_AI:
            success = self.game_logic.setup_game(mode, "玩家", "电脑", difficulty, True)
        elif mode == MODE_HUMAN_VS_HUMAN:
            success = self.game_logic.setup_game(mode, "玩家1", "玩家2")
        elif mode == MODE_AI_VS_AI:
            success = self.game_logic.setup_game(mode, "电脑1", "电脑2", difficulty)
        else:
            success = False

        if success:
            # 再次确保棋盘已清空
            self.board_canvas.clear_board()

            # 开始游戏
            if self.game_logic.start_game():
                self.update_display()
                self.status_bar.set_status("游戏开始！")

                # 如果第一个玩家是AI，启动AI思考
                if self.game_logic.is_current_player_ai():
                    self.start_ai_thinking()
            else:
                show_message_box("错误", "游戏启动失败", "error")
        else:
            show_message_box("错误", "游戏设置失败", "error")

    def start_new_game_with_settings(self, settings: dict) -> None:
        """使用新设置开始游戏"""
        # 转换模式名称
        mode_map = {
            "人机对战": MODE_HUMAN_VS_AI,
            "人人对战": MODE_HUMAN_VS_HUMAN,
            "AI对战": MODE_AI_VS_AI
        }

        mode = mode_map.get(settings['mode'], MODE_HUMAN_VS_AI)
        difficulty = settings['difficulty']

        # 立即清空棋盘
        self.board_canvas.clear_board()

        # 更新控件设置
        self.game_controls.set_ai_difficulty(difficulty)
        self.game_controls.set_game_mode(settings['mode'])

        # 开始新游戏
        self.quick_start_game(mode)

    # 界面更新方法

    def update_display(self) -> None:
        """更新界面显示"""
        # 更新玩家信息
        current_player = self.game_logic.get_current_player()
        if current_player:
            piece_symbol = get_piece_symbol(current_player.piece_type)
            self.player_info.set_current_player(current_player.name, piece_symbol)

        # 更新步数
        game_info = self.game_logic.get_game_info()
        self.player_info.set_move_count(game_info['move_count'])

        # 更新按钮状态
        self.update_button_states()

        # 更新统计信息
        self.update_stats_display()

    def update_button_states(self) -> None:
        """更新按钮状态"""
        # 悔棋按钮
        self.game_controls.set_button_state('undo', self.game_logic.can_undo())

        # 重置按钮
        can_reset = self.game_logic.game_state in [GameState.ONGOING, GameState.FINISHED]
        self.game_controls.set_button_state('reset', can_reset)

    def update_stats_display(self) -> None:
        """更新统计信息显示"""
        game_info = self.game_logic.get_game_info()

        # 更新游戏时间
        if 'game_duration' in game_info:
            time_str = format_time(game_info['game_duration'])
            self.time_label.config(text=f"游戏时间: {time_str}")

        # 更新游戏模式
        mode_text = {
            MODE_HUMAN_VS_AI: "人机对战",
            MODE_HUMAN_VS_HUMAN: "人人对战",
            MODE_AI_VS_AI: "AI对战"
        }.get(game_info['game_mode'], "未知模式")

        self.mode_label.config(text=f"模式: {mode_text}")

    # 计时器方法

    def start_game_timer(self) -> None:
        """启动游戏计时器"""
        self.game_timer_running = True
        self.start_time = time.time()
        self.update_timer()

    def stop_game_timer(self) -> None:
        """停止游戏计时器"""
        self.game_timer_running = False

    def update_timer(self) -> None:
        """更新计时器显示"""
        if self.game_timer_running:
            elapsed = time.time() - self.start_time
            time_str = format_time(elapsed)
            self.status_bar.set_time(time_str)

            # 每秒更新一次
            self.root.after(1000, self.update_timer)

    # 公共方法

    def run(self) -> None:
        """启动游戏窗口"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("游戏被用户中断")
        except Exception as e:
            print(f"游戏运行出错: {e}")
            import traceback
            traceback.print_exc()

    def get_root(self) -> tk.Tk:
        """获取根窗口"""
        return self.root

    def show_about_dialog(self) -> None:
        """显示关于对话框"""
        about_text = """五子棋游戏 v1.0

一个使用Python tkinter开发的五子棋游戏

功能特性：
• 人机对战、人人对战、AI对战
• 三种AI难度级别
• 悔棋和重置功能
• 游戏统计和计时

开发：AI Assistant
年份：2025"""

        messagebox.showinfo("关于五子棋游戏", about_text)


# 工具函数

def create_game_window() -> GameWindow:
    """
    创建游戏窗口实例

    Returns:
        GameWindow: 游戏窗口实例
    """
    return GameWindow()


def main() -> None:
    """主函数"""
    try:
        # 创建并运行游戏窗口
        game_window = create_game_window()
        game_window.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
图形用户界面模块
包含游戏窗口和GUI组件
"""

# 导出主要类
from .game_window import GameWindow, create_game_window
from .components import (
    BoardCanvas, GameControls, StatusBar, PlayerInfo,
    create_board_canvas, create_game_controls, create_status_bar, create_player_info,
    show_message_box, show_game_result_dialog, show_settings_dialog,
    format_time, get_piece_symbol, validate_canvas_size
)

__all__ = [
    # 主要组件类
    'BoardCanvas',
    'GameControls',
    'StatusBar',
    'PlayerInfo',

    # 组件工厂方法
    'create_board_canvas',
    'create_game_controls',
    'create_status_bar',
    'create_player_info',

    # 工具函数
    'show_message_box',
    'show_game_result_dialog',
    'show_settings_dialog',
    'format_time',
    'get_piece_symbol',
    'validate_canvas_size',

    # 主窗口
    'GameWindow',
    'create_game_window'
]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
玩家类测试脚本
验证Player、HumanPlayer、AIPlayer类的各项功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.player import (
    HumanPlayer, AIPlayer, create_human_player, create_ai_player,
    create_player_pair, create_human_vs_human, create_ai_vs_ai,
    get_opponent_piece_type, is_valid_piece_type, get_piece_type_name
)
from game.board import GameBoard
from utils.constants import BLACK_PIECE, WHITE_PIECE, AI_EASY, AI_MEDIUM, AI_HARD


def test_player_creation():
    """测试玩家创建"""
    print("🧪 测试玩家创建...")
    
    # 测试人类玩家创建
    human = create_human_player("测试玩家", BLACK_PIECE)
    assert human.name == "测试玩家", "玩家名称应正确"
    assert human.piece_type == BLACK_PIECE, "棋子类型应正确"
    assert human.wins == 0, "初始胜利数应为0"
    assert not human.has_move_ready(), "初始状态不应有可用落子"
    
    # 测试AI玩家创建
    ai = create_ai_player("测试AI", WHITE_PIECE, AI_MEDIUM)
    assert ai.name == "测试AI", "AI名称应正确"
    assert ai.piece_type == WHITE_PIECE, "AI棋子类型应正确"
    assert ai.difficulty == AI_MEDIUM, "AI难度应正确"
    assert ai.get_difficulty_name() == "中等", "难度名称应正确"
    
    print("✅ 玩家创建测试通过")


def test_human_player_moves():
    """测试人类玩家落子"""
    print("🧪 测试人类玩家落子...")
    
    human = create_human_player("测试玩家", BLACK_PIECE)
    board = GameBoard()
    
    # 初始状态没有落子
    assert human.get_move(board) is None, "初始状态应无落子"
    
    # 设置落子
    assert human.set_move(7, 7), "设置落子应成功"
    assert human.has_move_ready(), "设置后应有可用落子"
    
    # 获取落子
    move = human.get_move(board)
    assert move == (7, 7), "获取的落子应正确"
    assert not human.has_move_ready(), "获取后应清除落子状态"
    
    # 测试无效落子
    assert not human.set_move(-1, -1), "无效坐标应设置失败"
    
    print("✅ 人类玩家落子测试通过")


def test_ai_player_moves():
    """测试AI玩家落子"""
    print("🧪 测试AI玩家落子...")
    
    ai = create_ai_player("测试AI", WHITE_PIECE, AI_EASY)
    board = GameBoard()
    
    # AI应该能够选择落子
    move = ai.get_move(board)
    assert move is not None, "AI应能选择落子"
    assert isinstance(move, tuple), "落子应为元组"
    assert len(move) == 2, "落子应有两个坐标"
    
    row, col = move
    assert board.is_valid_move(row, col), "AI选择的位置应有效"
    
    # 测试难度设置
    assert ai.set_difficulty(AI_HARD), "设置难度应成功"
    assert ai.difficulty == AI_HARD, "难度应更新"
    assert ai.get_difficulty_name() == "困难", "难度名称应更新"
    
    print("✅ AI玩家落子测试通过")


def test_player_statistics():
    """测试玩家统计功能"""
    print("🧪 测试玩家统计功能...")
    
    player = create_human_player("测试玩家", BLACK_PIECE)
    
    # 初始统计
    assert player.get_total_games() == 0, "初始游戏数应为0"
    assert player.get_win_rate() == 0.0, "初始胜率应为0"
    
    # 添加统计数据
    player.add_win()
    player.add_win()
    player.add_loss()
    player.add_draw()
    
    assert player.wins == 2, "胜利数应正确"
    assert player.losses == 1, "失败数应正确"
    assert player.draws == 1, "平局数应正确"
    assert player.get_total_games() == 4, "总游戏数应正确"
    assert player.get_win_rate() == 0.5, "胜率应正确"
    
    # 重置统计
    player.reset_stats()
    assert player.wins == 0, "重置后胜利数应为0"
    assert player.get_total_games() == 0, "重置后总游戏数应为0"
    
    print("✅ 玩家统计功能测试通过")


def test_player_factory_methods():
    """测试玩家工厂方法"""
    print("🧪 测试玩家工厂方法...")
    
    # 测试人机对战创建
    human, ai = create_player_pair("玩家", "电脑", AI_MEDIUM, True)
    assert human.piece_type == BLACK_PIECE, "人类先手应为黑棋"
    assert ai.piece_type == WHITE_PIECE, "AI后手应为白棋"
    
    # 测试AI先手
    ai, human = create_player_pair("玩家", "电脑", AI_MEDIUM, False)
    assert ai.piece_type == BLACK_PIECE, "AI先手应为黑棋"
    assert human.piece_type == WHITE_PIECE, "人类后手应为白棋"
    
    # 测试人人对战
    p1, p2 = create_human_vs_human("玩家1", "玩家2")
    assert p1.piece_type == BLACK_PIECE, "玩家1应为黑棋"
    assert p2.piece_type == WHITE_PIECE, "玩家2应为白棋"
    
    # 测试AI对战
    ai1, ai2 = create_ai_vs_ai("AI1", "AI2", AI_EASY, AI_HARD)
    assert ai1.piece_type == BLACK_PIECE, "AI1应为黑棋"
    assert ai2.piece_type == WHITE_PIECE, "AI2应为白棋"
    assert ai1.difficulty == AI_EASY, "AI1难度应正确"
    assert ai2.difficulty == AI_HARD, "AI2难度应正确"
    
    print("✅ 玩家工厂方法测试通过")


def test_utility_functions():
    """测试工具函数"""
    print("🧪 测试工具函数...")
    
    # 测试对手棋子类型
    assert get_opponent_piece_type(BLACK_PIECE) == WHITE_PIECE, "黑棋对手应为白棋"
    assert get_opponent_piece_type(WHITE_PIECE) == BLACK_PIECE, "白棋对手应为黑棋"
    
    # 测试棋子类型验证
    assert is_valid_piece_type(BLACK_PIECE), "黑棋应有效"
    assert is_valid_piece_type(WHITE_PIECE), "白棋应有效"
    assert not is_valid_piece_type(99), "无效类型应无效"
    
    # 测试棋子名称
    assert get_piece_type_name(BLACK_PIECE) == "黑棋", "黑棋名称应正确"
    assert get_piece_type_name(WHITE_PIECE) == "白棋", "白棋名称应正确"
    assert get_piece_type_name(99) == "未知", "无效类型名称应为未知"
    
    print("✅ 工具函数测试通过")


def test_player_representation():
    """测试玩家字符串表示"""
    print("🧪 测试玩家字符串表示...")
    
    human = create_human_player("测试玩家", BLACK_PIECE)
    ai = create_ai_player("测试AI", WHITE_PIECE, AI_MEDIUM)
    
    # 测试字符串表示
    human_str = str(human)
    assert "测试玩家" in human_str, "字符串应包含玩家名"
    assert "黑棋" in human_str, "字符串应包含棋子类型"
    
    ai_str = str(ai)
    assert "测试AI" in ai_str, "字符串应包含AI名"
    assert "白棋" in ai_str, "字符串应包含棋子类型"
    
    # 测试棋子符号
    assert human.get_piece_symbol() == "●", "黑棋符号应正确"
    assert ai.get_piece_symbol() == "○", "白棋符号应正确"
    
    print("✅ 玩家字符串表示测试通过")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始玩家类功能测试...")
    print("=" * 50)
    
    try:
        test_player_creation()
        test_human_player_moves()
        test_ai_player_moves()
        test_player_statistics()
        test_player_factory_methods()
        test_utility_functions()
        test_player_representation()
        
        print("=" * 50)
        print("🎉 所有玩家测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()

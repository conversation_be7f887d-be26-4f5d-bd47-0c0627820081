#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
棋盘类测试脚本
验证GameBoard类的各项功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from game.board import GameBoard
from utils.constants import BLACK_PIECE, WHITE_PIECE, EMPTY_CELL, GAME_BLACK_WIN, GAME_ONGOING


def test_board_initialization():
    """测试棋盘初始化"""
    print("🧪 测试棋盘初始化...")
    
    board = GameBoard()
    assert board.size == 15, "默认棋盘大小应为15"
    assert board.get_move_count() == 0, "初始棋子数应为0"
    assert board.get_last_move() is None, "初始最后一步应为None"
    assert not board.is_board_full(), "初始棋盘不应满"
    
    # 检查所有位置都为空
    for row in range(board.size):
        for col in range(board.size):
            assert board.get_piece(row, col) == EMPTY_CELL, f"位置({row},{col})应为空"
    
    print("✅ 棋盘初始化测试通过")


def test_move_operations():
    """测试落子操作"""
    print("🧪 测试落子操作...")
    
    board = GameBoard()
    
    # 测试有效落子
    assert board.make_move(7, 7, BLACK_PIECE), "中心位置落子应成功"
    assert board.get_piece(7, 7) == BLACK_PIECE, "落子后位置应有黑棋"
    assert board.get_move_count() == 1, "落子后棋子数应为1"
    assert board.get_last_move() == (7, 7), "最后一步应为(7,7)"
    
    # 测试重复落子
    assert not board.make_move(7, 7, WHITE_PIECE), "重复位置落子应失败"
    
    # 测试边界外落子
    assert not board.make_move(-1, 0, WHITE_PIECE), "边界外落子应失败"
    assert not board.make_move(15, 15, WHITE_PIECE), "边界外落子应失败"
    
    # 测试无效玩家类型
    assert not board.make_move(8, 8, 99), "无效玩家类型应失败"
    
    print("✅ 落子操作测试通过")


def test_undo_operations():
    """测试悔棋操作"""
    print("🧪 测试悔棋操作...")
    
    board = GameBoard()
    
    # 空棋盘悔棋应失败
    assert not board.undo_move(), "空棋盘悔棋应失败"
    
    # 落子后悔棋
    board.make_move(7, 7, BLACK_PIECE)
    board.make_move(8, 8, WHITE_PIECE)
    
    assert board.undo_move(), "悔棋应成功"
    assert board.get_piece(8, 8) == EMPTY_CELL, "悔棋后位置应为空"
    assert board.get_move_count() == 1, "悔棋后棋子数应减1"
    assert board.get_last_move() == (7, 7), "悔棋后最后一步应更新"
    
    # 再次悔棋
    assert board.undo_move(), "再次悔棋应成功"
    assert board.get_piece(7, 7) == EMPTY_CELL, "悔棋后位置应为空"
    assert board.get_move_count() == 0, "悔棋后棋子数应为0"
    assert board.get_last_move() is None, "悔棋后最后一步应为None"
    
    print("✅ 悔棋操作测试通过")


def test_win_detection():
    """测试胜负判断"""
    print("🧪 测试胜负判断...")
    
    board = GameBoard()
    
    # 测试水平获胜
    for col in range(5):
        board.make_move(7, 7 + col, BLACK_PIECE)
    
    result = board.check_winner(7, 11)  # 检查最后一步
    assert result == GAME_BLACK_WIN, "水平五子连珠应获胜"
    
    # 重置棋盘测试垂直获胜
    board.reset()
    for row in range(5):
        board.make_move(7 + row, 7, WHITE_PIECE)
    
    result = board.check_winner(11, 7)  # 检查最后一步
    assert result != GAME_ONGOING, "垂直五子连珠应获胜"
    
    print("✅ 胜负判断测试通过")


def test_board_utilities():
    """测试棋盘工具方法"""
    print("🧪 测试棋盘工具方法...")
    
    board = GameBoard()
    
    # 测试空位置获取
    empty_positions = board.get_empty_positions()
    assert len(empty_positions) == 15 * 15, "空棋盘应有225个空位置"
    
    # 落几个子后再测试
    board.make_move(7, 7, BLACK_PIECE)
    board.make_move(8, 8, WHITE_PIECE)
    
    empty_positions = board.get_empty_positions()
    assert len(empty_positions) == 15 * 15 - 2, "落子后空位置应减少"
    
    # 测试棋盘复制
    board_copy = board.copy_board()
    assert board_copy[7][7] == BLACK_PIECE, "复制的棋盘应保持状态"
    assert board_copy[8][8] == WHITE_PIECE, "复制的棋盘应保持状态"
    
    # 测试字符串表示
    board_str = str(board)
    assert "GameBoard" in board_str, "字符串表示应包含类名"
    assert "15x15" in board_str, "字符串表示应包含大小"
    
    print("✅ 棋盘工具方法测试通过")


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始棋盘类功能测试...")
    print("=" * 50)
    
    try:
        test_board_initialization()
        test_move_operations()
        test_undo_operations()
        test_win_detection()
        test_board_utilities()
        
        print("=" * 50)
        print("🎉 所有棋盘测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_all_tests()

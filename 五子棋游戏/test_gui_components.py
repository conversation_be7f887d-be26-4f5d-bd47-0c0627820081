#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI组件测试脚本
验证GUI组件的各项功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.components import (
    BoardCanvas, GameControls, StatusBar, PlayerInfo,
    create_board_canvas, create_game_controls, create_status_bar, create_player_info,
    show_message_box, format_time, get_piece_symbol, validate_canvas_size
)
from utils.constants import BLACK_PIECE, WHITE_PIECE, AI_EASY, AI_MEDIUM


class GUIComponentTester:
    """GUI组件测试器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("GUI组件测试")
        self.root.geometry("800x600")
        
        self.setup_test_interface()
        
    def setup_test_interface(self):
        """设置测试界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：棋盘测试
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 右侧：控制面板测试
        right_frame = tk.Frame(main_frame, width=250)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.pack_propagate(False)
        
        # 创建棋盘组件
        self.board_canvas = create_board_canvas(left_frame, size=15, cell_size=25)
        self.board_canvas.get_canvas().pack()
        
        # 设置棋盘事件
        self.board_canvas.set_click_callback(self.on_board_click)
        
        # 创建控制面板
        self.game_controls = create_game_controls(right_frame)
        self.game_controls.get_frame().pack(fill=tk.X, pady=5)
        
        # 设置控制面板回调
        self.game_controls.set_button_callbacks(
            new_game=self.on_new_game,
            undo=self.on_undo,
            reset=self.on_reset,
            settings=self.on_settings,
            exit_game=self.on_exit
        )
        
        # 创建玩家信息面板
        self.player_info = create_player_info(right_frame)
        self.player_info.get_frame().pack(fill=tk.X, pady=5)
        
        # 创建状态栏
        self.status_bar = create_status_bar(self.root)
        self.status_bar.get_frame().pack(side=tk.BOTTOM, fill=tk.X)
        
        # 创建测试按钮
        self.create_test_buttons(right_frame)
        
        # 初始化测试状态
        self.current_player = BLACK_PIECE
        self.move_count = 0
        self.update_display()
    
    def create_test_buttons(self, parent):
        """创建测试按钮"""
        test_frame = tk.LabelFrame(parent, text="测试功能")
        test_frame.pack(fill=tk.X, pady=10)
        
        # 测试棋子绘制
        tk.Button(
            test_frame,
            text="测试棋子绘制",
            command=self.test_piece_drawing
        ).pack(fill=tk.X, pady=2)
        
        # 测试高亮效果
        tk.Button(
            test_frame,
            text="测试高亮效果",
            command=self.test_highlight_effects
        ).pack(fill=tk.X, pady=2)
        
        # 测试清空棋盘
        tk.Button(
            test_frame,
            text="清空棋盘",
            command=self.test_clear_board
        ).pack(fill=tk.X, pady=2)
        
        # 测试消息框
        tk.Button(
            test_frame,
            text="测试消息框",
            command=self.test_message_boxes
        ).pack(fill=tk.X, pady=2)
        
        # 测试工具函数
        tk.Button(
            test_frame,
            text="测试工具函数",
            command=self.test_utility_functions
        ).pack(fill=tk.X, pady=2)
    
    def on_board_click(self, row, col):
        """处理棋盘点击"""
        print(f"点击位置: ({row}, {col})")
        
        # 绘制棋子
        success = self.board_canvas.draw_piece(row, col, self.current_player)
        if success:
            # 高亮最后一步
            self.board_canvas.highlight_last_move(row, col)
            
            # 切换玩家
            self.current_player = WHITE_PIECE if self.current_player == BLACK_PIECE else BLACK_PIECE
            self.move_count += 1
            
            # 更新显示
            self.update_display()
            
            # 更新状态
            player_name = "黑棋" if self.current_player == BLACK_PIECE else "白棋"
            self.status_bar.set_status(f"轮到 {player_name} 落子")
    
    def update_display(self):
        """更新显示"""
        # 更新当前玩家
        player_name = "黑棋" if self.current_player == BLACK_PIECE else "白棋"
        piece_symbol = get_piece_symbol(self.current_player)
        self.player_info.set_current_player(player_name, piece_symbol)
        
        # 更新步数
        self.player_info.set_move_count(self.move_count)
        
        # 更新时间（模拟）
        self.status_bar.set_time(format_time(self.move_count * 10))
    
    def test_piece_drawing(self):
        """测试棋子绘制"""
        print("🧪 测试棋子绘制...")
        
        # 在固定位置绘制测试棋子
        test_positions = [(1, 1), (1, 2), (2, 1), (2, 2)]
        for i, (row, col) in enumerate(test_positions):
            piece_type = BLACK_PIECE if i % 2 == 0 else WHITE_PIECE
            self.board_canvas.draw_piece(row, col, piece_type)
        
        self.status_bar.set_status("已绘制测试棋子")
    
    def test_highlight_effects(self):
        """测试高亮效果"""
        print("🧪 测试高亮效果...")
        
        # 测试最后一步高亮
        self.board_canvas.highlight_last_move(7, 7)
        
        # 测试悬停效果
        self.board_canvas.show_hover_effect(8, 8)
        
        self.status_bar.set_status("已显示高亮效果")
    
    def test_clear_board(self):
        """测试清空棋盘"""
        print("🧪 测试清空棋盘...")
        
        self.board_canvas.clear_board()
        self.current_player = BLACK_PIECE
        self.move_count = 0
        self.update_display()
        
        self.status_bar.set_status("棋盘已清空")
    
    def test_message_boxes(self):
        """测试消息框"""
        print("🧪 测试消息框...")
        
        show_message_box("测试", "这是一个信息消息框", "info")
        show_message_box("警告", "这是一个警告消息框", "warning")
        
        self.status_bar.set_status("消息框测试完成")
    
    def test_utility_functions(self):
        """测试工具函数"""
        print("🧪 测试工具函数...")
        
        # 测试时间格式化
        time_str = format_time(125)  # 2:05
        print(f"格式化时间: {time_str}")
        
        # 测试棋子符号
        black_symbol = get_piece_symbol(BLACK_PIECE)
        white_symbol = get_piece_symbol(WHITE_PIECE)
        print(f"黑棋符号: {black_symbol}, 白棋符号: {white_symbol}")
        
        # 测试画布大小验证
        valid = validate_canvas_size(15, 30)
        print(f"画布大小验证: {valid}")
        
        self.status_bar.set_status("工具函数测试完成")
    
    def on_new_game(self):
        """新游戏"""
        print("🎮 新游戏")
        self.test_clear_board()
        self.status_bar.set_status("开始新游戏")
    
    def on_undo(self):
        """悔棋"""
        print("↶ 悔棋")
        self.status_bar.set_status("悔棋功能（演示）")
    
    def on_reset(self):
        """重置"""
        print("🔄 重置")
        self.test_clear_board()
        self.status_bar.set_status("游戏已重置")
    
    def on_settings(self):
        """设置"""
        print("⚙️ 设置")
        difficulty = self.game_controls.get_ai_difficulty()
        mode = self.game_controls.get_game_mode()
        print(f"当前设置 - 难度: {difficulty}, 模式: {mode}")
        self.status_bar.set_status("设置功能（演示）")
    
    def on_exit(self):
        """退出"""
        print("🚪 退出")
        if show_message_box("确认", "确定要退出吗？", "question"):
            self.root.quit()
    
    def run(self):
        """运行测试"""
        print("🚀 启动GUI组件测试...")
        self.status_bar.set_status("GUI组件测试已启动 - 点击棋盘进行测试")
        self.root.mainloop()


def run_gui_test():
    """运行GUI测试"""
    try:
        tester = GUIComponentTester()
        tester.run()
        print("✅ GUI组件测试完成")
        return True
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    run_gui_test()

# 2024.3.27 1.43am

import requests
from time import sleep
import copy

# 一个人只能为人助力3个未组队完成的队
ck = [
    {'authorization': 'bearer bb57bd0a-1c79-4214-b884-1c125ed67e9b', 'userId': '6588f5042cc5764714952609',
     'name': '陈惠民'},
    {'authorization': 'bearer 1bcd7382-f20c-4e23-8a2a-d9d0e8880573', 'userId': '659523488b5d352a24a1ba82',
     'name': '张浩锐'},
    {'authorization': 'bearer 953b2658-1871-4795-93e3-1c96a745482c', 'userId': '6588f801fd6c2e619f85d34a',
     'name': '张书诚'},
    {'authorization': 'bearer 00dcc049-2a06-4819-8fb4-31672ac6fda7', 'userId': '658939c1c50a2758bf9a6313',
     'name': '朱宇森小号'}
]
ck1 = copy.deepcopy(ck)
temp = ''
tradeAreaId = '6569501e15720000b8008864'  # 广药云浮地区代号，固定
activityId = '6601395792a9a4279200eca4'  # 活动代号，大概率随活动变
team_id = ''  # 队伍id
authorization = ''  # 个人安全认证token


def join_team(authorization, team_id, member_Id):  # 加入队伍
    headers = {
        'authorization': authorization
    }

    data = {
        "id": team_id,
        "memberId": member_Id  # 成员id
    }

    response = requests.post('https://ymixy.cloudyee.com/api/activity/teamactivity/joinTeam', headers=headers,
                             json=data)
    if response.json()['httpStatus'] == 200:
        if response.json()['message'] == 'success':
            print('助力成功')
            return 1
        else:
            print(response.json()['description'])
            print('助力失败')
            return 0
    else:
        print('请求失败')
        return 0


def createTeamActivity(authorization, userId):  # 创建队伍
    headers = {
        'authorization': authorization
    }

    data = {
        "tradeAreaId": tradeAreaId,
        "activity": {
            "activityId": activityId
        },
        "leader": {
            "userId": userId  # 队长id
        }
    }

    response = requests.post('https://ymixy.cloudyee.com/api/activity/teamactivity/createTeamActivity', headers=headers,
                             json=data)

    if response.json()['httpStatus'] == 200:
        if response.json()['message'] == 'success':
            print('创建队伍成功')
            print('队伍id: ', response.json()['data']['id'])
            return response.json()['data']['id']
        else:
            print(response.json()['description'])
            return 0
    else:
        print('请求失败')
        return 0


count = 0
num = 1

for i in range(1000):
    print(num)
    print('_______________________________________')
    for j in ck:
        print(f'当前队长: {j["name"]}')
        team_id = createTeamActivity(j['authorization'], j['userId'])
        if team_id == 0:
            print('创建队伍失败')
            temp = ck1.pop(0)
            ck1.append(temp)
            continue
        for k in ck1[1:]:
            print(f'助力人:{k["name"]}')
            is_success = join_team(k['authorization'], team_id, k['userId'])
            sleep(0.2)
            if is_success == 1:
                count += 1
                print(f'当前助力人数为{count}')
            if count == 3:
                count = 0
                print('组队完成…………')
                print('_______________________________________')
                break
        temp = ck1.pop(0)
        ck1.append(temp)
    num += 1
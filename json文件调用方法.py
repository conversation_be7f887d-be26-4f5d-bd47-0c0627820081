'''您可以使用Python标准库中的json模块读取该JSON文件并访问其中的数据。

假设您的JSON文件名为data.json，并且与您的Python可执行文件在同一目录下，您可以使用以下代码读取它：'''

import json
import os

# 获取当前脚本的绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))
# 拼接JSON文件的路径
json_path = os.path.join(script_dir, 'data.json')

# 打开JSON文件并读取数据
with open(json_path, 'r') as f:
    data = json.load(f)

# 访问JSON数据
print(data)


'''这将打开名为data.json的文件，将其读取为JSON格式，并将其存储在名为data的变量中。然后，您可以像使用任何其他Python字典一样访问JSON数据.'''

'''如果JSON文件在其他目录中，您可以使用相对路径或绝对路径来指定JSON文件的路径。以下是使用绝对路径的示例代码：'''

import json

# 指定JSON文件的绝对路径
json_path = '/path/to/data.json'

# 打开JSON文件并读取数据
with open(json_path, 'r') as f:
    data = json.load(f)

# 访问JSON数据
print(data)

'''在此示例中，我们将JSON文件的绝对路径指定为字符串'/path/to/data.json'。
您需要将路径替换为实际路径。如果您使用相对路径，请确保它是相对于您的Python脚本的路径.'''

# 修改JSON数据
data['name'] = 'New Name'

# 添加JSON数据
data['age'] = 25

# 删除JSON数据
del data['age']

# 保存JSON文件
with open('example.json', 'w') as f:
    json.dump(data, f)
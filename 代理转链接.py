import requests
import yaml
import base64
import urllib.parse
import sys

# 目标URL
URL = 'https://mxlsub.me/free'

# 定义必要的请求头
HEADERS = {
    'User-Agent': (
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
        'AppleWebKit/537.36 (KHTML, like Gecko) '
        'Chrome/130.0.0.0 Safari/537.36 Edg/130.0.0.0'
    ),
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Referer': 'https://0p0.me/'  # 可选
}


def utf8_to_base64(s):
    """将字符串转换为 UTF-8 编码后再进行 Base64 编码"""
    return base64.urlsafe_b64encode(s.encode('utf-8')).decode('utf-8').rstrip('=')


def convert_vless(proxy):
    try:
        uuid = proxy['uuid']
        server = proxy['server']
        port = proxy['port']
        tls = proxy.get('tls', False)
        network = proxy.get('network', 'tcp').lower()
        name = proxy.get('name', 'Unnamed')
        servername = proxy.get('servername', '')

        url = f"vless://{uuid}@{server}:{port}?security={'tls' if tls else 'none'}"

        if network == 'ws':
            ws_opts = proxy.get('ws-opts', {})
            path = ws_opts.get('path', '')
            host = ws_opts.get('headers', {}).get('Host', '')
            url += f"&type=ws&sni={urllib.parse.quote(servername)}&path={urllib.parse.quote(path)}&host={urllib.parse.quote(host)}"
        elif network == 'tcp':
            flow = proxy.get('flow', '')
            reality_opts = proxy.get('reality-opts', {})
            skip_cert_verify = proxy.get('skip-cert-verify', False)
            if flow:
                url += f"&flow={urllib.parse.quote(flow)}"
            if reality_opts:
                reality_params = []
                public_key = reality_opts.get('public-key')
                short_id = reality_opts.get('short-id')
                if public_key:
                    reality_params.append(f"publickey={urllib.parse.quote(public_key)}")
                if short_id:
                    reality_params.append(f"shortid={urllib.parse.quote(short_id)}")
                if reality_params:
                    url += f"&reality={'&'.join(reality_params)}"
            url += f"&type=tcp&sni={urllib.parse.quote(servername)}"

        url += f"#${urllib.parse.quote(name)}"
        return url
    except KeyError as e:
        print(f"VLESS 类型缺少必要字段: {e}", file=sys.stderr)
        return None


def convert_ss(proxy):
    try:
        cipher = proxy['cipher']
        password = proxy['password']
        server = proxy['server']
        port = proxy['port']
        udp = proxy.get('udp', False)
        name = proxy.get('name', 'Unnamed')

        method_password = f"{cipher}:{password}"
        encoded_method_password = base64.urlsafe_b64encode(method_password.encode('utf-8')).decode('utf-8').rstrip('=')
        url = f"ss://{encoded_method_password}@{server}:{port}"
        if udp:
            url += "?udp=true"
        url += f"#${urllib.parse.quote(name)}"
        return url
    except KeyError as e:
        print(f"Shadowsocks (SS) 类型缺少必要字段: {e}", file=sys.stderr)
        return None


def convert_trojan(proxy):
    try:
        password = proxy['password']
        server = proxy['server']
        port = proxy['port']
        tls = proxy.get('tls', False)
        udp = proxy.get('udp', False)
        servername = proxy.get('servername', '')
        skip_cert_verify = proxy.get('skip-cert-verify', False)
        name = proxy.get('name', 'Unnamed')

        params = []
        if servername:
            params.append(f"sni={urllib.parse.quote(servername)}")
        if skip_cert_verify:
            params.append(f"skip-cert-verify={skip_cert_verify}")
        if udp:
            params.append(f"udp={udp}")
        param_str = f"?{'&'.join(params)}" if params else ""

        url = f"trojan://{urllib.parse.quote(password)}@{server}:{port}{param_str}#${urllib.parse.quote(name)}"
        return url
    except KeyError as e:
        print(f"Trojan 类型缺少必要字段: {e}", file=sys.stderr)
        return None


def convert_vmess(proxy):
    try:
        uuid = proxy['uuid']
        server = proxy['server']
        port = proxy['port']
        alter_id = proxy['alterId']
        cipher = proxy['cipher']
        network = proxy.get('network', 'tcp').lower()
        name = proxy.get('name', 'Unnamed')
        host = proxy.get('host', '')
        path = proxy.get('path', '')
        tls = proxy.get('tls', False)

        vmess_obj = {
            "v": "2",
            "ps": name,
            "add": server,
            "port": str(port),
            "id": uuid,
            "aid": str(alter_id),
            "net": network,
            "type": "none",
            "host": host,
            "path": path,
            "tls": "tls" if tls else ""
        }

        # 移除空字段
        vmess_obj = {k: v for k, v in vmess_obj.items() if v}

        vmess_json = yaml.dump(vmess_obj, sort_keys=False)
        vmess_base64 = base64.urlsafe_b64encode(vmess_json.encode('utf-8')).decode('utf-8').rstrip('=')
        url = f"vmess://{vmess_base64}#${urllib.parse.quote(name)}"
        return url
    except KeyError as e:
        print(f"Vmess 类型缺少必要字段: {e}", file=sys.stderr)
        return None


def convert_proxy(proxy):
    proxy_type = proxy.get('type', '').lower()
    if proxy_type == 'vless':
        return convert_vless(proxy)
    elif proxy_type == 'ss':
        return convert_ss(proxy)
    elif proxy_type == 'trojan':
        return convert_trojan(proxy)
    elif proxy_type == 'vmess':
        return convert_vmess(proxy)
    else:
        print(f"不支持的代理类型: {proxy_type}", file=sys.stderr)
        return None


def main():
    try:
        response = requests.get(URL, headers=HEADERS, timeout=10)
        response.encoding = 'utf-8'
        print("最终使用的编码:", response.encoding)

        try:
            data = yaml.safe_load(response.text)
        except yaml.YAMLError as e:
            print(f"解析YAML时发生错误: {e}", file=sys.stderr)
            sys.exit(1)

        proxies = data.get('proxies')
        if not proxies:
            print("'proxies' 部分未找到。", file=sys.stderr)
            sys.exit(1)

        print("提取的 'proxies' 内容:")
        for proxy in proxies:
            print(proxy)

        converted_urls = []
        for index, proxy in enumerate(proxies, start=1):
            url = convert_proxy(proxy)
            if url:
                converted_urls.append(url)
            else:
                print(f"代理项 {index} 转换失败。", file=sys.stderr)

        if not converted_urls:
            print("没有找到可转换的代理类型（仅支持 'vless'、'ss'、'trojan' 和 'vmess'）。", file=sys.stderr)
            sys.exit(1)

        with open('response.txt', 'w', encoding='utf-8') as file:
            file.write('\n'.join(converted_urls))
        print("转换后的代理链接已成功保存到 'response.txt' 文件中。")

    except requests.exceptions.RequestException as e:
        print(f"请求过程中发生错误: {e}", file=sys.stderr)


if __name__ == "__main__":
    main()

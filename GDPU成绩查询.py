# coding=utf-8
import requests
import re
import json
proxies = {"http": f"{requests.get('http://*************:8866/get_ip?type=3').text}"}
ck = 'route=979e8ca7ca476c5c3460db7fc11ce328; JSESSIONID=11EADB476B25381C419C10214082B18B'
xnm = '2023'  # 年份，大一为2021，大二为2022，以此类推
xqm = '3'  # 3为上学期，12为下学期
url = 'http://jwsys.gdpu.edu.cn/cjcx/cjcx_cxXsgrcj.html?doType=query'
data = {'xnm': xnm,
        'xqm': xqm,
        'queryModel.showCount': '15',
        'queryModel.currentPage': '1'}
headers = {"Cookie": ck
           }
req = requests.post(url, data=data, headers=headers,proxies=proxies)
a = req.json()
s1 = 0
s2 = 0
msg = ''
jxb_id = ''
kcmc = ''
url = 'http://jwsys.gdpu.edu.cn/cjcx/cjcx_cxCjxqGjh.html?gnmkdm=N305005'
headers = {
    "Cookie": ck,
    'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'}
for i in a['items']:
    jxb_id = i['jxb_id']
    kcmc = i['kcmc']
    msg += kcmc + '\n'
    data = {'xnm': xnm,
            'xqm': xqm,
            'jxb_id': jxb_id,
            'kcmc': kcmc
            }
    req = requests.post(url, data=data, headers=headers,proxies=proxies)
    b = req.text
    result = re.findall(r'<td valign="middle">(.*?)</td>', b)
    lst = [s.replace('&nbsp;', '') for s in result]
    output = '\n'.join(' '.join(sub_lst) for sub_lst in zip(lst[::3], lst[1::3], lst[2::3]))
    msg += output + '\n'
    s1 += float(i['jd'])
    s2 += 5
s1 = round(s1, 2)
msg += '总绩点为:' + str(s1) + '\n' + '标准总绩点:' + str(s2) + '\n' + '合格率' + format(s1 / s2, '.2%') + '\n' + '平均绩点:' + format(
    s1 / s2 * 5, '.3')

url = "http://*************:8866/get_cj"
response = requests.get(url)
req = response.text
req = req.strip('"')
req = req.replace(r"\n", "\n")
if msg != req:
    url = 'http://*************:8866/change_cj'
    data = {'msg': msg}
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, data=json.dumps(data), headers=headers)
    if response.status_code == 200:
        result = response.json()
        print(result['result'])
    else:
        print('An error occurred.')
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": msg,
        "summary": '大二第一学期成绩',
        "contentType": 1,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"
                 ]
    }
    headers = {
        "Content-Type": "application/json"
    }
    req = requests.post(url, json=data, headers=headers)

print(msg)

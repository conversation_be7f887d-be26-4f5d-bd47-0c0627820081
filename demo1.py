import requests
from datetime import datetime, timedelta
import os
import time
from message_push import wxpusher

# 配置文件路径
config_file_path = '/ql/data/config/config.sh'
# 获取当前时间
current_time = datetime.now()
# 定义比较时间
compared_time_360_LibreChat = os.getenv('compared_time_360_LibreChat')
compared_time_360_NextChat = os.getenv('compared_time_360_NextChat')
compared_time_360_OpenWebUI = os.getenv('compared_time_360_OpenWebUI')

print(compared_time_360_LibreChat, compared_time_360_NextChat, compared_time_360_OpenWebUI)

compared_time_360_LibreChat = datetime.strptime(compared_time_360_LibreChat, "%Y-%m-%d %H:%M:%S.%f")
compared_time_360_NextChat = datetime.strptime(compared_time_360_NextChat, "%Y-%m-%d %H:%M:%S.%f")
compared_time_360_OpenWebUI = datetime.strptime(compared_time_360_OpenWebUI, "%Y-%m-%d %H:%M:%S.%f")

# 格式化当前时间
formatted_time = current_time.strftime('%Y/%m/%d %H:%M:%S.%f')


def generate_link(asset):
    protocol = 'https' if asset['service']['name'] == 'http/ssl' else 'http'
    host = asset.get('domain', asset.get('ip'))
    port = asset['port']

    if not host or '*' in host or ':' in host:
        return None

    if port == 80 or port == 443:
        return f"{protocol}://{host}"
    else:
        return f"{protocol}://{host}:{port}"


cookies = {
    'cert_common': '35ceb6ee-7daa-4cc0-a30a-2701b90de085'
}

headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Authorization': '233',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
}

json_data_template = {
    'latest': True,
    'ignore_cache': False,
    'shortcuts': [
        '635fcb52cc57190bd8826d09',
        '635fcbaacc57190bd8826d0b',
        '63734bfa9c27d4249ca7261c',
    ],
    'start': 0,
    'size': 100,
    'device': {
        'device_type': 'PC',
        'os': 'Windows',
        'os_version': '10.0',
        'language': 'zh_CN',
        'network': '4g',
        'browser_info': 'Chrome（版本: *********&nbsp;&nbsp;内核: Blink）',
        'fingerprint': '2af81ce4',
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'date': formatted_time,
        'UUID': '612c8c36-07c8-5957-8a28-4f042bf4a1e5',
    },
}

# 需要查询的关键字、对应的时间变量和值，以及输出文件路径
queries_and_times = {
    "LibreChat": (compared_time_360_LibreChat, 'LibreChat.txt'),
    "NextChat": (compared_time_360_NextChat, 'NextChat.txt'),
    "Open WebUI": (compared_time_360_OpenWebUI, 'OpenWebUI.txt')
}

new_compared_times = {}

for query, (compared_time, output_file_path) in queries_and_times.items():
    json_data = json_data_template.copy()
    json_data['query'] = f'"{query}"'

    links = []
    data = []

    for i in range(10):
        if i == 9:
            wxpusher(f'360爬{query}出错', f'<p style="color:red;">360爬{query}出错</p>', 1)
        response = requests.post(
            'https://quake.360.net/api/search/query_string/quake_service',
            cookies=cookies,
            headers=headers,
            json=json_data
        )
        print(f"正在进行第{i + 1}次请求 for {query}")
        if response.status_code == 200:
            if response.json()['code'] == 0:
                print(f"{query}请求成功")
                print(response.json()['meta']['pagination'])
                data = response.json()['data']
                new_compared_time = datetime.strptime(data[0]['time'], "%Y-%m-%dT%H:%M:%S.%fZ") + timedelta(hours=8)
                new_compared_times[query] = new_compared_time
                break
            else:
                print(f"{query}请求失败，状态码:", response.json()['code'])
                time.sleep(5)
        else:
            print(f"{query}请求失败，状态码:", response.status_code)

    for i in data:
        utc_time = datetime.strptime(i['time'], "%Y-%m-%dT%H:%M:%S.%fZ")
        beijing_time = utc_time + timedelta(hours=8)

        if beijing_time > compared_time:
            print(beijing_time.strftime("%Y-%m-%d %H:%M:%S.%f"))
            links.append(generate_link(i))
        else:
            break

    links = [link for link in links if link]
    if links:
        with open(output_file_path, 'a', encoding='utf-8') as f:
            f.write('\n'.join(links) + '\n')

# 读取配置文件内容
with open(config_file_path, 'r') as file:
    lines = file.readlines()

# 修改指定变量的值
with open(config_file_path, 'w') as file:
    for line in lines:
        if line.startswith('export compared_time_360_LibreChat='):
            file.write(f'export compared_time_360_LibreChat="{new_compared_times.get("LibreChat", compared_time_360_LibreChat)}"\n')
        elif line.startswith('export compared_time_360_NextChat='):
            file.write(f'export compared_time_360_NextChat="{new_compared_times.get("NextChat", compared_time_360_NextChat)}"\n')
        elif line.startswith('export compared_time_360_OpenWebUI='):
            file.write(f'export compared_time_360_OpenWebUI="{new_compared_times.get("Open WebUI", compared_time_360_OpenWebUI)}"\n')
        else:
            file.write(line)

print(f'compared_time_360_LibreChat 已更新为: {new_compared_times.get("LibreChat", compared_time_360_LibreChat)}')
print(f'compared_time_360_NextChat 已更新为: {new_compared_times.get("NextChat", compared_time_360_NextChat)}')
print(f'compared_time_360_OpenWebUI 已更新为: {new_compared_times.get("Open WebUI", compared_time_360_OpenWebUI)}')
import requests

url = 'http://*************:8866/change_cj'
msg = '666'
ip = '************:4252'
data = {'msg': msg}

# Define a dictionary with proxies for HTTP and HTTPS protocols
proxies = {
    "http": f"http://{ip}",
    "https": f"https://{ip}"
}

# Pass the proxies parameter to the requests.post method
response = requests.post(url, json=data)
response.raise_for_status()
result = response.json()

print(result['result'])

author: 若离|IceTiki|https://github.com/IceTiki/ruoli-sign-optimization
notion: 代理设置、正则匹配、获取历史签到、二维码签到等功能请查看文档 documents/用户配置高级教程.md
# YAML有引用和锚点，可以节省配置长度(备注:锚点要放在引用的前面)(可以参考https://www.runoob.com/w3cnote/yaml-intro.html)
# ======================================================================
# 全局设置
# ======================================================================
apple: "https://apple.ruoli.cc/captcha/validate" # 请在「https://apple.ruoli.cc/captcha/docs」获取图形验证码识别API
locationOffsetRange: 50 # 签到坐标随机偏移范围(单位：米)(可以为0)
maxTry: 1 # 最大尝试次数
logDir: "_log/" # 日志保存地址
delay: [ 5, 10 ] # 多用户时，各用户之间任务执行延迟(时间范围可以使用浮点数)
captcha: # 图片验证码识别(不需要可以不填)
  tencentSecretId: "" # 腾讯云OCR
  tencentSecretKey: "" # 腾讯云OCR
  captchaLen: 4 # 验证码长度
sendMessage: # 推送函数的整体运行情况(不需要的项目不用填，可以删掉或者放着不管)
  rl_emailApiUrl: http://api.ruoli.cc/mail/sendMail # 邮箱API的地址
  rl_email: "" # email 接受通知消息的邮箱
  iceCream_token: "" # iceCream(若离开发的qq推送)(https://ice.ruoli.cc)
  qmsg_key: "" # qmsg推送的key
  qmsg_qq: "" # qmsg推送的qq号
  qmsg_isGroup: 0 # 此qq号是否为群(是的话填1，反之为0)
  pushplus_parameters: "" # pushplus(https://www.pushplus.plus)参数，填入令牌(token)即可推送。也可以填入"token=xxx&topic=xxx"形式自定义更多参数
  severchan_sendkey: "" # severchan(https://sct.ftqq.com/)参数，填入令牌(SendKey)即可推送, 暂不支持自定义参数。
  gotify_url: "" # Gotify 服务器地址，Gotify 是一个可自行搭建的推送服务，可前往这里找到安装教程：https://gotify.net/docs/install
  gotify_apptoken: "" # Gotify Application Token（注意不是 Client Token）
  smtp_host: "smtp.qq.com" # SMTP服务器域名
  smtp_user: "*****@qq.com" # SMTP服务器用户名
  smtp_key: "" # SMTP服务器密钥
  smtp_sender: "*****@qq.com" # 发送邮箱
  smtp_senderName: "发送者名字" # 发送者名字(选填)
  smtp_receivers:
    - "*****@qq.com" # 接收邮箱(可填多个)
  apprise: "" # apprise统一推送模块的配置, 见https://github.com/caronc/apprise
# ======================================================================
# 用户组配置(填写自己所需的类型，并删掉不需要的。可以配置多用户(复制粘贴多配置几个就好)。)
# ======================================================================
users: #!!!!!别误删了这一行!!!!!
  - # !!!!!!!!!!配置多用户时不要遗漏这一行("- "代表数组里一项的开始)!!!!!!!!!!
    type: 1 # 任务类型[信息收集:0|签到:1|查寝:2|教师工作日志:3|政工签到:4]
    schoolName: 广东药科大学
    username: "2130506164"
    password: "JxB@Mg2G"
    proxy: "http://14.157.102.17:4215"
    # 一般需填项-------
    abnormalReason: "无" # 范围外签到原因
    photo: sign.jpg # 签到照片(不需要可不填)
    signLevel: 1 # 签到等级[0:正常签到|1:请假后依然签到|2:请假或已经打卡后依然重复签到]
    title: 0 # [str:签到任务的标题|0:取最后一个未签到的任务]
    checkTitle: 1 # 是否检查表单各项的标题[1:检查|0:不检查]
    forms: #表单信息
      - form:
          itle: 今日晨检体温
          value: 37.2以下
      - form:
          title: 今日健康状况
          value: 无不适
      - form:
          title: 明日所在地
          value: 在家
      - form:
          title: 疫苗接种情况
          value: 完成第三针，未打第四针
      - form:
          title: 是否曾感染新冠
          value: 是 #  如果存在选择题附带额外信息，请增加一个extraValue项
    # 经纬度查询地址（请自行选择自己的学校地址，address，lon，lat都要填查询到的）：http://api.map.baidu.com/lbsapi/getpoint/index.html
    lon: 112.19484
    lat: 23.034602
    address: "广东药科大学(云浮校区)"
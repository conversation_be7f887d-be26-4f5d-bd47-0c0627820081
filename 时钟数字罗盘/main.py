#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时钟数字罗盘主程序
功能：
1. 螺旋式时间显示
2. 实时时间更新
3. 中文时间格式
4. 创新的数字罗盘界面

作者：AI Assistant
创建时间：2025年
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_environment():
    """检查运行环境"""
    print("🔍 正在检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查tkinter
    try:
        import tkinter
        print("✅ tkinter模块可用")
    except ImportError:
        print("❌ tkinter模块不可用")
        print("   请安装tkinter:")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo yum install tkinter")
        return False
    
    # 检查项目模块
    try:
        from gui.clock_window import ClockWindow
        print("✅ 时钟窗口模块可用")
    except ImportError as e:
        print(f"❌ 时钟窗口模块导入失败: {e}")
        return False
    
    try:
        from gui.spiral_canvas import SpiralCanvas
        print("✅ 螺旋画布模块可用")
    except ImportError as e:
        print(f"❌ 螺旋画布模块导入失败: {e}")
        return False
    
    try:
        from clock.time_manager import TimeManager
        print("✅ 时间管理器模块可用")
    except ImportError as e:
        print(f"❌ 时间管理器模块导入失败: {e}")
        return False
    
    try:
        from clock.spiral_calculator import SpiralCalculator
        print("✅ 螺旋计算器模块可用")
    except ImportError as e:
        print(f"❌ 螺旋计算器模块导入失败: {e}")
        return False
    
    print("✅ 环境检查通过")
    return True


def print_welcome_message():
    """打印欢迎信息"""
    print("=" * 50)
    print("🕐 时钟数字罗盘")
    print("=" * 50)
    print("一个创新的螺旋式时间显示应用")
    print()
    print("功能特性：")
    print("• 🌀 螺旋式时间布局")
    print("• ⏰ 实时时间更新")
    print("• 🇨🇳 中文时间显示")
    print("• 🎨 多主题支持")
    print("• ✨ 视觉效果配置")
    print()
    print("操作提示：")
    print("• 菜单栏 - 访问所有功能")
    print("• F5 - 强制更新")
    print("• F11 - 全屏模式")
    print("• Ctrl+Q - 退出应用")
    print("=" * 50)


def print_exit_message():
    """打印退出信息"""
    print()
    print("=" * 50)
    print("👋 感谢使用时钟数字罗盘！")
    print("=" * 50)
    print("应用特色：")
    print("• ✅ 创新的螺旋式时间显示")
    print("• ✅ 流畅的实时更新体验")
    print("• ✅ 丰富的主题和视觉效果")
    print("• ✅ 直观的用户界面设计")
    print()
    print("期待您的再次使用！")
    print("=" * 50)


def main():
    """主函数"""
    try:
        # 检查运行环境
        if not check_environment():
            print("\n❌ 环境检查失败，无法启动应用")
            print("请解决上述问题后重试")
            return 1
        
        print()
        
        # 打印欢迎信息
        print_welcome_message()
        
        print("🕐 时钟数字罗盘启动中...")
        
        # 导入时钟窗口模块
        from gui.clock_window import ClockWindow
        
        # 创建并启动时钟窗口
        print("🔄 正在创建时钟窗口...")
        clock_window = ClockWindow()
        print("✅ 时钟窗口创建成功")
        
        print("🖥️  正在显示主窗口...")
        print("🎉 应用已启动！享受您的螺旋时钟体验吧！")
        print()
        print("💡 提示：可以通过菜单栏体验各种功能")
        
        # 运行应用
        clock_window.run()
        
        print("👋 时钟应用结束，感谢使用！")
        
    except KeyboardInterrupt:
        print("\n⚠️ 应用被用户中断")
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {e}")
        print("请确保所有必要的模块都已正确安装")
        import traceback
        traceback.print_exc()
        return 1
    except Exception as e:
        print(f"\n❌ 应用运行出错: {e}")
        print("错误详情：")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # 打印退出信息
        print_exit_message()
    
    return 0


def show_help():
    """显示帮助信息"""
    print("时钟数字罗盘 - 使用帮助")
    print("=" * 40)
    print()
    print("用法：")
    print("  python main.py          # 启动应用")
    print("  python main.py --help   # 显示帮助")
    print("  python main.py --check  # 仅检查环境")
    print()
    print("功能说明：")
    print("• 螺旋式时间显示 - 以创新的螺旋形式展示时间")
    print("• 实时更新 - 每秒自动刷新时间显示")
    print("• 中文格式 - 支持中文数字时间格式")
    print("• 多主题 - 经典、深色、现代三种主题")
    print("• 视觉效果 - 阴影、螺旋路径等效果")
    print()
    print("系统要求：")
    print("• Python 3.6+")
    print("• tkinter模块")
    print("• 支持图形界面的系统")
    print()
    print("快捷键：")
    print("• F5 - 强制更新时间显示")
    print("• F11 - 切换全屏模式")
    print("• Ctrl+Q - 退出应用")


def show_version():
    """显示版本信息"""
    print("时钟数字罗盘 v1.0")
    print("作者：AI Assistant")
    print("创建时间：2025年")
    print()
    print("技术栈：")
    print("• Python 3.6+")
    print("• tkinter GUI框架")
    print("• 螺旋布局算法")
    print("• 模块化架构设计")


if __name__ == "__main__":
    # 解析命令行参数
    args = sys.argv[1:]
    
    if "--help" in args or "-h" in args:
        show_help()
        sys.exit(0)
    
    if "--version" in args or "-v" in args:
        show_version()
        sys.exit(0)
    
    if "--check" in args or "-c" in args:
        print("🔍 环境检查模式")
        if check_environment():
            print("\n✅ 环境检查完成，所有检查都通过")
            sys.exit(0)
        else:
            print("\n❌ 环境检查失败")
            sys.exit(1)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)

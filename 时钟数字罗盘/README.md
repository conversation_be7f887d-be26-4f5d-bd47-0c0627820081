# 时钟数字罗盘

一个创新的时间显示应用，以螺旋形式展示时间信息，将传统时钟概念与数字罗盘设计相结合。

## 项目特性

- 🌀 **螺旋式布局**：基于极坐标方程的创新时间显示方式
- 🕐 **实时更新**：每秒自动刷新时间显示
- 🇨🇳 **中文显示**：支持中文数字和时间格式
- 🎨 **多主题支持**：经典、深色、现代三种主题
- ⚡ **高性能**：基于tkinter的轻量级实现

## 项目结构

```
时钟数字罗盘/
├── main.py              # 程序入口（待实现）
├── clock/               # 时钟逻辑模块
│   ├── __init__.py
│   ├── time_manager.py     # 时间管理器（待实现）
│   └── spiral_calculator.py # 螺旋布局计算器（待实现）
├── gui/                 # 图形界面模块
│   ├── __init__.py
│   ├── base_canvas.py      # 通用Canvas基类（待实现）
│   ├── spiral_canvas.py    # 螺旋画布组件（待实现）
│   └── clock_window.py     # 主窗口（待实现）
├── utils/               # 工具模块
│   ├── __init__.py
│   └── constants.py        # 常量定义 ✅
├── requirements.txt     # 依赖包 ✅
└── README.md           # 项目说明 ✅
```

## 技术架构

- **GUI框架**：Python tkinter标准库
- **螺旋算法**：基于极坐标方程 r = a * θ^b
- **时间处理**：Python datetime标准库
- **架构模式**：模块化设计，参考五子棋游戏架构

## 安装和运行

### 环境要求
- Python 3.6+
- tkinter（通常随Python安装）

### 快速启动
```bash
cd 时钟数字罗盘
python main.py
```

### 使用启动脚本（推荐）
```bash
# 环境检查和启动
python run_clock.py

# 仅检查环境
python run_clock.py --check

# 运行测试
python run_clock.py --test

# 演示模式
python run_clock.py --demo

# 显示帮助
python run_clock.py --help
```

### 命令行选项
```bash
# main.py选项
python main.py --help      # 显示帮助
python main.py --version   # 显示版本
python main.py --check     # 环境检查

# run_clock.py选项（推荐）
python run_clock.py --check   # 环境检查
python run_clock.py --test    # 运行测试
python run_clock.py --demo    # 演示模式
python run_clock.py --info    # 系统信息
```

## 开发状态

- ✅ 项目基础结构
- ✅ 常量配置系统
- ⏳ 时间管理器模块
- ⏳ 螺旋布局计算器
- ⏳ GUI组件系统
- ⏳ 主窗口和应用框架
- ⏳ 视觉效果优化

## 配置说明

项目的所有配置参数都定义在 `utils/constants.py` 中，包括：

- **螺旋参数**：控制螺旋的形状和大小
- **颜色主题**：支持多种预设主题
- **字体配置**：可调整的字体大小和样式
- **时间格式**：中文数字转换和显示格式

## 许可证

本项目采用开源许可证，欢迎贡献和改进。

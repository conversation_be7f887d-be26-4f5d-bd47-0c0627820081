#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时钟数字罗盘启动脚本
提供多种启动选项和环境检查功能

用法：
  python run_clock.py              # 启动应用
  python run_clock.py --help       # 显示帮助
  python run_clock.py --check      # 环境检查
  python run_clock.py --test       # 运行测试
  python run_clock.py --demo       # 演示模式
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_tkinter():
    """检查tkinter模块"""
    print("🖥️  检查tkinter模块...")
    
    try:
        import tkinter
        print("✅ tkinter模块可用")
        return True
    except ImportError:
        print("❌ tkinter模块不可用")
        print("   安装方法：")
        print("   Ubuntu/Debian: sudo apt-get install python3-tk")
        print("   CentOS/RHEL: sudo yum install tkinter")
        print("   macOS: tkinter通常随Python安装")
        print("   Windows: tkinter通常随Python安装")
        return False


def check_project_structure():
    """检查项目结构"""
    print("📁 检查项目结构...")
    
    required_files = [
        "main.py",
        "clock/__init__.py",
        "clock/time_manager.py",
        "clock/spiral_calculator.py",
        "gui/__init__.py",
        "gui/base_canvas.py",
        "gui/spiral_canvas.py",
        "gui/clock_window.py",
        "utils/__init__.py",
        "utils/constants.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = Path(project_root) / file_path
        if not full_path.exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要的项目文件：")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 项目结构完整")
    return True


def check_modules():
    """检查项目模块"""
    print("📦 检查项目模块...")
    
    modules_to_check = [
        ("gui.clock_window", "ClockWindow"),
        ("gui.spiral_canvas", "SpiralCanvas"),
        ("gui.base_canvas", "BaseCanvas"),
        ("clock.time_manager", "TimeManager"),
        ("clock.spiral_calculator", "SpiralCalculator"),
        ("utils.constants", "DEFAULT_THEME")
    ]
    
    for module_name, class_name in modules_to_check:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
        except ImportError as e:
            print(f"❌ {module_name}.{class_name} - 导入失败: {e}")
            return False
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name} - 属性错误: {e}")
            return False
    
    print("✅ 所有模块检查通过")
    return True


def run_environment_check():
    """运行完整的环境检查"""
    print("🔍 开始环境检查...")
    print("=" * 50)
    
    checks = [
        check_python_version,
        check_tkinter,
        check_project_structure,
        check_modules
    ]
    
    all_passed = True
    for check_func in checks:
        if not check_func():
            all_passed = False
        print()
    
    if all_passed:
        print("🎉 所有检查都通过！应用可以正常运行。")
        return True
    else:
        print("❌ 部分检查失败，请解决上述问题后重试。")
        return False


def run_tests():
    """运行项目测试"""
    print("🧪 运行项目测试...")
    print("=" * 50)
    
    test_files = [
        "tests/test_main.py",
        "tests/test_time_manager.py",
        "tests/test_spiral_calculator.py",
        "tests/test_base_canvas.py",
        "tests/test_spiral_canvas.py",
        "tests/test_clock_window.py",
        "tests/test_integration.py"
    ]
    
    passed_tests = 0
    total_tests = 0
    
    for test_file in test_files:
        test_path = Path(project_root) / test_file
        if test_path.exists():
            total_tests += 1
            print(f"\n🔬 运行 {test_file}...")
            
            try:
                result = subprocess.run(
                    [sys.executable, str(test_path)],
                    capture_output=True,
                    text=True,
                    timeout=60,
                    cwd=project_root
                )
                
                if result.returncode == 0:
                    print(f"✅ {test_file} - 测试通过")
                    passed_tests += 1
                else:
                    print(f"❌ {test_file} - 测试失败")
                    if result.stderr:
                        print(f"错误信息：{result.stderr[:200]}...")
                        
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_file} - 测试超时")
            except Exception as e:
                print(f"❌ {test_file} - 测试出错：{e}")
    
    print(f"\n📊 测试结果：{passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests and total_tests > 0:
        print("🎉 所有测试都通过！")
        return True
    else:
        print("⚠️ 部分测试失败，但应用仍可能正常运行")
        return False


def run_application():
    """运行主应用"""
    print("🚀 启动时钟数字罗盘...")
    print("=" * 50)
    
    try:
        # 运行main.py
        main_path = Path(project_root) / "main.py"
        result = subprocess.run([sys.executable, str(main_path)], cwd=project_root)
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n⚠️ 应用被用户中断")
        return 0
    except Exception as e:
        print(f"\n❌ 应用启动失败：{e}")
        return 1


def run_demo():
    """运行演示模式"""
    print("🎭 启动演示模式...")
    print("=" * 50)
    
    try:
        # 运行完整应用演示
        demo_path = Path(project_root) / "demo_complete_app.py"
        if demo_path.exists():
            result = subprocess.run([sys.executable, str(demo_path)], cwd=project_root)
            return result.returncode
        else:
            print("❌ 演示程序不存在，启动普通模式...")
            return run_application()
            
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
        return 0
    except Exception as e:
        print(f"\n❌ 演示启动失败：{e}")
        return 1


def show_system_info():
    """显示系统信息"""
    print("💻 系统信息")
    print("=" * 30)
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"项目路径: {project_root}")
    
    # 检查可选依赖
    optional_modules = ["PIL", "numpy", "matplotlib"]
    print("\n📦 可选模块:")
    for module in optional_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} (未安装)")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="时钟数字罗盘启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法：
  python run_clock.py              # 启动应用
  python run_clock.py --check      # 仅检查环境
  python run_clock.py --test       # 运行测试
  python run_clock.py --demo       # 演示模式
  python run_clock.py --info       # 显示系统信息
        """
    )
    
    parser.add_argument("--check", "-c", action="store_true", help="仅进行环境检查")
    parser.add_argument("--test", "-t", action="store_true", help="运行项目测试")
    parser.add_argument("--demo", "-d", action="store_true", help="启动演示模式")
    parser.add_argument("--info", "-i", action="store_true", help="显示系统信息")
    parser.add_argument("--version", "-v", action="store_true", help="显示版本信息")
    
    args = parser.parse_args()
    
    # 显示版本信息
    if args.version:
        print("时钟数字罗盘 v1.0")
        print("作者：AI Assistant")
        return 0
    
    # 显示系统信息
    if args.info:
        show_system_info()
        return 0
    
    # 仅进行环境检查
    if args.check:
        return 0 if run_environment_check() else 1
    
    # 运行测试
    if args.test:
        env_ok = run_environment_check()
        if not env_ok:
            print("\n⚠️ 环境检查失败，但仍尝试运行测试...")
        
        test_ok = run_tests()
        return 0 if test_ok else 1
    
    # 演示模式
    if args.demo:
        if not run_environment_check():
            print("\n❌ 环境检查失败，无法启动演示")
            return 1
        return run_demo()
    
    # 默认：启动应用
    if not run_environment_check():
        print("\n❌ 环境检查失败，无法启动应用")
        return 1
    
    return run_application()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 启动脚本被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 启动脚本出现严重错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

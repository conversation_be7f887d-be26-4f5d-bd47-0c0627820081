#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整时钟数字罗盘应用演示
展示完整的应用功能和用户体验
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from gui.clock_window import ClockWindow
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在项目根目录运行此演示程序")
    sys.exit(1)


def print_welcome_message():
    """打印欢迎信息"""
    print("=" * 60)
    print("🕐 时钟数字罗盘 - 完整应用演示")
    print("=" * 60)
    print()
    print("功能特性：")
    print("• 🌀 螺旋式时间显示 - 创新的时间布局方式")
    print("• ⏰ 实时时间更新 - 每秒自动刷新")
    print("• 🇨🇳 中文时间显示 - 支持中文数字格式")
    print("• 🎨 多主题支持 - 经典、深色、现代三种主题")
    print("• ✨ 视觉效果 - 阴影、螺旋路径等效果")
    print("• ⌨️  键盘快捷键 - 便捷的操作方式")
    print()
    print("操作指南：")
    print("• 菜单栏 - 访问所有功能和设置")
    print("• 右键菜单 - 快速访问常用功能")
    print("• F5 - 强制更新时间显示")
    print("• F11 - 切换全屏模式")
    print("• Ctrl+Q - 退出应用")
    print()
    print("主题切换：")
    print("• 视图 → 主题 → 选择主题")
    print("• 经典主题 - 温暖的米色调")
    print("• 深色主题 - 现代的深色调")
    print("• 现代主题 - 简洁的白色调")
    print()
    print("视觉效果：")
    print("• 视图 → 切换阴影 - 为文字添加阴影效果")
    print("• 视图 → 切换螺旋路径 - 显示/隐藏螺旋轨迹")
    print()
    print("🚀 正在启动应用...")
    print("=" * 60)


def print_startup_info():
    """打印启动信息"""
    print()
    print("📋 应用启动信息：")
    print("• 窗口标题：时钟数字罗盘")
    print("• 初始主题：经典主题")
    print("• 自动更新：已启用")
    print("• 更新间隔：1秒")
    print("• 窗口大小：800x800（可调整）")
    print()
    print("💡 提示：")
    print("• 应用启动后会自动居中显示")
    print("• 时间会以螺旋形式实时更新")
    print("• 可以通过菜单栏体验各种功能")
    print("• 按Ctrl+Q或关闭窗口退出应用")
    print()


def print_exit_message():
    """打印退出信息"""
    print()
    print("=" * 60)
    print("👋 感谢使用时钟数字罗盘！")
    print("=" * 60)
    print()
    print("应用特色回顾：")
    print("• ✅ 创新的螺旋式时间显示")
    print("• ✅ 流畅的实时更新体验")
    print("• ✅ 丰富的主题和视觉效果")
    print("• ✅ 直观的用户界面设计")
    print("• ✅ 完整的功能集成")
    print()
    print("技术亮点：")
    print("• 🔧 模块化架构设计")
    print("• 🎯 精确的螺旋布局算法")
    print("• 🎨 灵活的主题系统")
    print("• ⚡ 高效的性能优化")
    print("• 🧪 全面的测试覆盖")
    print()
    print("期待您的再次使用！")
    print("=" * 60)


def run_demo():
    """运行完整应用演示"""
    try:
        # 打印欢迎信息
        print_welcome_message()
        
        # 打印启动信息
        print_startup_info()
        
        # 创建并运行时钟窗口
        print("🔄 正在初始化应用组件...")
        clock_window = ClockWindow()
        
        print("✅ 应用组件初始化完成")
        print("🖥️  正在显示主窗口...")
        print()
        print("🎉 应用已启动！享受您的螺旋时钟体验吧！")
        print()
        print("💬 如需帮助，请查看菜单栏中的'帮助'选项")
        print("🔧 如需设置，请查看菜单栏中的'工具'选项")
        print()
        
        # 运行应用主循环
        clock_window.run()
        
    except KeyboardInterrupt:
        print("\n⚠️  应用被用户中断（Ctrl+C）")
    except Exception as e:
        print(f"\n❌ 应用运行出错: {e}")
        import traceback
        print("\n📋 错误详情：")
        traceback.print_exc()
        return 1
    finally:
        # 打印退出信息
        print_exit_message()
    
    return 0


def check_environment():
    """检查运行环境"""
    print("🔍 正在检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查tkinter
    try:
        import tkinter
        print("✅ tkinter模块可用")
    except ImportError:
        print("❌ tkinter模块不可用")
        print("   请安装tkinter: sudo apt-get install python3-tk (Ubuntu/Debian)")
        return False
    
    # 检查项目模块
    try:
        from gui.clock_window import ClockWindow
        from gui.spiral_canvas import SpiralCanvas
        from clock.time_manager import TimeManager
        from clock.spiral_calculator import SpiralCalculator
        print("✅ 项目模块完整")
    except ImportError as e:
        print(f"❌ 项目模块缺失: {e}")
        return False
    
    print("✅ 环境检查通过")
    return True


def main():
    """主函数"""
    print("🚀 时钟数字罗盘 - 完整应用演示启动器")
    print()
    
    # 检查运行环境
    if not check_environment():
        print("\n❌ 环境检查失败，无法启动应用")
        return 1
    
    print()
    
    # 运行演示
    return run_demo()


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"\n💥 启动器出现严重错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

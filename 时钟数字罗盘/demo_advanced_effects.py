#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级视觉效果演示程序
展示时钟数字罗盘的高级主题和视觉效果功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from gui.spiral_canvas import SpiralCanvas
    from utils.constants import EXTENDED_THEMES, ADVANCED_VISUAL_EFFECTS
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保在项目根目录运行此演示程序")
    sys.exit(1)


class AdvancedEffectsDemo:
    """高级视觉效果演示类"""
    
    def __init__(self):
        """初始化演示程序"""
        self.root = tk.Tk()
        self.root.title("高级视觉效果演示 - 时钟数字罗盘")
        self.root.geometry("1200x800")
        self.root.configure(bg='#F0F0F0')
        
        # 当前设置
        self.current_theme = 'classic'
        self.effects_state = {
            'shadows': True,
            'spiral_path': False,
            'text_glow': False,
            'gradient_text': False,
            'pulse_animation': False,
            'particle_effects': False
        }
        
        # 创建界面
        self.setup_ui()
        
        # 启动自动更新
        self.auto_update = True
        self.update_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='#F0F0F0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：螺旋画布
        canvas_frame = tk.Frame(main_frame, bg='#F0F0F0')
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = tk.Label(canvas_frame, text="高级视觉效果演示", 
                              font=('Arial', 16, 'bold'), bg='#F0F0F0')
        title_label.pack(pady=(0, 10))
        
        # 创建螺旋画布
        self.spiral_canvas = SpiralCanvas(canvas_frame, 600)
        self.spiral_canvas.get_canvas().pack()
        
        # 右侧：控制面板
        control_frame = tk.Frame(main_frame, bg='#F0F0F0', width=300)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(20, 0))
        control_frame.pack_propagate(False)
        
        self.setup_controls(control_frame)
        
        # 底部：状态栏
        self.status_bar = tk.Label(self.root, text="就绪", relief=tk.SUNKEN, 
                                  anchor=tk.W, bg='#E0E0E0')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_controls(self, parent):
        """设置控制面板"""
        # 标题
        title_label = tk.Label(parent, text="控制面板", font=('Arial', 14, 'bold'), bg='#F0F0F0')
        title_label.pack(pady=(0, 15))
        
        # 主题选择
        self.setup_theme_controls(parent)
        
        # 视觉效果控制
        self.setup_effects_controls(parent)
        
        # 操作按钮
        self.setup_action_buttons(parent)
        
        # 信息显示
        self.setup_info_display(parent)
    
    def setup_theme_controls(self, parent):
        """设置主题控制"""
        theme_frame = tk.LabelFrame(parent, text="主题选择", bg='#F0F0F0', font=('Arial', 10, 'bold'))
        theme_frame.pack(fill=tk.X, pady=5)
        
        self.theme_var = tk.StringVar(value=self.current_theme)
        
        # 创建主题选择按钮
        for theme_name, theme_config in EXTENDED_THEMES.items():
            theme_display_name = theme_config.get('name', theme_name.title())
            
            rb = tk.Radiobutton(theme_frame, text=theme_display_name, 
                               variable=self.theme_var, value=theme_name,
                               command=self.change_theme, bg='#F0F0F0')
            rb.pack(anchor=tk.W, padx=5, pady=2)
    
    def setup_effects_controls(self, parent):
        """设置视觉效果控制"""
        effects_frame = tk.LabelFrame(parent, text="视觉效果", bg='#F0F0F0', font=('Arial', 10, 'bold'))
        effects_frame.pack(fill=tk.X, pady=5)
        
        # 基础效果
        basic_frame = tk.Frame(effects_frame, bg='#F0F0F0')
        basic_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Label(basic_frame, text="基础效果:", font=('Arial', 9, 'bold'), bg='#F0F0F0').pack(anchor=tk.W)
        
        self.shadows_var = tk.BooleanVar(value=self.effects_state['shadows'])
        tk.Checkbutton(basic_frame, text="阴影效果", variable=self.shadows_var,
                      command=self.toggle_shadows, bg='#F0F0F0').pack(anchor=tk.W)
        
        self.spiral_path_var = tk.BooleanVar(value=self.effects_state['spiral_path'])
        tk.Checkbutton(basic_frame, text="螺旋路径", variable=self.spiral_path_var,
                      command=self.toggle_spiral_path, bg='#F0F0F0').pack(anchor=tk.W)
        
        # 高级效果
        advanced_frame = tk.Frame(effects_frame, bg='#F0F0F0')
        advanced_frame.pack(fill=tk.X, padx=5, pady=2)
        
        tk.Label(advanced_frame, text="高级效果:", font=('Arial', 9, 'bold'), bg='#F0F0F0').pack(anchor=tk.W)
        
        self.text_glow_var = tk.BooleanVar(value=self.effects_state['text_glow'])
        tk.Checkbutton(advanced_frame, text="文字发光", variable=self.text_glow_var,
                      command=self.toggle_text_glow, bg='#F0F0F0').pack(anchor=tk.W)
        
        self.gradient_text_var = tk.BooleanVar(value=self.effects_state['gradient_text'])
        tk.Checkbutton(advanced_frame, text="渐变文字", variable=self.gradient_text_var,
                      command=self.toggle_gradient_text, bg='#F0F0F0').pack(anchor=tk.W)
        
        self.pulse_animation_var = tk.BooleanVar(value=self.effects_state['pulse_animation'])
        tk.Checkbutton(advanced_frame, text="脉冲动画", variable=self.pulse_animation_var,
                      command=self.toggle_pulse_animation, bg='#F0F0F0').pack(anchor=tk.W)
        
        self.particle_effects_var = tk.BooleanVar(value=self.effects_state['particle_effects'])
        tk.Checkbutton(advanced_frame, text="粒子效果", variable=self.particle_effects_var,
                      command=self.toggle_particle_effects, bg='#F0F0F0').pack(anchor=tk.W)
    
    def setup_action_buttons(self, parent):
        """设置操作按钮"""
        action_frame = tk.LabelFrame(parent, text="操作", bg='#F0F0F0', font=('Arial', 10, 'bold'))
        action_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(action_frame, text="强制更新", command=self.force_update,
                 width=15).pack(pady=2)
        tk.Button(action_frame, text="重置效果", command=self.reset_effects,
                 width=15).pack(pady=2)
        tk.Button(action_frame, text="随机主题", command=self.random_theme,
                 width=15).pack(pady=2)
        tk.Button(action_frame, text="效果预设", command=self.show_presets,
                 width=15).pack(pady=2)
    
    def setup_info_display(self, parent):
        """设置信息显示"""
        info_frame = tk.LabelFrame(parent, text="信息", bg='#F0F0F0', font=('Arial', 10, 'bold'))
        info_frame.pack(fill=tk.X, pady=5)
        
        self.info_text = tk.Text(info_frame, height=8, width=30, font=('Arial', 8),
                                wrap=tk.WORD, bg='#FFFFFF')
        self.info_text.pack(padx=5, pady=5)
        
        # 滚动条
        scrollbar = tk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=scrollbar.set)
    
    def change_theme(self):
        """更改主题"""
        theme_name = self.theme_var.get()
        self.current_theme = theme_name
        self.spiral_canvas.set_advanced_theme(theme_name)
        
        theme_config = EXTENDED_THEMES.get(theme_name, {})
        theme_display_name = theme_config.get('name', theme_name)
        self.status_bar.config(text=f"主题已切换到：{theme_display_name}")
        
        self.update_info_display()
    
    def toggle_shadows(self):
        """切换阴影效果"""
        self.effects_state['shadows'] = self.shadows_var.get()
        self.spiral_canvas.enable_shadows = self.effects_state['shadows']
        self.spiral_canvas.force_update()
        self.update_status("阴影效果")
    
    def toggle_spiral_path(self):
        """切换螺旋路径"""
        self.effects_state['spiral_path'] = self.spiral_path_var.get()
        if self.effects_state['spiral_path']:
            self.spiral_canvas.show_spiral_path = True
            self.spiral_canvas.draw_spiral_paths()
        else:
            self.spiral_canvas.show_spiral_path = False
            self.spiral_canvas.clear_spiral_paths()
        self.update_status("螺旋路径")
    
    def toggle_text_glow(self):
        """切换文字发光"""
        self.effects_state['text_glow'] = self.text_glow_var.get()
        self.spiral_canvas.enable_text_glow = self.effects_state['text_glow']
        self.spiral_canvas.force_update()
        self.update_status("文字发光")
    
    def toggle_gradient_text(self):
        """切换渐变文字"""
        self.effects_state['gradient_text'] = self.gradient_text_var.get()
        self.spiral_canvas.enable_gradient_text = self.effects_state['gradient_text']
        self.spiral_canvas.force_update()
        self.update_status("渐变文字")
    
    def toggle_pulse_animation(self):
        """切换脉冲动画"""
        self.effects_state['pulse_animation'] = self.pulse_animation_var.get()
        self.spiral_canvas.enable_pulse_animation = self.effects_state['pulse_animation']
        if self.effects_state['pulse_animation']:
            self.spiral_canvas.start_pulse_animation()
        else:
            self.spiral_canvas.stop_pulse_animation()
        self.update_status("脉冲动画")
    
    def toggle_particle_effects(self):
        """切换粒子效果"""
        self.effects_state['particle_effects'] = self.particle_effects_var.get()
        self.spiral_canvas.enable_particle_effects = self.effects_state['particle_effects']
        if self.effects_state['particle_effects']:
            self.spiral_canvas.draw_particle_effects()
        else:
            self.spiral_canvas.clear_particle_effects()
        self.update_status("粒子效果")
    
    def force_update(self):
        """强制更新"""
        self.spiral_canvas.force_update()
        self.status_bar.config(text="强制更新完成")
    
    def reset_effects(self):
        """重置所有效果"""
        # 重置状态
        self.effects_state = {
            'shadows': True,
            'spiral_path': False,
            'text_glow': False,
            'gradient_text': False,
            'pulse_animation': False,
            'particle_effects': False
        }
        
        # 更新UI
        self.shadows_var.set(True)
        self.spiral_path_var.set(False)
        self.text_glow_var.set(False)
        self.gradient_text_var.set(False)
        self.pulse_animation_var.set(False)
        self.particle_effects_var.set(False)
        
        # 应用到画布
        self.spiral_canvas.enable_shadows = True
        self.spiral_canvas.show_spiral_path = False
        self.spiral_canvas.enable_text_glow = False
        self.spiral_canvas.enable_gradient_text = False
        self.spiral_canvas.enable_pulse_animation = False
        self.spiral_canvas.enable_particle_effects = False
        
        self.spiral_canvas.clear_spiral_paths()
        self.spiral_canvas.clear_particle_effects()
        self.spiral_canvas.stop_pulse_animation()
        self.spiral_canvas.force_update()
        
        self.status_bar.config(text="所有效果已重置")
        self.update_info_display()
    
    def random_theme(self):
        """随机选择主题"""
        import random
        theme_names = list(EXTENDED_THEMES.keys())
        random_theme = random.choice(theme_names)
        self.theme_var.set(random_theme)
        self.change_theme()
    
    def show_presets(self):
        """显示效果预设"""
        presets = {
            "经典模式": {"shadows": True, "spiral_path": False, "text_glow": False},
            "炫酷模式": {"shadows": True, "spiral_path": True, "text_glow": True, "particle_effects": True},
            "简约模式": {"shadows": False, "spiral_path": False, "text_glow": False},
            "动感模式": {"shadows": True, "pulse_animation": True, "gradient_text": True}
        }
        
        preset_window = tk.Toplevel(self.root)
        preset_window.title("效果预设")
        preset_window.geometry("300x200")
        preset_window.configure(bg='#F0F0F0')
        
        for preset_name, preset_config in presets.items():
            tk.Button(preset_window, text=preset_name, 
                     command=lambda p=preset_config: self.apply_preset(p, preset_window),
                     width=20).pack(pady=5)
    
    def apply_preset(self, preset_config, window):
        """应用效果预设"""
        for effect, value in preset_config.items():
            if effect in self.effects_state:
                self.effects_state[effect] = value
                
                # 更新UI变量
                if effect == 'shadows':
                    self.shadows_var.set(value)
                elif effect == 'spiral_path':
                    self.spiral_path_var.set(value)
                elif effect == 'text_glow':
                    self.text_glow_var.set(value)
                elif effect == 'gradient_text':
                    self.gradient_text_var.set(value)
                elif effect == 'pulse_animation':
                    self.pulse_animation_var.set(value)
                elif effect == 'particle_effects':
                    self.particle_effects_var.set(value)
        
        # 应用所有效果
        self.apply_all_effects()
        window.destroy()
        self.status_bar.config(text="预设已应用")
    
    def apply_all_effects(self):
        """应用所有效果设置"""
        self.spiral_canvas.enable_shadows = self.effects_state['shadows']
        self.spiral_canvas.show_spiral_path = self.effects_state['spiral_path']
        self.spiral_canvas.enable_text_glow = self.effects_state['text_glow']
        self.spiral_canvas.enable_gradient_text = self.effects_state['gradient_text']
        self.spiral_canvas.enable_pulse_animation = self.effects_state['pulse_animation']
        self.spiral_canvas.enable_particle_effects = self.effects_state['particle_effects']
        
        # 应用特殊效果
        if self.effects_state['spiral_path']:
            self.spiral_canvas.draw_spiral_paths()
        else:
            self.spiral_canvas.clear_spiral_paths()
            
        if self.effects_state['particle_effects']:
            self.spiral_canvas.draw_particle_effects()
        else:
            self.spiral_canvas.clear_particle_effects()
            
        if self.effects_state['pulse_animation']:
            self.spiral_canvas.start_pulse_animation()
        else:
            self.spiral_canvas.stop_pulse_animation()
        
        self.spiral_canvas.force_update()
    
    def update_status(self, effect_name):
        """更新状态栏"""
        status = "启用" if self.effects_state.get(effect_name.lower().replace(' ', '_'), False) else "禁用"
        self.status_bar.config(text=f"{effect_name}已{status}")
        self.update_info_display()
    
    def update_info_display(self):
        """更新信息显示"""
        info_text = f"当前主题：{EXTENDED_THEMES.get(self.current_theme, {}).get('name', self.current_theme)}\n\n"
        info_text += "视觉效果状态：\n"
        
        for effect, enabled in self.effects_state.items():
            effect_name = effect.replace('_', ' ').title()
            status = "✓" if enabled else "✗"
            info_text += f"{status} {effect_name}\n"
        
        info_text += f"\n更新时间：{self.spiral_canvas.get_current_time_info()['display_text']}"
        
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, info_text)
    
    def update_timer(self):
        """定时更新"""
        if self.auto_update:
            self.spiral_canvas.update_time_display()
            self.update_info_display()
        
        self.root.after(1000, self.update_timer)
    
    def run(self):
        """运行演示程序"""
        print("🎨 高级视觉效果演示程序启动")
        print("功能说明：")
        print("- 6种扩展主题：经典、深色、现代、霓虹、日落、海洋")
        print("- 多种视觉效果：阴影、发光、渐变、动画、粒子")
        print("- 实时效果切换和预设配置")
        print("\n享受您的视觉效果体验！")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 演示程序已退出")
        finally:
            self.root.quit()


def main():
    """主函数"""
    try:
        demo = AdvancedEffectsDemo()
        demo.run()
    except Exception as e:
        print(f"❌ 演示程序运行出错: {e}")
        return 1
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
螺旋时钟画布组件
基于BaseCanvas创建专门的螺旋时钟显示组件

功能包括：
1. 集成螺旋计算器和时间管理器
2. 动态螺旋时间显示
3. 文字重叠处理和视觉优化
4. 实时更新机制
5. 主题和样式支持
"""

import sys
import os
from typing import List, Dict, Any, Optional

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from .base_canvas import BaseCanvas
    from clock.spiral_calculator import SpiralCalculator
    from clock.time_manager import TimeManager
    from utils.constants import (
        DEFAULT_THEME, CANVAS_SIZE,
        SPIRAL_A, SPIRAL_B, CENTER_X, CENTER_Y,
        TEXT_STYLES, FONT_SIZES,
        SPIRAL_OPTIMIZATION, SPIRAL_DRAWING,
        EXTENDED_THEMES, ADVANCED_VISUAL_EFFECTS,
        ANIMATION_EFFECTS, TEXT_EFFECTS
    )
except ImportError as e:
    print(f"导入错误: {e}")
    # 提供备用导入
    import tkinter as tk
    
    class BaseCanvas:
        def __init__(self, parent, width, height, bg_color=None):
            self.canvas = tk.Canvas(parent, width=width, height=height, bg=bg_color or '#DEB887')
            self.width = width
            self.height = height
        def draw_text(self, x, y, text, **kwargs):
            return self.canvas.create_text(x, y, text=text, **kwargs)
        def delete_object(self, obj_id):
            self.canvas.delete(obj_id)
        def clear_canvas(self):
            self.canvas.delete('all')
        def get_canvas(self):
            return self.canvas
    
    # 备用常量
    DEFAULT_THEME = {'canvas_bg': '#DEB887', 'text_primary': '#000000'}
    CANVAS_SIZE = 700
    SPIRAL_A, SPIRAL_B = 20.0, 0.8
    CENTER_X, CENTER_Y = 350, 350
    TEXT_STYLES = {'hour': {'font': ('Arial', 24, 'bold')}}
    FONT_SIZES = {'hour': 24, 'minute': 20, 'second': 16}
    SPIRAL_OPTIMIZATION = {'min_text_distance': 50}
    SPIRAL_DRAWING = {'show_spiral_path': False}


class SpiralCanvas(BaseCanvas):
    """
    螺旋时钟画布组件
    
    集成螺旋计算器和时间管理器，实现动态的螺旋时间显示
    """
    
    def __init__(self, parent, size: int = CANVAS_SIZE, theme: Dict[str, str] = None):
        """
        初始化螺旋时钟画布
        
        Args:
            parent: 父容器
            size (int): 画布大小（正方形）
            theme (Dict[str, str], optional): 主题配置
        """
        # 使用主题配置
        self.theme = theme or DEFAULT_THEME
        bg_color = self.theme.get('canvas_bg', DEFAULT_THEME['canvas_bg'])
        
        # 初始化基类
        super().__init__(parent, size, size, bg_color)
        
        # 初始化组件
        self.size = size
        self.center_x = size // 2
        self.center_y = size // 2
        
        # 创建螺旋计算器和时间管理器
        self.spiral_calc = SpiralCalculator(self.center_x, self.center_y, SPIRAL_A, SPIRAL_B)
        self.time_manager = TimeManager()
        
        # 存储时间显示对象
        self.time_text_objects: List[int] = []
        self.spiral_path_objects: List[int] = []
        self.shadow_objects: List[int] = []
        
        # 显示配置
        self.show_spiral_path = SPIRAL_DRAWING.get('show_spiral_path', False)
        self.enable_shadows = True
        self.enable_optimization = SPIRAL_OPTIMIZATION.get('overlap_detection', True)

        # 高级视觉效果配置
        self.enable_text_glow = ADVANCED_VISUAL_EFFECTS.get('text_glow', {}).get('enabled', False)
        self.enable_gradient_text = ADVANCED_VISUAL_EFFECTS.get('gradient_text', {}).get('enabled', False)
        self.enable_pulse_animation = ADVANCED_VISUAL_EFFECTS.get('pulse_animation', {}).get('enabled', False)
        self.enable_particle_effects = ADVANCED_VISUAL_EFFECTS.get('particle_effects', {}).get('enabled', False)

        # 动画状态
        self.animation_frame = 0
        self.pulse_scale = 1.0
        self.pulse_direction = 1

        # 上次显示的时间（用于变化检测）
        self.last_display_time = None
        
        # 初始化显示
        self.setup_canvas()
    
    def setup_canvas(self) -> None:
        """设置画布初始状态"""
        # 绘制螺旋路径（如果启用）
        if self.show_spiral_path:
            self.draw_spiral_paths()
        
        # 绘制初始时间
        self.update_time_display()
    
    def update_time_display(self) -> bool:
        """
        更新时间显示
        
        Returns:
            bool: 是否有更新
        """
        # 获取当前时间数据
        time_data = self.time_manager.get_time_components_for_spiral()
        
        # 检查时间是否发生变化
        current_time_str = f"{time_data['hour']['value']}-{time_data['minute']['value']}-{time_data['second']['value']}"
        if current_time_str == self.last_display_time:
            return False  # 时间未变化，无需更新
        
        # 清除旧的时间显示
        self.clear_time_display()
        
        # 计算螺旋位置
        positions = self.spiral_calc.get_time_positions(time_data)
        
        # 应用位置优化（避免重叠）
        if self.enable_optimization:
            positions = self.spiral_calc.optimize_positions_for_overlap(
                positions, 
                SPIRAL_OPTIMIZATION.get('min_text_distance', 50)
            )
        
        # 绘制时间文字
        self.draw_time_texts(positions)
        
        # 更新最后显示时间
        self.last_display_time = current_time_str
        
        return True
    
    def draw_time_texts(self, positions: List[Dict[str, Any]]) -> None:
        """
        绘制时间文字
        
        Args:
            positions (List[Dict]): 位置数据列表
        """
        for pos_data in positions:
            x, y = pos_data['x'], pos_data['y']
            text = pos_data['text']
            time_unit = pos_data['time_unit']
            font_size = pos_data.get('font_size', FONT_SIZES.get(time_unit, 16))
            
            # 获取文字样式
            text_style = self.get_text_style(time_unit, font_size)
            
            # 绘制阴影（如果启用）
            if self.enable_shadows:
                shadow_id = self.draw_text_shadow(x, y, text, text_style)
                if shadow_id:
                    self.shadow_objects.append(shadow_id)
            
            # 绘制主文字
            text_id = self.draw_text(x, y, text, **text_style)
            self.time_text_objects.append(text_id)
    
    def draw_text_shadow(self, x: int, y: int, text: str, style: Dict[str, Any]) -> Optional[int]:
        """
        绘制文字阴影
        
        Args:
            x, y (int): 文字位置
            text (str): 文字内容
            style (Dict): 文字样式
            
        Returns:
            Optional[int]: 阴影对象ID
        """
        shadow_style = style.copy()
        shadow_style['fill'] = '#888888'  # 阴影颜色
        
        # 阴影偏移
        shadow_x = x + 2
        shadow_y = y + 2
        
        return self.draw_text(shadow_x, shadow_y, text, **shadow_style)
    
    def get_text_style(self, time_unit: str, font_size: int) -> Dict[str, Any]:
        """
        获取文字样式
        
        Args:
            time_unit (str): 时间单位
            font_size (int): 字体大小
            
        Returns:
            Dict[str, Any]: 文字样式配置
        """
        # 基础样式
        base_style = TEXT_STYLES.get(time_unit, TEXT_STYLES.get('hour', {}))
        
        # 颜色配置
        colors = {
            'hour': self.theme.get('text_primary', '#000000'),
            'minute': self.theme.get('text_secondary', '#8B4513'),
            'second': self.theme.get('accent', '#654321')
        }
        
        # 构建样式
        style = {
            'fill': colors.get(time_unit, colors['hour']),
            'font': ('Arial', font_size, 'bold' if time_unit == 'hour' else 'normal'),
            'anchor': 'center'
        }
        
        # 合并基础样式
        if 'font' in base_style:
            style['font'] = base_style['font']
        
        return style
    
    def draw_spiral_paths(self) -> None:
        """绘制螺旋路径线"""
        path_color = SPIRAL_DRAWING.get('path_color', '#CCCCCC')
        path_width = SPIRAL_DRAWING.get('path_width', 1)
        
        # 为每个层级绘制螺旋路径
        for layer in range(3):  # 时、分、秒三层
            points = self.spiral_calc.get_spiral_path_points(
                start_angle=0,
                end_angle=6.28,  # 2π
                layer=layer,
                step=0.2
            )
            
            # 绘制路径
            if len(points) > 1:
                for i in range(len(points) - 1):
                    x1, y1 = points[i]
                    x2, y2 = points[i + 1]
                    
                    line_id = self.draw_line(
                        x1, y1, x2, y2,
                        fill=path_color,
                        width=path_width
                    )
                    self.spiral_path_objects.append(line_id)
    
    def clear_time_display(self) -> None:
        """清除时间显示"""
        # 清除时间文字
        for text_id in self.time_text_objects:
            self.delete_object(text_id)
        self.time_text_objects.clear()
        
        # 清除阴影
        for shadow_id in self.shadow_objects:
            self.delete_object(shadow_id)
        self.shadow_objects.clear()
    
    def clear_spiral_paths(self) -> None:
        """清除螺旋路径"""
        for path_id in self.spiral_path_objects:
            self.delete_object(path_id)
        self.spiral_path_objects.clear()
    
    def toggle_spiral_paths(self) -> None:
        """切换螺旋路径显示"""
        self.show_spiral_path = not self.show_spiral_path
        
        if self.show_spiral_path:
            self.draw_spiral_paths()
        else:
            self.clear_spiral_paths()
    
    def toggle_shadows(self) -> None:
        """切换阴影效果"""
        self.enable_shadows = not self.enable_shadows
        # 重新绘制以应用变化
        self.update_time_display()
    
    def set_theme(self, theme: Dict[str, str]) -> None:
        """
        设置主题
        
        Args:
            theme (Dict[str, str]): 主题配置
        """
        self.theme = theme
        
        # 更新画布背景色
        bg_color = theme.get('canvas_bg', DEFAULT_THEME['canvas_bg'])
        self.canvas.config(bg=bg_color)
        
        # 重新绘制以应用新主题
        self.update_time_display()
    
    def get_current_time_info(self) -> Dict[str, Any]:
        """
        获取当前时间信息
        
        Returns:
            Dict[str, Any]: 时间信息
        """
        return self.time_manager.get_time_for_display()
    
    def resize_canvas(self, new_size: int) -> None:
        """
        调整画布大小
        
        Args:
            new_size (int): 新的画布大小
        """
        self.size = new_size
        self.center_x = new_size // 2
        self.center_y = new_size // 2
        
        # 更新基类大小
        self.resize(new_size, new_size)
        
        # 更新螺旋计算器
        self.spiral_calc.update_parameters(center_x=self.center_x, center_y=self.center_y)
        
        # 重新绘制
        self.clear_canvas()
        self.setup_canvas()
    
    def force_update(self) -> None:
        """强制更新显示"""
        self.last_display_time = None  # 重置时间缓存
        self.update_time_display()

    def draw_text_with_glow(self, x: int, y: int, text: str, style: Dict[str, Any]) -> List[int]:
        """
        绘制带发光效果的文字

        Args:
            x, y (int): 文字位置
            text (str): 文字内容
            style (Dict): 文字样式

        Returns:
            List[int]: 绘制对象ID列表
        """
        if not self.enable_text_glow:
            return [self.draw_text(x, y, text, **style)]

        glow_config = ADVANCED_VISUAL_EFFECTS.get('text_glow', {})
        glow_color = glow_config.get('color', '#FFD700')
        glow_radius = glow_config.get('radius', 3)

        object_ids = []

        # 绘制发光层
        for i in range(glow_radius, 0, -1):
            glow_style = style.copy()
            glow_style['fill'] = glow_color
            alpha = 0.3 * (glow_radius - i + 1) / glow_radius

            # 绘制多个偏移的文字创造发光效果
            for dx in range(-i, i+1):
                for dy in range(-i, i+1):
                    if dx*dx + dy*dy <= i*i:
                        glow_id = self.draw_text(x + dx, y + dy, text, **glow_style)
                        object_ids.append(glow_id)

        # 绘制主文字
        main_id = self.draw_text(x, y, text, **style)
        object_ids.append(main_id)

        return object_ids

    def draw_text_with_outline(self, x: int, y: int, text: str, style: Dict[str, Any]) -> List[int]:
        """
        绘制带轮廓的文字

        Args:
            x, y (int): 文字位置
            text (str): 文字内容
            style (Dict): 文字样式

        Returns:
            List[int]: 绘制对象ID列表
        """
        outline_config = TEXT_EFFECTS.get('outline', {})
        outline_width = outline_config.get('width', 1)
        outline_color = outline_config.get('color', '#000000')

        object_ids = []

        # 绘制轮廓
        outline_style = style.copy()
        outline_style['fill'] = outline_color

        for dx in range(-outline_width, outline_width + 1):
            for dy in range(-outline_width, outline_width + 1):
                if dx != 0 or dy != 0:
                    outline_id = self.draw_text(x + dx, y + dy, text, **outline_style)
                    object_ids.append(outline_id)

        # 绘制主文字
        main_id = self.draw_text(x, y, text, **style)
        object_ids.append(main_id)

        return object_ids

    def apply_pulse_animation(self) -> None:
        """应用脉冲动画效果"""
        if not self.enable_pulse_animation:
            return

        pulse_config = ADVANCED_VISUAL_EFFECTS.get('pulse_animation', {})
        scale_range = pulse_config.get('scale_range', (0.9, 1.1))
        duration = pulse_config.get('duration', 2000)

        # 计算当前脉冲比例
        import time
        current_time = time.time() * 1000  # 转换为毫秒
        cycle_position = (current_time % duration) / duration

        # 使用正弦波创建平滑的脉冲效果
        import math
        pulse_factor = math.sin(cycle_position * 2 * math.pi)
        scale_min, scale_max = scale_range
        self.pulse_scale = scale_min + (scale_max - scale_min) * (pulse_factor + 1) / 2

    def draw_particle_effects(self) -> None:
        """绘制粒子效果"""
        if not self.enable_particle_effects:
            return

        particle_config = ADVANCED_VISUAL_EFFECTS.get('particle_effects', {})
        particle_count = particle_config.get('particle_count', 20)
        colors = particle_config.get('colors', ['#FFD700', '#FF6B6B', '#4ECDC4'])

        import random
        import math

        # 清除旧的粒子
        if hasattr(self, 'particle_objects'):
            for particle_id in self.particle_objects:
                self.delete_object(particle_id)

        self.particle_objects = []

        # 生成新的粒子
        for i in range(particle_count):
            # 随机位置（在画布范围内）
            x = random.randint(50, self.size - 50)
            y = random.randint(50, self.size - 50)

            # 随机颜色
            color = random.choice(colors)

            # 绘制粒子（小圆点）
            particle_id = self.draw_circle(x, y, 2, fill=color, outline=color)
            self.particle_objects.append(particle_id)

    def toggle_text_glow(self) -> None:
        """切换文字发光效果"""
        self.enable_text_glow = not self.enable_text_glow
        self.force_update()

    def toggle_gradient_text(self) -> None:
        """切换渐变文字效果"""
        self.enable_gradient_text = not self.enable_gradient_text
        self.force_update()

    def toggle_pulse_animation(self) -> None:
        """切换脉冲动画效果"""
        self.enable_pulse_animation = not self.enable_pulse_animation
        if self.enable_pulse_animation:
            self.start_pulse_animation()
        else:
            self.stop_pulse_animation()

    def toggle_particle_effects(self) -> None:
        """切换粒子效果"""
        self.enable_particle_effects = not self.enable_particle_effects
        if self.enable_particle_effects:
            self.draw_particle_effects()
        else:
            self.clear_particle_effects()

    def start_pulse_animation(self) -> None:
        """启动脉冲动画"""
        if self.enable_pulse_animation:
            self.apply_pulse_animation()
            self.canvas.after(50, self.start_pulse_animation)  # 20 FPS

    def stop_pulse_animation(self) -> None:
        """停止脉冲动画"""
        self.pulse_scale = 1.0

    def clear_particle_effects(self) -> None:
        """清除粒子效果"""
        if hasattr(self, 'particle_objects'):
            for particle_id in self.particle_objects:
                self.delete_object(particle_id)
            self.particle_objects.clear()

    def set_advanced_theme(self, theme_name: str) -> None:
        """
        设置高级主题

        Args:
            theme_name (str): 主题名称
        """
        if theme_name in EXTENDED_THEMES:
            theme = EXTENDED_THEMES[theme_name]
            self.set_theme(theme)

            # 根据主题调整视觉效果
            if theme_name == 'neon':
                self.enable_text_glow = True
                self.enable_particle_effects = True
            elif theme_name == 'modern':
                self.enable_shadows = True
                self.enable_text_glow = False
            elif theme_name == 'classic':
                self.enable_shadows = True
                self.enable_text_glow = False
                self.enable_particle_effects = False


# 测试和验证函数
def test_spiral_canvas():
    """测试螺旋画布功能"""
    print("🧪 开始测试SpiralCanvas...")
    
    import tkinter as tk
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("螺旋时钟画布测试")
    root.geometry("800x600")
    
    # 创建SpiralCanvas实例
    spiral_canvas = SpiralCanvas(root, 500)
    spiral_canvas.get_canvas().pack(padx=20, pady=20)
    
    # 添加控制按钮
    control_frame = tk.Frame(root)
    control_frame.pack(pady=10)
    
    def toggle_paths():
        spiral_canvas.toggle_spiral_paths()
    
    def toggle_shadows():
        spiral_canvas.toggle_shadows()
    
    def force_update():
        spiral_canvas.force_update()
    
    tk.Button(control_frame, text="切换螺旋路径", command=toggle_paths).pack(side=tk.LEFT, padx=5)
    tk.Button(control_frame, text="切换阴影", command=toggle_shadows).pack(side=tk.LEFT, padx=5)
    tk.Button(control_frame, text="强制更新", command=force_update).pack(side=tk.LEFT, padx=5)
    
    print("✅ SpiralCanvas测试窗口已创建")
    
    # 运行测试（注释掉以避免阻塞）
    # root.mainloop()


if __name__ == "__main__":
    test_spiral_canvas()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时钟主窗口模块
实现ClockWindow主窗口类，整合螺旋画布组件和应用框架

功能包括：
1. 主窗口创建和布局管理
2. 螺旋画布组件集成
3. 菜单栏和工具栏
4. 定时器更新机制
5. 主题和设置管理
6. 应用生命周期管理
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os
from typing import Optional, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from gui.spiral_canvas import SpiralCanvas
    from utils.constants import (
        WINDOW_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT,
        CANVAS_SIZE, DEFAULT_THEME, THEMES,
        UPDATE_INTERVAL, CLOCK_UPDATE_CONFIG,
        EXTENDED_THEMES, ADVANCED_VISUAL_EFFECTS
    )
except ImportError as e:
    print(f"导入错误: {e}")
    # 提供备用常量
    WINDOW_TITLE = "时钟数字罗盘"
    WINDOW_WIDTH = WINDOW_HEIGHT = 800
    WINDOW_MIN_WIDTH = WINDOW_MIN_HEIGHT = 600
    CANVAS_SIZE = 700
    DEFAULT_THEME = {'background': '#F5F5DC', 'canvas_bg': '#DEB887'}
    THEMES = {'classic': DEFAULT_THEME}
    UPDATE_INTERVAL = 1000
    CLOCK_UPDATE_CONFIG = {'auto_update': True}


class ClockWindow:
    """
    时钟主窗口类
    
    整合螺旋画布组件，提供完整的时钟应用界面和功能
    """
    
    def __init__(self):
        """初始化时钟主窗口"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(WINDOW_TITLE)
        self.root.configure(bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        self.root.resizable(True, True)  # 允许调整大小
        
        # 应用状态
        self.current_theme = 'classic'
        self.auto_update_enabled = CLOCK_UPDATE_CONFIG.get('auto_update', True)
        self.timer_running = False
        
        # GUI组件
        self.spiral_canvas: Optional[SpiralCanvas] = None
        self.status_bar: Optional[tk.Label] = None
        self.menu_bar: Optional[tk.Menu] = None
        
        # 初始化界面
        self.setup_ui()
        self.setup_menu()
        self.setup_events()
        
        # 设置窗口属性
        self.setup_window_properties()
        self.center_window()
        
        # 启动定时更新
        self.start_timer()
    
    def setup_ui(self) -> None:
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建标题
        title_label = tk.Label(
            main_frame,
            text=WINDOW_TITLE,
            font=('Arial', 18, 'bold'),
            bg=DEFAULT_THEME.get('background', '#F5F5DC'),
            fg=DEFAULT_THEME.get('text_primary', '#000000')
        )
        title_label.pack(pady=(0, 20))
        
        # 创建螺旋画布
        canvas_frame = tk.Frame(main_frame, bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        self.spiral_canvas = SpiralCanvas(canvas_frame, CANVAS_SIZE, DEFAULT_THEME)
        self.spiral_canvas.get_canvas().pack()
        
        # 创建控制面板
        self.create_control_panel(main_frame)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_control_panel(self, parent) -> None:
        """创建控制面板"""
        control_frame = tk.Frame(parent, bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        control_frame.pack(fill=tk.X, pady=(20, 0))
        
        # 左侧：时间信息
        info_frame = tk.Frame(control_frame, bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        info_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.time_info_label = tk.Label(
            info_frame,
            text="",
            font=('Arial', 12),
            bg=DEFAULT_THEME.get('background', '#F5F5DC'),
            fg=DEFAULT_THEME.get('text_primary', '#000000'),
            justify=tk.LEFT
        )
        self.time_info_label.pack(anchor=tk.W)
        
        # 右侧：控制按钮
        button_frame = tk.Frame(control_frame, bg=DEFAULT_THEME.get('background', '#F5F5DC'))
        button_frame.pack(side=tk.RIGHT)
        
        # 强制更新按钮
        tk.Button(
            button_frame,
            text="强制更新",
            command=self.force_update,
            font=('Arial', 10),
            width=10
        ).pack(side=tk.LEFT, padx=5)
        
        # 切换自动更新按钮
        self.auto_update_button = tk.Button(
            button_frame,
            text="暂停更新",
            command=self.toggle_auto_update,
            font=('Arial', 10),
            width=10
        )
        self.auto_update_button.pack(side=tk.LEFT, padx=5)
    
    def create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = tk.Label(
            self.root,
            text="就绪",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#E0E0E0',
            font=('Arial', 9)
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def setup_menu(self) -> None:
        """设置菜单栏"""
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)
        
        # 文件菜单
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="退出", command=self.on_exit, accelerator="Ctrl+Q")
        
        # 视图菜单
        view_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="视图", menu=view_menu)
        
        # 主题子菜单
        theme_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="主题", menu=theme_menu)

        # 基础主题
        basic_theme_menu = tk.Menu(theme_menu, tearoff=0)
        theme_menu.add_cascade(label="基础主题", menu=basic_theme_menu)

        for theme_name in THEMES.keys():
            basic_theme_menu.add_command(
                label=theme_name.title(),
                command=lambda t=theme_name: self.change_theme(t)
            )

        # 扩展主题
        extended_theme_menu = tk.Menu(theme_menu, tearoff=0)
        theme_menu.add_cascade(label="扩展主题", menu=extended_theme_menu)

        for theme_name, theme_config in EXTENDED_THEMES.items():
            extended_theme_menu.add_command(
                label=theme_config.get('name', theme_name.title()),
                command=lambda t=theme_name: self.change_extended_theme(t)
            )
        
        view_menu.add_separator()

        # 基础视觉效果
        view_menu.add_command(label="切换阴影", command=self.toggle_shadows)
        view_menu.add_command(label="切换螺旋路径", command=self.toggle_spiral_paths)

        # 高级视觉效果子菜单
        effects_menu = tk.Menu(view_menu, tearoff=0)
        view_menu.add_cascade(label="高级效果", menu=effects_menu)

        effects_menu.add_command(label="切换文字发光", command=self.toggle_text_glow)
        effects_menu.add_command(label="切换渐变文字", command=self.toggle_gradient_text)
        effects_menu.add_command(label="切换脉冲动画", command=self.toggle_pulse_animation)
        effects_menu.add_command(label="切换粒子效果", command=self.toggle_particle_effects)
        effects_menu.add_separator()
        effects_menu.add_command(label="重置所有效果", command=self.reset_all_effects)
        
        # 工具菜单
        tools_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="设置", command=self.show_settings)
        
        # 帮助菜单
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def setup_events(self) -> None:
        """设置事件绑定"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
        
        # 键盘快捷键
        self.root.bind('<Control-q>', lambda e: self.on_exit())
        self.root.bind('<F5>', lambda e: self.force_update())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())
    
    def setup_window_properties(self) -> None:
        """设置窗口属性"""
        # 设置最小尺寸
        self.root.minsize(WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT)
        
        # 设置初始大小
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        
        # 设置图标（如果有的话）
        try:
            # self.root.iconbitmap('clock_icon.ico')  # 可以添加图标文件
            pass
        except:
            pass
    
    def center_window(self) -> None:
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def start_timer(self) -> None:
        """启动定时更新"""
        self.timer_running = True
        self.update_display()
    
    def stop_timer(self) -> None:
        """停止定时更新"""
        self.timer_running = False
    
    def update_display(self) -> None:
        """更新显示"""
        if self.timer_running and self.auto_update_enabled:
            # 更新螺旋画布
            updated = self.spiral_canvas.update_time_display()
            
            # 更新时间信息
            self.update_time_info()
            
            # 更新状态栏
            if updated:
                self.status_bar.config(text="时间已更新")
            else:
                self.status_bar.config(text="等待时间变化...")
        
        # 继续定时更新
        if self.timer_running:
            self.root.after(UPDATE_INTERVAL, self.update_display)
    
    def update_time_info(self) -> None:
        """更新时间信息显示"""
        if self.spiral_canvas:
            time_info = self.spiral_canvas.get_current_time_info()
            info_text = f"当前时间：{time_info['display_text']}"
            self.time_info_label.config(text=info_text)
    
    def force_update(self) -> None:
        """强制更新"""
        if self.spiral_canvas:
            self.spiral_canvas.force_update()
            self.update_time_info()
            self.status_bar.config(text="强制更新完成")
    
    def toggle_auto_update(self) -> None:
        """切换自动更新"""
        self.auto_update_enabled = not self.auto_update_enabled
        
        if self.auto_update_enabled:
            self.auto_update_button.config(text="暂停更新")
            self.status_bar.config(text="自动更新已启用")
        else:
            self.auto_update_button.config(text="恢复更新")
            self.status_bar.config(text="自动更新已暂停")
    
    def change_theme(self, theme_name: str) -> None:
        """更改主题"""
        if theme_name in THEMES:
            self.current_theme = theme_name
            theme = THEMES[theme_name]
            
            # 更新螺旋画布主题
            if self.spiral_canvas:
                self.spiral_canvas.set_theme(theme)
            
            # 更新窗口背景
            bg_color = theme.get('background', '#F5F5DC')
            self.root.configure(bg=bg_color)
            
            # 更新其他组件颜色（这里可以扩展）
            
            self.status_bar.config(text=f"主题已切换到：{theme_name}")
    
    def toggle_shadows(self) -> None:
        """切换阴影效果"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_shadows()
            status = "启用" if self.spiral_canvas.enable_shadows else "禁用"
            self.status_bar.config(text=f"阴影效果已{status}")
    
    def toggle_spiral_paths(self) -> None:
        """切换螺旋路径"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_spiral_paths()
            status = "显示" if self.spiral_canvas.show_spiral_path else "隐藏"
            self.status_bar.config(text=f"螺旋路径已{status}")
    
    def toggle_fullscreen(self) -> None:
        """切换全屏模式"""
        current_state = self.root.attributes('-fullscreen')
        self.root.attributes('-fullscreen', not current_state)
    
    def show_settings(self) -> None:
        """显示设置对话框"""
        # 这里可以实现设置对话框
        messagebox.showinfo("设置", "设置功能将在后续版本中实现")
    
    def show_about(self) -> None:
        """显示关于对话框"""
        about_text = f"""{WINDOW_TITLE} v1.0

一个创新的螺旋式时间显示应用

功能特性：
• 螺旋式时间布局
• 实时时间更新
• 中文时间显示
• 多主题支持
• 视觉效果配置

开发：AI Assistant
年份：2025"""
        
        messagebox.showinfo("关于", about_text)
    
    def on_exit(self) -> None:
        """处理退出"""
        self.on_window_close()
    
    def on_window_close(self) -> None:
        """处理窗口关闭事件"""
        # 停止定时器
        self.stop_timer()
        
        # 退出应用
        self.root.quit()
        self.root.destroy()
    
    def run(self) -> None:
        """启动时钟窗口"""
        try:
            print(f"🕐 {WINDOW_TITLE}启动中...")
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 应用被用户中断")
        except Exception as e:
            print(f"❌ 应用运行出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            print("🔚 应用已退出")
    
    def get_root(self) -> tk.Tk:
        """获取根窗口"""
        return self.root

    def change_extended_theme(self, theme_name: str) -> None:
        """更改扩展主题"""
        if theme_name in EXTENDED_THEMES and self.spiral_canvas:
            self.current_theme = theme_name
            self.spiral_canvas.set_advanced_theme(theme_name)

            # 更新窗口背景
            theme = EXTENDED_THEMES[theme_name]
            bg_color = theme.get('background', '#F5F5DC')
            self.root.configure(bg=bg_color)

            # 更新其他UI组件的颜色
            self.update_ui_colors(theme)

            theme_display_name = theme.get('name', theme_name)
            self.status_bar.config(text=f"主题已切换到：{theme_display_name}")

    def update_ui_colors(self, theme: Dict[str, str]) -> None:
        """更新UI组件颜色"""
        bg_color = theme.get('background', '#F5F5DC')
        text_color = theme.get('text_primary', '#000000')

        # 更新标题标签（如果存在）
        try:
            # 查找标题标签并更新颜色
            for widget in self.root.winfo_children():
                if isinstance(widget, tk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, tk.Label) and "时钟数字罗盘" in child.cget('text'):
                            child.configure(bg=bg_color, fg=text_color)
                        elif isinstance(child, tk.Frame):
                            child.configure(bg=bg_color)
                            # 递归更新子组件
                            self.update_frame_colors(child, bg_color, text_color)
        except:
            pass  # 忽略更新错误

    def update_frame_colors(self, frame: tk.Frame, bg_color: str, text_color: str) -> None:
        """递归更新框架内组件颜色"""
        try:
            frame.configure(bg=bg_color)
            for child in frame.winfo_children():
                if isinstance(child, tk.Label):
                    child.configure(bg=bg_color, fg=text_color)
                elif isinstance(child, tk.Frame):
                    self.update_frame_colors(child, bg_color, text_color)
        except:
            pass

    def toggle_text_glow(self) -> None:
        """切换文字发光效果"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_text_glow()
            status = "启用" if self.spiral_canvas.enable_text_glow else "禁用"
            self.status_bar.config(text=f"文字发光效果已{status}")

    def toggle_gradient_text(self) -> None:
        """切换渐变文字效果"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_gradient_text()
            status = "启用" if self.spiral_canvas.enable_gradient_text else "禁用"
            self.status_bar.config(text=f"渐变文字效果已{status}")

    def toggle_pulse_animation(self) -> None:
        """切换脉冲动画效果"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_pulse_animation()
            status = "启用" if self.spiral_canvas.enable_pulse_animation else "禁用"
            self.status_bar.config(text=f"脉冲动画效果已{status}")

    def toggle_particle_effects(self) -> None:
        """切换粒子效果"""
        if self.spiral_canvas:
            self.spiral_canvas.toggle_particle_effects()
            status = "启用" if self.spiral_canvas.enable_particle_effects else "禁用"
            self.status_bar.config(text=f"粒子效果已{status}")

    def reset_all_effects(self) -> None:
        """重置所有视觉效果"""
        if self.spiral_canvas:
            # 重置所有效果到默认状态
            self.spiral_canvas.enable_text_glow = False
            self.spiral_canvas.enable_gradient_text = False
            self.spiral_canvas.enable_pulse_animation = False
            self.spiral_canvas.enable_particle_effects = False
            self.spiral_canvas.enable_shadows = True

            # 清除效果
            self.spiral_canvas.stop_pulse_animation()
            self.spiral_canvas.clear_particle_effects()

            # 强制更新显示
            self.spiral_canvas.force_update()

            self.status_bar.config(text="所有视觉效果已重置")

    def show_theme_info(self) -> None:
        """显示当前主题信息"""
        if self.current_theme in EXTENDED_THEMES:
            theme = EXTENDED_THEMES[self.current_theme]
            theme_name = theme.get('name', self.current_theme)

            info_text = f"当前主题：{theme_name}\n\n"
            info_text += "主题配色：\n"
            info_text += f"• 背景色：{theme.get('background', 'N/A')}\n"
            info_text += f"• 画布色：{theme.get('canvas_bg', 'N/A')}\n"
            info_text += f"• 主文字：{theme.get('text_primary', 'N/A')}\n"
            info_text += f"• 次文字：{theme.get('text_secondary', 'N/A')}\n"
            info_text += f"• 强调色：{theme.get('accent', 'N/A')}\n"

            messagebox.showinfo("主题信息", info_text)

    def show_effects_info(self) -> None:
        """显示当前视觉效果信息"""
        if self.spiral_canvas:
            info_text = "当前视觉效果状态：\n\n"
            info_text += f"• 阴影效果：{'启用' if self.spiral_canvas.enable_shadows else '禁用'}\n"
            info_text += f"• 螺旋路径：{'显示' if self.spiral_canvas.show_spiral_path else '隐藏'}\n"
            info_text += f"• 文字发光：{'启用' if self.spiral_canvas.enable_text_glow else '禁用'}\n"
            info_text += f"• 渐变文字：{'启用' if self.spiral_canvas.enable_gradient_text else '禁用'}\n"
            info_text += f"• 脉冲动画：{'启用' if self.spiral_canvas.enable_pulse_animation else '禁用'}\n"
            info_text += f"• 粒子效果：{'启用' if self.spiral_canvas.enable_particle_effects else '禁用'}\n"

            messagebox.showinfo("视觉效果信息", info_text)


# 工具函数

def create_clock_window() -> ClockWindow:
    """
    创建时钟窗口实例
    
    Returns:
        ClockWindow: 时钟窗口实例
    """
    return ClockWindow()


def main() -> None:
    """主函数"""
    try:
        # 创建并运行时钟窗口
        clock_window = create_clock_window()
        clock_window.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

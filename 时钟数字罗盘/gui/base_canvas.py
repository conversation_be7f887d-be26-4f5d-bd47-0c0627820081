#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Canvas基类模块
从五子棋游戏的BoardCanvas中抽取通用绘图功能

功能包括：
1. 基础Canvas创建和配置
2. 通用事件处理框架
3. 坐标转换工具方法
4. 基础绘图方法
5. 画布管理和清理
"""

import tkinter as tk
from typing import Callable, Optional, Tuple, Dict, Any, List
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from utils.constants import DEFAULT_THEME
except ImportError:
    # 如果导入失败，使用本地定义
    DEFAULT_THEME = {
        'background': '#F5F5DC',
        'canvas_bg': '#DEB887',
        'text_primary': '#000000',
        'text_secondary': '#8B4513',
        'accent': '#654321',
        'border': '#8B4513'
    }


class BaseCanvas:
    """
    通用Canvas基类
    
    提供基础的Canvas功能，包括事件处理、坐标转换、绘图方法等
    可以被具体的Canvas组件继承和扩展
    """
    
    def __init__(self, parent, width: int, height: int, bg_color: str = None, **kwargs):
        """
        初始化基础Canvas
        
        Args:
            parent: 父容器
            width (int): 画布宽度
            height (int): 画布高度
            bg_color (str, optional): 背景颜色，默认使用主题颜色
            **kwargs: 其他Canvas配置参数
        """
        self.parent = parent
        self.width = width
        self.height = height
        self.bg_color = bg_color or DEFAULT_THEME['canvas_bg']
        
        # 创建Canvas
        canvas_config = {
            'width': width,
            'height': height,
            'bg': self.bg_color,
            'highlightthickness': kwargs.get('highlightthickness', 1),
            'highlightbackground': kwargs.get('highlightbackground', DEFAULT_THEME['border'])
        }
        canvas_config.update(kwargs)
        
        self.canvas = tk.Canvas(parent, **canvas_config)
        
        # 存储绘制对象
        self.drawn_objects: Dict[str, List[int]] = {}  # 按类型分组的绘制对象ID
        self.object_data: Dict[int, Dict[str, Any]] = {}  # 对象数据存储
        
        # 事件回调
        self.on_click: Optional[Callable] = None
        self.on_hover: Optional[Callable] = None
        self.on_leave: Optional[Callable] = None
        
        # 初始化
        self.setup_events()
    
    def setup_events(self) -> None:
        """设置基础事件绑定"""
        self.canvas.bind('<Button-1>', self._on_canvas_click)
        self.canvas.bind('<Motion>', self._on_canvas_motion)
        self.canvas.bind('<Leave>', self._on_canvas_leave)
        self.canvas.bind('<Enter>', self._on_canvas_enter)
    
    def clear_canvas(self) -> None:
        """清空画布上的所有内容"""
        self.canvas.delete('all')
        self.drawn_objects.clear()
        self.object_data.clear()
    
    def draw_text(self, x: int, y: int, text: str, **kwargs) -> int:
        """
        绘制文字
        
        Args:
            x (int): X坐标
            y (int): Y坐标
            text (str): 文字内容
            **kwargs: 文字样式参数
            
        Returns:
            int: 文字对象ID
        """
        # 设置默认样式
        text_config = {
            'fill': kwargs.get('fill', DEFAULT_THEME['text_primary']),
            'font': kwargs.get('font', ('Arial', 12)),
            'anchor': kwargs.get('anchor', 'center')
        }
        text_config.update(kwargs)
        
        text_id = self.canvas.create_text(x, y, text=text, **text_config)
        
        # 记录对象
        self._add_object('text', text_id, {
            'x': x, 'y': y, 'text': text, 'config': text_config
        })
        
        return text_id
    
    def draw_line(self, x1: int, y1: int, x2: int, y2: int, **kwargs) -> int:
        """
        绘制直线
        
        Args:
            x1, y1 (int): 起点坐标
            x2, y2 (int): 终点坐标
            **kwargs: 线条样式参数
            
        Returns:
            int: 线条对象ID
        """
        line_config = {
            'fill': kwargs.get('fill', DEFAULT_THEME['text_primary']),
            'width': kwargs.get('width', 1)
        }
        line_config.update(kwargs)
        
        line_id = self.canvas.create_line(x1, y1, x2, y2, **line_config)
        
        # 记录对象
        self._add_object('line', line_id, {
            'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2, 'config': line_config
        })
        
        return line_id
    
    def draw_circle(self, x: int, y: int, radius: int, **kwargs) -> int:
        """
        绘制圆形
        
        Args:
            x, y (int): 圆心坐标
            radius (int): 半径
            **kwargs: 圆形样式参数
            
        Returns:
            int: 圆形对象ID
        """
        circle_config = {
            'outline': kwargs.get('outline', DEFAULT_THEME['text_primary']),
            'fill': kwargs.get('fill', ''),
            'width': kwargs.get('width', 1)
        }
        circle_config.update(kwargs)
        
        circle_id = self.canvas.create_oval(
            x - radius, y - radius,
            x + radius, y + radius,
            **circle_config
        )
        
        # 记录对象
        self._add_object('circle', circle_id, {
            'x': x, 'y': y, 'radius': radius, 'config': circle_config
        })
        
        return circle_id
    
    def draw_rectangle(self, x1: int, y1: int, x2: int, y2: int, **kwargs) -> int:
        """
        绘制矩形
        
        Args:
            x1, y1 (int): 左上角坐标
            x2, y2 (int): 右下角坐标
            **kwargs: 矩形样式参数
            
        Returns:
            int: 矩形对象ID
        """
        rect_config = {
            'outline': kwargs.get('outline', DEFAULT_THEME['text_primary']),
            'fill': kwargs.get('fill', ''),
            'width': kwargs.get('width', 1)
        }
        rect_config.update(kwargs)
        
        rect_id = self.canvas.create_rectangle(x1, y1, x2, y2, **rect_config)
        
        # 记录对象
        self._add_object('rectangle', rect_id, {
            'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2, 'config': rect_config
        })
        
        return rect_id
    
    def delete_object(self, object_id: int) -> bool:
        """
        删除指定对象
        
        Args:
            object_id (int): 对象ID
            
        Returns:
            bool: 删除是否成功
        """
        if object_id in self.object_data:
            self.canvas.delete(object_id)
            
            # 从记录中移除
            for obj_type, obj_list in self.drawn_objects.items():
                if object_id in obj_list:
                    obj_list.remove(object_id)
                    break
            
            del self.object_data[object_id]
            return True
        return False
    
    def delete_objects_by_type(self, object_type: str) -> int:
        """
        删除指定类型的所有对象
        
        Args:
            object_type (str): 对象类型
            
        Returns:
            int: 删除的对象数量
        """
        if object_type not in self.drawn_objects:
            return 0
        
        objects_to_delete = self.drawn_objects[object_type].copy()
        count = 0
        
        for obj_id in objects_to_delete:
            if self.delete_object(obj_id):
                count += 1
        
        return count
    
    def get_canvas(self) -> tk.Canvas:
        """获取Canvas对象"""
        return self.canvas
    
    def get_size(self) -> Tuple[int, int]:
        """获取画布大小"""
        return (self.width, self.height)
    
    def get_center(self) -> Tuple[int, int]:
        """获取画布中心坐标"""
        return (self.width // 2, self.height // 2)
    
    def is_point_in_canvas(self, x: int, y: int) -> bool:
        """
        检查点是否在画布范围内
        
        Args:
            x, y (int): 坐标点
            
        Returns:
            bool: 是否在范围内
        """
        return 0 <= x <= self.width and 0 <= y <= self.height
    
    def set_click_callback(self, callback: Callable) -> None:
        """设置点击回调"""
        self.on_click = callback
    
    def set_hover_callback(self, callback: Callable) -> None:
        """设置悬停回调"""
        self.on_hover = callback
    
    def set_leave_callback(self, callback: Callable) -> None:
        """设置离开回调"""
        self.on_leave = callback
    
    def update_canvas(self) -> None:
        """强制更新画布显示"""
        self.canvas.update_idletasks()
        self.canvas.update()
    
    def resize(self, new_width: int, new_height: int) -> None:
        """
        调整画布大小
        
        Args:
            new_width (int): 新宽度
            new_height (int): 新高度
        """
        self.width = new_width
        self.height = new_height
        self.canvas.config(width=new_width, height=new_height)
    
    def _add_object(self, obj_type: str, obj_id: int, obj_data: Dict[str, Any]) -> None:
        """
        添加对象到记录中
        
        Args:
            obj_type (str): 对象类型
            obj_id (int): 对象ID
            obj_data (Dict): 对象数据
        """
        if obj_type not in self.drawn_objects:
            self.drawn_objects[obj_type] = []
        
        self.drawn_objects[obj_type].append(obj_id)
        self.object_data[obj_id] = {
            'type': obj_type,
            'data': obj_data
        }
    
    def _on_canvas_click(self, event) -> None:
        """处理画布点击事件"""
        if self.on_click:
            self.on_click(event.x, event.y, event)
    
    def _on_canvas_motion(self, event) -> None:
        """处理鼠标移动事件"""
        if self.on_hover:
            self.on_hover(event.x, event.y, event)
    
    def _on_canvas_leave(self, event) -> None:
        """处理鼠标离开事件"""
        if self.on_leave:
            self.on_leave(event)
    
    def _on_canvas_enter(self, event) -> None:
        """处理鼠标进入事件"""
        # 可以在子类中重写
        pass


# 测试和验证函数
def test_base_canvas():
    """测试基础Canvas功能"""
    print("🧪 开始测试BaseCanvas...")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("BaseCanvas测试")
    root.geometry("600x500")
    
    # 创建BaseCanvas实例
    canvas = BaseCanvas(root, 500, 400)
    canvas.get_canvas().pack(padx=20, pady=20)
    
    # 测试绘图功能
    canvas.draw_text(250, 50, "BaseCanvas测试", font=('Arial', 16, 'bold'))
    canvas.draw_line(50, 100, 450, 100, fill='blue', width=2)
    canvas.draw_circle(150, 200, 30, outline='red', width=2)
    canvas.draw_rectangle(300, 150, 400, 250, outline='green', fill='lightgreen')
    
    # 设置事件回调
    def on_click(x, y, event):
        print(f"点击位置: ({x}, {y})")
        canvas.draw_circle(x, y, 5, fill='red')
    
    canvas.set_click_callback(on_click)
    
    print("✅ BaseCanvas测试窗口已创建，点击画布测试交互功能")
    
    # 运行测试（注释掉以避免阻塞）
    # root.mainloop()


if __name__ == "__main__":
    test_base_canvas()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容性测试：验证BaseCanvas不影响五子棋游戏功能
"""

import unittest
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 添加五子棋游戏目录到路径
gomoku_path = os.path.join(os.path.dirname(project_root), '五子棋游戏')
sys.path.insert(0, gomoku_path)

# 导入要测试的模块
try:
    from gui.base_canvas import BaseCanvas
    # 尝试导入五子棋游戏的组件
    from gui.components import BoardCanvas
except ImportError as e:
    print(f"导入错误: {e}")
    print("注意：此测试需要五子棋游戏项目在相邻目录")


class TestCompatibility(unittest.TestCase):
    """兼容性测试类"""
    
    def setUp(self):
        """测试前的设置"""
        import tkinter as tk
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口
    
    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_base_canvas_independence(self):
        """测试BaseCanvas的独立性"""
        # 创建BaseCanvas实例
        base_canvas = BaseCanvas(self.root, 400, 300)
        
        # 验证BaseCanvas功能正常
        self.assertIsNotNone(base_canvas.canvas)
        self.assertEqual(base_canvas.width, 400)
        self.assertEqual(base_canvas.height, 300)
        
        # 测试绘图功能
        text_id = base_canvas.draw_text(100, 100, "测试")
        self.assertIsInstance(text_id, int)
        
        line_id = base_canvas.draw_line(0, 0, 100, 100)
        self.assertIsInstance(line_id, int)
        
        # 验证对象管理
        self.assertEqual(len(base_canvas.drawn_objects), 2)
        self.assertIn('text', base_canvas.drawn_objects)
        self.assertIn('line', base_canvas.drawn_objects)
    
    @unittest.skipIf('gui.components' not in sys.modules, "五子棋游戏组件不可用")
    def test_board_canvas_still_works(self):
        """测试五子棋游戏的BoardCanvas仍然正常工作"""
        try:
            # 创建BoardCanvas实例
            board_canvas = BoardCanvas(self.root)
            
            # 验证BoardCanvas功能正常
            self.assertIsNotNone(board_canvas.canvas)
            self.assertEqual(board_canvas.size, 15)  # 默认15x15棋盘
            
            # 测试棋盘绘制
            board_canvas.draw_board()
            
            # 测试棋子绘制
            success = board_canvas.draw_piece(7, 7, 1)  # 在中心放置黑棋
            self.assertTrue(success)
            
            # 测试位置转换
            position = board_canvas.get_position_from_click(200, 200)
            self.assertIsInstance(position, tuple)
            
        except Exception as e:
            self.fail(f"BoardCanvas功能测试失败: {e}")
    
    def test_no_namespace_conflicts(self):
        """测试命名空间无冲突"""
        # 验证BaseCanvas和BoardCanvas可以同时存在
        base_canvas = BaseCanvas(self.root, 300, 300)
        
        # 检查BaseCanvas的方法不会与标准tkinter.Canvas冲突
        canvas_widget = base_canvas.get_canvas()
        
        # 验证标准Canvas方法仍然可用
        self.assertTrue(hasattr(canvas_widget, 'create_text'))
        self.assertTrue(hasattr(canvas_widget, 'create_line'))
        self.assertTrue(hasattr(canvas_widget, 'delete'))
        
        # 验证BaseCanvas的扩展方法
        self.assertTrue(hasattr(base_canvas, 'draw_text'))
        self.assertTrue(hasattr(base_canvas, 'draw_line'))
        self.assertTrue(hasattr(base_canvas, 'clear_canvas'))
    
    def test_event_handling_compatibility(self):
        """测试事件处理兼容性"""
        base_canvas = BaseCanvas(self.root, 300, 300)
        
        # 测试事件绑定不会干扰标准Canvas事件
        canvas_widget = base_canvas.get_canvas()
        
        # 验证可以设置自定义事件回调
        callback_called = False
        
        def test_callback(x, y, event):
            nonlocal callback_called
            callback_called = True
        
        base_canvas.set_click_callback(test_callback)
        
        # 验证回调已设置
        self.assertEqual(base_canvas.on_click, test_callback)
    
    def test_memory_management(self):
        """测试内存管理"""
        base_canvas = BaseCanvas(self.root, 300, 300)
        
        # 创建大量对象
        for i in range(100):
            base_canvas.draw_text(i * 3, i * 2, f"文字{i}")
            base_canvas.draw_line(i, i, i + 10, i + 10)
        
        # 验证对象被正确记录
        self.assertEqual(len(base_canvas.drawn_objects['text']), 100)
        self.assertEqual(len(base_canvas.drawn_objects['line']), 100)
        self.assertEqual(len(base_canvas.object_data), 200)
        
        # 清空画布
        base_canvas.clear_canvas()
        
        # 验证内存被正确释放
        self.assertEqual(len(base_canvas.object_data), 0)
        self.assertEqual(len(base_canvas.drawn_objects), 0)
    
    def test_inheritance_potential(self):
        """测试继承潜力"""
        # 创建一个继承BaseCanvas的测试类
        class TestCanvas(BaseCanvas):
            def __init__(self, parent, width, height):
                super().__init__(parent, width, height)
                self.custom_data = {}
            
            def draw_custom_shape(self, x, y):
                """自定义绘制方法"""
                circle_id = self.draw_circle(x, y, 10, fill='red')
                text_id = self.draw_text(x, y, "●", fill='white')
                self.custom_data[f"{x},{y}"] = [circle_id, text_id]
                return circle_id, text_id
        
        # 测试继承类
        test_canvas = TestCanvas(self.root, 300, 300)
        
        # 验证基类功能正常
        self.assertEqual(test_canvas.width, 300)
        self.assertEqual(test_canvas.height, 300)
        
        # 验证自定义功能
        circle_id, text_id = test_canvas.draw_custom_shape(100, 100)
        self.assertIsInstance(circle_id, int)
        self.assertIsInstance(text_id, int)
        self.assertIn("100,100", test_canvas.custom_data)
    
    def test_performance_baseline(self):
        """测试性能基准"""
        import time
        
        base_canvas = BaseCanvas(self.root, 500, 500)
        
        # 测试绘制性能
        start_time = time.time()
        
        for i in range(50):
            base_canvas.draw_text(i * 10, i * 8, f"文字{i}")
            base_canvas.draw_circle(i * 8, i * 6, 5)
            base_canvas.draw_line(i * 5, i * 3, i * 5 + 20, i * 3 + 20)
        
        end_time = time.time()
        draw_time = end_time - start_time
        
        # 验证绘制时间在合理范围内（应该很快）
        self.assertLess(draw_time, 1.0, "绘制时间过长")
        
        # 测试清理性能
        start_time = time.time()
        base_canvas.clear_canvas()
        end_time = time.time()
        clear_time = end_time - start_time
        
        # 验证清理时间在合理范围内
        self.assertLess(clear_time, 0.1, "清理时间过长")


def run_compatibility_tests():
    """运行所有兼容性测试"""
    print("🧪 开始运行兼容性测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestCompatibility)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有兼容性测试通过!")
        return 0
    else:
        print(f"\n❌ 兼容性测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_compatibility_tests()
    sys.exit(exit_code)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpiralCanvas单元测试
验证螺旋时钟画布组件的各项功能
"""

import unittest
import tkinter as tk
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from gui.spiral_canvas import SpiralCanvas
    from clock.time_manager import TimeManager
    from clock.spiral_calculator import SpiralCalculator
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestSpiralCanvas(unittest.TestCase):
    """SpiralCanvas测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建测试用的Tkinter根窗口
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口避免干扰
        
        # 创建SpiralCanvas实例
        self.spiral_canvas = SpiralCanvas(self.root, 400)
    
    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.spiral_canvas.size, 400)
        self.assertEqual(self.spiral_canvas.center_x, 200)
        self.assertEqual(self.spiral_canvas.center_y, 200)
        self.assertIsInstance(self.spiral_canvas.spiral_calc, SpiralCalculator)
        self.assertIsInstance(self.spiral_canvas.time_manager, TimeManager)
        self.assertIsInstance(self.spiral_canvas.time_text_objects, list)
    
    def test_canvas_creation(self):
        """测试Canvas创建"""
        canvas_widget = self.spiral_canvas.get_canvas()
        self.assertIsInstance(canvas_widget, tk.Canvas)
        self.assertEqual(int(canvas_widget['width']), 400)
        self.assertEqual(int(canvas_widget['height']), 400)
    
    def test_time_display_update(self):
        """测试时间显示更新"""
        # 第一次更新应该返回True（有变化）
        result = self.spiral_canvas.update_time_display()
        self.assertTrue(result)
        
        # 验证时间文字对象被创建
        self.assertGreater(len(self.spiral_canvas.time_text_objects), 0)
        
        # 立即再次更新应该返回False（无变化）
        result = self.spiral_canvas.update_time_display()
        self.assertFalse(result)
    
    def test_clear_time_display(self):
        """测试清除时间显示"""
        # 先更新时间显示
        self.spiral_canvas.update_time_display()
        initial_count = len(self.spiral_canvas.time_text_objects)
        self.assertGreater(initial_count, 0)
        
        # 清除时间显示
        self.spiral_canvas.clear_time_display()
        
        # 验证对象列表被清空
        self.assertEqual(len(self.spiral_canvas.time_text_objects), 0)
        self.assertEqual(len(self.spiral_canvas.shadow_objects), 0)
    
    def test_text_style_generation(self):
        """测试文字样式生成"""
        # 测试不同时间单位的样式
        hour_style = self.spiral_canvas.get_text_style('hour', 24)
        minute_style = self.spiral_canvas.get_text_style('minute', 20)
        second_style = self.spiral_canvas.get_text_style('second', 16)
        
        # 验证样式包含必要字段
        for style in [hour_style, minute_style, second_style]:
            self.assertIn('fill', style)
            self.assertIn('font', style)
            self.assertIn('anchor', style)
        
        # 验证字体大小正确
        self.assertEqual(hour_style['font'][1], 24)
        self.assertEqual(minute_style['font'][1], 20)
        self.assertEqual(second_style['font'][1], 16)
    
    def test_shadow_functionality(self):
        """测试阴影功能"""
        # 启用阴影
        self.spiral_canvas.enable_shadows = True
        self.spiral_canvas.update_time_display()
        
        # 验证阴影对象被创建
        self.assertGreater(len(self.spiral_canvas.shadow_objects), 0)
        
        # 禁用阴影
        self.spiral_canvas.enable_shadows = False
        self.spiral_canvas.update_time_display()
        
        # 验证阴影对象被清除
        self.assertEqual(len(self.spiral_canvas.shadow_objects), 0)
    
    def test_spiral_path_toggle(self):
        """测试螺旋路径切换"""
        # 初始状态应该没有螺旋路径
        initial_path_count = len(self.spiral_canvas.spiral_path_objects)
        
        # 启用螺旋路径
        self.spiral_canvas.show_spiral_path = False
        self.spiral_canvas.toggle_spiral_paths()
        
        # 验证螺旋路径被创建
        self.assertTrue(self.spiral_canvas.show_spiral_path)
        self.assertGreater(len(self.spiral_canvas.spiral_path_objects), initial_path_count)
        
        # 禁用螺旋路径
        self.spiral_canvas.toggle_spiral_paths()
        
        # 验证螺旋路径被清除
        self.assertFalse(self.spiral_canvas.show_spiral_path)
        self.assertEqual(len(self.spiral_canvas.spiral_path_objects), 0)
    
    def test_theme_setting(self):
        """测试主题设置"""
        # 定义测试主题
        test_theme = {
            'canvas_bg': '#FF0000',
            'text_primary': '#00FF00',
            'text_secondary': '#0000FF',
            'accent': '#FFFF00'
        }
        
        # 设置主题
        self.spiral_canvas.set_theme(test_theme)
        
        # 验证主题已应用
        self.assertEqual(self.spiral_canvas.theme, test_theme)
        self.assertEqual(self.spiral_canvas.canvas['bg'], '#FF0000')
    
    def test_canvas_resize(self):
        """测试画布大小调整"""
        # 调整大小
        new_size = 600
        self.spiral_canvas.resize_canvas(new_size)
        
        # 验证新大小
        self.assertEqual(self.spiral_canvas.size, new_size)
        self.assertEqual(self.spiral_canvas.center_x, new_size // 2)
        self.assertEqual(self.spiral_canvas.center_y, new_size // 2)
        
        # 验证Canvas配置已更新
        canvas_widget = self.spiral_canvas.get_canvas()
        self.assertEqual(int(canvas_widget['width']), new_size)
        self.assertEqual(int(canvas_widget['height']), new_size)
    
    def test_force_update(self):
        """测试强制更新"""
        # 先进行一次正常更新
        self.spiral_canvas.update_time_display()
        last_time = self.spiral_canvas.last_display_time
        
        # 强制更新
        self.spiral_canvas.force_update()
        
        # 验证时间缓存被重置并重新更新
        self.assertIsNotNone(self.spiral_canvas.last_display_time)
        self.assertGreater(len(self.spiral_canvas.time_text_objects), 0)
    
    def test_current_time_info(self):
        """测试获取当前时间信息"""
        time_info = self.spiral_canvas.get_current_time_info()
        
        # 验证时间信息结构
        self.assertIn('current_time', time_info)
        self.assertIn('formatted_time', time_info)
        self.assertIn('display_text', time_info)
        self.assertIn('is_changed', time_info)
        self.assertIn('timestamp', time_info)
        
        # 验证数据类型
        self.assertIsInstance(time_info['display_text'], str)
        self.assertIsInstance(time_info['is_changed'], bool)
        self.assertIsInstance(time_info['timestamp'], float)
    
    def test_optimization_toggle(self):
        """测试优化功能切换"""
        # 测试启用优化
        self.spiral_canvas.enable_optimization = True
        result = self.spiral_canvas.update_time_display()
        self.assertTrue(result)
        
        # 测试禁用优化
        self.spiral_canvas.enable_optimization = False
        self.spiral_canvas.force_update()
        
        # 验证仍能正常显示
        self.assertGreater(len(self.spiral_canvas.time_text_objects), 0)
    
    def test_shadow_toggle(self):
        """测试阴影切换"""
        # 初始状态
        initial_shadow_state = self.spiral_canvas.enable_shadows
        
        # 切换阴影
        self.spiral_canvas.toggle_shadows()
        
        # 验证状态改变
        self.assertNotEqual(self.spiral_canvas.enable_shadows, initial_shadow_state)
        
        # 再次切换
        self.spiral_canvas.toggle_shadows()
        
        # 验证状态恢复
        self.assertEqual(self.spiral_canvas.enable_shadows, initial_shadow_state)
    
    def test_integration_with_components(self):
        """测试与其他组件的集成"""
        # 验证螺旋计算器集成
        self.assertIsNotNone(self.spiral_canvas.spiral_calc)
        
        # 验证时间管理器集成
        self.assertIsNotNone(self.spiral_canvas.time_manager)
        
        # 测试完整的数据流
        time_data = self.spiral_canvas.time_manager.get_time_components_for_spiral()
        positions = self.spiral_canvas.spiral_calc.get_time_positions(time_data)
        
        # 验证数据流正常
        self.assertIsInstance(time_data, dict)
        self.assertIsInstance(positions, list)
        self.assertGreater(len(positions), 0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效主题设置
        try:
            self.spiral_canvas.set_theme(None)
            # 应该能处理None主题
        except Exception as e:
            self.fail(f"设置None主题时出现异常: {e}")
        
        # 测试无效大小调整
        try:
            self.spiral_canvas.resize_canvas(0)
            # 应该能处理边界情况
        except Exception as e:
            self.fail(f"调整为0大小时出现异常: {e}")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行SpiralCanvas单元测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSpiralCanvas)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

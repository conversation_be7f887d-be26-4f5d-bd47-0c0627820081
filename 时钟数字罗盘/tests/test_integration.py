#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试：验证时间管理器、螺旋计算器和螺旋画布的协作
"""

import unittest
import math
import sys
import os
import tkinter as tk
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from clock.time_manager import TimeManager
    from clock.spiral_calculator import SpiralCalculator
    from gui.spiral_canvas import SpiralCanvas
    from gui.base_canvas import BaseCanvas
    from gui.clock_window import ClockWindow
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestIntegration(unittest.TestCase):
    """集成测试类"""

    def setUp(self):
        """测试前的设置"""
        self.time_manager = TimeManager()
        self.spiral_calculator = SpiralCalculator(350, 350)

        # 创建GUI测试环境
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口
        self.spiral_canvas = SpiralCanvas(self.root, 400)

    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_time_to_spiral_workflow(self):
        """测试完整的时间到螺旋位置转换流程"""
        # 1. 获取时间数据
        time_data = self.time_manager.get_time_components_for_spiral()
        
        # 验证时间数据结构
        self.assertIn('hour', time_data)
        self.assertIn('minute', time_data)
        self.assertIn('second', time_data)
        
        # 2. 计算螺旋位置
        positions = self.spiral_calculator.get_time_positions(time_data)
        
        # 验证位置数据
        self.assertEqual(len(positions), 3)
        
        for position in positions:
            self.assertIn('text', position)
            self.assertIn('x', position)
            self.assertIn('y', position)
            self.assertIsInstance(position['x'], int)
            self.assertIsInstance(position['y'], int)
            
            # 验证位置在合理范围内
            self.assertGreaterEqual(position['x'], 0)
            self.assertGreaterEqual(position['y'], 0)
            self.assertLessEqual(position['x'], 700)  # 假设画布大小为700
            self.assertLessEqual(position['y'], 700)
    
    def test_specific_time_positioning(self):
        """测试特定时间的位置计算"""
        # 创建特定时间：12:30:45
        test_time = datetime(2025, 1, 1, 12, 30, 45)
        formatted_time = TimeManager.format_chinese_time(test_time)
        
        # 构建时间组件数据
        time_components = {
            'hour': {
                'text': formatted_time['hour']['text'],
                'value': formatted_time['hour']['value'],
                'angle_value': formatted_time['hour']['value'] % 12,
                'layer': 'hour'
            },
            'minute': {
                'text': formatted_time['minute']['text'],
                'value': formatted_time['minute']['value'],
                'angle_value': formatted_time['minute']['value'],
                'layer': 'minute'
            },
            'second': {
                'text': formatted_time['second']['text'],
                'value': formatted_time['second']['value'],
                'angle_value': formatted_time['second']['value'],
                'layer': 'second'
            }
        }
        
        # 计算位置
        positions = self.spiral_calculator.get_time_positions(time_components)
        
        # 验证结果
        self.assertEqual(len(positions), 3)
        
        # 验证小时位置（12点 -> 0度）
        hour_pos = next(p for p in positions if p['time_unit'] == 'hour')
        self.assertEqual(hour_pos['text'], '十二时')
        self.assertEqual(hour_pos['value'], 12)
        
        # 验证分钟位置（30分）
        minute_pos = next(p for p in positions if p['time_unit'] == 'minute')
        self.assertEqual(minute_pos['text'], '三十分')
        self.assertEqual(minute_pos['value'], 30)
        
        # 验证秒钟位置（45秒）
        second_pos = next(p for p in positions if p['time_unit'] == 'second')
        self.assertEqual(second_pos['text'], '四十五秒')
        self.assertEqual(second_pos['value'], 45)
    
    def test_position_optimization_integration(self):
        """测试位置优化的集成功能"""
        # 获取当前时间的位置
        time_data = self.time_manager.get_time_components_for_spiral()
        positions = self.spiral_calculator.get_time_positions(time_data)
        
        # 应用位置优化
        optimized_positions = self.spiral_calculator.optimize_positions_for_overlap(positions)
        
        # 验证优化结果
        self.assertEqual(len(optimized_positions), len(positions))
        
        # 检查是否有重叠
        for i, pos1 in enumerate(optimized_positions):
            for j, pos2 in enumerate(optimized_positions):
                if i >= j:
                    continue
                
                distance = math.sqrt((pos1['x'] - pos2['x'])**2 + (pos1['y'] - pos2['y'])**2)
                # 距离应该大于最小阈值（考虑字体大小）
                min_distance = max(pos1.get('font_size', 16), pos2.get('font_size', 16)) * 2
                self.assertGreaterEqual(distance, min_distance * 0.8)  # 允许一些容差
    
    def test_angle_calculation_accuracy(self):
        """测试角度计算的准确性"""
        # 测试关键时间点的角度
        test_cases = [
            # (小时, 期望角度度数)
            (0, 0),    # 12点 -> 0度
            (3, 90),   # 3点 -> 90度
            (6, 180),  # 6点 -> 180度
            (9, 270),  # 9点 -> 270度
        ]
        
        for hour, expected_degrees in test_cases:
            angle_radians = self.spiral_calculator.calculate_time_angle(hour, 'hour')
            # 减去层级偏移来获取基础角度
            base_angle = angle_radians - 0  # hour层级偏移为0
            actual_degrees = math.degrees(base_angle) % 360
            
            self.assertAlmostEqual(actual_degrees, expected_degrees, places=1,
                                 msg=f"小时 {hour} 的角度计算错误")
    
    def test_spiral_layer_separation(self):
        """测试螺旋层级分离"""
        # 获取时间数据
        time_data = self.time_manager.get_time_components_for_spiral()
        positions = self.spiral_calculator.get_time_positions(time_data)
        
        # 按层级分组
        layers = {}
        for pos in positions:
            layer = pos['layer']
            if layer not in layers:
                layers[layer] = []
            layers[layer].append(pos)
        
        # 验证每个层级都有数据
        self.assertIn('hour', layers)
        self.assertIn('minute', layers)
        self.assertIn('second', layers)
        
        # 验证不同层级的距离中心的距离不同
        center_x, center_y = 350, 350
        
        for layer_name, layer_positions in layers.items():
            for pos in layer_positions:
                distance = math.sqrt((pos['x'] - center_x)**2 + (pos['y'] - center_y)**2)
                
                # 验证距离在合理范围内
                self.assertGreater(distance, 20)   # 最小距离
                self.assertLess(distance, 400)     # 最大距离
    
    def test_real_time_simulation(self):
        """测试实时时间变化的模拟"""
        # 模拟连续的时间获取
        positions_list = []
        
        for _ in range(3):  # 模拟3次时间获取
            time_data = self.time_manager.get_time_components_for_spiral()
            positions = self.spiral_calculator.get_time_positions(time_data)
            positions_list.append(positions)
        
        # 验证每次都能正常获取位置
        for positions in positions_list:
            self.assertEqual(len(positions), 3)
            for pos in positions:
                self.assertIsInstance(pos['x'], int)
                self.assertIsInstance(pos['y'], int)
                self.assertIsInstance(pos['text'], str)
    
    def test_edge_case_times(self):
        """测试边界时间情况"""
        edge_times = [
            datetime(2025, 1, 1, 0, 0, 0),    # 午夜
            datetime(2025, 1, 1, 12, 0, 0),   # 正午
            datetime(2025, 1, 1, 23, 59, 59), # 深夜
        ]
        
        for test_time in edge_times:
            formatted_time = TimeManager.format_chinese_time(test_time)
            
            # 构建时间组件
            time_components = {
                'hour': {
                    'text': formatted_time['hour']['text'],
                    'value': formatted_time['hour']['value'],
                    'angle_value': formatted_time['hour']['value'] % 12,
                    'layer': 'hour'
                },
                'minute': {
                    'text': formatted_time['minute']['text'],
                    'value': formatted_time['minute']['value'],
                    'angle_value': formatted_time['minute']['value'],
                    'layer': 'minute'
                },
                'second': {
                    'text': formatted_time['second']['text'],
                    'value': formatted_time['second']['value'],
                    'angle_value': formatted_time['second']['value'],
                    'layer': 'second'
                }
            }
            
            # 计算位置
            positions = self.spiral_calculator.get_time_positions(time_components)
            
            # 验证边界情况下仍能正常工作
            self.assertEqual(len(positions), 3)
            for pos in positions:
                self.assertIsInstance(pos['x'], int)
                self.assertIsInstance(pos['y'], int)
                self.assertTrue(0 <= pos['x'] <= 700)
                self.assertTrue(0 <= pos['y'] <= 700)

    def test_spiral_canvas_integration(self):
        """测试螺旋画布的完整集成"""
        # 测试初始化集成
        self.assertIsInstance(self.spiral_canvas.time_manager, TimeManager)
        self.assertIsInstance(self.spiral_canvas.spiral_calc, SpiralCalculator)

        # 测试时间显示更新
        result = self.spiral_canvas.update_time_display()
        self.assertTrue(result)  # 第一次更新应该成功

        # 验证文字对象被创建
        self.assertGreater(len(self.spiral_canvas.time_text_objects), 0)

        # 测试数据一致性
        time_info = self.spiral_canvas.get_current_time_info()
        self.assertIn('display_text', time_info)
        self.assertIsInstance(time_info['display_text'], str)

    def test_full_component_chain(self):
        """测试完整的组件链"""
        # 1. 时间管理器获取时间
        time_data = self.spiral_canvas.time_manager.get_time_components_for_spiral()

        # 2. 螺旋计算器计算位置
        positions = self.spiral_canvas.spiral_calc.get_time_positions(time_data)

        # 3. 螺旋画布显示时间
        self.spiral_canvas.draw_time_texts(positions)

        # 验证整个链条工作正常
        self.assertEqual(len(time_data), 3)  # 时、分、秒
        self.assertEqual(len(positions), 3)
        self.assertGreater(len(self.spiral_canvas.time_text_objects), 0)

    def test_visual_effects_integration(self):
        """测试视觉效果集成"""
        # 测试阴影效果
        self.spiral_canvas.enable_shadows = True
        self.spiral_canvas.update_time_display()

        # 验证阴影对象被创建
        if self.spiral_canvas.enable_shadows:
            self.assertGreater(len(self.spiral_canvas.shadow_objects), 0)

        # 测试螺旋路径
        self.spiral_canvas.toggle_spiral_paths()
        if self.spiral_canvas.show_spiral_path:
            self.assertGreater(len(self.spiral_canvas.spiral_path_objects), 0)

    def test_theme_integration(self):
        """测试主题集成"""
        # 测试不同主题
        themes = [
            {'canvas_bg': '#FF0000', 'text_primary': '#FFFFFF'},
            {'canvas_bg': '#00FF00', 'text_primary': '#000000'},
            {'canvas_bg': '#0000FF', 'text_primary': '#FFFF00'}
        ]

        for theme in themes:
            self.spiral_canvas.set_theme(theme)
            self.spiral_canvas.update_time_display()

            # 验证主题应用成功
            self.assertEqual(self.spiral_canvas.theme, theme)
            self.assertEqual(self.spiral_canvas.canvas['bg'], theme['canvas_bg'])

    def test_performance_integration(self):
        """测试性能集成"""
        import time

        # 测试多次更新的性能
        start_time = time.time()

        for _ in range(10):
            self.spiral_canvas.force_update()

        end_time = time.time()
        total_time = end_time - start_time

        # 验证性能在合理范围内
        self.assertLess(total_time, 2.0, "多次更新耗时过长")

        # 测试大量对象的处理
        self.spiral_canvas.clear_canvas()

        # 创建大量测试对象
        for i in range(50):
            self.spiral_canvas.draw_text(i * 8, i * 6, f"测试{i}")

        # 验证清理性能
        start_time = time.time()
        self.spiral_canvas.clear_canvas()
        end_time = time.time()

        clear_time = end_time - start_time
        self.assertLess(clear_time, 0.5, "清理耗时过长")

    def test_complete_application_integration(self):
        """测试完整应用集成"""
        # 创建完整的时钟窗口
        clock_window = ClockWindow()
        clock_window.root.withdraw()  # 隐藏窗口避免干扰

        try:
            # 验证所有组件都正确集成
            self.assertIsInstance(clock_window.spiral_canvas, SpiralCanvas)
            self.assertIsInstance(clock_window.spiral_canvas.time_manager, TimeManager)
            self.assertIsInstance(clock_window.spiral_canvas.spiral_calc, SpiralCalculator)

            # 测试完整的数据流
            # 1. 时间管理器获取时间
            time_data = clock_window.spiral_canvas.time_manager.get_time_components_for_spiral()
            self.assertEqual(len(time_data), 3)

            # 2. 螺旋计算器计算位置
            positions = clock_window.spiral_canvas.spiral_calc.get_time_positions(time_data)
            self.assertEqual(len(positions), 3)

            # 3. 螺旋画布显示时间
            result = clock_window.spiral_canvas.update_time_display()
            self.assertIsInstance(result, bool)

            # 4. 主窗口更新显示
            clock_window.update_time_info()
            time_text = clock_window.time_info_label.cget('text')
            self.assertIn('当前时间', time_text)

            # 测试主题切换的完整流程
            clock_window.change_theme('classic')
            self.assertEqual(clock_window.current_theme, 'classic')

            # 测试视觉效果切换
            clock_window.toggle_shadows()
            clock_window.toggle_spiral_paths()

            # 测试强制更新
            clock_window.force_update()

        finally:
            # 清理资源
            clock_window.stop_timer()
            clock_window.root.destroy()

    def test_application_lifecycle(self):
        """测试应用生命周期"""
        # 创建应用
        clock_window = ClockWindow()
        clock_window.root.withdraw()

        try:
            # 验证初始状态
            self.assertTrue(clock_window.timer_running)
            self.assertTrue(clock_window.auto_update_enabled)

            # 测试暂停和恢复
            clock_window.toggle_auto_update()
            self.assertFalse(clock_window.auto_update_enabled)

            clock_window.toggle_auto_update()
            self.assertTrue(clock_window.auto_update_enabled)

            # 测试停止定时器
            clock_window.stop_timer()
            self.assertFalse(clock_window.timer_running)

            # 测试重新启动
            clock_window.start_timer()
            self.assertTrue(clock_window.timer_running)

        finally:
            # 正常关闭应用
            clock_window.stop_timer()
            clock_window.root.destroy()

    def test_error_resilience(self):
        """测试错误恢复能力"""
        clock_window = ClockWindow()
        clock_window.root.withdraw()

        try:
            # 测试组件异常情况下的恢复能力
            original_canvas = clock_window.spiral_canvas

            # 临时设置为None模拟异常
            clock_window.spiral_canvas = None

            # 这些操作应该能处理异常而不崩溃
            clock_window.force_update()
            clock_window.update_time_info()
            clock_window.toggle_shadows()
            clock_window.toggle_spiral_paths()

            # 恢复正常状态
            clock_window.spiral_canvas = original_canvas

            # 验证恢复后仍能正常工作
            result = clock_window.spiral_canvas.update_time_display()
            self.assertIsInstance(result, bool)

        finally:
            clock_window.stop_timer()
            clock_window.root.destroy()


def run_integration_tests():
    """运行所有集成测试"""
    print("🧪 开始运行集成测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有集成测试通过!")
        return 0
    else:
        print(f"\n❌ 集成测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_integration_tests()
    sys.exit(exit_code)

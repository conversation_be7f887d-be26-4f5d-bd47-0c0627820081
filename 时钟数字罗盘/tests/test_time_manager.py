#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间管理器单元测试
验证TimeManager类的各项功能
"""

import unittest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from clock.time_manager import TimeManager
except ImportError as e:
    print(f"导入错误: {e}")
    # 如果导入失败，跳过测试
    sys.exit(1)


class TestTimeManager(unittest.TestCase):
    """时间管理器测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.time_manager = TimeManager()
    
    def test_chinese_number_conversion_basic(self):
        """测试基础数字转换 (0-10)"""
        expected_results = {
            0: '零', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五',
            6: '六', 7: '七', 8: '八', 9: '九', 10: '十'
        }
        
        for num, expected in expected_results.items():
            with self.subTest(num=num):
                result = TimeManager.convert_to_chinese_number(num)
                self.assertEqual(result, expected, f"数字 {num} 转换错误")
    
    def test_chinese_number_conversion_teens(self):
        """测试十几的数字转换 (11-19)"""
        expected_results = {
            11: '十一', 12: '十二', 13: '十三', 14: '十四', 15: '十五',
            16: '十六', 17: '十七', 18: '十八', 19: '十九'
        }
        
        for num, expected in expected_results.items():
            with self.subTest(num=num):
                result = TimeManager.convert_to_chinese_number(num)
                self.assertEqual(result, expected, f"数字 {num} 转换错误")
    
    def test_chinese_number_conversion_tens(self):
        """测试整十和几十几的数字转换 (20-59)"""
        test_cases = [
            (20, '二十'), (21, '二十一'), (25, '二十五'),
            (30, '三十'), (35, '三十五'), (40, '四十'),
            (45, '四十五'), (50, '五十'), (59, '五十九')
        ]
        
        for num, expected in test_cases:
            with self.subTest(num=num):
                result = TimeManager.convert_to_chinese_number(num)
                self.assertEqual(result, expected, f"数字 {num} 转换错误")
    
    def test_chinese_number_conversion_edge_cases(self):
        """测试边界情况"""
        # 测试超出范围的数字
        with self.assertRaises(ValueError):
            TimeManager.convert_to_chinese_number(-1)
        
        with self.assertRaises(ValueError):
            TimeManager.convert_to_chinese_number(60)
    
    def test_format_chinese_time_midnight(self):
        """测试午夜时间格式化"""
        test_time = datetime(2025, 1, 1, 0, 0, 0)
        result = TimeManager.format_chinese_time(test_time)
        
        self.assertEqual(result['hour']['text'], '十二时')
        self.assertEqual(result['minute']['text'], '零分')
        self.assertEqual(result['second']['text'], '零秒')
        self.assertEqual(result['hour']['value'], 0)
    
    def test_format_chinese_time_noon(self):
        """测试正午时间格式化"""
        test_time = datetime(2025, 1, 1, 12, 0, 0)
        result = TimeManager.format_chinese_time(test_time)
        
        self.assertEqual(result['hour']['text'], '十二时')
        self.assertEqual(result['minute']['text'], '零分')
        self.assertEqual(result['second']['text'], '零秒')
        self.assertEqual(result['hour']['value'], 12)
    
    def test_format_chinese_time_afternoon(self):
        """测试下午时间格式化"""
        test_time = datetime(2025, 1, 1, 15, 25, 30)
        result = TimeManager.format_chinese_time(test_time)
        
        self.assertEqual(result['hour']['text'], '三时')  # 15点 -> 3点
        self.assertEqual(result['minute']['text'], '二十五分')
        self.assertEqual(result['second']['text'], '三十秒')
        self.assertEqual(result['hour']['value'], 15)
    
    def test_get_time_for_display(self):
        """测试获取显示时间数据"""
        result = self.time_manager.get_time_for_display()
        
        # 验证返回的数据结构
        self.assertIn('current_time', result)
        self.assertIn('formatted_time', result)
        self.assertIn('display_text', result)
        self.assertIn('is_changed', result)
        self.assertIn('timestamp', result)
        
        # 验证数据类型
        self.assertIsInstance(result['current_time'], datetime)
        self.assertIsInstance(result['formatted_time'], dict)
        self.assertIsInstance(result['display_text'], str)
        self.assertIsInstance(result['is_changed'], bool)
        self.assertIsInstance(result['timestamp'], float)
    
    def test_get_time_components_for_spiral(self):
        """测试获取螺旋组件数据"""
        result = self.time_manager.get_time_components_for_spiral()
        
        # 验证返回的数据结构
        self.assertIn('hour', result)
        self.assertIn('minute', result)
        self.assertIn('second', result)
        
        # 验证每个组件的数据结构
        for unit in ['hour', 'minute', 'second']:
            component = result[unit]
            self.assertIn('text', component)
            self.assertIn('value', component)
            self.assertIn('angle_value', component)
            self.assertIn('layer', component)
            
            # 验证角度值范围
            if unit == 'hour':
                self.assertGreaterEqual(component['angle_value'], 0)
                self.assertLessEqual(component['angle_value'], 11)
            else:  # minute 和 second
                self.assertGreaterEqual(component['angle_value'], 0)
                self.assertLessEqual(component['angle_value'], 59)
    
    def test_time_change_detection(self):
        """测试时间变化检测"""
        # 第一次调用
        result1 = self.time_manager.get_time_for_display()
        self.assertTrue(result1['is_changed'])  # 第一次应该是变化的
        
        # 立即第二次调用（同一秒内）
        result2 = self.time_manager.get_time_for_display()
        # 由于在同一秒内，可能不会变化（取决于执行速度）
        # 这里主要验证逻辑正确性
        self.assertIsInstance(result2['is_changed'], bool)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行时间管理器单元测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestTimeManager)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

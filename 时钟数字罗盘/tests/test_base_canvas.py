#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BaseCanvas单元测试
验证通用Canvas基类的各项功能
"""

import unittest
import tkinter as tk
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from gui.base_canvas import BaseCanvas
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestBaseCanvas(unittest.TestCase):
    """BaseCanvas测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建测试用的Tkinter根窗口
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口避免干扰
        
        # 创建BaseCanvas实例
        self.canvas = BaseCanvas(self.root, 400, 300)
        
        # 测试回调标志
        self.click_called = False
        self.hover_called = False
        self.leave_called = False
    
    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.canvas.width, 400)
        self.assertEqual(self.canvas.height, 300)
        self.assertIsInstance(self.canvas.canvas, tk.Canvas)
        self.assertEqual(self.canvas.get_size(), (400, 300))
        self.assertEqual(self.canvas.get_center(), (200, 150))
    
    def test_canvas_creation(self):
        """测试Canvas创建"""
        canvas_widget = self.canvas.get_canvas()
        self.assertIsInstance(canvas_widget, tk.Canvas)
        self.assertEqual(int(canvas_widget['width']), 400)
        self.assertEqual(int(canvas_widget['height']), 300)
    
    def test_draw_text(self):
        """测试文字绘制"""
        text_id = self.canvas.draw_text(100, 50, "测试文字")
        
        # 验证返回了有效的对象ID
        self.assertIsInstance(text_id, int)
        self.assertGreater(text_id, 0)
        
        # 验证对象被记录
        self.assertIn('text', self.canvas.drawn_objects)
        self.assertIn(text_id, self.canvas.drawn_objects['text'])
        self.assertIn(text_id, self.canvas.object_data)
        
        # 验证对象数据
        obj_data = self.canvas.object_data[text_id]
        self.assertEqual(obj_data['type'], 'text')
        self.assertEqual(obj_data['data']['text'], "测试文字")
        self.assertEqual(obj_data['data']['x'], 100)
        self.assertEqual(obj_data['data']['y'], 50)
    
    def test_draw_line(self):
        """测试直线绘制"""
        line_id = self.canvas.draw_line(10, 10, 100, 100)
        
        # 验证返回了有效的对象ID
        self.assertIsInstance(line_id, int)
        self.assertGreater(line_id, 0)
        
        # 验证对象被记录
        self.assertIn('line', self.canvas.drawn_objects)
        self.assertIn(line_id, self.canvas.drawn_objects['line'])
        
        # 验证对象数据
        obj_data = self.canvas.object_data[line_id]
        self.assertEqual(obj_data['type'], 'line')
        self.assertEqual(obj_data['data']['x1'], 10)
        self.assertEqual(obj_data['data']['y1'], 10)
        self.assertEqual(obj_data['data']['x2'], 100)
        self.assertEqual(obj_data['data']['y2'], 100)
    
    def test_draw_circle(self):
        """测试圆形绘制"""
        circle_id = self.canvas.draw_circle(150, 150, 25)
        
        # 验证返回了有效的对象ID
        self.assertIsInstance(circle_id, int)
        self.assertGreater(circle_id, 0)
        
        # 验证对象被记录
        self.assertIn('circle', self.canvas.drawn_objects)
        self.assertIn(circle_id, self.canvas.drawn_objects['circle'])
        
        # 验证对象数据
        obj_data = self.canvas.object_data[circle_id]
        self.assertEqual(obj_data['type'], 'circle')
        self.assertEqual(obj_data['data']['x'], 150)
        self.assertEqual(obj_data['data']['y'], 150)
        self.assertEqual(obj_data['data']['radius'], 25)
    
    def test_draw_rectangle(self):
        """测试矩形绘制"""
        rect_id = self.canvas.draw_rectangle(50, 50, 150, 100)
        
        # 验证返回了有效的对象ID
        self.assertIsInstance(rect_id, int)
        self.assertGreater(rect_id, 0)
        
        # 验证对象被记录
        self.assertIn('rectangle', self.canvas.drawn_objects)
        self.assertIn(rect_id, self.canvas.drawn_objects['rectangle'])
        
        # 验证对象数据
        obj_data = self.canvas.object_data[rect_id]
        self.assertEqual(obj_data['type'], 'rectangle')
        self.assertEqual(obj_data['data']['x1'], 50)
        self.assertEqual(obj_data['data']['y1'], 50)
        self.assertEqual(obj_data['data']['x2'], 150)
        self.assertEqual(obj_data['data']['y2'], 100)
    
    def test_delete_object(self):
        """测试对象删除"""
        # 创建一个文字对象
        text_id = self.canvas.draw_text(200, 200, "待删除文字")
        
        # 验证对象存在
        self.assertIn(text_id, self.canvas.object_data)
        
        # 删除对象
        success = self.canvas.delete_object(text_id)
        self.assertTrue(success)
        
        # 验证对象已被删除
        self.assertNotIn(text_id, self.canvas.object_data)
        self.assertNotIn(text_id, self.canvas.drawn_objects.get('text', []))
        
        # 尝试删除不存在的对象
        success = self.canvas.delete_object(99999)
        self.assertFalse(success)
    
    def test_delete_objects_by_type(self):
        """测试按类型删除对象"""
        # 创建多个不同类型的对象
        text_id1 = self.canvas.draw_text(50, 50, "文字1")
        text_id2 = self.canvas.draw_text(100, 100, "文字2")
        line_id = self.canvas.draw_line(0, 0, 50, 50)
        
        # 验证对象存在
        self.assertEqual(len(self.canvas.drawn_objects.get('text', [])), 2)
        self.assertEqual(len(self.canvas.drawn_objects.get('line', [])), 1)
        
        # 删除所有文字对象
        deleted_count = self.canvas.delete_objects_by_type('text')
        self.assertEqual(deleted_count, 2)
        
        # 验证文字对象已被删除，线条对象仍存在
        self.assertEqual(len(self.canvas.drawn_objects.get('text', [])), 0)
        self.assertEqual(len(self.canvas.drawn_objects.get('line', [])), 1)
        self.assertNotIn(text_id1, self.canvas.object_data)
        self.assertNotIn(text_id2, self.canvas.object_data)
        self.assertIn(line_id, self.canvas.object_data)
    
    def test_clear_canvas(self):
        """测试清空画布"""
        # 创建多个对象
        self.canvas.draw_text(100, 100, "文字")
        self.canvas.draw_line(0, 0, 100, 100)
        self.canvas.draw_circle(200, 200, 30)
        
        # 验证对象存在
        self.assertGreater(len(self.canvas.object_data), 0)
        
        # 清空画布
        self.canvas.clear_canvas()
        
        # 验证所有对象都被清除
        self.assertEqual(len(self.canvas.object_data), 0)
        self.assertEqual(len(self.canvas.drawn_objects), 0)
    
    def test_point_in_canvas(self):
        """测试点是否在画布范围内"""
        # 测试有效点
        self.assertTrue(self.canvas.is_point_in_canvas(100, 100))
        self.assertTrue(self.canvas.is_point_in_canvas(0, 0))
        self.assertTrue(self.canvas.is_point_in_canvas(400, 300))
        
        # 测试无效点
        self.assertFalse(self.canvas.is_point_in_canvas(-1, 100))
        self.assertFalse(self.canvas.is_point_in_canvas(100, -1))
        self.assertFalse(self.canvas.is_point_in_canvas(401, 100))
        self.assertFalse(self.canvas.is_point_in_canvas(100, 301))
    
    def test_resize(self):
        """测试画布大小调整"""
        # 调整大小
        self.canvas.resize(500, 400)
        
        # 验证新大小
        self.assertEqual(self.canvas.width, 500)
        self.assertEqual(self.canvas.height, 400)
        self.assertEqual(self.canvas.get_size(), (500, 400))
        self.assertEqual(self.canvas.get_center(), (250, 200))
        
        # 验证Canvas配置已更新
        canvas_widget = self.canvas.get_canvas()
        self.assertEqual(int(canvas_widget['width']), 500)
        self.assertEqual(int(canvas_widget['height']), 400)
    
    def test_event_callbacks(self):
        """测试事件回调设置"""
        # 设置回调函数
        def click_callback(x, y, event):
            self.click_called = True
            self.click_x = x
            self.click_y = y
        
        def hover_callback(x, y, event):
            self.hover_called = True
            self.hover_x = x
            self.hover_y = y
        
        def leave_callback(event):
            self.leave_called = True
        
        self.canvas.set_click_callback(click_callback)
        self.canvas.set_hover_callback(hover_callback)
        self.canvas.set_leave_callback(leave_callback)
        
        # 验证回调已设置
        self.assertEqual(self.canvas.on_click, click_callback)
        self.assertEqual(self.canvas.on_hover, hover_callback)
        self.assertEqual(self.canvas.on_leave, leave_callback)
    
    def test_drawing_with_custom_styles(self):
        """测试自定义样式绘制"""
        # 测试自定义文字样式
        text_id = self.canvas.draw_text(
            100, 100, "自定义文字",
            fill='red', font=('Arial', 16, 'bold')
        )
        
        obj_data = self.canvas.object_data[text_id]
        self.assertEqual(obj_data['data']['config']['fill'], 'red')
        self.assertEqual(obj_data['data']['config']['font'], ('Arial', 16, 'bold'))
        
        # 测试自定义线条样式
        line_id = self.canvas.draw_line(
            0, 0, 100, 100,
            fill='blue', width=3
        )
        
        obj_data = self.canvas.object_data[line_id]
        self.assertEqual(obj_data['data']['config']['fill'], 'blue')
        self.assertEqual(obj_data['data']['config']['width'], 3)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行BaseCanvas单元测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestBaseCanvas)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

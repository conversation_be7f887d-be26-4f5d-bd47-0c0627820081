#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ClockWindow单元测试
验证时钟主窗口的各项功能
"""

import unittest
import tkinter as tk
import sys
import os
import time

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from gui.clock_window import ClockWindow, create_clock_window
    from gui.spiral_canvas import SpiralCanvas
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestClockWindow(unittest.TestCase):
    """ClockWindow测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建ClockWindow实例（但不显示）
        self.clock_window = ClockWindow()
        self.clock_window.root.withdraw()  # 隐藏窗口避免干扰
    
    def tearDown(self):
        """测试后的清理"""
        if self.clock_window and self.clock_window.root:
            self.clock_window.stop_timer()
            self.clock_window.root.destroy()
    
    def test_initialization(self):
        """测试初始化"""
        # 验证窗口创建
        self.assertIsInstance(self.clock_window.root, tk.Tk)
        self.assertEqual(self.clock_window.root.title(), "时钟数字罗盘")
        
        # 验证组件创建
        self.assertIsInstance(self.clock_window.spiral_canvas, SpiralCanvas)
        self.assertIsInstance(self.clock_window.status_bar, tk.Label)
        self.assertIsInstance(self.clock_window.menu_bar, tk.Menu)
        
        # 验证初始状态
        self.assertTrue(self.clock_window.auto_update_enabled)
        self.assertTrue(self.clock_window.timer_running)
        self.assertEqual(self.clock_window.current_theme, 'classic')
    
    def test_window_properties(self):
        """测试窗口属性"""
        root = self.clock_window.root
        
        # 验证窗口大小设置
        root.update_idletasks()
        self.assertGreaterEqual(root.winfo_width(), 600)  # 最小宽度
        self.assertGreaterEqual(root.winfo_height(), 600)  # 最小高度
        
        # 验证窗口可调整大小
        self.assertTrue(root.resizable()[0])  # 宽度可调整
        self.assertTrue(root.resizable()[1])  # 高度可调整
    
    def test_spiral_canvas_integration(self):
        """测试螺旋画布集成"""
        spiral_canvas = self.clock_window.spiral_canvas
        
        # 验证螺旋画布存在且正常工作
        self.assertIsNotNone(spiral_canvas)
        
        # 测试时间显示更新
        result = spiral_canvas.update_time_display()
        self.assertIsInstance(result, bool)
        
        # 验证时间信息获取
        time_info = spiral_canvas.get_current_time_info()
        self.assertIn('display_text', time_info)
        self.assertIsInstance(time_info['display_text'], str)
    
    def test_menu_creation(self):
        """测试菜单创建"""
        menu_bar = self.clock_window.menu_bar
        
        # 验证菜单栏存在
        self.assertIsNotNone(menu_bar)
        
        # 验证菜单项（通过检查菜单栏的配置）
        # 注意：tkinter的Menu对象不容易直接检查内容，这里主要验证创建成功
        self.assertIsInstance(menu_bar, tk.Menu)
    
    def test_timer_functionality(self):
        """测试定时器功能"""
        # 验证定时器初始状态
        self.assertTrue(self.clock_window.timer_running)
        
        # 测试停止定时器
        self.clock_window.stop_timer()
        self.assertFalse(self.clock_window.timer_running)
        
        # 测试启动定时器
        self.clock_window.start_timer()
        self.assertTrue(self.clock_window.timer_running)
    
    def test_auto_update_toggle(self):
        """测试自动更新切换"""
        # 初始状态应该是启用的
        self.assertTrue(self.clock_window.auto_update_enabled)
        
        # 切换自动更新
        self.clock_window.toggle_auto_update()
        self.assertFalse(self.clock_window.auto_update_enabled)
        
        # 再次切换
        self.clock_window.toggle_auto_update()
        self.assertTrue(self.clock_window.auto_update_enabled)
    
    def test_force_update(self):
        """测试强制更新"""
        # 记录更新前的状态
        initial_time = self.clock_window.spiral_canvas.last_display_time
        
        # 执行强制更新
        self.clock_window.force_update()
        
        # 验证更新后的状态
        # 注意：由于时间可能在同一秒内，这里主要验证方法执行不出错
        self.assertIsNotNone(self.clock_window.spiral_canvas.last_display_time)
    
    def test_theme_change(self):
        """测试主题切换"""
        # 测试切换到不同主题
        available_themes = ['classic', 'dark', 'modern']
        
        for theme_name in available_themes:
            try:
                self.clock_window.change_theme(theme_name)
                self.assertEqual(self.clock_window.current_theme, theme_name)
            except KeyError:
                # 如果主题不存在，跳过测试
                pass
    
    def test_visual_effects_toggle(self):
        """测试视觉效果切换"""
        spiral_canvas = self.clock_window.spiral_canvas
        
        # 测试阴影切换
        initial_shadow_state = spiral_canvas.enable_shadows
        self.clock_window.toggle_shadows()
        self.assertNotEqual(spiral_canvas.enable_shadows, initial_shadow_state)
        
        # 测试螺旋路径切换
        initial_path_state = spiral_canvas.show_spiral_path
        self.clock_window.toggle_spiral_paths()
        self.assertNotEqual(spiral_canvas.show_spiral_path, initial_path_state)
    
    def test_time_info_update(self):
        """测试时间信息更新"""
        # 执行时间信息更新
        self.clock_window.update_time_info()
        
        # 验证时间信息标签有内容
        time_text = self.clock_window.time_info_label.cget('text')
        self.assertIsInstance(time_text, str)
        self.assertIn('当前时间', time_text)
    
    def test_status_bar_updates(self):
        """测试状态栏更新"""
        status_bar = self.clock_window.status_bar
        
        # 测试不同的状态消息
        test_messages = ["测试消息1", "测试消息2", "测试消息3"]
        
        for message in test_messages:
            status_bar.config(text=message)
            self.assertEqual(status_bar.cget('text'), message)
    
    def test_window_center(self):
        """测试窗口居中"""
        # 执行窗口居中
        self.clock_window.center_window()
        
        # 验证窗口位置（这里主要验证方法执行不出错）
        root = self.clock_window.root
        root.update_idletasks()
        
        # 获取窗口位置
        geometry = root.geometry()
        self.assertIsInstance(geometry, str)
        self.assertIn('+', geometry)  # 位置信息应该包含+号
    
    def test_keyboard_shortcuts(self):
        """测试键盘快捷键绑定"""
        root = self.clock_window.root
        
        # 验证快捷键绑定存在
        # 注意：这里主要验证绑定设置，实际触发需要GUI交互
        bindings = root.bind()
        
        # 检查是否有绑定（具体的绑定检查比较复杂）
        self.assertIsInstance(bindings, (list, tuple, str))
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效主题设置
        try:
            self.clock_window.change_theme("invalid_theme")
            # 应该能处理无效主题而不崩溃
        except Exception as e:
            self.fail(f"处理无效主题时出现异常: {e}")
        
        # 测试在组件未初始化时的操作
        try:
            # 临时设置spiral_canvas为None
            original_canvas = self.clock_window.spiral_canvas
            self.clock_window.spiral_canvas = None
            
            # 这些操作应该能处理None情况
            self.clock_window.force_update()
            self.clock_window.toggle_shadows()
            self.clock_window.toggle_spiral_paths()
            
            # 恢复原始canvas
            self.clock_window.spiral_canvas = original_canvas
            
        except Exception as e:
            self.fail(f"处理None组件时出现异常: {e}")
    
    def test_create_clock_window_function(self):
        """测试创建时钟窗口的工具函数"""
        # 测试工具函数
        window = create_clock_window()
        window.root.withdraw()  # 隐藏窗口
        
        # 验证返回的是ClockWindow实例
        self.assertIsInstance(window, ClockWindow)
        
        # 清理
        window.stop_timer()
        window.root.destroy()


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行ClockWindow单元测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestClockWindow)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

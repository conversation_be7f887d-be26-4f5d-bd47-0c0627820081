#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
螺旋布局计算器单元测试
验证SpiralCalculator类的各项功能
"""

import unittest
import math
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from clock.spiral_calculator import SpiralCalculator
    from clock.time_manager import TimeManager
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestSpiralCalculator(unittest.TestCase):
    """螺旋计算器测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.center_x = 350
        self.center_y = 350
        self.calculator = SpiralCalculator(self.center_x, self.center_y)
        self.time_manager = TimeManager()
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.calculator.center_x, self.center_x)
        self.assertEqual(self.calculator.center_y, self.center_y)
        self.assertGreater(self.calculator.spiral_a, 0)
        self.assertGreater(self.calculator.spiral_b, 0)
    
    def test_calculate_position_basic(self):
        """测试基础位置计算"""
        # 测试0度角（正东方向）
        x, y = self.calculator.calculate_position(0)
        self.assertGreater(x, self.center_x)  # x应该大于中心x
        self.assertEqual(y, self.center_y)    # y应该等于中心y（在水平线上）
        
        # 测试90度角（正北方向）
        x, y = self.calculator.calculate_position(math.pi/2)
        self.assertEqual(x, self.center_x)    # x应该等于中心x（在垂直线上）
        self.assertLess(y, self.center_y)    # y应该小于中心y（向上）
        
        # 测试180度角（正西方向）
        x, y = self.calculator.calculate_position(math.pi)
        self.assertLess(x, self.center_x)    # x应该小于中心x
        self.assertEqual(y, self.center_y)   # y应该等于中心y（在水平线上）
    
    def test_calculate_position_with_layers(self):
        """测试多层螺旋位置计算"""
        angle = math.pi / 4  # 45度
        
        # 计算不同层级的位置
        pos_layer0 = self.calculator.calculate_position(angle, 0)
        pos_layer1 = self.calculator.calculate_position(angle, 1)
        pos_layer2 = self.calculator.calculate_position(angle, 2)
        
        # 验证不同层级的位置不同
        self.assertNotEqual(pos_layer0, pos_layer1)
        self.assertNotEqual(pos_layer1, pos_layer2)
        
        # 验证层级越高，距离中心越远
        dist0 = math.sqrt((pos_layer0[0] - self.center_x)**2 + (pos_layer0[1] - self.center_y)**2)
        dist1 = math.sqrt((pos_layer1[0] - self.center_x)**2 + (pos_layer1[1] - self.center_y)**2)
        dist2 = math.sqrt((pos_layer2[0] - self.center_x)**2 + (pos_layer2[1] - self.center_y)**2)
        
        self.assertLess(dist0, dist1)
        self.assertLess(dist1, dist2)
    
    def test_calculate_time_angle(self):
        """测试时间角度计算"""
        # 测试小时角度
        self.assertAlmostEqual(self.calculator.calculate_time_angle(0, 'hour'), 0, places=5)
        self.assertAlmostEqual(self.calculator.calculate_time_angle(3, 'hour'), math.pi/2, places=5)
        self.assertAlmostEqual(self.calculator.calculate_time_angle(6, 'hour'), math.pi, places=5)
        self.assertAlmostEqual(self.calculator.calculate_time_angle(9, 'hour'), 3*math.pi/2, places=5)
        
        # 测试分钟角度
        self.assertAlmostEqual(self.calculator.calculate_time_angle(0, 'minute'), math.pi/2, places=5)  # 有偏移
        self.assertAlmostEqual(self.calculator.calculate_time_angle(15, 'minute'), math.pi/2 + 15*6*math.pi/180, places=5)
        
        # 测试秒钟角度
        self.assertAlmostEqual(self.calculator.calculate_time_angle(0, 'second'), math.pi, places=5)  # 有偏移
        self.assertAlmostEqual(self.calculator.calculate_time_angle(30, 'second'), math.pi + 30*6*math.pi/180, places=5)
    
    def test_calculate_time_angle_invalid_unit(self):
        """测试无效时间单位"""
        with self.assertRaises(ValueError):
            self.calculator.calculate_time_angle(5, 'invalid_unit')
    
    def test_get_time_positions(self):
        """测试获取时间位置"""
        # 获取时间组件数据
        time_components = self.time_manager.get_time_components_for_spiral()
        
        # 计算位置
        positions = self.calculator.get_time_positions(time_components)
        
        # 验证返回的数据结构
        self.assertEqual(len(positions), 3)  # 应该有3个时间单位
        
        for position in positions:
            # 验证必要的字段
            self.assertIn('text', position)
            self.assertIn('x', position)
            self.assertIn('y', position)
            self.assertIn('layer', position)
            self.assertIn('angle', position)
            self.assertIn('font_size', position)
            self.assertIn('time_unit', position)
            
            # 验证数据类型
            self.assertIsInstance(position['x'], int)
            self.assertIsInstance(position['y'], int)
            self.assertIsInstance(position['angle'], float)
            self.assertIsInstance(position['font_size'], int)
            
            # 验证时间单位
            self.assertIn(position['time_unit'], ['hour', 'minute', 'second'])
    
    def test_optimize_positions_for_overlap(self):
        """测试位置重叠优化"""
        # 创建两个很近的位置
        positions = [
            {'x': 100, 'y': 100, 'layer_index': 0, 'angle': 0},
            {'x': 105, 'y': 105, 'layer_index': 0, 'angle': 0.1}  # 很近的位置
        ]
        
        # 优化位置
        optimized = self.calculator.optimize_positions_for_overlap(positions, min_distance=50)
        
        # 验证位置被调整了
        self.assertEqual(len(optimized), 2)
        
        # 计算优化后的距离
        pos1, pos2 = optimized[0], optimized[1]
        distance = math.sqrt((pos1['x'] - pos2['x'])**2 + (pos1['y'] - pos2['y'])**2)
        
        # 距离应该增加了
        original_distance = math.sqrt((100 - 105)**2 + (100 - 105)**2)
        self.assertGreater(distance, original_distance)
    
    def test_get_spiral_path_points(self):
        """测试获取螺旋路径点"""
        points = self.calculator.get_spiral_path_points(0, math.pi, 0, 0.5)
        
        # 验证返回了点列表
        self.assertIsInstance(points, list)
        self.assertGreater(len(points), 0)
        
        # 验证每个点都是坐标元组
        for point in points:
            self.assertIsInstance(point, tuple)
            self.assertEqual(len(point), 2)
            self.assertIsInstance(point[0], int)
            self.assertIsInstance(point[1], int)
    
    def test_cache_functionality(self):
        """测试缓存功能"""
        # 清除缓存
        self.calculator.clear_cache()
        self.assertEqual(len(self.calculator._position_cache), 0)
        
        # 计算位置（应该被缓存）
        pos1 = self.calculator.calculate_position(math.pi/4, 0)
        self.assertGreater(len(self.calculator._position_cache), 0)
        
        # 再次计算相同位置（应该从缓存获取）
        pos2 = self.calculator.calculate_position(math.pi/4, 0)
        self.assertEqual(pos1, pos2)
    
    def test_update_parameters(self):
        """测试参数更新"""
        original_a = self.calculator.spiral_a
        original_b = self.calculator.spiral_b
        
        # 更新参数
        new_a = original_a * 2
        new_b = original_b * 1.5
        self.calculator.update_parameters(spiral_a=new_a, spiral_b=new_b)
        
        # 验证参数已更新
        self.assertEqual(self.calculator.spiral_a, new_a)
        self.assertEqual(self.calculator.spiral_b, new_b)
        
        # 验证缓存被清除
        self.assertEqual(len(self.calculator._position_cache), 0)
    
    def test_position_consistency(self):
        """测试位置计算的一致性"""
        angle = math.pi / 3
        layer = 1
        
        # 多次计算相同参数应该得到相同结果
        pos1 = self.calculator.calculate_position(angle, layer)
        pos2 = self.calculator.calculate_position(angle, layer)
        pos3 = self.calculator.calculate_position(angle, layer)
        
        self.assertEqual(pos1, pos2)
        self.assertEqual(pos2, pos3)


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行螺旋计算器单元测试...\n")
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestSpiralCalculator)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

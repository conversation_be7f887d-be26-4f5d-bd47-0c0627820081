#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
main.py模块测试
验证程序入口点的各项功能
"""

import unittest
import sys
import os
import subprocess
from unittest.mock import patch, MagicMock
from io import StringIO

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    import main
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestMain(unittest.TestCase):
    """main.py测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 重定向标准输出以捕获打印信息
        self.held, sys.stdout = sys.stdout, StringIO()
    
    def tearDown(self):
        """测试后的清理"""
        # 恢复标准输出
        sys.stdout = self.held
    
    def test_check_environment(self):
        """测试环境检查功能"""
        # 测试环境检查函数
        result = main.check_environment()
        
        # 在正常环境下应该返回True
        self.assertIsInstance(result, bool)
        
        # 获取输出内容
        output = sys.stdout.getvalue()
        self.assertIn("正在检查运行环境", output)
    
    def test_print_welcome_message(self):
        """测试欢迎信息打印"""
        main.print_welcome_message()
        
        output = sys.stdout.getvalue()
        self.assertIn("时钟数字罗盘", output)
        self.assertIn("螺旋式时间布局", output)
        self.assertIn("实时时间更新", output)
    
    def test_print_exit_message(self):
        """测试退出信息打印"""
        main.print_exit_message()
        
        output = sys.stdout.getvalue()
        self.assertIn("感谢使用时钟数字罗盘", output)
        self.assertIn("创新的螺旋式时间显示", output)
    
    def test_show_help(self):
        """测试帮助信息显示"""
        main.show_help()
        
        output = sys.stdout.getvalue()
        self.assertIn("使用帮助", output)
        self.assertIn("python main.py", output)
        self.assertIn("功能说明", output)
        self.assertIn("系统要求", output)
    
    def test_show_version(self):
        """测试版本信息显示"""
        main.show_version()
        
        output = sys.stdout.getvalue()
        self.assertIn("时钟数字罗盘 v1.0", output)
        self.assertIn("AI Assistant", output)
        self.assertIn("技术栈", output)
    
    @patch('main.ClockWindow')
    def test_main_function_success(self, mock_clock_window):
        """测试main函数成功执行"""
        # 模拟ClockWindow
        mock_window = MagicMock()
        mock_clock_window.return_value = mock_window
        
        # 模拟check_environment返回True
        with patch('main.check_environment', return_value=True):
            result = main.main()
        
        # 验证返回值
        self.assertEqual(result, 0)
        
        # 验证ClockWindow被创建和运行
        mock_clock_window.assert_called_once()
        mock_window.run.assert_called_once()
    
    @patch('main.check_environment')
    def test_main_function_environment_fail(self, mock_check_env):
        """测试环境检查失败的情况"""
        # 模拟环境检查失败
        mock_check_env.return_value = False
        
        result = main.main()
        
        # 验证返回错误码
        self.assertEqual(result, 1)
        
        # 验证输出包含错误信息
        output = sys.stdout.getvalue()
        self.assertIn("环境检查失败", output)
    
    @patch('main.ClockWindow')
    @patch('main.check_environment')
    def test_main_function_import_error(self, mock_check_env, mock_clock_window):
        """测试导入错误的情况"""
        # 模拟环境检查通过
        mock_check_env.return_value = True
        
        # 模拟导入错误
        mock_clock_window.side_effect = ImportError("模拟导入错误")
        
        result = main.main()
        
        # 验证返回错误码
        self.assertEqual(result, 1)
        
        # 验证输出包含错误信息
        output = sys.stdout.getvalue()
        self.assertIn("模块导入失败", output)
    
    @patch('main.ClockWindow')
    @patch('main.check_environment')
    def test_main_function_runtime_error(self, mock_check_env, mock_clock_window):
        """测试运行时错误的情况"""
        # 模拟环境检查通过
        mock_check_env.return_value = True
        
        # 模拟运行时错误
        mock_window = MagicMock()
        mock_window.run.side_effect = RuntimeError("模拟运行时错误")
        mock_clock_window.return_value = mock_window
        
        result = main.main()
        
        # 验证返回错误码
        self.assertEqual(result, 1)
        
        # 验证输出包含错误信息
        output = sys.stdout.getvalue()
        self.assertIn("应用运行出错", output)
    
    @patch('main.ClockWindow')
    @patch('main.check_environment')
    def test_main_function_keyboard_interrupt(self, mock_check_env, mock_clock_window):
        """测试键盘中断的情况"""
        # 模拟环境检查通过
        mock_check_env.return_value = True
        
        # 模拟键盘中断
        mock_window = MagicMock()
        mock_window.run.side_effect = KeyboardInterrupt()
        mock_clock_window.return_value = mock_window
        
        result = main.main()
        
        # 验证返回正常码（键盘中断不算错误）
        self.assertEqual(result, 0)
        
        # 验证输出包含中断信息
        output = sys.stdout.getvalue()
        self.assertIn("应用被用户中断", output)


class TestMainCommandLine(unittest.TestCase):
    """命令行参数测试类"""
    
    def test_help_argument(self):
        """测试--help参数"""
        # 测试--help参数
        result = subprocess.run([
            sys.executable, 
            os.path.join(project_root, 'main.py'), 
            '--help'
        ], capture_output=True, text=True, timeout=10)
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("使用帮助", result.stdout)
    
    def test_version_argument(self):
        """测试--version参数"""
        # 测试--version参数
        result = subprocess.run([
            sys.executable, 
            os.path.join(project_root, 'main.py'), 
            '--version'
        ], capture_output=True, text=True, timeout=10)
        
        self.assertEqual(result.returncode, 0)
        self.assertIn("时钟数字罗盘 v1.0", result.stdout)
    
    def test_check_argument(self):
        """测试--check参数"""
        # 测试--check参数
        result = subprocess.run([
            sys.executable, 
            os.path.join(project_root, 'main.py'), 
            '--check'
        ], capture_output=True, text=True, timeout=15)
        
        # 应该正常退出（环境检查）
        self.assertIn(result.returncode, [0, 1])  # 可能成功或失败
        self.assertIn("环境检查", result.stdout)


class TestModuleIntegration(unittest.TestCase):
    """模块集成测试类"""
    
    def test_all_modules_importable(self):
        """测试所有模块都可以正常导入"""
        try:
            from gui.clock_window import ClockWindow
            from gui.spiral_canvas import SpiralCanvas
            from gui.base_canvas import BaseCanvas
            from clock.time_manager import TimeManager
            from clock.spiral_calculator import SpiralCalculator
            
            # 验证类可以实例化（不运行GUI）
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            try:
                # 测试基础组件
                time_manager = TimeManager()
                spiral_calc = SpiralCalculator(100, 100)
                base_canvas = BaseCanvas(root, 100, 100)
                spiral_canvas = SpiralCanvas(root, 100)
                
                # 验证组件功能
                self.assertIsNotNone(time_manager.get_current_time())
                self.assertIsNotNone(spiral_calc.calculate_position(0))
                self.assertIsNotNone(base_canvas.get_canvas())
                self.assertIsNotNone(spiral_canvas.get_canvas())
                
            finally:
                root.destroy()
                
        except ImportError as e:
            self.fail(f"模块导入失败: {e}")
        except Exception as e:
            self.fail(f"模块集成测试失败: {e}")
    
    def test_project_structure(self):
        """测试项目结构完整性"""
        required_files = [
            "main.py",
            "clock/__init__.py",
            "clock/time_manager.py",
            "clock/spiral_calculator.py",
            "gui/__init__.py",
            "gui/base_canvas.py",
            "gui/spiral_canvas.py",
            "gui/clock_window.py",
            "utils/__init__.py",
            "utils/constants.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            full_path = os.path.join(project_root, file_path)
            if not os.path.exists(full_path):
                missing_files.append(file_path)
        
        if missing_files:
            self.fail(f"缺少必要的项目文件: {missing_files}")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行main.py模块测试...\n")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestMain))
    suite.addTests(loader.loadTestsFromTestCase(TestMainCommandLine))
    suite.addTests(loader.loadTestsFromTestCase(TestModuleIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

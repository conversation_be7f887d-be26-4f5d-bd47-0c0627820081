#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视觉效果优化单元测试
验证高级主题和视觉效果功能
"""

import unittest
import tkinter as tk
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 导入要测试的模块
try:
    from gui.spiral_canvas import SpiralCanvas
    from gui.clock_window import ClockWindow
    from utils.constants import EXTENDED_THEMES, ADVANCED_VISUAL_EFFECTS, TEXT_EFFECTS
except ImportError as e:
    print(f"导入错误: {e}")
    sys.exit(1)


class TestVisualEffects(unittest.TestCase):
    """视觉效果测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建测试用的Tkinter根窗口
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏窗口避免干扰
        
        # 创建SpiralCanvas实例
        self.spiral_canvas = SpiralCanvas(self.root, 400)
    
    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_extended_themes_availability(self):
        """测试扩展主题可用性"""
        # 验证扩展主题配置存在
        self.assertIsInstance(EXTENDED_THEMES, dict)
        self.assertGreater(len(EXTENDED_THEMES), 3)  # 应该有超过3个主题
        
        # 验证每个主题都有必要的配置
        required_keys = ['name', 'background', 'canvas_bg', 'text_primary', 'text_secondary', 'accent']
        
        for theme_name, theme_config in EXTENDED_THEMES.items():
            with self.subTest(theme=theme_name):
                self.assertIsInstance(theme_config, dict)
                for key in required_keys:
                    self.assertIn(key, theme_config, f"主题 {theme_name} 缺少配置项: {key}")
    
    def test_advanced_visual_effects_config(self):
        """测试高级视觉效果配置"""
        # 验证高级视觉效果配置存在
        self.assertIsInstance(ADVANCED_VISUAL_EFFECTS, dict)
        
        # 验证必要的效果配置
        expected_effects = ['text_glow', 'gradient_text', 'pulse_animation', 'particle_effects']
        
        for effect in expected_effects:
            with self.subTest(effect=effect):
                self.assertIn(effect, ADVANCED_VISUAL_EFFECTS)
                effect_config = ADVANCED_VISUAL_EFFECTS[effect]
                self.assertIsInstance(effect_config, dict)
                self.assertIn('enabled', effect_config)
    
    def test_spiral_canvas_visual_effects_initialization(self):
        """测试螺旋画布视觉效果初始化"""
        # 验证视觉效果属性存在
        self.assertTrue(hasattr(self.spiral_canvas, 'enable_text_glow'))
        self.assertTrue(hasattr(self.spiral_canvas, 'enable_gradient_text'))
        self.assertTrue(hasattr(self.spiral_canvas, 'enable_pulse_animation'))
        self.assertTrue(hasattr(self.spiral_canvas, 'enable_particle_effects'))
        
        # 验证初始状态
        self.assertIsInstance(self.spiral_canvas.enable_text_glow, bool)
        self.assertIsInstance(self.spiral_canvas.enable_gradient_text, bool)
        self.assertIsInstance(self.spiral_canvas.enable_pulse_animation, bool)
        self.assertIsInstance(self.spiral_canvas.enable_particle_effects, bool)
    
    def test_text_glow_toggle(self):
        """测试文字发光效果切换"""
        # 获取初始状态
        initial_state = self.spiral_canvas.enable_text_glow
        
        # 切换效果
        self.spiral_canvas.toggle_text_glow()
        
        # 验证状态改变
        self.assertNotEqual(self.spiral_canvas.enable_text_glow, initial_state)
        
        # 再次切换
        self.spiral_canvas.toggle_text_glow()
        
        # 验证状态恢复
        self.assertEqual(self.spiral_canvas.enable_text_glow, initial_state)
    
    def test_gradient_text_toggle(self):
        """测试渐变文字效果切换"""
        initial_state = self.spiral_canvas.enable_gradient_text
        
        self.spiral_canvas.toggle_gradient_text()
        self.assertNotEqual(self.spiral_canvas.enable_gradient_text, initial_state)
        
        self.spiral_canvas.toggle_gradient_text()
        self.assertEqual(self.spiral_canvas.enable_gradient_text, initial_state)
    
    def test_pulse_animation_toggle(self):
        """测试脉冲动画效果切换"""
        initial_state = self.spiral_canvas.enable_pulse_animation
        
        self.spiral_canvas.toggle_pulse_animation()
        self.assertNotEqual(self.spiral_canvas.enable_pulse_animation, initial_state)
        
        self.spiral_canvas.toggle_pulse_animation()
        self.assertEqual(self.spiral_canvas.enable_pulse_animation, initial_state)
    
    def test_particle_effects_toggle(self):
        """测试粒子效果切换"""
        initial_state = self.spiral_canvas.enable_particle_effects
        
        self.spiral_canvas.toggle_particle_effects()
        self.assertNotEqual(self.spiral_canvas.enable_particle_effects, initial_state)
        
        self.spiral_canvas.toggle_particle_effects()
        self.assertEqual(self.spiral_canvas.enable_particle_effects, initial_state)
    
    def test_advanced_theme_setting(self):
        """测试高级主题设置"""
        # 测试设置不同的扩展主题
        for theme_name in EXTENDED_THEMES.keys():
            with self.subTest(theme=theme_name):
                try:
                    self.spiral_canvas.set_advanced_theme(theme_name)
                    # 验证主题设置不会出错
                    self.assertTrue(True)
                except Exception as e:
                    self.fail(f"设置主题 {theme_name} 时出错: {e}")
    
    def test_text_with_glow_drawing(self):
        """测试带发光效果的文字绘制"""
        # 启用发光效果
        self.spiral_canvas.enable_text_glow = True
        
        # 绘制带发光效果的文字
        style = {'fill': '#000000', 'font': ('Arial', 12)}
        object_ids = self.spiral_canvas.draw_text_with_glow(100, 100, "测试文字", style)
        
        # 验证返回了对象ID列表
        self.assertIsInstance(object_ids, list)
        self.assertGreater(len(object_ids), 0)
        
        # 验证所有ID都是整数
        for obj_id in object_ids:
            self.assertIsInstance(obj_id, int)
    
    def test_text_with_outline_drawing(self):
        """测试带轮廓的文字绘制"""
        style = {'fill': '#000000', 'font': ('Arial', 12)}
        object_ids = self.spiral_canvas.draw_text_with_outline(100, 100, "测试文字", style)
        
        # 验证返回了对象ID列表
        self.assertIsInstance(object_ids, list)
        self.assertGreater(len(object_ids), 0)
        
        # 验证所有ID都是整数
        for obj_id in object_ids:
            self.assertIsInstance(obj_id, int)
    
    def test_pulse_animation_application(self):
        """测试脉冲动画应用"""
        # 启用脉冲动画
        self.spiral_canvas.enable_pulse_animation = True
        
        # 应用脉冲动画
        try:
            self.spiral_canvas.apply_pulse_animation()
            # 验证脉冲比例在合理范围内
            self.assertGreaterEqual(self.spiral_canvas.pulse_scale, 0.5)
            self.assertLessEqual(self.spiral_canvas.pulse_scale, 2.0)
        except Exception as e:
            self.fail(f"应用脉冲动画时出错: {e}")
    
    def test_particle_effects_drawing(self):
        """测试粒子效果绘制"""
        # 启用粒子效果
        self.spiral_canvas.enable_particle_effects = True
        
        # 绘制粒子效果
        try:
            self.spiral_canvas.draw_particle_effects()
            
            # 验证粒子对象被创建
            if hasattr(self.spiral_canvas, 'particle_objects'):
                self.assertIsInstance(self.spiral_canvas.particle_objects, list)
        except Exception as e:
            self.fail(f"绘制粒子效果时出错: {e}")
    
    def test_particle_effects_clearing(self):
        """测试粒子效果清除"""
        # 先创建粒子效果
        self.spiral_canvas.enable_particle_effects = True
        self.spiral_canvas.draw_particle_effects()
        
        # 清除粒子效果
        self.spiral_canvas.clear_particle_effects()
        
        # 验证粒子对象被清除
        if hasattr(self.spiral_canvas, 'particle_objects'):
            self.assertEqual(len(self.spiral_canvas.particle_objects), 0)


class TestClockWindowVisualEffects(unittest.TestCase):
    """时钟窗口视觉效果测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建ClockWindow实例（但不显示）
        self.clock_window = ClockWindow()
        self.clock_window.root.withdraw()  # 隐藏窗口避免干扰
    
    def tearDown(self):
        """测试后的清理"""
        if self.clock_window and self.clock_window.root:
            self.clock_window.stop_timer()
            self.clock_window.root.destroy()
    
    def test_extended_theme_change(self):
        """测试扩展主题切换"""
        # 测试切换到不同的扩展主题
        for theme_name in EXTENDED_THEMES.keys():
            with self.subTest(theme=theme_name):
                try:
                    self.clock_window.change_extended_theme(theme_name)
                    self.assertEqual(self.clock_window.current_theme, theme_name)
                except Exception as e:
                    self.fail(f"切换到主题 {theme_name} 时出错: {e}")
    
    def test_visual_effects_methods(self):
        """测试视觉效果方法"""
        # 测试各种视觉效果切换方法
        methods_to_test = [
            'toggle_text_glow',
            'toggle_gradient_text',
            'toggle_pulse_animation',
            'toggle_particle_effects'
        ]
        
        for method_name in methods_to_test:
            with self.subTest(method=method_name):
                try:
                    method = getattr(self.clock_window, method_name)
                    method()
                    # 验证方法执行不出错
                    self.assertTrue(True)
                except Exception as e:
                    self.fail(f"执行方法 {method_name} 时出错: {e}")
    
    def test_reset_all_effects(self):
        """测试重置所有效果"""
        # 先启用一些效果
        if self.clock_window.spiral_canvas:
            self.clock_window.spiral_canvas.enable_text_glow = True
            self.clock_window.spiral_canvas.enable_particle_effects = True
        
        # 重置所有效果
        self.clock_window.reset_all_effects()
        
        # 验证效果被重置
        if self.clock_window.spiral_canvas:
            self.assertFalse(self.clock_window.spiral_canvas.enable_text_glow)
            self.assertFalse(self.clock_window.spiral_canvas.enable_particle_effects)
            self.assertTrue(self.clock_window.spiral_canvas.enable_shadows)  # 默认启用
    
    def test_ui_color_update(self):
        """测试UI颜色更新"""
        # 测试更新UI颜色
        test_theme = EXTENDED_THEMES.get('dark', {})
        
        try:
            self.clock_window.update_ui_colors(test_theme)
            # 验证方法执行不出错
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"更新UI颜色时出错: {e}")


class TestVisualEffectsIntegration(unittest.TestCase):
    """视觉效果集成测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.root = tk.Tk()
        self.root.withdraw()
        self.spiral_canvas = SpiralCanvas(self.root, 300)
    
    def tearDown(self):
        """测试后的清理"""
        if self.root:
            self.root.destroy()
    
    def test_multiple_effects_combination(self):
        """测试多种效果组合"""
        # 启用多种效果
        self.spiral_canvas.enable_shadows = True
        self.spiral_canvas.enable_text_glow = True
        self.spiral_canvas.show_spiral_path = True
        
        # 更新显示
        try:
            result = self.spiral_canvas.update_time_display()
            self.assertIsInstance(result, bool)
        except Exception as e:
            self.fail(f"多种效果组合时更新显示出错: {e}")
    
    def test_theme_and_effects_interaction(self):
        """测试主题和效果的交互"""
        # 设置霓虹主题
        self.spiral_canvas.set_advanced_theme('neon')
        
        # 启用相关效果
        self.spiral_canvas.enable_text_glow = True
        self.spiral_canvas.enable_particle_effects = True
        
        # 更新显示
        try:
            result = self.spiral_canvas.update_time_display()
            self.assertIsInstance(result, bool)
        except Exception as e:
            self.fail(f"主题和效果交互时出错: {e}")
    
    def test_performance_with_effects(self):
        """测试启用效果时的性能"""
        import time
        
        # 启用所有效果
        self.spiral_canvas.enable_shadows = True
        self.spiral_canvas.enable_text_glow = True
        self.spiral_canvas.enable_gradient_text = True
        self.spiral_canvas.enable_particle_effects = True
        
        # 测试更新性能
        start_time = time.time()
        
        for _ in range(5):
            self.spiral_canvas.force_update()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 验证性能在合理范围内（5次更新应该在2秒内完成）
        self.assertLess(total_time, 2.0, "启用所有效果时性能过慢")


def run_tests():
    """运行所有测试"""
    print("🧪 开始运行视觉效果优化测试...\n")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestVisualEffects))
    suite.addTests(loader.loadTestsFromTestCase(TestClockWindowVisualEffects))
    suite.addTests(loader.loadTestsFromTestCase(TestVisualEffectsIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
        return 0
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    exit_code = run_tests()
    sys.exit(exit_code)

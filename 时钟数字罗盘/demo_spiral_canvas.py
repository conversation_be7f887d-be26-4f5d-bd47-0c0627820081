#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
螺旋时钟画布演示程序
展示SpiralCanvas的各项功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from gui.spiral_canvas import SpiralCanvas
    from utils.constants import THEMES, DEFAULT_THEME
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此演示程序")
    sys.exit(1)


class SpiralCanvasDemo:
    """螺旋时钟画布演示类"""
    
    def __init__(self):
        """初始化演示程序"""
        self.root = tk.Tk()
        self.root.title("螺旋时钟画布演示")
        self.root.geometry("900x700")
        self.root.configure(bg='#F0F0F0')
        
        # 创建主要组件
        self.setup_ui()
        
        # 启动自动更新
        self.auto_update = True
        self.update_timer()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = tk.Frame(self.root, bg='#F0F0F0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧：螺旋画布
        canvas_frame = tk.Frame(main_frame, bg='#F0F0F0')
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 创建螺旋画布
        self.spiral_canvas = SpiralCanvas(canvas_frame, 500)
        self.spiral_canvas.get_canvas().pack(pady=10)
        
        # 右侧：控制面板
        control_frame = tk.Frame(main_frame, bg='#F0F0F0', width=200)
        control_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        control_frame.pack_propagate(False)
        
        self.setup_controls(control_frame)
        
        # 底部：状态栏
        self.setup_status_bar()
    
    def setup_controls(self, parent):
        """设置控制面板"""
        # 标题
        title_label = tk.Label(parent, text="控制面板", font=('Arial', 14, 'bold'), bg='#F0F0F0')
        title_label.pack(pady=(0, 10))
        
        # 基础控制
        basic_frame = tk.LabelFrame(parent, text="基础控制", bg='#F0F0F0')
        basic_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(basic_frame, text="强制更新", command=self.force_update).pack(fill=tk.X, pady=2)
        tk.Button(basic_frame, text="清除显示", command=self.clear_display).pack(fill=tk.X, pady=2)
        
        # 自动更新控制
        self.auto_update_var = tk.BooleanVar(value=True)
        tk.Checkbutton(basic_frame, text="自动更新", variable=self.auto_update_var,
                      command=self.toggle_auto_update, bg='#F0F0F0').pack(fill=tk.X, pady=2)
        
        # 视觉效果控制
        effects_frame = tk.LabelFrame(parent, text="视觉效果", bg='#F0F0F0')
        effects_frame.pack(fill=tk.X, pady=5)
        
        tk.Button(effects_frame, text="切换阴影", command=self.toggle_shadows).pack(fill=tk.X, pady=2)
        tk.Button(effects_frame, text="切换螺旋路径", command=self.toggle_spiral_paths).pack(fill=tk.X, pady=2)
        
        # 主题选择
        theme_frame = tk.LabelFrame(parent, text="主题选择", bg='#F0F0F0')
        theme_frame.pack(fill=tk.X, pady=5)
        
        self.theme_var = tk.StringVar(value="classic")
        for theme_name in THEMES.keys():
            tk.Radiobutton(theme_frame, text=theme_name.title(), variable=self.theme_var,
                          value=theme_name, command=self.change_theme, bg='#F0F0F0').pack(anchor=tk.W)
        
        # 大小控制
        size_frame = tk.LabelFrame(parent, text="画布大小", bg='#F0F0F0')
        size_frame.pack(fill=tk.X, pady=5)
        
        self.size_var = tk.IntVar(value=500)
        size_scale = tk.Scale(size_frame, from_=300, to=700, variable=self.size_var,
                             orient=tk.HORIZONTAL, command=self.resize_canvas, bg='#F0F0F0')
        size_scale.pack(fill=tk.X, pady=2)
        
        # 信息显示
        info_frame = tk.LabelFrame(parent, text="时间信息", bg='#F0F0F0')
        info_frame.pack(fill=tk.X, pady=5)
        
        self.time_info_label = tk.Label(info_frame, text="", font=('Arial', 10), 
                                       bg='#F0F0F0', justify=tk.LEFT, wraplength=180)
        self.time_info_label.pack(fill=tk.X, pady=2)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = tk.Label(self.root, text="就绪", relief=tk.SUNKEN, anchor=tk.W, bg='#E0E0E0')
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def update_timer(self):
        """定时更新"""
        if self.auto_update:
            # 更新螺旋画布
            updated = self.spiral_canvas.update_time_display()
            
            # 更新时间信息显示
            self.update_time_info()
            
            # 更新状态
            if updated:
                self.status_bar.config(text="时间已更新")
            else:
                self.status_bar.config(text="等待时间变化...")
        
        # 每秒更新一次
        self.root.after(1000, self.update_timer)
    
    def update_time_info(self):
        """更新时间信息显示"""
        time_info = self.spiral_canvas.get_current_time_info()
        
        info_text = f"当前时间：\n{time_info['display_text']}\n\n"
        info_text += f"时间戳：{time_info['timestamp']:.0f}\n"
        info_text += f"已变化：{'是' if time_info['is_changed'] else '否'}\n\n"
        info_text += f"文字对象：{len(self.spiral_canvas.time_text_objects)}\n"
        info_text += f"阴影对象：{len(self.spiral_canvas.shadow_objects)}\n"
        info_text += f"路径对象：{len(self.spiral_canvas.spiral_path_objects)}"
        
        self.time_info_label.config(text=info_text)
    
    def force_update(self):
        """强制更新"""
        self.spiral_canvas.force_update()
        self.status_bar.config(text="强制更新完成")
    
    def clear_display(self):
        """清除显示"""
        self.spiral_canvas.clear_time_display()
        self.status_bar.config(text="显示已清除")
    
    def toggle_auto_update(self):
        """切换自动更新"""
        self.auto_update = self.auto_update_var.get()
        status = "启用" if self.auto_update else "禁用"
        self.status_bar.config(text=f"自动更新已{status}")
    
    def toggle_shadows(self):
        """切换阴影效果"""
        self.spiral_canvas.toggle_shadows()
        status = "启用" if self.spiral_canvas.enable_shadows else "禁用"
        self.status_bar.config(text=f"阴影效果已{status}")
    
    def toggle_spiral_paths(self):
        """切换螺旋路径"""
        self.spiral_canvas.toggle_spiral_paths()
        status = "显示" if self.spiral_canvas.show_spiral_path else "隐藏"
        self.status_bar.config(text=f"螺旋路径已{status}")
    
    def change_theme(self):
        """更改主题"""
        theme_name = self.theme_var.get()
        theme = THEMES.get(theme_name, DEFAULT_THEME)
        self.spiral_canvas.set_theme(theme)
        self.status_bar.config(text=f"主题已切换到：{theme_name}")
    
    def resize_canvas(self, value):
        """调整画布大小"""
        new_size = int(value)
        self.spiral_canvas.resize_canvas(new_size)
        self.status_bar.config(text=f"画布大小已调整到：{new_size}x{new_size}")
    
    def run(self):
        """运行演示程序"""
        print("🚀 螺旋时钟画布演示程序启动")
        print("功能说明：")
        print("- 实时显示螺旋式时间")
        print("- 支持主题切换")
        print("- 可调整视觉效果")
        print("- 支持画布大小调整")
        print("\n按Ctrl+C或关闭窗口退出")
        
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 演示程序已退出")
        finally:
            self.root.quit()


def main():
    """主函数"""
    try:
        demo = SpiralCanvasDemo()
        demo.run()
    except Exception as e:
        print(f"❌ 演示程序运行出错: {e}")
        return 1
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

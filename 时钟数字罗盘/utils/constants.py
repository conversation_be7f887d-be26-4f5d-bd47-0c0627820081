#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时钟数字罗盘常量定义模块
定义螺旋布局、颜色主题、窗口尺寸等核心参数

包含：
- 螺旋布局参数
- 窗口配置参数  
- 颜色主题配置
- 字体和显示配置
- 时间格式配置
"""

import math

# ==================== 窗口配置参数 ====================

# 窗口基础配置
WINDOW_TITLE = '时钟数字罗盘'
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 800
WINDOW_MIN_WIDTH = 600
WINDOW_MIN_HEIGHT = 600

# 画布配置
CANVAS_SIZE = 700
CANVAS_MARGIN = 50

# ==================== 螺旋布局参数 ====================

# 螺旋方程参数 r = a * θ^b
SPIRAL_A = 20.0          # 螺旋起始半径系数
SPIRAL_B = 0.8           # 螺旋增长率
SPIRAL_THETA_STEP = 0.1  # 角度步进

# 螺旋中心配置
CENTER_X = CANVAS_SIZE // 2
CENTER_Y = CANVAS_SIZE // 2
CENTER_OFFSET = 30       # 中心偏移量

# 螺旋层级配置
SPIRAL_LAYERS = {
    'hour': {'layer': 0, 'angle_offset': 0},           # 时针层
    'minute': {'layer': 1, 'angle_offset': math.pi/2}, # 分针层  
    'second': {'layer': 2, 'angle_offset': math.pi}    # 秒针层
}

# 时间单位角度配置
HOUR_ANGLE_STEP = 30 * math.pi / 180    # 每小时30度
MINUTE_ANGLE_STEP = 6 * math.pi / 180   # 每分钟6度
SECOND_ANGLE_STEP = 6 * math.pi / 180   # 每秒6度

# 螺旋优化参数
SPIRAL_OPTIMIZATION = {
    'min_text_distance': 50,        # 文字间最小距离（像素）
    'overlap_detection': True,      # 启用重叠检测
    'position_cache_size': 1000,    # 位置缓存大小
    'auto_adjust_radius': True      # 自动调整半径避免重叠
}

# 螺旋绘制参数
SPIRAL_DRAWING = {
    'show_spiral_path': False,      # 是否显示螺旋路径线
    'path_color': '#CCCCCC',        # 螺旋路径颜色
    'path_width': 1,                # 螺旋路径线宽
    'path_alpha': 0.3               # 螺旋路径透明度
}

# Canvas基类配置
CANVAS_CONFIG = {
    'default_highlightthickness': 1,    # 默认边框厚度
    'default_relief': 'flat',           # 默认边框样式
    'cursor_normal': 'arrow',           # 普通光标
    'cursor_hover': 'hand2',            # 悬停光标
    'update_interval': 50               # 画布更新间隔（毫秒）
}

# 绘图对象默认样式
DRAWING_STYLES = {
    'text': {
        'font_family': 'Arial',
        'font_size': 12,
        'font_weight': 'normal'
    },
    'line': {
        'width': 1,
        'capstyle': 'round',
        'joinstyle': 'round'
    },
    'shape': {
        'outline_width': 1,
        'fill_alpha': 0.5
    }
}

# 螺旋时钟显示样式
SPIRAL_DISPLAY_STYLES = {
    'shadow': {
        'enabled': True,
        'offset_x': 2,
        'offset_y': 2,
        'color': '#888888',
        'alpha': 0.6
    },
    'text_colors': {
        'hour': {
            'classic': '#000000',
            'dark': '#FFFFFF',
            'modern': '#333333'
        },
        'minute': {
            'classic': '#8B4513',
            'dark': '#CCCCCC',
            'modern': '#666666'
        },
        'second': {
            'classic': '#654321',
            'dark': '#4A90E2',
            'modern': '#FF6B6B'
        }
    },
    'highlight': {
        'enabled': False,
        'color': '#FFD700',
        'width': 2
    }
}

# 时钟更新配置
CLOCK_UPDATE_CONFIG = {
    'auto_update': True,
    'update_interval': 1000,    # 毫秒
    'smooth_transition': False,
    'transition_duration': 300  # 毫秒
}

# 高级视觉效果配置
ADVANCED_VISUAL_EFFECTS = {
    'text_glow': {
        'enabled': False,
        'color': '#FFD700',
        'radius': 3,
        'intensity': 0.8
    },
    'gradient_text': {
        'enabled': False,
        'start_color': '#FF6B6B',
        'end_color': '#4ECDC4',
        'direction': 'vertical'  # 'vertical', 'horizontal', 'radial'
    },
    'pulse_animation': {
        'enabled': False,
        'duration': 2000,  # 毫秒
        'scale_range': (0.9, 1.1),
        'opacity_range': (0.7, 1.0)
    },
    'particle_effects': {
        'enabled': False,
        'particle_count': 20,
        'colors': ['#FFD700', '#FF6B6B', '#4ECDC4'],
        'speed': 1.0
    }
}

# 扩展主题配置
EXTENDED_THEMES = {
    'classic': {
        'name': '经典主题',
        'background': '#F5F5DC',
        'canvas_bg': '#DEB887',
        'text_primary': '#000000',
        'text_secondary': '#8B4513',
        'accent': '#654321',
        'border': '#8B4513',
        'shadow': '#888888',
        'highlight': '#FFD700'
    },
    'dark': {
        'name': '深色主题',
        'background': '#2F2F2F',
        'canvas_bg': '#404040',
        'text_primary': '#FFFFFF',
        'text_secondary': '#CCCCCC',
        'accent': '#4A90E2',
        'border': '#666666',
        'shadow': '#000000',
        'highlight': '#00BFFF'
    },
    'modern': {
        'name': '现代主题',
        'background': '#FFFFFF',
        'canvas_bg': '#F8F9FA',
        'text_primary': '#333333',
        'text_secondary': '#666666',
        'accent': '#FF6B6B',
        'border': '#E9ECEF',
        'shadow': '#CCCCCC',
        'highlight': '#FF4757'
    },
    'neon': {
        'name': '霓虹主题',
        'background': '#0D1117',
        'canvas_bg': '#161B22',
        'text_primary': '#00FF41',
        'text_secondary': '#00BFFF',
        'accent': '#FF1493',
        'border': '#30363D',
        'shadow': '#000000',
        'highlight': '#FFFF00'
    },
    'sunset': {
        'name': '日落主题',
        'background': '#FFF5E6',
        'canvas_bg': '#FFE4B5',
        'text_primary': '#8B4513',
        'text_secondary': '#CD853F',
        'accent': '#FF6347',
        'border': '#DEB887',
        'shadow': '#D2691E',
        'highlight': '#FF4500'
    },
    'ocean': {
        'name': '海洋主题',
        'background': '#E6F3FF',
        'canvas_bg': '#B3D9FF',
        'text_primary': '#003366',
        'text_secondary': '#0066CC',
        'accent': '#00BFFF',
        'border': '#87CEEB',
        'shadow': '#4682B4',
        'highlight': '#00CED1'
    }
}

# 动画效果配置
ANIMATION_EFFECTS = {
    'fade_in': {
        'duration': 500,
        'easing': 'ease_in_out',
        'steps': 20
    },
    'slide_in': {
        'duration': 300,
        'distance': 50,
        'direction': 'up',  # 'up', 'down', 'left', 'right'
        'easing': 'ease_out'
    },
    'scale_in': {
        'duration': 400,
        'from_scale': 0.5,
        'to_scale': 1.0,
        'easing': 'bounce'
    },
    'rotate_in': {
        'duration': 600,
        'from_angle': -180,
        'to_angle': 0,
        'easing': 'ease_in_out'
    }
}

# 文字效果配置
TEXT_EFFECTS = {
    'shadow': {
        'offset_x': 2,
        'offset_y': 2,
        'blur': 0,
        'color': '#888888',
        'opacity': 0.6
    },
    'outline': {
        'width': 1,
        'color': '#000000',
        'style': 'solid'  # 'solid', 'dashed', 'dotted'
    },
    'glow': {
        'radius': 5,
        'color': '#FFD700',
        'intensity': 0.8,
        'layers': 3
    },
    'emboss': {
        'depth': 2,
        'angle': 45,
        'highlight_color': '#FFFFFF',
        'shadow_color': '#000000'
    }
}

# ==================== 颜色主题配置 ====================

# 经典主题（参考五子棋游戏配色）
CLASSIC_THEME = {
    'background': '#F5F5DC',    # 米色背景
    'canvas_bg': '#DEB887',     # 浅棕色画布
    'text_primary': '#000000',  # 主要文字颜色
    'text_secondary': '#8B4513', # 次要文字颜色
    'accent': '#654321',        # 强调色
    'border': '#8B4513'         # 边框色
}

# 深色主题
DARK_THEME = {
    'background': '#2F2F2F',    # 深灰背景
    'canvas_bg': '#404040',     # 深灰画布
    'text_primary': '#FFFFFF',  # 白色文字
    'text_secondary': '#CCCCCC', # 浅灰文字
    'accent': '#4A90E2',        # 蓝色强调
    'border': '#666666'         # 灰色边框
}

# 现代主题
MODERN_THEME = {
    'background': '#FFFFFF',    # 白色背景
    'canvas_bg': '#F8F9FA',     # 浅灰画布
    'text_primary': '#333333',  # 深灰文字
    'text_secondary': '#666666', # 中灰文字
    'accent': '#FF6B6B',        # 红色强调
    'border': '#E9ECEF'         # 浅灰边框
}

# 默认主题
DEFAULT_THEME = CLASSIC_THEME

# 主题映射
THEMES = {
    'classic': CLASSIC_THEME,
    'dark': DARK_THEME,
    'modern': MODERN_THEME
}

# ==================== 字体和显示配置 ====================

# 字体配置
FONT_FAMILY = 'Arial'
FONT_SIZES = {
    'hour': 24,      # 小时字体大小
    'minute': 20,    # 分钟字体大小  
    'second': 16,    # 秒钟字体大小
    'title': 28      # 标题字体大小
}

# 文字样式
TEXT_STYLES = {
    'hour': {'font': (FONT_FAMILY, FONT_SIZES['hour'], 'bold')},
    'minute': {'font': (FONT_FAMILY, FONT_SIZES['minute'], 'normal')},
    'second': {'font': (FONT_FAMILY, FONT_SIZES['second'], 'normal')},
    'title': {'font': (FONT_FAMILY, FONT_SIZES['title'], 'bold')}
}

# ==================== 时间格式配置 ====================

# 中文数字映射
CHINESE_NUMBERS = {
    0: '零', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五',
    6: '六', 7: '七', 8: '八', 9: '九', 10: '十'
}

# 时间单位
TIME_UNITS = {
    'hour': '时',
    'minute': '分', 
    'second': '秒'
}

# 更新频率（毫秒）
UPDATE_INTERVAL = 1000  # 每秒更新一次

# 时间显示配置
TIME_DISPLAY_CONFIG = {
    'use_12_hour_format': True,     # 使用12小时制
    'show_am_pm': False,            # 不显示上午/下午（中文习惯）
    'midnight_as_twelve': True,     # 午夜显示为十二时而不是零时
    'noon_as_twelve': True          # 正午显示为十二时
}

# 时间范围验证
TIME_RANGES = {
    'hour': {'min': 0, 'max': 23},
    'minute': {'min': 0, 'max': 59},
    'second': {'min': 0, 'max': 59}
}

# ==================== 动画配置 ====================

# 动画参数
ANIMATION_DURATION = 500    # 动画持续时间（毫秒）
ANIMATION_STEPS = 20        # 动画步数
ANIMATION_EASING = 'ease'   # 缓动类型

# ==================== 配置验证 ====================

def validate_constants():
    """验证常量配置的合理性"""
    assert WINDOW_WIDTH > 0, "窗口宽度必须大于0"
    assert WINDOW_HEIGHT > 0, "窗口高度必须大于0"
    assert CANVAS_SIZE > 0, "画布大小必须大于0"
    assert SPIRAL_A > 0, "螺旋系数A必须大于0"
    assert SPIRAL_B > 0, "螺旋系数B必须大于0"
    assert UPDATE_INTERVAL > 0, "更新间隔必须大于0"
    
    # 验证主题配置完整性
    for theme_name, theme in THEMES.items():
        required_keys = ['background', 'canvas_bg', 'text_primary', 'text_secondary', 'accent', 'border']
        for key in required_keys:
            assert key in theme, f"主题 {theme_name} 缺少必要的颜色配置: {key}"
    
    print("✅ 常量配置验证通过")

# 在模块导入时自动验证
if __name__ == "__main__":
    validate_constants()

"""
工具模块
包含常量定义和工具函数
"""

# 导出常量和工具函数
from .constants import (
    # 窗口配置
    WINDOW_TITLE, WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_MIN_WIDTH, WINDOW_MIN_HEIGHT,
    CANVAS_SIZE, CANVAS_MARGIN,

    # 螺旋布局参数
    SPIRAL_A, SPIRAL_B, SPIRAL_THETA_STEP,
    CENTER_X, CENTER_Y, CENTER_OFFSET,
    SPIRAL_LAYERS,
    HOUR_ANGLE_STEP, MINUTE_ANGLE_STEP, SECOND_ANGLE_STEP,
    SPIRAL_OPTIMIZATION, SPIRAL_DRAWING,

    # Canvas配置
    CANVAS_CONFIG, DRAWING_STYLES,

    # 螺旋显示样式
    SPIRAL_DISPLAY_STYLES, CLOCK_UPDATE_CONFIG,

    # 高级视觉效果
    ADVANCED_VISUAL_EFFECTS, EXTENDED_THEMES, ANIMATION_EFFECTS, TEXT_EFFECTS,

    # 颜色主题
    CLASSIC_THEME, DARK_THEME, MODERN_THEME, DEFAULT_THEME, THEMES,

    # 字体配置
    FONT_FAMILY, FONT_SIZES, TEXT_STYLES,

    # 时间格式
    CHINESE_NUMBERS, TIME_UNITS, UPDATE_INTERVAL,

    # 动画配置
    ANIMATION_DURATION, ANIMATION_STEPS, ANIMATION_EASING,

    # 验证函数
    validate_constants
)

__all__ = [
    # 窗口配置
    'WINDOW_TITLE', 'WINDOW_WIDTH', 'WINDOW_HEIGHT', 'WINDOW_MIN_WIDTH', 'WINDOW_MIN_HEIGHT',
    'CANVAS_SIZE', 'CANVAS_MARGIN',

    # 螺旋布局参数
    'SPIRAL_A', 'SPIRAL_B', 'SPIRAL_THETA_STEP',
    'CENTER_X', 'CENTER_Y', 'CENTER_OFFSET',
    'SPIRAL_LAYERS',
    'HOUR_ANGLE_STEP', 'MINUTE_ANGLE_STEP', 'SECOND_ANGLE_STEP',
    'SPIRAL_OPTIMIZATION', 'SPIRAL_DRAWING',

    # Canvas配置
    'CANVAS_CONFIG', 'DRAWING_STYLES',

    # 螺旋显示样式
    'SPIRAL_DISPLAY_STYLES', 'CLOCK_UPDATE_CONFIG',

    # 高级视觉效果
    'ADVANCED_VISUAL_EFFECTS', 'EXTENDED_THEMES', 'ANIMATION_EFFECTS', 'TEXT_EFFECTS',

    # 颜色主题
    'CLASSIC_THEME', 'DARK_THEME', 'MODERN_THEME', 'DEFAULT_THEME', 'THEMES',

    # 字体配置
    'FONT_FAMILY', 'FONT_SIZES', 'TEXT_STYLES',

    # 时间格式
    'CHINESE_NUMBERS', 'TIME_UNITS', 'UPDATE_INTERVAL',

    # 动画配置
    'ANIMATION_DURATION', 'ANIMATION_STEPS', 'ANIMATION_EASING',

    # 验证函数
    'validate_constants'
]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间管理器测试脚本
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_chinese_number_conversion():
    """测试中文数字转换功能"""
    print("🧪 测试中文数字转换...")
    
    # 本地实现的转换函数（用于测试）
    def convert_to_chinese_number(num: int) -> str:
        CHINESE_NUMBERS = {
            0: '零', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五',
            6: '六', 7: '七', 8: '八', 9: '九', 10: '十'
        }
        
        if num < 0 or num > 59:
            raise ValueError(f"数字 {num} 超出支持范围 (0-59)")
        
        # 处理0-10的基础数字
        if num <= 10:
            return CHINESE_NUMBERS[num]
        
        # 处理11-19：十一、十二...十九
        if 11 <= num <= 19:
            ones = num % 10
            return f"十{CHINESE_NUMBERS[ones]}"
        
        # 处理20-59：二十、二十一...五十九
        if 20 <= num <= 59:
            tens = num // 10
            ones = num % 10
            if ones == 0:
                return f"{CHINESE_NUMBERS[tens]}十"
            else:
                return f"{CHINESE_NUMBERS[tens]}十{CHINESE_NUMBERS[ones]}"
        
        return str(num)
    
    # 测试用例
    test_cases = [0, 1, 5, 10, 11, 15, 20, 23, 30, 45, 59]
    print("📝 测试结果:")
    
    for num in test_cases:
        try:
            result = convert_to_chinese_number(num)
            print(f"  {num:2d} -> {result}")
        except Exception as e:
            print(f"  {num:2d} -> 错误: {e}")
    
    print("✅ 中文数字转换测试完成!")

def test_time_formatting():
    """测试时间格式化功能"""
    print("\n⏰ 测试时间格式化...")
    
    from datetime import datetime
    
    # 创建测试时间
    test_times = [
        datetime(2025, 1, 1, 0, 0, 0),    # 午夜
        datetime(2025, 1, 1, 8, 30, 45),  # 上午
        datetime(2025, 1, 1, 12, 0, 0),   # 正午
        datetime(2025, 1, 1, 15, 25, 10), # 下午
        datetime(2025, 1, 1, 23, 59, 59)  # 深夜
    ]
    
    print("📝 测试结果:")
    for test_time in test_times:
        hour_str = f"{test_time.hour:02d}:{test_time.minute:02d}:{test_time.second:02d}"
        print(f"  {hour_str} -> 测试时间格式化")
    
    print("✅ 时间格式化测试完成!")

def main():
    """主测试函数"""
    print("🚀 开始时间管理器功能测试\n")
    
    try:
        test_chinese_number_conversion()
        test_time_formatting()
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

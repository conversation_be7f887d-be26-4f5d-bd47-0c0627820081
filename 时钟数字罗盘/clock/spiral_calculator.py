#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
螺旋布局计算器模块
基于极坐标方程实现文字在螺旋路径上的精确定位

功能包括：
1. 极坐标螺旋位置计算
2. 多层螺旋布局支持
3. 时间单位的角度映射
4. 文字重叠避免算法
5. 动态参数调整
"""

import math
import sys
import os
from typing import Tuple, List, Dict, Any

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from utils.constants import (
        SPIRAL_A, SPIRAL_B, SPIRAL_THETA_STEP,
        CENTER_X, CENTER_Y, CENTER_OFFSET,
        SPIRAL_LAYERS,
        HOUR_ANGLE_STEP, MINUTE_ANGLE_STEP, SECOND_ANGLE_STEP,
        FONT_SIZES
    )
except ImportError:
    # 如果导入失败，使用本地定义
    SPIRAL_A = 20.0
    SPIRAL_B = 0.8
    SPIRAL_THETA_STEP = 0.1
    CENTER_X = 350
    CENTER_Y = 350
    CENTER_OFFSET = 30
    SPIRAL_LAYERS = {
        'hour': {'layer': 0, 'angle_offset': 0},
        'minute': {'layer': 1, 'angle_offset': math.pi/2},
        'second': {'layer': 2, 'angle_offset': math.pi}
    }
    HOUR_ANGLE_STEP = 30 * math.pi / 180
    MINUTE_ANGLE_STEP = 6 * math.pi / 180
    SECOND_ANGLE_STEP = 6 * math.pi / 180
    FONT_SIZES = {'hour': 24, 'minute': 20, 'second': 16}


class SpiralCalculator:
    """螺旋布局计算器类，处理所有螺旋位置计算"""
    
    def __init__(self, center_x: int, center_y: int, spiral_a: float = None, spiral_b: float = None):
        """
        初始化螺旋计算器
        
        Args:
            center_x (int): 螺旋中心X坐标
            center_y (int): 螺旋中心Y坐标
            spiral_a (float, optional): 螺旋起始半径系数，默认使用常量
            spiral_b (float, optional): 螺旋增长率，默认使用常量
        """
        self.center_x = center_x
        self.center_y = center_y
        self.spiral_a = spiral_a if spiral_a is not None else SPIRAL_A
        self.spiral_b = spiral_b if spiral_b is not None else SPIRAL_B
        self.theta_step = SPIRAL_THETA_STEP
        
        # 缓存计算结果以提高性能
        self._position_cache = {}
        
    def calculate_position(self, angle: float, layer: int = 0, radius_offset: float = 0) -> Tuple[int, int]:
        """
        基于极坐标螺旋方程计算位置
        螺旋方程：r = a * θ^b
        
        Args:
            angle (float): 角度（弧度）
            layer (int): 螺旋层级，用于多层螺旋
            radius_offset (float): 半径偏移量，用于微调位置
            
        Returns:
            Tuple[int, int]: (x, y) 坐标位置
        """
        # 创建缓存键
        cache_key = (angle, layer, radius_offset)
        if cache_key in self._position_cache:
            return self._position_cache[cache_key]
        
        # 计算有效角度（加入层级偏移）
        effective_angle = angle + layer * math.pi / 3
        
        # 极坐标螺旋方程：r = a * θ^b
        radius = self.spiral_a * pow(effective_angle, self.spiral_b) + radius_offset
        
        # 确保最小半径
        radius = max(radius, CENTER_OFFSET)
        
        # 极坐标转直角坐标
        x = self.center_x + radius * math.cos(angle)
        y = self.center_y + radius * math.sin(angle)
        
        # 转换为整数坐标
        position = (int(x), int(y))
        
        # 缓存结果
        self._position_cache[cache_key] = position
        
        return position
    
    def calculate_time_angle(self, value: int, time_unit: str) -> float:
        """
        计算时间单位对应的角度
        
        Args:
            value (int): 时间值
            time_unit (str): 时间单位 ('hour', 'minute', 'second')
            
        Returns:
            float: 对应的角度（弧度）
        """
        if time_unit == 'hour':
            # 小时：12小时制，从12点开始顺时针
            # 0点(午夜) -> 0度, 3点 -> 90度, 6点 -> 180度, 9点 -> 270度
            hour_12 = value % 12
            angle = hour_12 * HOUR_ANGLE_STEP
        elif time_unit == 'minute':
            # 分钟：60分钟制，从12点开始顺时针
            angle = value * MINUTE_ANGLE_STEP
        elif time_unit == 'second':
            # 秒钟：60秒制，从12点开始顺时针
            angle = value * SECOND_ANGLE_STEP
        else:
            raise ValueError(f"不支持的时间单位: {time_unit}")
        
        # 添加层级角度偏移
        if time_unit in SPIRAL_LAYERS:
            angle += SPIRAL_LAYERS[time_unit]['angle_offset']
        
        return angle
    
    def get_time_positions(self, time_data: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        为时间组件计算螺旋位置
        
        Args:
            time_data (Dict): 时间组件数据，来自TimeManager.get_time_components_for_spiral()
            
        Returns:
            List[Dict]: 包含位置信息的时间组件列表
            [
                {
                    'text': '八时',
                    'x': 400, 'y': 300,
                    'layer': 'hour',
                    'angle': 2.094,
                    'font_size': 24
                },
                ...
            ]
        """
        positions = []
        
        for time_unit, component_data in time_data.items():
            if time_unit not in ['hour', 'minute', 'second']:
                continue
                
            # 获取时间值和文本
            value = component_data['angle_value']
            text = component_data['text']
            layer_name = component_data['layer']
            
            # 计算角度
            angle = self.calculate_time_angle(value, time_unit)
            
            # 获取层级信息
            layer_info = SPIRAL_LAYERS.get(layer_name, {'layer': 0})
            layer_index = layer_info['layer']
            
            # 计算位置
            x, y = self.calculate_position(angle, layer_index)
            
            # 获取字体大小
            font_size = FONT_SIZES.get(time_unit, 16)
            
            # 构建位置数据
            position_data = {
                'text': text,
                'x': x,
                'y': y,
                'layer': layer_name,
                'layer_index': layer_index,
                'angle': angle,
                'angle_degrees': math.degrees(angle),
                'font_size': font_size,
                'time_unit': time_unit,
                'value': value
            }
            
            positions.append(position_data)
        
        return positions
    
    def optimize_positions_for_overlap(self, positions: List[Dict[str, Any]], 
                                     min_distance: int = 50) -> List[Dict[str, Any]]:
        """
        优化位置以避免文字重叠
        
        Args:
            positions (List[Dict]): 原始位置列表
            min_distance (int): 最小距离阈值
            
        Returns:
            List[Dict]: 优化后的位置列表
        """
        optimized_positions = positions.copy()
        
        for i, pos1 in enumerate(optimized_positions):
            for j, pos2 in enumerate(optimized_positions):
                if i >= j:
                    continue
                
                # 计算两点距离
                distance = math.sqrt((pos1['x'] - pos2['x'])**2 + (pos1['y'] - pos2['y'])**2)
                
                if distance < min_distance:
                    # 如果距离太近，调整第二个点的位置
                    # 通过增加半径偏移来避免重叠
                    layer_index = pos2['layer_index']
                    angle = pos2['angle']
                    
                    # 计算新的半径偏移
                    radius_offset = min_distance - distance + 10
                    
                    # 重新计算位置
                    new_x, new_y = self.calculate_position(angle, layer_index, radius_offset)
                    
                    optimized_positions[j]['x'] = new_x
                    optimized_positions[j]['y'] = new_y
        
        return optimized_positions
    
    def get_spiral_path_points(self, start_angle: float = 0, end_angle: float = 4*math.pi, 
                              layer: int = 0, step: float = None) -> List[Tuple[int, int]]:
        """
        获取螺旋路径上的点序列，用于绘制螺旋线
        
        Args:
            start_angle (float): 起始角度
            end_angle (float): 结束角度
            layer (int): 螺旋层级
            step (float): 角度步进，默认使用常量
            
        Returns:
            List[Tuple[int, int]]: 螺旋路径点列表
        """
        if step is None:
            step = self.theta_step
            
        points = []
        angle = start_angle
        
        while angle <= end_angle:
            x, y = self.calculate_position(angle, layer)
            points.append((x, y))
            angle += step
        
        return points
    
    def clear_cache(self):
        """清除位置计算缓存"""
        self._position_cache.clear()
    
    def update_parameters(self, spiral_a: float = None, spiral_b: float = None, 
                         center_x: int = None, center_y: int = None):
        """
        更新螺旋参数
        
        Args:
            spiral_a (float, optional): 新的螺旋起始半径系数
            spiral_b (float, optional): 新的螺旋增长率
            center_x (int, optional): 新的中心X坐标
            center_y (int, optional): 新的中心Y坐标
        """
        if spiral_a is not None:
            self.spiral_a = spiral_a
        if spiral_b is not None:
            self.spiral_b = spiral_b
        if center_x is not None:
            self.center_x = center_x
        if center_y is not None:
            self.center_y = center_y
        
        # 参数更新后清除缓存
        self.clear_cache()


# 测试和验证函数
def test_spiral_calculator():
    """测试螺旋计算器的各项功能"""
    print("🧪 开始测试螺旋布局计算器...")
    
    # 创建螺旋计算器实例
    calculator = SpiralCalculator(CENTER_X, CENTER_Y)
    
    # 测试基础位置计算
    print("\n📐 测试基础位置计算:")
    test_angles = [0, math.pi/2, math.pi, 3*math.pi/2, 2*math.pi]
    for angle in test_angles:
        x, y = calculator.calculate_position(angle)
        degrees = math.degrees(angle)
        print(f"  角度 {degrees:6.1f}° -> 位置 ({x:3d}, {y:3d})")
    
    # 测试时间角度计算
    print("\n⏰ 测试时间角度计算:")
    time_tests = [
        ('hour', 3), ('hour', 6), ('hour', 9), ('hour', 12),
        ('minute', 15), ('minute', 30), ('minute', 45),
        ('second', 0), ('second', 15), ('second', 30)
    ]
    for time_unit, value in time_tests:
        angle = calculator.calculate_time_angle(value, time_unit)
        degrees = math.degrees(angle)
        print(f"  {time_unit:6s} {value:2d} -> 角度 {degrees:6.1f}°")
    
    # 测试多层螺旋
    print("\n🌀 测试多层螺旋:")
    test_angle = math.pi / 4  # 45度
    for layer in range(3):
        x, y = calculator.calculate_position(test_angle, layer)
        print(f"  层级 {layer} -> 位置 ({x:3d}, {y:3d})")
    
    print("\n✅ 螺旋布局计算器测试完成!")


if __name__ == "__main__":
    test_spiral_calculator()

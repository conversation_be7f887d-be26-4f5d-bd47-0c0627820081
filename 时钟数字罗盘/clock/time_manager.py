#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间管理器模块
负责时间获取、格式化和中文转换功能

功能包括：
1. 实时时间获取
2. 数字到中文的转换（处理特殊情况）
3. 时间格式化为中文显示
4. 为螺旋显示准备数据结构
"""

from datetime import datetime
from typing import Dict, Any
import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from utils.constants import CHINESE_NUMBERS, TIME_UNITS
except ImportError:
    # 如果导入失败，使用本地定义
    CHINESE_NUMBERS = {
        0: '零', 1: '一', 2: '二', 3: '三', 4: '四', 5: '五',
        6: '六', 7: '七', 8: '八', 9: '九', 10: '十'
    }
    TIME_UNITS = {
        'hour': '时',
        'minute': '分',
        'second': '秒'
    }


class TimeManager:
    """时间管理器类，处理所有时间相关的操作"""
    
    def __init__(self):
        """初始化时间管理器"""
        self.last_time = None
        
    @staticmethod
    def get_current_time() -> datetime:
        """
        获取当前系统时间
        
        Returns:
            datetime: 当前时间对象
        """
        return datetime.now()
    
    @staticmethod
    def convert_to_chinese_number(num: int) -> str:
        """
        将数字转换为中文数字
        处理特殊情况：10->十, 11->十一, 20->二十, 21->二十一等
        
        Args:
            num (int): 要转换的数字 (0-59)
            
        Returns:
            str: 中文数字字符串
            
        Examples:
            >>> TimeManager.convert_to_chinese_number(5)
            '五'
            >>> TimeManager.convert_to_chinese_number(10)
            '十'
            >>> TimeManager.convert_to_chinese_number(15)
            '十五'
            >>> TimeManager.convert_to_chinese_number(20)
            '二十'
            >>> TimeManager.convert_to_chinese_number(23)
            '二十三'
        """
        if num < 0 or num > 59:
            raise ValueError(f"数字 {num} 超出支持范围 (0-59)")
        
        # 处理0-10的基础数字
        if num <= 10:
            return CHINESE_NUMBERS[num]
        
        # 处理11-19：十一、十二...十九
        if 11 <= num <= 19:
            ones = num % 10
            return f"十{CHINESE_NUMBERS[ones]}"
        
        # 处理20-59：二十、二十一...五十九
        if 20 <= num <= 59:
            tens = num // 10
            ones = num % 10
            if ones == 0:
                return f"{CHINESE_NUMBERS[tens]}十"
            else:
                return f"{CHINESE_NUMBERS[tens]}十{CHINESE_NUMBERS[ones]}"
        
        return str(num)  # 兜底情况
    
    @classmethod
    def format_chinese_time(cls, time_obj: datetime) -> Dict[str, Dict[str, Any]]:
        """
        将时间对象格式化为中文显示格式
        
        Args:
            time_obj (datetime): 时间对象
            
        Returns:
            Dict[str, Dict[str, Any]]: 格式化的时间数据
            {
                'hour': {'text': '八时', 'value': 8},
                'minute': {'text': '四十五分', 'value': 45},
                'second': {'text': '三十二秒', 'value': 32}
            }
        """
        hour = time_obj.hour
        minute = time_obj.minute
        second = time_obj.second
        
        # 转换为中文（处理12小时制）
        display_hour = hour
        if hour == 0:
            display_hour = 12  # 午夜显示为12点
        elif hour > 12:
            display_hour = hour - 12  # 下午时间转换为12小时制

        hour_cn = cls.convert_to_chinese_number(display_hour)
        minute_cn = cls.convert_to_chinese_number(minute)
        second_cn = cls.convert_to_chinese_number(second)

        # 生成时间文本
        hour_text = f"{hour_cn}{TIME_UNITS['hour']}"
            
        minute_text = f"{minute_cn}{TIME_UNITS['minute']}"
        second_text = f"{second_cn}{TIME_UNITS['second']}"
        
        return {
            'hour': {
                'text': hour_text,
                'value': hour,
                'chinese': hour_cn
            },
            'minute': {
                'text': minute_text,
                'value': minute,
                'chinese': minute_cn
            },
            'second': {
                'text': second_text,
                'value': second,
                'chinese': second_cn
            }
        }
    
    def get_time_for_display(self) -> Dict[str, Any]:
        """
        获取用于螺旋显示的完整时间数据
        
        Returns:
            Dict[str, Any]: 包含时间数据和元信息的字典
            {
                'current_time': datetime对象,
                'formatted_time': 格式化的时间数据,
                'display_text': '八时四十五分三十二秒',
                'is_changed': 是否与上次获取的时间不同
            }
        """
        current_time = self.get_current_time()
        formatted_time = self.format_chinese_time(current_time)
        
        # 生成完整的显示文本
        display_text = (
            f"{formatted_time['hour']['text']}"
            f"{formatted_time['minute']['text']}"
            f"{formatted_time['second']['text']}"
        )
        
        # 检查时间是否发生变化
        is_changed = (
            self.last_time is None or 
            current_time.second != self.last_time.second or
            current_time.minute != self.last_time.minute or
            current_time.hour != self.last_time.hour
        )
        
        # 更新最后记录的时间
        self.last_time = current_time
        
        return {
            'current_time': current_time,
            'formatted_time': formatted_time,
            'display_text': display_text,
            'is_changed': is_changed,
            'timestamp': current_time.timestamp()
        }
    
    def get_time_components_for_spiral(self) -> Dict[str, Dict[str, Any]]:
        """
        获取用于螺旋布局的时间组件数据
        为每个时间单位提供角度计算所需的信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 螺旋布局所需的时间组件数据
        """
        time_data = self.get_time_for_display()
        formatted_time = time_data['formatted_time']
        
        return {
            'hour': {
                'text': formatted_time['hour']['text'],
                'value': formatted_time['hour']['value'],
                'angle_value': formatted_time['hour']['value'] % 12,  # 12小时制
                'layer': 'hour'
            },
            'minute': {
                'text': formatted_time['minute']['text'],
                'value': formatted_time['minute']['value'],
                'angle_value': formatted_time['minute']['value'],
                'layer': 'minute'
            },
            'second': {
                'text': formatted_time['second']['text'],
                'value': formatted_time['second']['value'],
                'angle_value': formatted_time['second']['value'],
                'layer': 'second'
            }
        }


# 测试和验证函数
def test_time_manager():
    """测试时间管理器的各项功能"""
    print("🧪 开始测试时间管理器...")
    
    # 测试中文数字转换
    test_cases = [0, 1, 5, 10, 11, 15, 20, 23, 30, 45, 59]
    print("\n📝 测试中文数字转换:")
    for num in test_cases:
        try:
            result = TimeManager.convert_to_chinese_number(num)
            print(f"  {num:2d} -> {result}")
        except Exception as e:
            print(f"  {num:2d} -> 错误: {e}")
    
    # 测试时间格式化
    print("\n⏰ 测试时间格式化:")
    tm = TimeManager()
    time_data = tm.get_time_for_display()
    print(f"  当前时间: {time_data['display_text']}")
    print(f"  时间戳: {time_data['timestamp']}")
    print(f"  是否变化: {time_data['is_changed']}")
    
    # 测试螺旋组件数据
    print("\n🌀 测试螺旋组件数据:")
    spiral_data = tm.get_time_components_for_spiral()
    for unit, data in spiral_data.items():
        print(f"  {unit}: {data['text']} (值:{data['value']}, 角度值:{data['angle_value']})")
    
    print("\n✅ 时间管理器测试完成!")


if __name__ == "__main__":
    test_time_manager()

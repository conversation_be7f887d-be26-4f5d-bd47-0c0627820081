#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试游戏监控的消息推送功能
"""

import sys
import os
from datetime import datetime

# 添加斌哥游戏更新监控目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '斌哥游戏更新监控'))

from game_monitor import GameMonitor

def test_push_functionality():
    """测试推送功能"""
    print("🚀 测试游戏监控的消息推送功能")
    print("=" * 60)
    
    # 测试消息推送模块是否可用
    try:
        from message_push import wxpusher
        print("✅ 消息推送模块导入成功")
        
        # 发送测试推送
        test_title = "🧪 斌哥游戏监控 - 推送测试"
        test_content = f"""📱 推送功能测试

⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 测试内容:
• 消息推送模块正常工作
• 游戏监控集成推送功能
• 下载链接推送功能

如果您收到这条消息，说明推送功能配置正确！

🔗 接下来运行游戏监控时，如果检测到更新，会自动发送推送通知。"""
        
        print("📤 发送测试推送...")
        wxpusher(test_title, test_content, content_type=1)
        print("✅ 测试推送发送成功！")
        
    except ImportError as e:
        print(f"❌ 消息推送模块导入失败: {e}")
        print("💡 请确保 message_push.py 文件在正确的位置")
        return False
    except Exception as e:
        print(f"❌ 推送测试失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎮 测试游戏监控器的推送集成")
    print("=" * 60)
    
    # 创建启用推送的监控器
    monitor = GameMonitor(
        data_dir="斌哥游戏更新监控", 
        extract_downloads=True, 
        enable_push=True
    )
    
    print(f"📊 监控器状态:")
    print(f"  推送功能: {'✅ 已启用' if monitor.enable_push else '❌ 未启用'}")
    print(f"  下载提取: {'✅ 已启用' if monitor.extract_downloads else '❌ 未启用'}")
    print(f"  数据目录: {monitor.data_dir}")
    
    # 测试推送方法
    print("\n📤 测试监控器推送方法...")
    try:
        monitor.send_push_notification(
            "🧪 监控器推送测试",
            "这是通过游戏监控器发送的测试推送消息。\n\n如果收到此消息，说明监控器的推送功能正常工作！",
            content_type=1
        )
        print("✅ 监控器推送测试成功！")
    except Exception as e:
        print(f"❌ 监控器推送测试失败: {e}")
        return False
    
    print("\n🎉 推送功能测试完成！")
    print("\n💡 使用说明:")
    print("1. 运行 python '斌哥游戏更新监控/game_monitor.py' - 启用推送的基础监控")
    print("2. 运行 python '斌哥游戏更新监控/download_monitor.py' - 启用推送的下载监控")
    print("3. 当检测到游戏更新时，会自动发送推送通知")
    print("4. 当提取到下载链接时，会发送包含下载链接的推送")
    
    return True

def test_mock_update_push():
    """测试模拟更新推送"""
    print("\n" + "=" * 60)
    print("🎭 测试模拟游戏更新推送")
    print("=" * 60)
    
    try:
        from message_push import wxpusher
        
        # 模拟游戏更新数据
        mock_title = "🎮 斌哥游戏平台 - 🎮新游戏1个 | 🔄更新2个"
        mock_content = """⏰ 检查时间: 2025-01-10 15:30:45

🎮 新游戏发布 (1个):
📱 超级英雄大战 (ID: 890)
   特性: 无敌, 秒杀
   评论: 15 | 浏览: 234
   🔗 https://bg.denwq.cn/appshop/info/id/890.html

🔄 历史游戏更新 (2个):
📱 快来当领主 (ID: 885)
   特性: 无敌
   评论: 31 | 浏览: 777
   🔗 https://bg.denwq.cn/appshop/info/id/885.html

📱 太空异形生存者 (ID: 884)
   特性: 秒杀
   评论: 106 | 浏览: 6234
   🔗 https://bg.denwq.cn/appshop/info/id/884.html
"""
        
        print("📤 发送模拟游戏更新推送...")
        wxpusher(mock_title, mock_content, content_type=1)
        print("✅ 模拟更新推送发送成功！")
        
        # 模拟下载链接推送
        mock_download_title = "🎮 游戏下载链接 - 共6个链接"
        mock_download_content = """📦 下载链接提取完成
⏰ 提取时间: 2025-01-10 15:31:20
🔗 总链接数: 6

🆕 新游戏下载链接 (1个):
📱 超级英雄大战 (ID: 890)
  1. 百度网盘
     🔗 https://pan.baidu.com/s/1example123?pwd=abcd
     🔑 密码: abcd
  2. 奶牛网盘
     🔗 https://www.feijipan.com/s/example456

🔄 重新上架游戏下载链接 (2个):
📱 快来当领主 (ID: 885)
  1. 奶牛网盘
     🔗 https://www.feijipan.com/s/Z00RumBg
  2. 360网盘
     🔗 https://www.yunpan.com/surl_ypSSF3IYh4e
  3. 百度网盘
     🔗 https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2
     🔑 密码: qlv2

📱 太空异形生存者 (ID: 884)
  1. 百度网盘
     🔗 https://pan.baidu.com/s/1example789?pwd=efgh
     🔑 密码: efgh
"""
        
        print("📤 发送模拟下载链接推送...")
        wxpusher(mock_download_title, mock_download_content, content_type=1)
        print("✅ 模拟下载链接推送发送成功！")
        
        print("\n🎉 模拟推送测试完成！")
        print("💡 这就是实际监控时推送消息的格式")
        
    except Exception as e:
        print(f"❌ 模拟推送测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 斌哥游戏监控 - 推送功能测试")
    print("=" * 60)
    
    success = test_push_functionality()
    if success:
        test_mock_update_push()
    
    print("\n" + "=" * 60)
    print("✅ 推送功能测试完成！" if success else "❌ 推送功能测试失败！")
    print("💡 现在可以运行游戏监控，享受自动推送通知功能！")

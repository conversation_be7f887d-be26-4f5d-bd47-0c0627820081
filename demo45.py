import requests
import re
import time
import json
from message_push import wxpusher  # 从 message_push 模块导入 wxpusher 函数

# 定义请求头，直接写入您的 Cookie
headers = {
    'User-Agent': 'Mozilla/5.0 (Linux; Android 12; RMX2072) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36',
    'Accept': 'text/html, */*; q=0.01',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Referer': 'https://bg.denwq.cn/',
    'X-Requested-With': 'XMLHttpRequest',
    'X-PJAX': 'true',
    'X-PJAX-Container': '#pjax',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'Cookie': 'HYBBS_HEX=jMJe9y%25252FZw8P6wNAUp%25252Fo89t3GiWz4%25252FB%25252FgNHMaIeJRGBbKewP7Rg07tg0JNWdyKLU7cljH7lDCyMasOvWW%252BjDrdseoEPZbXH7Xhsrtmz3DITFNZze3ipgDUA%252BUuCXd4RNIo%25252FEvAKbajWUBMjxw4KOkGoKnHq8%253D',
}

try:
    # 第一步：发送 GET 请求获取变量的 HTML 内容
    get_url = 'https://bg.denwq.cn/fresh/sign.html?_pjax=%23pjax'
    response_get = requests.get(get_url, headers=headers)
    response_get.raise_for_status()  # 检查请求是否成功

    html_content = response_get.text

    # 第二步：使用正则表达式提取 window.a1 到 window.a32 的值
    pattern = r'window\.a(\d+)\s*=\s*[\'"]([^\'"]+)[\'"]\s*;'
    matches = re.findall(pattern, html_content)

    if not matches:
        error_message = '未能在页面中找到变量。'
        print(error_message)
        wxpusher('斌哥游戏盒签到失败', error_message, 1)
        exit()

    # 将变量存储到字典中
    a = {}
    for var_num, var_value in matches:
        a[int(var_num)] = var_value

    # 检查是否获取了所有变量
    if len(a) < 32:
        error_message = f'提取的变量数量不足，提取到 {len(a)} 个变量，可能缺少一些变量。'
        print(error_message)
        wxpusher('斌哥游戏盒签到失败', error_message, 1)
        exit()

    # 第三步：按照特定顺序拼接生成 window['hykey2']
    concatenation_order = [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
        20,
        19, 18, 17, 16, 15, 14, 13, 12, 11,
        21, 22, 23, 24, 25, 26, 27, 28, 29,
        30, 31, 32
    ]

    # 拼接变量值
    hykey2 = ''.join([a[num] for num in concatenation_order])

    print("计算得到的 hykey2 值：", hykey2)

    # 第四步：发送签到的 POST 请求
    current_timestamp = int(time.time())

    post_url = f'https://bg.denwq.cn/fresh/sign/time/{current_timestamp}.html'

    # 请求头，沿用之前的 headers，可以根据需要添加或修改
    post_headers = headers.copy()
    post_headers.update({
        'Referer': 'https://bg.denwq.cn/fresh/sign.html',
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    })

    # 请求数据
    data = {
        'ac': 'sign',
        'key': hykey2
    }

    # 发送 POST 请求进行签到
    response_post = requests.post(post_url, headers=post_headers, data=data)
    response_post.raise_for_status()  # 检查请求是否成功

    # 处理响应
    result = response_post.json()
    if result.get('error') == True:
        # 签到成功
        print('🎉 签到成功！')
        print('信息：', result.get('info'))
    elif result.get('error') == False:
        # 重复签到
        print('⚠️ 您今天已经签到过了。')
        print('信息：', result.get('info'))
    else:
        # 其他情况
        error_message = f"未知的响应：{result}"
        print(error_message)
        wxpusher('斌哥游戏盒签到失败', error_message, 1)
except requests.exceptions.HTTPError as http_err:
    error_message = f"HTTP错误发生: {http_err}"
    print(error_message)
    wxpusher('斌哥游戏盒签到失败', error_message, 1)
except json.JSONDecodeError:
    error_message = '响应不是有效的 JSON 格式：' + response_post.text
    print(error_message)
    wxpusher('斌哥游戏盒签到失败', error_message, 1)
except Exception as err:
    error_message = f"其他错误发生: {err}"
    print(error_message)
    wxpusher('斌哥游戏盒签到失败', error_message, 1)
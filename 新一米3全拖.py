# 2024.3.27 1.43am
# 2024.5.24 1.29am
from time import sleep
import yimi

# 一个人只能为人助力3个未组队完成的队
yimi.init()
ck = yimi.need_help()
ck1 = yimi.helper()
activityId = '664eafd0514a7c2abbaddd8b'  # 活动代号，随活动变
count = 0

for j in ck:
    for i in range(4):
        print(f'正在进行{j["name"]}的第{i + 1}次组队')
        print('_______________________________________')
        team_id = yimi.createTeamActivity(j['authorization'], j['userId'], activityId)
        if team_id == 0:
            print('创建队伍失败')
            continue
        for k in ck1:
            print(f'助力人:{k["name"]}')
            is_success = yimi.join_team(k['authorization'], team_id, k['userId'])
            sleep(0.2)
            if is_success == 1:
                count += 1
                print(f'当前助力人数为{count}')
            if count == 3:
                count = 0
                print('组队完成…………')
                print('_______________________________________')
                break


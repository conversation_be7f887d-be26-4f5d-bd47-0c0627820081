import requests
from datetime import datetime, timezone, timedelta
import json
import os
import random
import time


def convert_timestamp_to_china_time(timestamp):
    """
    将 Unix 时间戳转换为中国时间（北京时间，UTC+8）
    """
    utc_time = datetime.utcfromtimestamp(timestamp)
    china_timezone = timezone(timedelta(hours=8))
    china_time = utc_time.replace(tzinfo=timezone.utc).astimezone(china_timezone)
    return china_time


def is_within_last_6_hours(post_timestamp):
    """
    检查帖子时间是否在脚本运行时间的最近6小时内
    """
    current_time = datetime.now(timezone(timedelta(hours=8)))  # 获取当前中国时间（UTC+8）
    time_difference = current_time - post_timestamp
    return time_difference <= timedelta(hours=6)


def load_existing_posts(filename):
    """
    加载已存在的帖子数据
    """
    if os.path.exists(filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    existing_posts = json.loads(content)
                    return existing_posts
                else:
                    return []
        except json.JSONDecodeError:
            print(f"文件 {filename} 含有无效的JSON内容，将初始化为空列表。")
            return []
    else:
        return []


def save_posts(filename, posts):
    """
    保存帖子数据到指定的JSON文件
    """
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(posts, f, ensure_ascii=False, indent=4)


def main():
    import warnings
    warnings.filterwarnings("ignore")  # 忽略SSL验证的警告

    headers = {
        'xy-direction': '87',
        'x-mini-mua': 'eyJhIjoiRUNGQUFGMDEiLCJjIjoyLCJrIjoiMDcyNTE1YTRlODA5ZmEzOWIwYzQwNGM0ZjI5MDNlN2Q5NGZkNjhkYzA4MmUxODYwODdhN2QwYzMwMzg2YTU3OSIsInAiOiJhIiwicyI6ImFmNTVmMWMzNGVjNTAxZDJkMWNkOGQwNDI3M2FlMjZmIiwidSI6IjAwMDAwMDAwNTAyZTVjNTQ4Y2U1MzI4ZDg3ZWMwMzExYjQ5ZGY4OGQiLCJ2IjoiMi42LjQifQ.9Z3AE1O2FkC0cWjXmAW67CSoU4t2FTis0iXL-1-vI4Oijpl5Spo1CzQPAOLLbv98XrKCXPiDDAu3hDk5If-9Q7Yp5bOg7iFcbgu4gSU9U6vdEMjZ7bDypVxqo2jQubkfzKNeS51YmJxtXNyADAL5A3wNzIdBtYKMu-g13Tt8pomTQbxt_e9aik1P6lI3_mi7kp3rdstdueVa7Jb7RnrOy8VdKLjtbGz3lEcKVlegKBVgaWEZ2dmZoge0MfBrH5OWJyf_DhuSL54RV1wpjakkt9IlXy48akzZ1yxU3bh5FtuAiimSyJA-9VfBQl5_uqoXa2oIWmsWpc9t71yTUdEfTYBeX6kCUvk9aLBfXvUy4k2tBwKZNeBfjZjjWmBV1DhhsUbkzrxtFR000i_r0CH9ZS-NzGx6rJTlpq6XPvTuQUUdtKgica0RL9gMUjZgu0G3NIO6mu_raLVi5AmpMd0v1Ax6blQ9tCWwgDMZ9H3B-GgQpLk2OnSeVE6lqGjOXGwtjw3-gusMwuNu4fkgVVns-U1T8jA6Oz4OB43eV62WR7BGmhpK5LEn3_ZLZ43uzgvrDP2NZ24mn4hzuhX-8etqJ4E2QKVIQbZ4BIrN8I7RjXEEnK7WjSAh4047T7iXAOb-uO628L0iV868rrkKDZVZUEPzBfnazFaZFXBycECzYt3gW_cuKvvrardeOO4avVnoMWeKQA001H79OG_NQIYQuTAUdq4LpTZU7fXujvYUC0pLtRFrjcVozTgJWD1QtFsjD1BJbVozcRsmzf8lsA0R8aNwhJDSMV9ks4-lkhQnjKC_4MZReRhU1P8KPDIWnF2yv0F8hrTAuOnYeSwHY6BnLz0kfCWEyNtlKrv5Goj3a7q8JXnuLsoZqbWi6u3gQl2IpkR3rJvZRrcsw92IvQn2qdvevTXZ70q580pe33ut42Qkl0LY_UmnzMYtdQWLQkZU_i76Tut4qFehVBRBlMwiOw93iX_1F4LSf_ElM7BugeOGnPDdzkcZ6we8GeNNv31BMpdQDa4nZtTOjGD8i25Csb3bZy-_USeSBgQUl3JFKUYEPG2IcIzwoI4TePMkygbrzdQMq6zv0SxEeoyO_aUZVxBhoUOi4-bjIFGmPhOf9Ct02qLWlOo-awKB4FIihsyd.',
        'xy-common-params': 'fid=172550414510a687e65a98f43963f184dbb9901b406b&device_fingerprint=20240904174945f66597c73b8f647e73e2a796a82f6d110114cd550e0f9402&device_fingerprint1=20240904174945f66597c73b8f647e73e2a796a82f6d110114cd550e0f9402&cpu_name=qcom&gid=7c5ff4f4817655d1f60505abe9249bf691bc507947359a097782418d&device_model=phone&launch_id=1730913446&tz=Asia%2FShanghai&channel=XiaomiPreload2022&versionName=8.57.0&overseas_channel=0&deviceId=ba27ddbe-74cd-3f9e-9944-4d4e58daa285&platform=android&sid=session.1725504165660788634157&identifier_flag=4&t=1730913447&project_id=ECFAAF&build=8570985&x_trace_page_current=app_loading_page&lang=zh-Hans&app_id=ECFAAF01&uis=light&teenager=0',
        'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) Resolution/1080*2400 Version/8.57.0 Build/8570985 Device/(Xiaomi;22127RK46C) discover/8.57.0 NetType/CellNetwork',
        'referer': 'https://app.xhs.cn/',
        'shield': 'XYAAAAAQAAAAEAAABTAAAAUzUWEe0xG1IbD9/c+qCLOlKGmTtFa+lG434Le+FfTKgQlIa3yuI1GZ3/+7MLz8N81Zh+2Ks2FAxKRWHdYbOgjXpijuGymO2Dv3zArgANRzIxEV+I',
        'xy-platform-info': 'platform=android&build=8570985&deviceId=ba27ddbe-74cd-3f9e-9944-4d4e58daa285',
        # 'accept-encoding': 'gzip',
    }

    params = {
        'oid': 'homefeed_recommend',
        'cursor_score': '',
        'geo': 'eyJsYXRpdHVkZSI6MjMuMDI4MjQ2LCJsb25naXR1ZGUiOjExMi4xODgzNzd9\n',
        'trace_id': 'aed5934c-e5fd-3df3-9929-603ba425d86b',
        'note_index': '0',
        'refresh_type': '2',
        'client_volume': '0.00',
        'known_signal': '{"hp_con":0,"hp_type":0,"m_active":0,"device_level":8,"device_model":"22127RK46C","nqe_level":6}',
        'unread_begin_note_id': '672ac8570000000019019cb2',
        'unread_end_note_id': '',
        'unread_note_count': '3',
        'preview_ad': '',
        'preview_type': '',
        'loaded_ad': '',
        'home_ads_id': '',
        'user_action': '0',
        'personalization': '1',
        'is_break_down': '0',
        'orientation': '',
        'launch_scenario': '1',
        'last_card_position': '0',
        'last_live_position': '0',
        'last_live_id': '',
        'enable_live_shooting': 'false',
        'enable_location_permission': 'true',
    }

    existing_posts = load_existing_posts('xhs.json')
    existing_ids = set(post['id'] for post in existing_posts)

    # 将已有的帖子按时间排序
    existing_posts.sort(key=lambda x: x.get('timestamp', 0), reverse=True)

    posts_to_save = existing_posts.copy()  # 初始化为已有的帖子

    new_posts_added = 0
    max_new_posts = 40

    while new_posts_added < max_new_posts:
        try:
            response = requests.get('https://edith.xiaohongshu.com/api/sns/v6/homefeed', params=params, headers=headers,
                                    timeout=10, verify=False)
            data = response.json()
            for i in data['data']:
                if i.get('type') == 'normal':
                    if i.get('recommend').get('category_name') == '科技数码':
                        print(i.get('id'))  # 文章id
                        print(convert_timestamp_to_china_time(i.get('timestamp')))  # 文章时间戳
                        print(i.get('model_type'))  # 帖子类型
                        print(i.get('recommend').get('category_name'))  # 帖子分类
                        print(i.get('display_title'))  # 帖子标题
                        print(i.get('desc'))  # 帖子内容
                        print(i.get('recommend').get('predict_click_ratio'))  # 影响度

                        print()
            if 'data' not in data:
                print("未获取到数据，可能是网络问题或API限制。")
                break

            for post in data['data']:
                if post.get('type') == 'normal':
                    post_timestamp = convert_timestamp_to_china_time(post.get('timestamp'))

                    if is_within_last_6_hours(post_timestamp):
                        if post.get('recommend', {}).get('category_name') == '科技数码':
                            post_data = {
                                'id': post.get('id'),
                                'title': post.get('display_title'),
                                'timestamp': post.get('timestamp')
                            }
                            if post_data['id'] not in existing_ids:
                                posts_to_save.append(post_data)
                                existing_ids.add(post_data['id'])
                                new_posts_added += 1

                                # 按帖子时间降序排序（越新越靠前）
                                posts_to_save.sort(key=lambda x: x.get('timestamp', 0), reverse=True)

                                # 实时保存帖子数据到xhs.json
                                save_posts('xhs.json', posts_to_save)

                                if new_posts_added >= max_new_posts:
                                    break

            if new_posts_added >= max_new_posts:
                break  # 已经获取到足够的新帖子，退出循环

            # 等待随机的时间间隔
            sleep_time = random.uniform(1, 4)
            time.sleep(sleep_time)

        except requests.exceptions.RequestException as e:
            print(f"请求发生错误：{e}")
            break  # 发生网络错误，退出循环

    print(f"已将帖子保存到xhs.json，新增了{new_posts_added}个新内容。")


if __name__ == '__main__':
    main()

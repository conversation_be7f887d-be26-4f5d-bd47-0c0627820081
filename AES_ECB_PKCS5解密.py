"""解密
算法类型：AES/ECB/PKCS5Padding

Key类型：javax.crypto.spec.SecretKeySpec

加密秘钥（文本）：2%!9vn8(&MK*49)_
加密秘钥（Base64）：MiUhOXZuOCgmTUsqNDkpXw==
加密秘钥（Hex）：********************************

加密Iv（文本）：null
加密Iv（Base64）：bnVsbA==
加密Iv（Hex）：6e756c6c
"""
import base64
from Cryptodome.Cipher import AES

# 定义填充函数，使被加密数据的字节码长度是16的整数倍
def pad(text):
    count = len(text.encode('utf-8'))
    add = 16 - (count % 16)
    entext = text + (chr(add) * add)
    return entext

# 定义去除填充字符的函数
def unpad(date):
    return date[0:-ord(date[-1])]

# 定义解密函数
def decrypt(decrData):
    # 将base64编码的密钥转换为字节码
    key = base64.b64decode("MiUhOXZuOCgmTUsqNDkpXw==")
    # 创建AES对象，使用ECB模式和SecretKeySpec类型的密钥
    aes = AES.new(key, AES.MODE_ECB)
    # 将base64编码的加密内容转换为字节码
    res = base64.b64decode(decrData)
    # 解密并去除填充字符
    msg = unpad(aes.decrypt(res).decode("utf-8"))
    return msg

# 测试解密函数
decrData = "YFPjOgXQ8dWZyzJdwrNXIkcW0vwOoYDV1k6Ygi1T5Q4j2i50yY8HnDbTWGgfJU70"
print(decrypt(decrData))
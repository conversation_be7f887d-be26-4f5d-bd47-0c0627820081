// ==UserScript==
// @name         探索者视角
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  在Discourse论坛上，通过悬停并按下'Z'键来预览链接，同时确保在预览界面中按'Z'键可以退出预览。
// <AUTHOR> Name
// @match        https://linux.do/*
// @grant        none

// ==/UserScript==

(function() {
    'use strict';

    // Utility functions
    const debounce = (func, wait) => {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    };

    const isValidUrl = (url) => {
        try {
            const urlObj = new URL(url);
            return ['http:', 'https:'].includes(urlObj.protocol);
        } catch {
            return false;
        }
    };

    const showError = (message) => {
        console.warn('[探索者视角]', message);
        // 可以在这里添加用户可见的错误提示
    };

    // Create iframe container
    const iframeContainer = document.createElement('div');
    iframeContainer.style.position = 'fixed';
    iframeContainer.style.top = 0;
    iframeContainer.style.left = 0;
    iframeContainer.style.width = '100%';
    iframeContainer.style.height = '100%';
    iframeContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
    iframeContainer.style.display = 'none';
    iframeContainer.style.justifyContent = 'center';
    iframeContainer.style.alignItems = 'center';
    iframeContainer.style.zIndex = 9999;

    // Create iframe element
    const iframe = document.createElement('iframe');
    iframe.style.width = '80%';
    iframe.style.height = '80%';
    iframe.style.border = 'none';

    // 安全性增强：添加sandbox属性和其他安全设置
    iframe.setAttribute('sandbox', 'allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox');
    iframe.setAttribute('loading', 'lazy');
    iframe.setAttribute('referrerpolicy', 'no-referrer-when-downgrade');

    // 错误处理：监听iframe加载错误
    iframe.addEventListener('error', () => {
        console.log('[探索者视角] iframe加载错误，URL:', iframe.src);
        showError(`预览加载失败: ${iframe.src}`);
        closePreview();
    });



    iframeContainer.appendChild(iframe);

    document.body.appendChild(iframeContainer);

    // Global variables
    let currentHoveredLink = null;
    let isZKeyPressed = false;
    let lastCloseTime = 0; // 防止快速重复操作
    let iframeLoadTimeout = null; // iframe加载超时控制

    // Add event listeners
    document.addEventListener('keydown', function(event) {
        // 防止修饰键组合触发（Ctrl+Z, Alt+Z等）
        if (event.ctrlKey || event.altKey || event.metaKey || event.shiftKey) {
            return;
        }

        if (event.key === 'Z' || event.key === 'z') {
            if (!isZKeyPressed) { // Only trigger on first keydown, prevent repeat
                isZKeyPressed = true;
                const currentTime = Date.now();

                // 防止快速重复操作（200ms冷却期）
                if (currentTime - lastCloseTime < 200) {
                    return;
                }

                // Check if preview is already open
                if (iframeContainer.style.display === 'flex') {
                    // Close the preview if it's already open
                    closePreview();
                    isZKeyPressed = false; // 重置z键状态，防止长按时意外重新打开
                    lastCloseTime = currentTime;
                } else if (currentHoveredLink) {
                    // 安全性检查：验证URL
                    if (!isValidUrl(currentHoveredLink.href)) {
                        showError(`无效的URL: ${currentHoveredLink.href}`);
                        return;
                    }

                    // 设置iframe加载超时（30秒）
                    if (iframeLoadTimeout) {
                        clearTimeout(iframeLoadTimeout);
                    }
                    iframeLoadTimeout = setTimeout(() => {
                        console.log('[探索者视角] iframe加载超时，URL:', currentHoveredLink.href);
                        showError('iframe加载超时');
                        closePreview();
                    }, 30000);

                    // Open preview if hovering over a link
                    console.log('[探索者视角] 打开预览，URL:', currentHoveredLink.href);
                    iframe.src = currentHoveredLink.href;
                    iframeContainer.style.display = 'flex';

                    // Send message to iframe to allow closing from within iframe
                    try {
                        iframe.contentWindow.postMessage({ action: 'open' }, '*');
                    } catch (error) {
                        showError('无法向iframe发送消息: ' + error.message);
                    }
                }
            }
        }
    });

    document.addEventListener('keyup', function(event) {
        if (event.key === 'Z' || event.key === 'z') {
            isZKeyPressed = false;
        }
    });

    // 防抖处理mouseover事件，提升性能
    const debouncedMouseOver = debounce(function(aTag) {
        // 检查页面是否可见和有焦点
        if (document.hidden || !document.hasFocus()) {
            return;
        }

        currentHoveredLink = aTag; // Update currently hovered link
        // Only trigger if Z key is currently being pressed
        if (isZKeyPressed) {
            // 防止在刚关闭预览后立即重新打开
            const currentTime = Date.now();
            if (currentTime - lastCloseTime < 200) {
                return;
            }

            // 安全性检查：验证URL
            if (!isValidUrl(aTag.href)) {
                showError(`无效的URL: ${aTag.href}`);
                return;
            }

            // 设置iframe加载超时（30秒）
            if (iframeLoadTimeout) {
                clearTimeout(iframeLoadTimeout);
            }
            iframeLoadTimeout = setTimeout(() => {
                console.log('[探索者视角] iframe加载超时，URL:', aTag.href);
                showError('iframe加载超时');
                closePreview();
            }, 30000);

            console.log('[探索者视角] 鼠标悬停打开预览，URL:', aTag.href);
            iframe.src = aTag.href;
            iframeContainer.style.display = 'flex';
        }
    }, 100); // 100ms防抖延迟

    document.body.addEventListener('mouseover', function(event) {
        const aTag = event.target.closest('a[href]');
        if (!aTag || aTag.href.startsWith('javascript:')) return;

        debouncedMouseOver(aTag);
    }, { passive: true });

    document.body.addEventListener('mouseout', function(event) {
        currentHoveredLink = null;
    });

    // 关闭预览的通用函数
    const closePreview = () => {
        console.log('[探索者视角] 关闭预览');
        iframeContainer.style.display = 'none';
        iframe.src = '';
        // 清理超时定时器
        if (iframeLoadTimeout) {
            clearTimeout(iframeLoadTimeout);
            iframeLoadTimeout = null;
        }
    };

    // Close the preview when clicking outside the iframe
    document.addEventListener('click', function(event) {
        if (!iframeContainer.contains(event.target) && event.target !== iframeContainer) {
            closePreview();
        }
    });

    iframeContainer.addEventListener('click', function(event) {
        if (event.target === iframeContainer) {
            closePreview();
        }
    });

    // Listen for messages from iframe with error handling
    window.addEventListener('message', function(event) {
        try {
            if (event.data && event.data.action === 'close') {
                closePreview();
            }
        } catch (error) {
            showError('处理iframe消息时出错: ' + error.message);
        }
    });

    // 页面失去焦点时重置状态（防止键盘状态异常）
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            isZKeyPressed = false;
            currentHoveredLink = null;
        }
    });

    // 窗口失去焦点时重置状态
    window.addEventListener('blur', function() {
        isZKeyPressed = false;
        currentHoveredLink = null;
    });

    // 页面卸载时清理资源
    window.addEventListener('beforeunload', function() {
        if (iframeLoadTimeout) {
            clearTimeout(iframeLoadTimeout);
        }
    });

    // 增强的iframe加载处理
    iframe.addEventListener('load', function() {
        console.log('[探索者视角] iframe加载完成，URL:', iframe.src);

        // 清理加载超时
        if (iframeLoadTimeout) {
            clearTimeout(iframeLoadTimeout);
            iframeLoadTimeout = null;
        }

        // 尝试给iframe内部添加键盘监听（如果同源）
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
                iframeDoc.addEventListener('keydown', function(event) {
                    if ((event.key === 'Z' || event.key === 'z') &&
                        !event.ctrlKey && !event.altKey && !event.metaKey && !event.shiftKey) {
                        console.log('[探索者视角] 从iframe内部关闭预览');
                        closePreview();
                        event.preventDefault();
                    }
                });
            }
        } catch (error) {
            // 跨域iframe无法访问，这是正常的
            console.log('[探索者视角] iframe跨域，无法添加内部键盘监听');
        }
    });

    // 添加iframe加载开始监听
    iframe.addEventListener('loadstart', function() {
        console.log('[探索者视角] iframe开始加载，URL:', iframe.src);
    });

    // 添加iframe DOM内容加载完成监听
    iframe.addEventListener('DOMContentLoaded', function() {
        console.log('[探索者视角] iframe DOM内容加载完成');
    });
})();

from urllib.parse import urlparse, urlunparse
websites = []
processed_websites = {}
output_file_path = r'C:\Users\<USER>\自用程序\fofa爬虫\LibreChat.txt'
def normalize_url(url):
    if not url.startswith(('http://', 'https://')):
        url = 'http://' + url
    return url
def get_base_url_with_port(url):
    parsed_url = urlparse(url)
    # 保留协议、主机名和端口
    if parsed_url.port:
        base_url = f"{parsed_url.hostname}:{parsed_url.port}"
    else:
        base_url = f"{parsed_url.hostname}"
    return base_url
# 读取文件内容
with open(output_file_path, 'r') as file:
    for line in file:
        website = line.strip()
        website = normalize_url(website)
        base_url = get_base_url_with_port(website)
        # 检查是否需要替换 https 为 http
        if base_url in processed_websites:
            if website.startswith('http://') and processed_websites[base_url].startswith('https://'):
                # 替换为 http://
                websites.remove(processed_websites[base_url])
                websites.append(website)
                processed_websites[base_url] = website
            elif website.startswith('http://') and processed_websites[base_url].startswith('http://'):
                # 如果已经是 http://，则跳过
                continue
            elif website.startswith('https://') and processed_websites[base_url].startswith('http://'):
                # 如果已经是 http://，则跳过 https://
                continue
        else:
            # 记录新网址
            processed_websites[base_url] = website
            websites.append(website)

with open(output_file_path, 'w', encoding='utf-8') as f:
    f.write('\n'.join(websites) + '\n')
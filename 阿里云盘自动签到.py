import requests


def update_access_token(refresh_token):
    """
    使用 refresh_token 更新 access_token

    :param refresh_token: refresh_token
    :return: 更新成功返回字典, 失败返回 False
    """
    data = requests.post(
        'https://auth.aliyundrive.com/v2/account/token',
        json={
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token,
        }
    ).json()

    try:
        if data['code'] in [
            'RefreshTokenExpired', 'InvalidParameter.RefreshToken',
        ]:
            print(f'更新 access_token 失败, 错误信息: {data}')
            return False
    except KeyError:
        pass

    print({
        'refresh_token': data['refresh_token']
    })
    return {
        'access_token': data['access_token'],
        'refresh_token': data['refresh_token']
    }


def sign_in(access_token):
    """
    签到函数

    :param access_token: access_token
    :return: 是否签到成功
    """
    data = requests.post(
        'https://member.aliyundrive.com/v1/activity/sign_in_list',
        headers={
            'Authorization': f'Bearer {access_token}',
        },
        json={},
    ).json()

    if 'success' not in data:
        print(f'签到失败, 错误信息: {data}')
        return False

    current_day = None
    for i, day in enumerate(data['result']['signInLogs']):
        if day['status'] == 'miss':
            current_day = data['result']['signInLogs'][i - 1]
            break

    reward = (
        '无奖励'
        if not current_day['reward']
        else f'获得{current_day["reward"]["name"]}{current_day["reward"]["description"]}'
    )
    print(f'签到成功, 本月累计签到 {data["result"]["signInCount"]} 天.')
    print(f'本次签到 {reward}')

    return True


refresh_tokens = ['c19e87d8292444b7932fff8ed0461002',
                  'efa8b54b0d6b490199c4a02f3fa25277',
                  '6b957d63b72e4b959ca2342e0a7bf4ee']

for token in refresh_tokens:

    def get_access_token():
        return update_access_token(token)['access_token']


    if not sign_in(get_access_token()):
        print('签到失败.')

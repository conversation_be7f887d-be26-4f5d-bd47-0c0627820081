"""
该程序用于从网站https://fofa.info/中扫描nextchat网站，寻找可用的gpt4网站
进行筛选后的数据获取响应存入input_data.json里面
然后运行该程序
fofa搜索关键字：
body=="bf1f72eb5224e6ea.js"
body=="b0ebe1618ba2d39f.css"
body=="4ae5a32a166d69a6.js"

"""
import requests
import json
import warnings
from urllib3.exceptions import InsecureRequestWarning
list = [
    "116.226.15.100:12345",
    "45.149.156.102",
    "121.89.194.26",
    "caijiacun.cn",
    "183.15.205.154:8000",
    "chatgpt.anjiaotong.cn",
    "cy.cch.plus",
    "chat.miketsu.cn:10820",
    "gpt.yoloqy.top"
]

warnings.simplefilter('ignore', InsecureRequestWarning)

headers = {
    'content-type': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0',
}

json_data = {
    'messages': [
        {
            'role': 'system',
            'content': '\nYou are ChatGPT, a large language model trained by OpenAI.\nKnowledge cutoff: 2021-09\nCurrent model: gpt-4\nCurrent time: 2024/5/10 22:14:35\nLatex inline: $x^2$ \nLatex block: $$e=mc^2$$\n\n',
        },
        {
            'role': 'user',
            'content': '请作一个简短的中文自我介绍：说明自己是什么模型（具体到确切的某个型号）、数据截止时间、研发公司，除此之外不要回答任何东西',
        },
    ],
    'stream': True,
    'model': 'gpt-4',
    'temperature': 0.5,
    'presence_penalty': 0,
    'frequency_penalty': 0,
    'top_p': 1,
}

input_file_path = 'input_data.json'

with open(input_file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

n = 0
for asset in data["data"]["assets"]:
    link = asset["link"]
    try:
        n += 1
        print(f"正在访问第{n}个链接")
        response = requests.post(f'{link}/api/config', headers=headers, verify=False, timeout=5)
        if response.status_code == 200:
            if not response.json().get('needCode', True):  # 使用 get 更安全一些
                print(link)
                if link.split('//')[1] not in list:
                    response = requests.post(f'{link}/api/openai/v1/chat/completions', headers=headers, json=json_data, verify=False)
                    print(response.text)
    except requests.exceptions.ProxyError as e:
        print(f"代理错误：{link}, 错误信息：{e}")
    except requests.exceptions.ReadTimeout:
        print(f"访问超时：{link}, 跳过此链接")
    except requests.exceptions.RequestException as e:
        print(f"请求遇到错误：{e}")
# -*- coding:UTF-8 -*-
import binascii
import requests
from bs4 import BeautifulSoup as bs
import time
import rsa


def get_cookie(yhm, mm):
    #proxies = {"http": f"{requests.get('http://*************:8866/get_ip?type=3').text}"}
    now_time = int(time.time())
    url = "http://jwsys.gdpu.edu.cn/xtgl/login_slogin.html?time=1659099698999"
    session = requests.Session()
    publickey = session.get(
        'http://jwsys.gdpu.edu.cn/xtgl/login_getPublicKey.html?time=1659104107279&_=1659104106979'
        ).json()
    modules = publickey["modulus"]
    page = session.get(url)
    soup = bs(page.text, "html.parser")
    # 获取认证口令csrftoken
    csrftoken = soup.find(id="csrftoken").get("value")
    password = str(mm).encode("utf8").decode("utf8")
    name = str(yhm).encode("utf8").decode("utf8")
    weibo_rsa_e = 65537
    message = str(password).encode()
    rsa_n = binascii.b2a_hex(binascii.a2b_base64(modules))
    key = rsa.PublicKey(int(rsa_n, 16), weibo_rsa_e)
    encropy_pwd = rsa.encrypt(message, key)
    password = binascii.b2a_base64(encropy_pwd)
    header = {
        'Accept': 'text/html, */*; q=0.01',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:58.0) Gecko/20100101 Firefox/58.0',
        'Connection': 'keep-alive',
        'Referer': url + str(now_time),
        'Upgrade-Insecure-Requests': '1',
    }
    data = {
        'csrftoken': csrftoken,
        'mm': password,
        'mm': password,
        'yhm': name
    }
    request = session.post(url, headers=header, data=data)
    cookie = request.request.headers['cookie']
    return cookie


if __name__ == '__main__':
    yhm = '2130506164'  # 账号
    mm = "@zyh0820.@"  # 密码
    cookie = get_cookie(yhm, mm)
    print(cookie)

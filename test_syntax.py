"""
简单语法测试
"""

import ast
import os

def test_file(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            source = f.read()
        ast.parse(source)
        print(f"✅ {filepath}")
        return True
    except SyntaxError as e:
        print(f"❌ {filepath} - 第{e.lineno}行: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filepath} - {e}")
        return False

files = [
    'telegram_resource_bot_project/chat_activity_analyzer.py',
    'telegram_resource_bot_project/quick_activity_check.py',
    'telegram_resource_bot_project/start_activity_analyzer.py'
]

print("语法检查:")
for f in files:
    if os.path.exists(f):
        test_file(f)
    else:
        print(f"⚠️ {f} 不存在")

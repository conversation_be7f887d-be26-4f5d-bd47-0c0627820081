import requests
import random
import json
import os
from sys import exit

# 临时的游客uuid生成
def uuid():
    e = [None] * 36
    hex_digits = "0123456789abcdef"

    for t in range(36):
        e[t] = hex_digits[random.randint(0, 15)]

    e[14] = "4"
    e[19] = hex_digits[(3 & int(e[19], 16)) | 8]
    e[8] = e[13] = e[18] = e[23] = "-"

    return 'web-' + ''.join(e)

def print_request_headers(response, *args, **kwargs):
    print("Request Headers:")
    for key, value in response.request.headers.items():
        print(f"{key}: {value}")


# 临时的游客authorization
def get_authorization():
    headers = {
        'Host': 'ymixy.cloudyee.com',
        'Connection': 'keep-alive',
        # 'Content-Length': '86',
        'authorization': 'Basic MW0tY3VzdG9tZXI6MzRhVmFlSVJ6V1lMaTdMQUF2aHltNml5cU1FMGdnbDU=',
        'charset': 'utf-8',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 12; RMX2072 Build/RKQ1.211103.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 XWEB/1160175 MMWEBSDK/20231202 MMWEBID/6657 MicroMessenger/8.0.47.2560(0x28002F30) WeChat/arm64 Weixin NetType/4G Language/zh_CN ABI/arm64 MiniProgramEnv/android',
        'content-type': 'application/x-www-form-urlencoded',
        # 'Accept-Encoding': 'gzip,compress,br,deflate',
        'x-gray-flag': '0;gray=false',
        'accept': 'application/json, text/plain, */*',
        'Referer': 'https://servicewechat.com/wx47a93213cc89cef0/422/page-frame.html',
    }

    data = {
        'grant_type': 'temp_longest_time',
        'scope': 'basic',
        'uuid': uuid(),
    }

    response = requests.post('https://ymixy.cloudyee.com/api/dsauth/oauth/token', headers=headers, data=data)

    if response.status_code == 200:
        if 'data' in response.json():
            return 'bearer ' + response.json()['data']['access_token']
        else:
            return None
    else:
        return None


# 用于已登陆账号刷新refresh token和获取新的access token
def get_refresh(refresh_token):
    headers = {
        'Host': 'ymixy.cloudyee.com',
        'Connection': 'keep-alive',
        # 'Content-Length': '87',
        'authorization': 'Basic MW0tY3VzdG9tZXI6MzRhVmFlSVJ6V1lMaTdMQUF2aHltNml5cU1FMGdnbDU=',
        'charset': 'utf-8',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 12; RMX2072 Build/RKQ1.211103.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/********* Mobile Safari/537.36 XWEB/1160175 MMWEBSDK/20231202 MMWEBID/6657 MicroMessenger/8.0.47.2560(0x28002F30) WeChat/arm64 Weixin NetType/4G Language/zh_CN ABI/arm64 MiniProgramEnv/android',
        'content-type': 'application/x-www-form-urlencoded',
        # 'Accept-Encoding': 'gzip,compress,br,deflate',
        'x-gray-flag': '0;gray=false',
        'accept': 'application/json, text/plain, */*',

    }

    data = {
        'grant_type': 'refresh_token',
        'refresh_token': refresh_token,
        'scope': 'basic',
    }

    response = requests.post('https://ymixy.cloudyee.com/api/dsauth/oauth/token', headers=headers, data=data, hooks={'response': print_request_headers})

    response_data = response.json()
    if response.status_code == 200:
        if 'message' in response_data:
            if response_data['message'] == 'success':
                print('刷新成功')
                return {
                    'access_token': response_data['data']['access_token'],
                    'refresh_token': response_data['data']['refresh_token'],
                }
            elif response_data['message'] == 'invalid_grant':
                print('非法token，刷新失败')
                return None
            else:
                print(response_data['description'])
                return None
        else:
            print('未知情况，请查看具体的response.json()数据')
            return None
    else:
        print('请求失败')
        return None


# 对目标账号刷新refresh token和获取新的access token
def init():
    file_path = '223yimi.json'

    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        k = 0
        # 遍历 ck 列表
        for user in data.get('ck', []):
            if user['is_refresh'] == 1 and user['refresh_token'].strip():
                # 调用 get_refresh 函数
                refresh_result = get_refresh(user['refresh_token'])
                # 更新用户的 access_token 和 refresh_token
                user['authorization'] = f"bearer {refresh_result['access_token']}"
                user['refresh_token'] = refresh_result['refresh_token']
                # 重置 is_refresh 标志
                user['is_refresh'] = 0
                print(f"成功刷新{user['name']}状态")
                k = 1
        # 将更新后的数据保存回 user.json 文件
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=4)

        if k == 0:
            print("未更新任何用户状态")
    else:
        print(f"{file_path} 文件不存在")


# 获取被助力者
def need_help():
    # 读取文件内容
    file_path = '223yimi.json'

    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

    result = []

    # 遍历 ck 列表
    for user in data.get('ck', []):
        if user.get('need_joinActivity') == 1:
            # 提取需要的属性
            filtered_user = {
                "authorization": user.get("authorization"),
                "userId": user.get("userId"),
                "name": user.get("name")
            }
            result.append(filtered_user)
            user['need_joinActivity'] = 0

    return result


# 获取助力者
def helper():
    # 读取文件内容
    file_path = '223yimi.json'

    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

    result = []

    # 遍历 ck 列表
    for user in data.get('ck', []):
        if user.get('help') == 1:
            # 提取需要的属性
            filtered_user = {
                "authorization": user.get("authorization"),
                "userId": user.get("userId"),
                "name": user.get("name")
            }
            result.append(filtered_user)

    return result


# 加入队伍
def join_team(authorization, team_id, member_Id):
    headers = {
        'authorization': authorization
    }

    data = {
        "id": team_id,
        "memberId": member_Id  # 成员id
    }

    response = requests.post('https://ymixy.cloudyee.com/api/activity/teamactivity/joinTeam', headers=headers,
                             json=data)
    if 'code' in response.json():
        if response.json()['message'] == 'invalid_token':
            exit(f'{response.json()["description"]}')
    if response.json()['httpStatus'] == 200:
        if response.json()['message'] == 'success':
            print('助力成功')
            return 1
        else:
            print(response.json()['description'])
            print('助力失败')
            return 0
    else:
        print('请求失败')
        return 0


# 创建队伍
def createTeamActivity(authorization, userId, activityId):
    headers = {
        'authorization': authorization
    }

    data = {
        "tradeAreaId": '6569501e15720000b8008864',  # 广药云浮地区代号，固定
        "activity": {
            "activityId": activityId
        },
        "leader": {
            "userId": userId  # 队长id
        }
    }

    response = requests.post('https://ymixy.cloudyee.com/api/activity/teamactivity/createTeamActivity', headers=headers,
                             json=data)
    if 'code' in response.json():
        if response.json()['message'] == 'invalid_token':
            exit(f'{response.json()["description"]}')
    if response.json()['httpStatus'] == 200:
        if response.json()['message'] == 'success':
            print('创建队伍成功')
            print('队伍id: ', response.json()['data']['id'])
            return response.json()['data']['id']
        else:
            print(response.json()['description'])
            return 0
    else:
        print('请求失败')
        return 0


# 获取历史组队信息
def getLeaderTeamRecord(user):
    headers = {
        'authorization': user['authorization']
    }

    params = {
        'tradeAreaId': '6569501e15720000b8008864',
    }

    response = requests.get(
        'https://ymixy.cloudyee.com/api/activity/teamactivity/getLeaderTeamRecord',
        params=params,
        headers=headers,
    )
    if 'message' in response.json():
        if response.json()['message'] == 'invalid_token':
            exit(f'{user["name"]} ：登录状态失效，{response.json()["description"]}')
    return response.json()['data']

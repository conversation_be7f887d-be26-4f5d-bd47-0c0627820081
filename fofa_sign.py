#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/9/24 22:28
# <AUTHOR> Cl0udG0d
# @File    : sign.py
# @Github: https://github.com/Cl0udG0d
from Cryptodome.Signature import PKCS1_v1_5
from Cryptodome.Hash import SHA256
from Cryptodome.PublicKey import RSA
import urllib.parse
import time
import base64



'''
加密算法部分感谢 tastypear
'''

private_key_str = '''******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''


def getSign(message):
    priv_key = RSA.importKey(private_key_str)
    h = SHA256.new(message.encode('utf-8'))
    signature = PKCS1_v1_5.new(priv_key).sign(h)
    result = base64.b64encode(signature).decode()
    return result


def getUrl(qbase64,page):
    ts = int(time.time() * 1000)
    size =  20
    message = f'fullfalsepage{page}qbase64{qbase64}size{size}ts{ts}'
    sign = urllib.parse.quote(getSign(message))
    url = f'https://api.fofa.info/v1/search?qbase64={urllib.parse.quote(qbase64)}&full=false&page=1&size={size}&ts={ts}&sign={sign}&app_id=9e9fb94330d97833acfbc041ee1a76793f1bc691'
    return url

# def getPage2Url(qbase64,page):
#     ts = int(time.time() * 1000)
#     message = f'fullfalsepage{page}qbase64{qbase64}size10ts{ts}'
#     sign = urllib.parse.quote(getSign(message))
#     url = f'https://api.fofa.info/v1/search?qbase64={urllib.parse.quote(qbase64)}&full=false&page={page}&size=10&ts={ts}&sign={sign}&app_id=9e9fb94330d97833acfbc041ee1a76793f1bc691'
#     return url

def getUrlTest(qbase64,page):
    ts = int(time.time() * 1000)
    size = 50
    message = f'fullfalsepage{page}qbase64{qbase64}size{size}ts{ts}'
    sign = urllib.parse.quote(getSign(message))
    url = f'https://api.fofa.info/v1/search?qbase64={urllib.parse.quote(qbase64)}&full=false&page={page}&size={size}&ts={ts}&sign={sign}&app_id=9e9fb94330d97833acfbc041ee1a76793f1bc691'
    return url


if __name__ == '__main__':
    print(getUrlTest("IkxpYnJlQ2hhdCI="))
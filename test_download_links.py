#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载链接提取功能
"""

import sys
import os

# 添加斌哥游戏更新监控目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '斌哥游戏更新监控'))

from game_monitor import GameMonitor

def test_download_extraction():
    """测试下载链接提取"""
    print("🚀 测试游戏监控和下载链接提取")
    print("=" * 50)
    
    # 创建启用下载提取的监控器
    monitor = GameMonitor(data_dir="斌哥游戏更新监控", extract_downloads=True)
    
    try:
        # 运行监控
        has_changes = monitor.run_monitor()
        
        if has_changes:
            print("\n🔔 检测到游戏更新，下载链接已提取！")
        else:
            print("\n✅ 无游戏更新")
            
        # 显示下载数据摘要
        downloads_file = os.path.join("斌哥游戏更新监控", "game_downloads.json")
        if os.path.exists(downloads_file):
            import json
            with open(downloads_file, 'r', encoding='utf-8') as f:
                downloads_data = json.load(f)
            
            print(f"\n📊 下载链接数据库:")
            print(f"  总游戏数: {len(downloads_data)}")
            
            # 显示最近的几个游戏的下载链接
            recent_games = list(downloads_data.items())[-3:]  # 最近3个
            
            for game_id, data in recent_games:
                game_name = data['game_info'].get('name', f'游戏_{game_id}')
                print(f"\n📱 {game_name} (ID: {game_id})")
                print(f"   下载链接数: {data['total_links']}")
                
                for link in data.get('download_links', []):
                    print(f"   📦 {link['pan_type']}: {link['url']}")
                    if link.get('password'):
                        print(f"      🔑 密码: {link['password']}")
        else:
            print("\n📁 暂无下载数据")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_download_extraction()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的下载链接提取功能
"""

import sys
import os

# 添加斌哥游戏更新监控目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '斌哥游戏更新监控'))

from game_monitor import GameMonitor

def test_extraction():
    """测试下载链接提取"""
    print("🚀 测试修复后的下载链接提取功能")
    print("=" * 60)
    
    # 创建监控器
    monitor = GameMonitor(data_dir="斌哥游戏更新监控", extract_downloads=True)
    
    # 测试提取游戏885（快来当领主）的下载链接
    game_id = 885
    print(f"🎮 测试游戏ID: {game_id}")
    
    try:
        result = monitor.extract_game_downloads(game_id, "测试提取")
        
        if result:
            print(f"\n✅ 提取成功!")
            print(f"游戏名称: {result['game_info'].get('name', '未知')}")
            print(f"下载链接数量: {result['total_links']}")
            
            if result['download_links']:
                print(f"\n📦 下载链接详情:")
                for i, link in enumerate(result['download_links'], 1):
                    print(f"  {i}. {link['pan_type']}")
                    print(f"     🔗 链接: {link['url']}")
                    if link.get('password'):
                        print(f"     🔑 密码: {link['password']}")
                    print(f"     📝 显示名称: {link['display_name']}")
                    print()
                
                print("🎉 下载链接提取成功！现在你可以使用这些链接下载游戏了。")
            else:
                print("⚠️ 未找到下载链接")
        else:
            print("❌ 提取失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_extraction()

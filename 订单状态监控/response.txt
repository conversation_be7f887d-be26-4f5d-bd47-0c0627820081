{"code": "0", "message": "成功", "data": {"pageNo": 1, "pageSize": 10, "count": 4, "first": 1, "last": 1, "prev": 1, "next": 1, "firstPage": true, "lastPage": true, "length": 8, "slider": 1, "list": [{"orderSubId": 306307955454189568, "stateFlag": 0, "orderStatusId": 4, "orderTypeId": 7, "oaoOrderId": "306307955454189568", "orderSubNo": "2025072511155725082780", "orderTime": 1753413357000, "tfOrderSimActed": {"page": {"pageNo": 1, "pageSize": -1, "count": 0, "first": 0, "last": 0, "prev": 0, "next": 2, "firstPage": false, "lastPage": false, "length": 8, "slider": 1, "orderBy": "", "funcName": "page", "funcParam": "", "message": "", "html": "<ul>\n<li><a href=\"javascript:\" onclick=\"page(0,-1,'');\">« 上一页</a></li>\n<li><a href=\"javascript:\" onclick=\"page(0,-1,'');\">1</a></li>\n<li><a href=\"javascript:\" onclick=\"page(0,-1,'');\">下一页 »</a></li>\n<li class=\"disabled controls\"><a href=\"javascript:\">当前 <input type=\"text\" value=\"1\" onkeypress=\"var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(this.value,-1,'');\" onclick=\"this.select();\"/> / <input type=\"text\" value=\"-1\" onkeypress=\"var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(1,this.value,'');\" onclick=\"this.select();\"/> 条，共 0 条</a></li>\n</ul>\n<div style=\"clear:both;\"></div>", "disabled": true, "notCount": false, "firstResult": 0, "maxResults": -1, "totalPage": 0}, "isNewRecord": true, "phone": "183****0027", "orderSubNo": "2025072511155725082780"}, "detailSim": {"queryStaffOrder": false, "syncFail": false, "orderSubId": 306307955454189568, "regName": "彭*", "psptId": "522121********0833", "phone": "183****0027", "baseSetName": "动感地带超星卡18元档-校园版", "terminalType": "1", "synOnliResuIssuc": "1", "openHeBaoPay": false, "openSmVip": false, "openGoldVip": false, "orderSubNo": "2025072511155725082780"}, "orderHall": {"orderSubId": 306307955454189568}, "orderStatus": {"orderStatusId": 4, "orderStatusName": "待备货"}, "changed": false, "countNumber": 0, "simAttrs": [{"orderSubId": 306307955454189568, "attrName": "paramCityName", "attrValue": "株*市"}, {"orderSubId": 306307955454189568, "attrName": "agreeRunningId", "attrValue": "958af579-104e-44a5-bac1-a664f1cf7676"}, {"orderSubId": 306307955454189568, "attrName": "paramCityCode", "attrValue": "0733"}, {"orderSubId": 306307955454189568, "attrName": "userStatement", "attrValue": "我已阅读入网协议和不得转租转售电话卡等告知内容,如有违反,由本人承担相应法律风险"}], "source": 0}], "orderBy": "", "funcName": "page", "funcParam": "", "message": "", "html": "<ul>\n<li class=\"disabled\"><a href=\"javascript:\">« 上一页</a></li>\n<li class=\"active\"><a href=\"javascript:\">1</a></li>\n<li class=\"disabled\"><a href=\"javascript:\">下一页 »</a></li>\n<li class=\"disabled controls\"><a href=\"javascript:\">当前 <input type=\"text\" value=\"1\" onkeypress=\"var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(this.value,10,'');\" onclick=\"this.select();\"/> / <input type=\"text\" value=\"10\" onkeypress=\"var e=window.event||this;var c=e.keyCode||e.which;if(c==13)page(1,this.value,'');\" onclick=\"this.select();\"/> 条，共 4 条</a></li>\n</ul>\n<div style=\"clear:both;\"></div>", "disabled": false, "notCount": false, "firstResult": 0, "maxResults": 10, "totalPage": 1}}
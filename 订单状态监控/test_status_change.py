#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from datetime import datetime

def test_status_change_detection():
    """测试状态变化检测功能"""
    print("=" * 50)
    print("状态变化检测测试")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 导入检测函数
    from demo44 import is_important_status_change, get_change_type, get_status_description
    
    # 测试用例
    test_cases = [
        ("待备货", "备货中"),
        ("待备货", "待确认"),
        ("备货中", "待确认"),
        ("备货中", "待备货"),
        ("待确认", "备货中"),
        ("已发货", "备货中"),
        ("备货中", "已完成"),
        ("备货中", "已取消"),
    ]
    
    print("测试状态变化检测:")
    print("-" * 30)
    
    for old_status, new_status in test_cases:
        is_important = is_important_status_change(old_status, new_status)
        change_type = get_change_type(old_status, new_status)
        description = get_status_description(old_status, new_status)
        
        status_icon = "🔔" if is_important else "❌"
        print(f"{status_icon} {old_status} → {new_status}")
        print(f"   重要变化: {'是' if is_important else '否'}")
        print(f"   变化类型: {change_type}")
        print(f"   描述: {description}")
        print()

if __name__ == "__main__":
    test_status_change_detection()

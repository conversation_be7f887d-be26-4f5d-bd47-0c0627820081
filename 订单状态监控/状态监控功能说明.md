# 全面状态监控功能说明

## 🎯 功能概述

系统现已升级为**全面状态监控模式**，不再仅限于监控从"待备货"开始的状态变化，而是监控所有重要的订单状态变化。

## 📊 监控的状态变化类型

### 1. 流程推进类 📦
- 待备货 → 待确认
- 待备货 → 已发货  
- 待确认 → 已发货

### 2. 订单完成类 ✅
- 任何状态 → 已完成

### 3. 订单取消类 ❌
- 任何状态 → 已取消

### 4. 状态回退类 🔄
- 待确认 → 待备货
- 已发货 → 待确认
- 已发货 → 待备货

## 🔔 通知消息格式

### 通知标题格式
```
{emoji} [{用户名}] {变更类型}通知
```

示例：
- `🔄 [彭力] 状态回退通知`
- `📦 [彭力] 流程推进通知`
- `✅ [彭力] 订单完成通知`

### 通知内容格式
```
账户：用户名 (账户描述)
订单号：2025072511155725082780
用户：彭*
手机号：183****0027
套餐：动感地带超星卡18元档-校园版
状态变更：待确认 → 待备货
变更类型：状态回退
变更时间：2025-07-30 14:30:00

订单状态异常回退，建议联系客服确认。
```

## ⚙️ 配置选项

在 `config.json` 中新增了监控配置：

```json
{
  "settings": {
    "monitoring": {
      "monitor_all_changes": true,      // 是否监控所有状态变化
      "notify_new_orders": false,       // 是否通知新订单
      "notify_status_rollback": true,   // 是否通知状态回退
      "notify_completion": true,        // 是否通知订单完成
      "notify_cancellation": true       // 是否通知订单取消
    }
  }
}
```

## 🎨 状态变化类型说明

| 变更类型 | Emoji | 说明 | 示例 |
|---------|-------|------|------|
| 流程推进 | 📦 | 订单按正常流程向前推进 | 待备货→待确认 |
| 状态回退 | 🔄 | 订单状态向前回退，可能异常 | 待确认→待备货 |
| 订单完成 | ✅ | 订单成功完成 | 任何状态→已完成 |
| 订单取消 | ❌ | 订单被取消 | 任何状态→已取消 |

## 🔍 日志输出说明

### 状态检测日志
```
[psptId: 彭力] 🔔 检测到重要状态变化: 订单 xxx 从 '待确认' 变更为 '待备货'
[psptId: 彭力] ℹ️ 检测到状态变化: 订单 xxx 从 'A' 变更为 'B' (不发送通知)
[psptId: 彭力] ⭕ 订单 xxx 状态无变化: 待备货
[psptId: 彭力] 🆕 新订单: xxx, 状态: 待备货
```

### 通知发送日志
```
[psptId: 彭力] 🔔 发送通知: 🔄 [彭力] 状态回退通知
[psptId: 彭力] 📄 内容预览: 状态回退 - 待确认 → 待备货
```

## 🚀 升级优势

### 之前的限制
- ❌ 只监控从"待备货"开始的变化
- ❌ 错过其他重要状态变化
- ❌ 无法检测状态回退等异常情况

### 现在的优势
- ✅ 监控所有重要状态变化
- ✅ 智能判断变化重要性
- ✅ 详细的变化类型分类
- ✅ 丰富的通知内容
- ✅ 可配置的监控选项

## 📝 使用建议

1. **首次运行**：系统会记录当前状态，不发送通知
2. **状态变化**：任何重要状态变化都会触发通知
3. **异常监控**：特别关注状态回退类通知，可能需要人工干预
4. **配置调整**：根据需要调整 `config.json` 中的监控选项

## 🔧 故障排除

### 如果没有收到通知
1. 检查状态变化是否在重要变化列表中
2. 确认 `message_push.py` 功能正常
3. 查看日志中的状态检测信息
4. 验证配置文件中的监控选项

### 如果收到过多通知
1. 调整 `config.json` 中的监控选项
2. 修改 `is_important_status_change()` 函数的判断逻辑

## 📈 监控效果

通过全面的状态监控，您现在可以：
- 及时了解订单的每一个重要变化
- 快速发现异常状态回退
- 准确掌握订单完成和取消情况
- 获得更详细的状态变化描述

这样的监控机制确保您不会错过任何重要的订单状态变化！

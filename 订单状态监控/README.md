# 多psptId订单状态监控系统

## 功能概述

本系统支持同时监控多个不同psptId对应的订单状态变化，当检测到订单状态从"待备货"变更为其他状态时，自动发送微信通知。

## 文件结构

```
订单状态监控/
├── demo44.py           # 主监控脚本
├── message_push.py     # 微信推送功能
├── config.json         # 配置文件
├── order_status.json   # 状态存储文件（自动生成）
├── response.txt        # API响应格式参考
└── README.md          # 使用说明
```

## 配置文件说明 (config.json)

### pspt_configs 配置
每个psptId的配置包含以下字段：

```json
{
  "pspt_id": "实际的psptId值",
  "name": "用户标识名称",
  "description": "账户描述信息",
  "enabled": true/false
}
```

### settings 全局设置
```json
{
  "request_timeout": 30,        // API请求超时时间（秒）
  "parallel_requests": false,   // 是否并行请求（建议false避免API限制）
  "max_retries": 3,            // 最大重试次数
  "retry_delay": 5             // 重试间隔（秒）
}
```

## 使用方法

### 1. 添加新的psptId监控

编辑 `config.json` 文件，在 `pspt_configs` 数组中添加新配置：

```json
{
  "pspt_id": "新的psptId值",
  "name": "用户D",
  "description": "新用户订单监控",
  "enabled": true
}
```

### 2. 启用/禁用特定psptId

将对应配置的 `enabled` 字段设置为 `true` 或 `false`。

### 3. 运行监控脚本

```bash
python demo44.py
```

## 状态文件格式 (order_status.json)

系统使用新的分层格式存储订单状态：

```json
{
  "version": "2.0",
  "last_update": "2025-07-30 10:30:00",
  "pspt_data": {
    "psptId1": {
      "name": "用户A",
      "orders": {
        "订单号1": {
          "status": "待备货",
          "last_update": "2025-07-30 10:30:00",
          "order_sub_id": 123456789
        }
      }
    }
  }
}
```

## 向后兼容性

系统自动检测并迁移旧格式的状态文件，无需手动处理。

## 通知格式

当检测到状态变更时，系统会发送包含以下信息的通知：

```
标题：[用户A] 订单状态变更通知

内容：
账户：用户A (主账户订单监控)
订单号：2025072511155725082780
用户：彭*
手机号：183****0027
套餐：动感地带超星卡18元档-校园版
状态变更：待备货 → 已发货
变更时间：2025-07-30 14:30:00
```

## 错误处理

- **单个psptId失败**：不影响其他psptId的监控，继续处理
- **配置文件错误**：程序终止，需要修复配置文件
- **网络错误**：自动重试，超过最大重试次数后跳过该psptId

## 日志格式

所有日志都包含psptId标识，便于区分：

```
[psptId: 用户A] 正在获取订单数据...
[psptId: 用户A] 获取到 2 个订单
[psptId: 用户A] 检测到状态变化: 订单 xxx 从 '待备货' 变更为 '已发货'
```

## 性能优化建议

1. **串行处理**：默认配置为串行处理，避免API限制
2. **合理的检查频率**：建议每5-10分钟运行一次
3. **监控数量**：建议同时监控的psptId数量不超过10个

## 故障排除

### 常见问题

1. **配置文件不存在**
   - 检查 `config.json` 文件是否存在
   - 参考示例配置创建新文件

2. **API请求失败**
   - 检查网络连接
   - 验证psptId是否正确
   - 检查请求频率是否过高

3. **通知发送失败**
   - 检查 `message_push.py` 中的配置
   - 验证微信推送服务是否正常

### 调试模式

运行脚本时会输出详细日志，包括：
- 配置加载状态
- API请求结果
- 状态变化检测
- 通知发送状态

## 更新日志

### v2.0 (当前版本)
- ✅ 支持多psptId同时监控
- ✅ 配置文件管理
- ✅ 新的状态文件格式
- ✅ 向后兼容性
- ✅ 错误隔离处理
- ✅ 优化的日志和通知系统

### v1.0
- ✅ 单psptId订单监控
- ✅ 状态变更检测
- ✅ 微信通知功能

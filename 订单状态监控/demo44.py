import requests
import json
import os
import time
from datetime import datetime
from message_push import wxpusher


# 文件路径配置
STATUS_FILE = 'order_status.json'
CONFIG_FILE = 'config.json'
STATUS_FILE_VERSION = '2.0'

# HTTP请求配置
headers = {
    'Host': 'wap.hn.10086.cn',
    'sec-ch-ua-platform': '"Windows"',
    'X-Requested-With': 'XMLHttpRequest',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'sec-ch-ua-mobile': '?0',
    'Origin': 'https://wap.hn.10086.cn',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty',
    'Referer': 'https://wap.hn.10086.cn/shop/myOrder/querySimOrderList',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
}

data = {
    'psptId': '01EB0287D8AC12782B6D3FC30FDC12EF91BF95084B5DCC5CDA041DB1BD02082EDDFF6CE2BD77000D',
}


def load_config():
    """
    加载配置文件

    Returns:
        dict: 配置字典，如果文件不存在则创建默认配置
    """
    try:
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                print(f"已加载配置文件: {len(config.get('pspt_configs', []))} 个psptId")
                return config
        else:
            print(f"配置文件 {CONFIG_FILE} 不存在，请先创建配置文件")
            return None
    except json.JSONDecodeError as e:
        print(f"配置文件JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"读取配置文件时发生错误: {e}")
        return None


def create_default_config():
    """
    创建默认配置文件
    """
    default_config = {
        "pspt_configs": [
            {
                "pspt_id": "01EB0287D8AC12782B6D3FC30FDC12EF91BF95084B5DCC5CDA041DB1BD02082EDDFF6CE2BD77000D",
                "name": "用户A",
                "description": "主账户订单监控",
                "enabled": True
            }
        ],
        "settings": {
            "request_timeout": 30,
            "parallel_requests": False,
            "max_retries": 3,
            "retry_delay": 5
        }
    }

    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        print(f"已创建默认配置文件: {CONFIG_FILE}")
        return default_config
    except Exception as e:
        print(f"创建配置文件时发生错误: {e}")
        return None


def migrate_old_status_file(old_status):
    """
    迁移旧格式的状态文件到新格式

    Args:
        old_status (dict): 旧格式的状态数据

    Returns:
        dict: 新格式的状态数据
    """
    print("检测到旧格式状态文件，正在迁移...")

    # 获取默认psptId（从配置文件中获取第一个）
    config = load_config()
    if not config or not config.get('pspt_configs'):
        print("无法获取配置信息，迁移失败")
        return None

    default_pspt = config['pspt_configs'][0]

    new_status = {
        "version": STATUS_FILE_VERSION,
        "last_update": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "pspt_data": {
            default_pspt['pspt_id']: {
                "name": default_pspt['name'],
                "orders": old_status
            }
        }
    }

    print(f"已迁移 {len(old_status)} 个订单到新格式")
    return new_status


def load_order_status():
    """
    读取本地订单状态文件，支持新旧格式自动迁移

    Returns:
        dict: 订单状态字典（新格式），如果文件不存在则返回空的新格式结构
    """
    try:
        if os.path.exists(STATUS_FILE):
            with open(STATUS_FILE, 'r', encoding='utf-8') as f:
                status_data = json.load(f)

            # 检查是否为新格式
            if 'version' in status_data and status_data['version'] == STATUS_FILE_VERSION:
                print("加载新格式状态文件")
                return status_data
            else:
                # 旧格式，需要迁移
                migrated_data = migrate_old_status_file(status_data)
                if migrated_data:
                    # 保存迁移后的数据
                    save_order_status(migrated_data)
                    return migrated_data
                else:
                    print("迁移失败，返回空状态")
                    return create_empty_status()
        else:
            print(f"状态文件 {STATUS_FILE} 不存在，将创建新文件")
            return create_empty_status()
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return create_empty_status()
    except Exception as e:
        print(f"读取状态文件时发生错误: {e}")
        return create_empty_status()


def create_empty_status():
    """
    创建空的新格式状态结构

    Returns:
        dict: 空的新格式状态字典
    """
    return {
        "version": STATUS_FILE_VERSION,
        "last_update": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        "pspt_data": {}
    }


def save_order_status(status_dict):
    """
    保存订单状态到本地文件（新格式）

    Args:
        status_dict (dict): 要保存的订单状态字典（新格式）
    """
    try:
        # 更新最后修改时间
        status_dict['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        with open(STATUS_FILE, 'w', encoding='utf-8') as f:
            json.dump(status_dict, f, ensure_ascii=False, indent=2)
        print(f"订单状态已保存到 {STATUS_FILE}")
    except Exception as e:
        print(f"保存状态文件时发生错误: {e}")


def fetch_current_orders(pspt_config, settings):
    """
    获取指定psptId的当前订单数据

    Args:
        pspt_config (dict): psptId配置信息
        settings (dict): 全局设置

    Returns:
        dict: API响应数据，如果请求失败则返回None
    """
    pspt_id = pspt_config['pspt_id']
    pspt_name = pspt_config['name']
    timeout = settings.get('request_timeout', 30)
    max_retries = settings.get('max_retries', 3)
    retry_delay = settings.get('retry_delay', 5)

    print(f"[psptId: {pspt_name}] 正在获取订单数据...")

    # 构建请求数据
    request_data = {
        'psptId': pspt_id,
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(
                'https://wap.hn.10086.cn/shop/myOrder/querySimOrderData',
                headers=headers,
                data=request_data,
                timeout=timeout
            )
            response.raise_for_status()
            result = response.json()
            print(f"[psptId: {pspt_name}] API请求成功")
            return result

        except requests.exceptions.Timeout:
            print(f"[psptId: {pspt_name}] 请求超时 (尝试 {attempt + 1}/{max_retries})")
        except requests.exceptions.HTTPError as e:
            print(f"[psptId: {pspt_name}] HTTP请求错误: {e} (尝试 {attempt + 1}/{max_retries})")
        except requests.exceptions.RequestException as e:
            print(f"[psptId: {pspt_name}] 请求异常: {e} (尝试 {attempt + 1}/{max_retries})")
        except json.JSONDecodeError as e:
            print(f"[psptId: {pspt_name}] 响应JSON解析错误: {e} (尝试 {attempt + 1}/{max_retries})")
        except Exception as e:
            print(f"[psptId: {pspt_name}] 获取订单数据时发生未知错误: {e} (尝试 {attempt + 1}/{max_retries})")

        # 如果不是最后一次尝试，等待后重试
        if attempt < max_retries - 1:
            print(f"[psptId: {pspt_name}] {retry_delay}秒后重试...")
            time.sleep(retry_delay)

    print(f"[psptId: {pspt_name}] 所有重试均失败，跳过此psptId")
    return None


def detect_status_changes(pspt_config, old_pspt_orders, current_orders):
    """
    检测指定psptId下的订单状态变化（监控所有状态变化）

    Args:
        pspt_config (dict): psptId配置信息
        old_pspt_orders (dict): 该psptId的历史订单状态
        current_orders (list): 当前订单列表

    Returns:
        list: 状态变化的订单列表
    """
    changes = []
    pspt_name = pspt_config['name']

    print(f"[psptId: {pspt_name}] 正在检测状态变化...")

    for order in current_orders:
        try:
            order_no = order.get('orderSubNo')
            current_status = order.get('orderStatus', {}).get('orderStatusName', '')

            if not order_no or not current_status:
                print(f"[psptId: {pspt_name}] 订单数据不完整，跳过: {order}")
                continue

            # 检查是否有历史状态
            if order_no in old_pspt_orders:
                old_status = old_pspt_orders[order_no].get('status', '')

                # 检测任何状态变化（不仅限于从"待备货"开始）
                if old_status != current_status:
                    # 判断是否为重要的状态变化
                    if is_important_status_change(old_status, current_status):
                        changes.append({
                            'pspt_config': pspt_config,
                            'order': order,
                            'order_no': order_no,
                            'old_status': old_status,
                            'new_status': current_status,
                            'change_type': get_change_type(old_status, current_status)
                        })
                        print(f"[psptId: {pspt_name}] 🔔 检测到重要状态变化: 订单 {order_no} 从 '{old_status}' 变更为 '{current_status}'")
                    else:
                        print(f"[psptId: {pspt_name}] ℹ️ 检测到状态变化: 订单 {order_no} 从 '{old_status}' 变更为 '{current_status}' (不发送通知)")
                else:
                    print(f"[psptId: {pspt_name}] ⭕ 订单 {order_no} 状态无变化: {current_status}")
            else:
                print(f"[psptId: {pspt_name}] 🆕 新订单: {order_no}, 状态: {current_status}")

        except Exception as e:
            print(f"[psptId: {pspt_name}] 处理订单时发生错误: {e}")
            continue

    return changes


def is_important_status_change(old_status, new_status):
    """
    判断是否为重要的状态变化，需要发送通知

    Args:
        old_status (str): 原状态
        new_status (str): 新状态

    Returns:
        bool: 是否为重要变化
    """
    # 定义重要的状态变化规则
    important_changes = [
        # 从待备货到其他状态（原有逻辑）
        ("待备货", "待确认"),
        ("待备货", "已发货"),
        ("待备货", "已完成"),
        ("待备货", "已取消"),

        # 从待确认到其他状态
        ("待确认", "已发货"),
        ("待确认", "已完成"),
        ("待确认", "已取消"),
        ("待确认", "待备货"),  # 状态回退

        # 从已发货到其他状态
        ("已发货", "已完成"),
        ("已发货", "已取消"),
        ("已发货", "待确认"),  # 状态回退
        ("已发货", "待备货"),  # 状态回退

        # 任何状态到已完成
        ("待备货", "已完成"),
        ("待确认", "已完成"),
        ("已发货", "已完成"),

        # 任何状态到已取消
        ("待备货", "已取消"),
        ("待确认", "已取消"),
        ("已发货", "已取消"),
    ]

    # 检查是否在重要变化列表中
    change_tuple = (old_status, new_status)
    return change_tuple in important_changes


def get_change_type(old_status, new_status):
    """
    获取状态变化类型

    Args:
        old_status (str): 原状态
        new_status (str): 新状态

    Returns:
        str: 变化类型描述
    """
    # 定义状态优先级（数字越大表示流程越靠后）
    status_priority = {
        "待备货": 1,
        "待确认": 2,
        "已发货": 3,
        "已完成": 4,
        "已取消": 0  # 取消状态特殊处理
    }

    old_priority = status_priority.get(old_status, 999)
    new_priority = status_priority.get(new_status, 999)

    if new_status == "已取消":
        return "订单取消"
    elif new_status == "已完成":
        return "订单完成"
    elif new_priority > old_priority:
        return "流程推进"
    elif new_priority < old_priority:
        return "状态回退"
    else:
        return "状态变更"


def send_notification(change_info):
    """
    发送状态变更通知（支持多种状态变化类型）

    Args:
        change_info (dict): 状态变更信息
    """
    try:
        pspt_config = change_info['pspt_config']
        order = change_info['order']
        order_no = change_info['order_no']
        old_status = change_info['old_status']
        new_status = change_info['new_status']
        change_type = change_info.get('change_type', '状态变更')

        pspt_name = pspt_config['name']
        pspt_description = pspt_config.get('description', '')

        # 提取订单详细信息
        detail_sim = order.get('detailSim', {})
        phone = detail_sim.get('phone', '未知')
        base_set_name = detail_sim.get('baseSetName', '未知套餐')
        reg_name = detail_sim.get('regName', '未知')

        # 根据变化类型选择合适的emoji和标题
        emoji_map = {
            "订单取消": "❌",
            "订单完成": "✅",
            "流程推进": "📦",
            "状态回退": "🔄",
            "状态变更": "🔔"
        }

        emoji = emoji_map.get(change_type, "🔔")

        # 构建通知内容
        title = f"{emoji} [{pspt_name}] {change_type}通知"
        content = f"""账户：{pspt_name} ({pspt_description})
订单号：{order_no}
用户：{reg_name}
手机号：{phone}
套餐：{base_set_name}
状态变更：{old_status} → {new_status}
变更类型：{change_type}
变更时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{get_status_description(old_status, new_status)}"""

        print(f"[psptId: {pspt_name}] 🔔 发送通知: {title}")
        print(f"[psptId: {pspt_name}] 📄 内容预览: {change_type} - {old_status} → {new_status}")

        # 调用微信推送
        wxpusher(title, content, 1)  # 1表示文字类型

    except Exception as e:
        print(f"发送通知时发生错误: {e}")


def get_status_description(old_status, new_status):
    """
    获取状态变化的详细描述

    Args:
        old_status (str): 原状态
        new_status (str): 新状态

    Returns:
        str: 状态变化描述
    """
    descriptions = {
        ("待备货", "待确认"): "订单已进入确认阶段，请注意查收相关通知。",
        ("待备货", "已发货"): "订单已发货，请注意查收快递信息。",
        ("待备货", "已完成"): "订单已完成，感谢您的使用！",
        ("待备货", "已取消"): "订单已取消，如有疑问请联系客服。",

        ("待确认", "已发货"): "订单确认完成并已发货，请注意查收快递。",
        ("待确认", "已完成"): "订单确认并完成，感谢您的使用！",
        ("待确认", "已取消"): "订单在确认阶段被取消，如有疑问请联系客服。",
        ("待确认", "待备货"): "订单状态回退到待备货，可能需要重新处理。",

        ("已发货", "已完成"): "订单已送达并完成，感谢您的使用！",
        ("已发货", "已取消"): "已发货订单被取消，请联系客服处理退货事宜。",
        ("已发货", "待确认"): "订单状态异常回退，建议联系客服确认。",
        ("已发货", "待备货"): "订单状态异常回退，建议联系客服确认。",
    }

    key = (old_status, new_status)
    return descriptions.get(key, f"订单状态从 {old_status} 变更为 {new_status}，请关注后续进展。")


def update_pspt_orders(pspt_config, current_orders):
    """
    根据当前订单数据更新指定psptId的订单状态

    Args:
        pspt_config (dict): psptId配置信息
        current_orders (list): 当前订单列表

    Returns:
        dict: 更新后的订单状态字典
    """
    orders_dict = {}
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    pspt_name = pspt_config['name']

    for order in current_orders:
        try:
            order_no = order.get('orderSubNo')
            current_status = order.get('orderStatus', {}).get('orderStatusName', '')
            order_sub_id = order.get('orderSubId')

            if order_no and current_status:
                orders_dict[order_no] = {
                    'status': current_status,
                    'last_update': current_time,
                    'order_sub_id': order_sub_id
                }
        except Exception as e:
            print(f"[psptId: {pspt_name}] 更新订单状态时发生错误: {e}")
            continue

    return orders_dict


def process_pspt_orders(pspt_config, old_status_data, settings):
    """
    处理单个psptId的订单监控

    Args:
        pspt_config (dict): psptId配置信息
        old_status_data (dict): 历史状态数据
        settings (dict): 全局设置

    Returns:
        tuple: (变更列表, 更新后的订单状态)
    """
    pspt_id = pspt_config['pspt_id']
    pspt_name = pspt_config['name']

    # 获取当前订单数据
    api_response = fetch_current_orders(pspt_config, settings)
    if not api_response:
        print(f"[psptId: {pspt_name}] 获取订单数据失败，跳过")
        return [], {}

    # 检查API响应格式
    if api_response.get('code') != '0':
        print(f"[psptId: {pspt_name}] API返回错误: {api_response.get('message', '未知错误')}")
        return [], {}

    current_orders = api_response.get('data', {}).get('list', [])
    if not current_orders:
        print(f"[psptId: {pspt_name}] 当前没有订单数据")
        return [], {}

    print(f"[psptId: {pspt_name}] 获取到 {len(current_orders)} 个订单")

    # 获取该psptId的历史订单状态
    old_pspt_orders = old_status_data.get('pspt_data', {}).get(pspt_id, {}).get('orders', {})

    # 检测状态变化
    changes = detect_status_changes(pspt_config, old_pspt_orders, current_orders)

    # 更新订单状态
    updated_orders = update_pspt_orders(pspt_config, current_orders)

    return changes, updated_orders


def main():
    """
    主执行函数 - 支持多psptId监控
    """
    print("=" * 60)
    print("多psptId订单状态监控脚本启动")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        # 1. 加载配置文件
        print("正在加载配置文件...")
        config = load_config()
        if not config:
            print("配置文件加载失败，程序退出")
            return

        pspt_configs = config.get('pspt_configs', [])
        settings = config.get('settings', {})

        # 过滤启用的psptId
        enabled_configs = [cfg for cfg in pspt_configs if cfg.get('enabled', True)]
        if not enabled_configs:
            print("没有启用的psptId配置，程序退出")
            return

        print(f"找到 {len(enabled_configs)} 个启用的psptId配置")

        # 2. 读取历史状态
        print("正在读取历史状态...")
        old_status_data = load_order_status()

        # 3. 处理每个psptId
        all_changes = []
        new_status_data = {
            "version": STATUS_FILE_VERSION,
            "last_update": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "pspt_data": {}
        }

        for i, pspt_config in enumerate(enabled_configs, 1):
            pspt_name = pspt_config['name']
            pspt_id = pspt_config['pspt_id']

            print(f"\n[{i}/{len(enabled_configs)}] 处理psptId: {pspt_name}")
            print("-" * 40)

            try:
                # 处理单个psptId的订单
                changes, updated_orders = process_pspt_orders(pspt_config, old_status_data, settings)

                # 收集变更
                all_changes.extend(changes)

                # 更新状态数据
                new_status_data['pspt_data'][pspt_id] = {
                    "name": pspt_name,
                    "orders": updated_orders
                }

                print(f"[psptId: {pspt_name}] 处理完成")

            except Exception as e:
                print(f"[psptId: {pspt_name}] 处理时发生错误: {e}")
                # 保留旧数据
                if pspt_id in old_status_data.get('pspt_data', {}):
                    new_status_data['pspt_data'][pspt_id] = old_status_data['pspt_data'][pspt_id]
                continue

        # 4. 发送通知（如果有变化）
        print(f"\n{'='*60}")
        if all_changes:
            print(f"检测到 {len(all_changes)} 个状态变化，正在发送通知...")
            for change in all_changes:
                send_notification(change)
        else:
            print("未检测到任何状态变化")

        # 5. 更新状态文件
        print("正在更新状态文件...")
        save_order_status(new_status_data)

        # 6. 显示当前所有订单状态
        print(f"\n当前所有订单状态:")
        print("=" * 60)
        for pspt_id, pspt_data in new_status_data['pspt_data'].items():
            pspt_name = pspt_data['name']
            orders = pspt_data['orders']

            print(f"\npsptId: {pspt_name}")
            print("-" * 30)
            if orders:
                for order_no, info in orders.items():
                    print(f"  订单号: {order_no}")
                    print(f"  状态: {info['status']}")
                    print(f"  更新时间: {info['last_update']}")
                    print("-" * 30)
            else:
                print("  暂无订单数据")
                print("-" * 30)

        print(f"\n{'='*60}")
        print("多psptId订单状态监控完成")

    except Exception as e:
        print(f"程序执行过程中发生未知错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()


#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
from datetime import datetime

# 从demo44.py导入配置
headers = {
    'Host': 'wap.hn.10086.cn',
    'sec-ch-ua-platform': '"Windows"',
    'X-Requested-With': 'XMLHttpRequest',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'sec-ch-ua-mobile': '?0',
    'Origin': 'https://wap.hn.10086.cn',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Dest': 'empty',
    'Referer': 'https://wap.hn.10086.cn/shop/myOrder/querySimOrderList',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
}

def debug_current_status():
    """调试当前订单状态"""
    print("=" * 50)
    print("订单状态调试工具")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 1. 读取状态文件
    print("\n1. 当前状态文件内容:")
    try:
        with open('order_status.json', 'r', encoding='utf-8') as f:
            status_data = json.load(f)
        
        for pspt_id, pspt_data in status_data.get('pspt_data', {}).items():
            print(f"psptId: {pspt_data.get('name', 'Unknown')}")
            for order_no, order_info in pspt_data.get('orders', {}).items():
                print(f"  订单号: {order_no}")
                print(f"  历史状态: {order_info.get('status')}")
                print(f"  更新时间: {order_info.get('last_update')}")
    except Exception as e:
        print(f"读取状态文件失败: {e}")
    
    # 2. 获取当前API状态
    print("\n2. 当前API返回状态:")
    try:
        data = {
            'psptId': '01EB0287D8AC12782B6D3FC30FDC12EF91BF95084B5DCC5CDA041DB1BD02082EDDFF6CE2BD77000D',
        }
        
        response = requests.post(
            'https://wap.hn.10086.cn/shop/myOrder/querySimOrderData',
            headers=headers,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            api_data = response.json()
            if api_data.get('code') == '0':
                orders = api_data.get('data', {}).get('list', [])
                for order in orders:
                    order_no = order.get('orderSubNo')
                    current_status = order.get('orderStatus', {}).get('orderStatusName', '')
                    print(f"  订单号: {order_no}")
                    print(f"  当前状态: {current_status}")
            else:
                print(f"API返回错误: {api_data.get('message')}")
        else:
            print(f"HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"获取API数据失败: {e}")
    
    # 3. 状态比较
    print("\n3. 状态变化分析:")
    try:
        # 重新读取进行比较
        with open('order_status.json', 'r', encoding='utf-8') as f:
            status_data = json.load(f)
        
        pspt_id = '01EB0287D8AC12782B6D3FC30FDC12EF91BF95084B5DCC5CDA041DB1BD02082EDDFF6CE2BD77000D'
        old_orders = status_data.get('pspt_data', {}).get(pspt_id, {}).get('orders', {})
        
        data = {
            'psptId': pspt_id,
        }
        
        response = requests.post(
            'https://wap.hn.10086.cn/shop/myOrder/querySimOrderData',
            headers=headers,
            data=data,
            timeout=30
        )
        
        if response.status_code == 200:
            api_data = response.json()
            if api_data.get('code') == '0':
                current_orders = api_data.get('data', {}).get('list', [])
                
                for order in current_orders:
                    order_no = order.get('orderSubNo')
                    current_status = order.get('orderStatus', {}).get('orderStatusName', '')
                    
                    if order_no in old_orders:
                        old_status = old_orders[order_no].get('status', '')
                        print(f"  订单号: {order_no}")
                        print(f"  历史状态: {old_status}")
                        print(f"  当前状态: {current_status}")
                        
                        if old_status != current_status:
                            print(f"  ✅ 检测到状态变化: {old_status} → {current_status}")
                            
                            if old_status == "待备货" and current_status != "待备货":
                                print(f"  🔔 符合通知条件: 从'待备货'变更为'{current_status}'")
                            else:
                                print(f"  ❌ 不符合通知条件: 只监控从'待备货'的变更")
                        else:
                            print(f"  ⭕ 状态无变化")
                    else:
                        print(f"  订单号: {order_no}")
                        print(f"  🆕 新订单，当前状态: {current_status}")
                        
    except Exception as e:
        print(f"状态比较失败: {e}")

if __name__ == "__main__":
    debug_current_status()

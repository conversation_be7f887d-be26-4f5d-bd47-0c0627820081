import re,selenium
from selenium import webdriver
from time import sleep
from selenium.webdriver.common.by import By
text = ''
line_text = ''
print("")
while True:
    line_text = input()
    if line_text =='':
    	break
    text += line_text + '\n'
list=re.findall('http.*\S',text)
set=set()
wuxiao=[]
option = webdriver.EdgeOptions()
option.use_chromium = True
option.add_argument('headless')
option.add_argument('disable-gpu')
option.add_argument('--disable-blink-features=AutomationControlled')
driver = webdriver.Edge(r'C:/Users/<USER>/Downloads/edgedriver_win32/msedgedriver.exe', options=option)
for j in range(6):
    if list[::-1]:
        for i in list:
            try:
                driver.get(i)
                sleep(1)
                driver.find_element(By.XPATH, '//*[@id="J_shopHomeRoot"]/taro-view-core/div/div/taro-image-core').click()
                sleep(1)
                driver.refresh()
                a=driver.current_url
                if 'token' in a:
                    print(a)
                    set.add(a)
            except selenium.common.exceptions.NoSuchElementException:
                wuxiao.append(i)
                list.remove(i)
                print('没有店铺签到活动')
            except:
                print('未知错误，待排查')
            else:
                list.remove(i)
    else:
        break
driver.quit()
print('-------------------')
for i in set:
    print(f'\"{i[80:]}\",')
if wuxiao:
    for i in wuxiao:
        print(i)
if list:
    for i in list:
        print(i)

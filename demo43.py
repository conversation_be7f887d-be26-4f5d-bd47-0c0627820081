import requests
import re
from datetime import datetime
import base64
import hashlib
import bcrypt

X_App_Device = "wGb15GI7MXeltWLlNXYlxWZyByT3QzRI5EI7wWZ4lGUgsTZsd2bvdEI7UGbn92bHByO3YkO0QjOEdjO1YjO1QjO4QEI7AyOgsjMhVjZmVzYiFTZ4UWN0gDO"

def get_v2_token():
    device_code = X_App_Device  # 抓包headers有，固定即可
    format_base64 = re.compile('\\r\\n|\\r|\\n|=')
    token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
    device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
    timestamp = int(datetime.now().timestamp())
    timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
    timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
    token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
    token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
    token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
    token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
    arg = f'$2y$10${timestamp_base64}/{token_md5}'
    salt = (arg[:28] + 'u').encode('utf-8')
    crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
    crypt_base64 = base64.b64encode(crypt).decode()
    return f'v2{crypt_base64}'

url = "https://api.coolapk.com/v6/message/send"
params = {
    "uid": "37335471"
}

cookies1 = {
    'uid': '3297413',
    'username': '%E8%82%86%E6%84%8F%E5%B8%8C',  # 肆意希
    'token': 'c5fc267eLQIksSnVXFSNVi7-CJIgS9EGbI9uZzsCGLtHvyzuEsMDfiYRe2ZBaEHSI3IKuNOWqizKIvcbfww18L-HiDyPw-Eqqib3t1dSV7Khs-5gHiosesizXcwKBjPlYFwKlMzvfX7ssxBkyqEMIICqURuG1gkt5ya7Rdpubh6dU2_lSIac_crpy0kYD4DmpiCgcuCSIuIcAE0Kqpou_6FXZLcavA',
}

cookies2 = {
    'uid': '35626512',
    'username': '%E5%93%94%E5%93%94%E5%93%94po',  # po
    'token': '21931948N9WjO3Z4qTpDlJaD539FGI1gcGy4pP963gPgFW5o4fFVSYXdoPCb4RwacD7WBeOW6QqY4QQWSAOIulaNV8MQxSYre_3ak1__2gOQp9zKszYwjOiQyVZYjK-CT_ktUZN8Q_b3odocGPFrftqTDY_WDv8V8KqjA0LN7KRVTDSWuKu80Vt0r4PWGq4fE9PkM4eCngL691hjbveZze3w1niZnQ',
}

headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.20; 12) +CoolMarket/13.3.6-2310232-universal",
    "X-Requested-With": "XMLHttpRequest",
    "X-Sdk-Int": "31",
    "X-Sdk-Locale": "zh-CN",
    "X-App-Id": "com.coolapk.market",
    "X-App-Token": get_v2_token(),  # 请替换为您的实际Token
    "X-App-Version": "13.3.6",
    "X-App-Code": "2310232",
    "X-Api-Version": "13",
    "X-App-Device": X_App_Device,  # 请替换为您的实际设备信息
    "X-Dark-Mode": "0",
    "X-App-Channel": "coolapk",
    "X-App-Mode": "universal",
    "X-App-Supported": "2310232"
    # 不需要指定 Content-Type，requests 会自动处理
}

# 表单数据
data = {
    "message": """目前性价比比较高的卡就这些[cos滑稽]
广东星29/235g+100分钟
https://note.szfx.top/64S00
广东星29/235g+100分钟，禁发地多
https://note.szfx.top/2dG1c
黑龙江星29/225g+100分钟
https://note.szfx.top/70mfg
湖北星29/185g
https://note.szfx.top/i8Moh
黑龙江联通29/160g+100分钟
https://note.szfx.top/xciqf""",
    "message_pic": "",
    "message_extra": "",
    "message_card": ""
}

# 发送 POST 请求
response = requests.post(url, headers=headers, params=params, data=data, cookies=cookies1)

# 输出响应
print("状态码：", response.status_code)
print("响应内容：", response.json())
import json
import os
import requests

# 定义请求的URL
url = 'https://www.91haoka.cn/api/gth/order-pages2'

# 定义查询参数
params = {
    'status': '100',
    '_page': '1',
    '_page_size': '200'
}

# 定义请求头
headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Connection': 'keep-alive',
    'Cookie': 'laravel_session=eyJpdiI6ImdLWUE2akgzcjZzR3pZNlVUeTN6TWc9PSIsInZhbHVlIjoiYjNrVW5oRnpJNzI2SUJ0bXpXcSt4VHo3YXJEUkJoRFJXVWFIZGVFaThoWGl5MUpTOGxZdlJ2aE5vR1wvRlBSZTAwejVCRTZVcE9Ba2dDbStTY2k5VmlBPT0iLCJtYWMiOiI0MDIyNTdhZDljMDllNmU4ODQ2NjZkY2E4NDY1NmVhYjQxMTM3YWU4ODc3ZmYyNGRlMGU5MDVhMTUyNThhYjk0In0%3D',
    'Referer': 'https://www.91haoka.cn/91haoka_platform/',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36 Edg/129.0.0.0',
}


def load_card_data(file_path):
    """
    从指定的 JSON 文件加载现有的卡片数据。
    如果文件不存在或内容无效，则返回空字典。
    """
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as file:
            try:
                return json.load(file)
            except json.JSONDecodeError:
                print("警告：无法解析 JSON 文件，开始使用空数据。")
                return {}
    return {}


def save_card_data(file_path, data):
    """
    将卡片数据保存到指定的 JSON 文件中。
    """
    with open(file_path, 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def update_card_data(new_data, card_data):
    """
    根据新的数据更新卡片数据。

    参数:
    - new_data: 新的原始 JSON 数据（字典）。
    - card_data: 当前的卡片数据（字典）。

    返回:
    - 更新后的卡片数据（字典）。
    """
    product_shop_id = str(new_data.get("product_shop_id"))
    if not product_shop_id:
        print("警告：'product_shop_id' 缺失，跳过该条数据。")
        return card_data

    # 提取新的字段
    # 变更次数初始值为1，如果已经存在则会在后续处理时增加
    new_change_count = 1
    new_product_name = new_data.get("product_name", "")
    new_order_pages_id_str = new_data.get("order_pages_id", "")
    new_title = new_data.get("title", "")
    new_order_pages_ids = new_order_pages_id_str.split('/') if new_order_pages_id_str else []
    new_first_order_page = new_order_pages_ids[0] if new_order_pages_ids else ""

    if product_shop_id not in card_data:
        # 如果是新的 product_shop_id，直接添加
        card_data[product_shop_id] = {
            "change_count": new_change_count,
            "products": [
                {
                    "product_name": new_product_name,
                    "title": new_title,
                    "order_pages_ids": new_order_pages_ids
                }
            ]
        }
        print(f"添加新的 product_shop_id: {product_shop_id}")
    else:
        # 检查现有数据中是否存在相同的第一个 order_page_id
        existing_products = card_data[product_shop_id].get("products", [])
        first_page_exists = any(
            entry.get("order_pages_ids", [])[0] == new_first_order_page
            for entry in existing_products
            if entry.get("order_pages_ids")
        )
        if first_page_exists:
            # 如果存在相同的第一个 order_page_id，则跳过
            print(f"跳过 product_shop_id {product_shop_id}，因为第一个 order_page_id 已存在。")
        else:
            # 否则，添加新的条目并增加 change_count 的值
            card_data[product_shop_id]['change_count'] += new_change_count
            card_data[product_shop_id]['products'].append({
                "product_name": new_product_name,
                "title": new_title,
                "order_pages_ids": new_order_pages_ids
            })
            print(
                f"在 product_shop_id {product_shop_id} 下添加新条目，并将 change_count 增加到 {card_data[product_shop_id]['change_count']}。")

    return card_data


def main():
    response = requests.get(url, headers=headers, params=params)

    # 检查响应状态码
    if response.status_code == 200:
        # 解析JSON响应
        data = response.json()
    else:
        print(f"请求失败，状态码：{response.status_code}")
    # 示例原始 JSON 数据列表
    original_json_list = data['data']['data']

    # 指定存储卡片数据的文件
    card_file = 'card.json'

    # 加载现有的卡片数据
    existing_card_data = load_card_data(card_file)

    # 逐一处理 original_json 列表中的每个对象
    for original_json in original_json_list:
        existing_card_data = update_card_data(original_json, existing_card_data)

    # 保存更新后的卡片数据
    save_card_data(card_file, existing_card_data)

    # 可选：打印更新后的卡片数据
    print(json.dumps(existing_card_data, ensure_ascii=False, indent=4))


if __name__ == "__main__":
    main()

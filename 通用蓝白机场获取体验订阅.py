import requests
from random import randint


def wxpusherts(msg):
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": msg,
        "summary": 'MT云间隔1.5小时推送',
        "contentType": 1,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"]
    }
    headers = {
        "Content-Type": "application/json"
    }
    req = requests.post(url, json=data, headers=headers)


def zh():
    a = '0123456789'
    b = [a[randint(0, 9)] for i in range(10)]
    return (''.join(b) + '@qq.com')


zhanghao = zh()
url = 'https://kfccloud.cc/api/v1/passport/auth/register'
data = {
    'email': zhang<PERSON>,
    'password': '12345678'
}

session = requests.Session()
response = session.post(url, data=data, timeout=5)

url = 'https://kfccloud.cc/api/v1/passport/auth/login'
data = {
    'email': zhanghao,
    'password': '12345678'
}

response = session.post(url,  data=data, timeout=5)
headers = {
    "authorization": response.json()['data']['auth_data']
}
url = 'https://kfccloud.cc/api/v1/user/getSubscribe'
response = session.get(url,headers=headers, timeout=5)
print(response.json()['data']['subscribe_url'])

import requests
import os
import warnings
import json
from urllib3.exceptions import InsecureRequestWarning
from colorama import init, Fore

# 初始化colorama
init(autoreset=True)

PROXY_URL = 'http://127.0.0.1:10809'
os.environ['HTTP_PROXY'] = PROXY_URL
os.environ['HTTPS_PROXY'] = PROXY_URL
warnings.simplefilter('ignore', InsecureRequestWarning)



headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}
data = {
    "name": "ty we",
    "email": "<EMAIL>",
    "password": "12345qwert"
}

# 文件路径
input_file_path = 'C:\\Users\\<USER>\\自用程序\\fofa爬虫\\LibreChat.txt'
json_file_path = 'openwebui.json'

# 读取Nextchat.json数据
try:
    with open(json_file_path, 'r') as json_file:
        nextchat_data = json.load(json_file)
except FileNotFoundError:
    nextchat_data = {}  # 如果文件不存在，则初始化为空字典
except json.JSONDecodeError:
    nextchat_data = {}  # 如果JSON格式错误，初始化为空字典

counter = len(nextchat_data) + 1  # 计算已有条目的数量

with open(input_file_path, 'r') as file:
    for line in file:
        line = line.strip()
        try:
            response = requests.post(f'{line}/api/v1/auths/signup', headers=headers, data=json.dumps(data), verify=False, timeout=5, allow_redirects=False)
            if response.status_code == 200:
                print(f"{line} 注册成功")
                if "role" in response.json():
                    if response.json()["role"] == "pending":
                        print(Fore.RED + "pending")  # 用红色字体输出
                    else:
                        print(Fore.GREEN + response.json()["role"])  # 用绿色字体输出
                        # 将符合要求的URL写入Nextchat.json（使用数字键）
                        nextchat_data[counter] = line  # 使用计数器作为键
                        counter += 1
                        with open(json_file_path, 'w') as json_file:
                            json.dump(nextchat_data, json_file, indent=4)  # 实时写入
                else:
                    print(Fore.RED + "网站没有提供角色信息")
            else:
                print(f'{line} {response.status_code}')
        except requests.exceptions.ProxyError as e:
            print(f"代理错误：{line}, 错误信息：{e}")
        except requests.exceptions.ReadTimeout:
            print(f"访问超时：{line}, 跳过此链接")
        except requests.exceptions.RequestException as e:
            print(f"请求遇到错误：{e}, {line}")
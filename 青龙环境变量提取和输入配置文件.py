import requests
from json import dumps as jsonDumps


class QL:
    def __init__(self, address: str, id: str, secret: str) -> None:
        """
        初始化
        """
        self.address = address
        self.id = id
        self.secret = secret
        self.valid = True
        self.login()

    def log(self, content: str) -> None:
        """
        日志
        """
        print(content)

    def login(self) -> None:
        """
        登录
        """
        url = f"{self.address}/open/auth/token?client_id={self.id}&client_secret={self.secret}"
        try:
            rjson = requests.get(url).json()
            if (rjson['code'] == 200):
                self.auth = f"{rjson['data']['token_type']} {rjson['data']['token']}"
            else:
                self.log(f"登录失败：{rjson['message']}")
        except Exception as e:
            self.valid = False
            self.log(f"登录失败：{str(e)}")

    def getEnvs(self) -> list:
        """
        获取环境变量
        """
        url = f"{self.address}/open/envs?searchValue=JD_R_WSCK"
        headers = {"Authorization": self.auth}
        try:
            rjson = requests.get(url, headers=headers).json()
            if (rjson['code'] == 200):
                return rjson['data']
            else:
                self.log(f"获取环境变量失败：{rjson['message']}")
        except Exception as e:
            self.log(f"获取环境变量失败：{str(e)}")

    def deleteEnvs(self, ids: list) -> bool:
        """
        删除环境变量
        """
        url = f"{self.address}/open/envs"
        headers = {"Authorization": self.auth, "content-type": "application/json"}
        try:
            rjson = requests.delete(url, headers=headers, data=jsonDumps(ids)).json()
            if (rjson['code'] == 200):
                self.log(f"删除环境变量成功：{len(ids)}")
                return True
            else:
                self.log(f"删除环境变量失败：{rjson['message']}")
                return False
        except Exception as e:
            self.log(f"删除环境变量失败：{str(e)}")
            return False

    def addEnvs(self, envs: list) -> bool:
        """
        新建环境变量
        """
        url = f"{self.address}/open/envs"
        headers = {"Authorization": self.auth, "content-type": "application/json"}
        try:
            rjson = requests.post(url, headers=headers, data=jsonDumps(envs)).json()
            if (rjson['code'] == 200):
                self.log(f"新建环境变量成功：{len(envs)}")
                return True
            else:
                self.log(f"新建环境变量失败：{rjson['message']}")
                return False
        except Exception as e:
            self.log(f"新建环境变量失败：{str(e)}")
            return False

    def updateEnv(self, env: dict) -> bool:
        """
        更新环境变量
        """
        url = f"{self.address}/open/envs"
        headers = {"Authorization": self.auth, "content-type": "application/json"}
        try:
            rjson = requests.put(url, headers=headers, data=jsonDumps(env)).json()
            if (rjson['code'] == 200):
                self.log(f"更新环境变量成功")
                return True
            else:
                self.log(f"更新环境变量失败：{rjson['message']}")
                return False
        except Exception as e:
            self.log(f"更新环境变量失败：{str(e)}")
            return False


if __name__ == "__main__":
    # 本地青龙1
    if 1:
        address = "http://127.0.0.1:5701"
        client_id = "d-EUJ9Df7I9e"
        client_secret = "_0mbRyNXV6CAd5QSIgGInxUH"

    # 本地青龙2
    if 0:
        address = "http://127.0.0.1:5702"
        client_id = "0QCBDgt1j-Ma"
        client_secret = "9-4S6aK5uwASdIYc6G1PItCL"

    # 本地青龙3
    if 0:
        address = "http://127.0.0.1:5703"
        client_id = "8O-xwRI9kOCs"
        client_secret = "d-FRlralOYZtp12hxabRazq-"

    # 本地青龙4
    if 0:
        address = "http://127.0.0.1:5704"
        client_id = "o-9uMn-aYZ8x"
        client_secret = "VA8BiiljNETb4-tR10_wXK2n"


    #  云端青龙1
    if 0:
        address = "http://*************:5700"
        client_id = "Ek-8LWibLQ7P"
        client_secret = "_s9gtLNrBe_Rx-sqVP5W_qBw"

    # 云端青龙2
    if 0:
        address = "http://*************:5702"
        client_id = "F-byT86j-eLp"
        client_secret = "0i_ER46EfiCJXrm9GlDXfLC5"

    # 云端青龙3
    if 0:
        address = "http://*************:5703"
        client_id = "xcwD_Bu_4hhY"
        client_secret = "3C4nFnonwzBT1AlhmxdS_9zN"

    ql = QL(address, client_id, client_secret)
    if 0:
        envs = ql.getEnvs()
        with open("JD_R_WSCK.txt", "w") as f:
            for i in envs:
                print(i)
                f.write(f"{i['value']}\n")
    if 1:
        with open('666.txt', 'r') as f:
            a = f.read().split('\n')
            for i in a:
                print(i)
                envs = [{"name": "JD_R_WSCK", "value": i}]
                result = ql.addEnvs(envs)
                print(result)
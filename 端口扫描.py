import socket
from concurrent.futures import ThreadPoolExecutor


# 定义扫描函数
def scan_port(ip, port):
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)  # 设置超时
        result = sock.connect_ex((ip, port))
        sock.close()
        if result == 0:
            return port
    except Exception as e:
        pass
    return None


# 扫描 IP 范围内的端口
def scan_ip_ports(ip, start_port=1, end_port=65535):
    open_ports = []
    with ThreadPoolExecutor(max_workers=100) as executor:
        futures = [executor.submit(scan_port, ip, port) for port in range(start_port, end_port + 1)]
        for future in futures:
            port = future.result()
            if port:
                open_ports.append(port)
    return open_ports


if __name__ == "__main__":
    target_ip = input("请输入要扫描的IP地址: ")
    open_ports = scan_ip_ports(target_ip)
    if open_ports:
        print(f"在 {target_ip} 上开放的端口有: {', '.join(map(str, open_ports))}")
    else:
        print(f"{target_ip} 上没有发现开放的端口。")

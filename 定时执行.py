import time
import requests
import datetime

# 定义请求的URL和负载数据
url = 'http://8.134.146.246:8866/change_cj'
msg = '666'
data = {'msg': msg}
headers = {'Content-Type': 'application/json'}

# 定义请求的起始时间和结束时间
start_time = "14:34:18"  # 下午 2 点 59 分 59 秒
end_time = "14:34:20"  # 下午 3 点 0 分 2 秒

# 将起始时间和结束时间转换为 datetime 对象
start_dt = datetime.datetime.strptime(start_time, "%H:%M:%S")
end_dt = datetime.datetime.strptime(end_time, "%H:%M:%S")

# 设置请求之间的时间间隔，单位为毫秒
interval = 100

# 循环直到当前时间超过结束时间
while True:
    # 获取当前时间
    current_time = datetime.datetime.now().time()

    # 如果当前时间超过结束时间，则跳出循环
    if current_time > end_dt.time():
        break

    # 如果当前时间在起始时间和结束时间范围内，则发送请求
    if start_dt.time() <= current_time <= end_dt.time():
        response = requests.post(url, json=data)
        print(response.json()['result'])

    # 等待一定时间后再发送下一个请求
    time.sleep(interval / 1000)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品监控系统测试脚本
用于测试API连接和推送功能

作者: Claude 4.0 sonnet
"""

import json
import sys
import os
from monitor import ProductMonitor
from message_push import wxpusher

def test_config():
    """测试配置文件"""
    print("🔧 测试配置文件...")
    try:
        monitor = ProductMonitor()
        print("✅ 配置文件加载成功")
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def test_api_connection():
    """测试API连接"""
    print("🌐 测试API连接...")
    try:
        monitor = ProductMonitor()
        data = monitor.fetch_api_data(1)
        if data and data.get("msg", {}).get("code") == 0:
            items_count = len(data.get("data", {}).get("data", []))
            print(f"✅ API连接成功，获取到 {items_count} 条数据")
            return True
        else:
            print("❌ API返回错误或无数据")
            return False
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False

def test_message_push():
    """测试消息推送"""
    print("📱 测试消息推送...")
    try:
        test_title = "🧪 商品监控系统测试"
        test_content = """
<div style="border-left: 3px solid #4CAF50; padding-left: 10px; margin: 10px 0;">
<strong>📱 测试上架商品</strong><br>
供应商: 测试供应商<br>
时间: 2025-07-23 12:00:00<br>
ID: 99999999
</div>
<div style="border-left: 3px solid #f44336; padding-left: 10px; margin: 10px 0;">
<strong>🚫 测试下架商品</strong><br>
供应商: 测试供应商<br>
时间: 2025-07-23 11:59:00<br>
ID: 99999998
</div>
<p><em>如果您收到这条消息，说明推送功能正常工作！</em></p>
"""
        
        wxpusher(test_title, test_content, 2)
        print("✅ 测试消息发送成功，请检查您的微信")
        return True
    except Exception as e:
        print(f"❌ 消息推送测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理和新的消息格式"""
    print("📊 测试数据处理和消息格式...")
    try:
        monitor = ProductMonitor()
        
        # 模拟测试数据（按n_id降序，模拟API返回顺序）
        test_items = [
            {
                "n_id": 99999999,
                "type": 4,
                "title": "测试商品上架（最新）",
                "name": "测试供应商",
                "nu_time": "2025-07-23 12:01:00"
            },
            {
                "n_id": 99999998,
                "type": 5,
                "title": "测试商品下架",
                "name": "测试供应商",
                "nu_time": "2025-07-23 12:00:00"
            },
            {
                "n_id": 99999997,
                "type": 4,
                "title": "测试商品上架（较早）",
                "name": "测试供应商",
                "nu_time": "2025-07-23 11:59:00"
            }
        ]
        
        # 清空历史数据中的测试项
        for n_id in ["99999999", "99999998", "99999997"]:
            monitor.history.pop(n_id, None)
        
        # 分析变化
        listings, delistings = monitor.analyze_changes(test_items)
        
        if len(listings) == 2 and len(delistings) == 1:
            print("✅ 数据处理功能正常")
            
            # 测试新的消息格式化
            title, content = monitor.format_message(listings, delistings)
            if title and content:
                print("✅ 消息格式化功能正常")
                print(f"标题: {title}")
                print("内容预览:")
                # 简化显示内容预览
                if "📱 测试商品上架（最新）" in content and "🚫 测试商品下架" in content:
                    print("  - 包含上架和下架商品信息")
                    print("  - 按时间顺序混合显示")
                    print("  - 保持颜色区分")
                return True
            else:
                print("❌ 消息格式化失败")
                return False
        else:
            print(f"❌ 数据处理异常: 上架{len(listings)}个, 下架{len(delistings)}个")
            return False
            
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False

def run_single_check():
    """运行单次检查"""
    print("🔍 运行单次监控检查...")
    try:
        monitor = ProductMonitor()
        success = monitor.run_single_check()
        if success:
            print("✅ 单次检查完成，请查看日志文件获取详细信息")
        else:
            print("⚠️ 单次检查完成，但未获取到数据")
        return True
    except Exception as e:
        print(f"❌ 单次检查失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("           商品监控系统 - 功能测试")
    print("=" * 60)
    
    tests = [
        ("配置文件", test_config),
        ("API连接", test_api_connection),
        ("数据处理和消息格式", test_data_processing),
        ("消息推送", test_message_push),
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        print(f"\n{name}测试:")
        print("-" * 30)
        if test_func():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 项通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行")
        
        # 询问是否运行单次检查
        try:
            choice = input("\n是否运行一次完整的监控检查？(y/n): ").lower().strip()
            if choice in ['y', 'yes', '是']:
                print()
                run_single_check()
        except KeyboardInterrupt:
            print("\n测试结束")
    else:
        print("❌ 部分测试失败，请检查配置和网络连接")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

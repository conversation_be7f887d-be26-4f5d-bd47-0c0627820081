#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
号卡平台商品监控系统 - 轻量级单文件版本
监控商品上下架状态变化并自动推送通知

作者: Claude 4.0 sonnet
版本: 1.0.0
"""

import json
import time
import logging
import requests
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from message_push import wxpusher

# 配置常量
CONFIG_FILE = "config.json"
HISTORY_FILE = "history.json"
LOG_FILE = "monitor.log"

class ProductMonitor:
    def __init__(self):
        """初始化监控器"""
        self.config = self.load_config()
        self.setup_logging()
        self.session = requests.Session()
        self.history = self.load_history()
        
        # 设置请求头
        self.session.headers.update(self.config["api"]["headers"])
        self.session.cookies.update(self.parse_cookies(self.config["api"]["cookies"]))
        
        logging.info("商品监控系统启动成功")
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"配置文件 {CONFIG_FILE} 不存在")
            raise
        except json.JSONDecodeError as e:
            logging.error(f"配置文件格式错误: {e}")
            raise
    
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        for item in cookie_string.split('; '):
            if '=' in item:
                key, value = item.split('=', 1)
                cookies[key] = value
        return cookies
    
    def load_history(self) -> Dict:
        """加载历史数据"""
        try:
            with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.info("历史文件不存在，创建新的历史记录")
            return {}
        except json.JSONDecodeError:
            logging.warning("历史文件格式错误，重新创建")
            return {}
    
    def save_history(self):
        """保存历史数据"""
        try:
            with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logging.error(f"保存历史数据失败: {e}")
    
    def fetch_api_data(self, page: int = 1) -> Optional[Dict]:
        """获取API数据"""
        params = self.config["api"]["params"].copy()
        params["page"] = page
        
        headers = {
            "X-XSRF-TOKEN": self.config["api"]["xsrf_token"]
        }
        
        try:
            response = self.session.get(
                self.config["api"]["base_url"],
                params=params,
                headers=headers,
                timeout=self.config["monitor"]["request_timeout"]
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求失败 (页面 {page}): {e}")
            return None
        except json.JSONDecodeError as e:
            logging.error(f"API响应解析失败 (页面 {page}): {e}")
            return None
    
    def fetch_first_page_data(self) -> List[Dict]:
        """获取第一页数据（单次执行模式）"""
        logging.info("正在获取第一页数据...")
        data = self.fetch_api_data(1)

        if not data or data.get("msg", {}).get("code") != 0:
            logging.warning("第一页数据获取失败或返回错误")
            return []

        items = data.get("data", {}).get("data", [])
        logging.info(f"获取到 {len(items)} 条商品通知")
        return items

    def analyze_changes(self, current_items: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """分析商品状态变化"""
        new_listings = []  # 新上架
        new_delistings = []  # 新下架

        for item in current_items:
            n_id = str(item.get("n_id"))
            item_type = item.get("type")

            # 检查是否为新通知
            if n_id not in self.history:
                if item_type == 4:  # 上架
                    new_listings.append(item)
                elif item_type == 5:  # 下架
                    new_delistings.append(item)

                # 记录到历史
                self.history[n_id] = {
                    "type": item_type,
                    "title": item.get("title", ""),
                    "name": item.get("name", ""),
                    "nu_time": item.get("nu_time", ""),
                    "first_seen": datetime.now().isoformat()
                }

        return new_listings, new_delistings

    def format_message(self, listings: List[Dict], delistings: List[Dict]) -> Tuple[str, str]:
        """格式化推送消息（按时间顺序混合显示）"""
        if not listings and not delistings:
            return "", ""

        # 生成标题
        title_parts = []
        if listings:
            title_parts.append(f"📱 {len(listings)}个商品上架")
        if delistings:
            title_parts.append(f"🚫 {len(delistings)}个商品下架")
        title = " | ".join(title_parts)

        # 合并所有商品变化，并标记类型
        all_changes = []
        for item in listings:
            all_changes.append({**item, 'change_type': 'listing'})
        for item in delistings:
            all_changes.append({**item, 'change_type': 'delisting'})

        # 按照n_id降序排列（API返回的数据顺序，新的在前）
        all_changes.sort(key=lambda x: x.get('n_id', 0), reverse=True)

        # 限制显示数量
        max_items = self.config["push"]["max_batch_size"]
        all_changes = all_changes[:max_items]

        # 生成内容
        content_parts = []
        for item in all_changes:
            if item['change_type'] == 'listing':
                # 上架商品 - 绿色边框
                content_parts.append(f"""
<div style="border-left: 3px solid #4CAF50; padding-left: 10px; margin: 10px 0;">
<strong>📱 {item.get('title', '未知商品')}</strong><br>
供应商: {item.get('name', '未知')}<br>
时间: {item.get('nu_time', '未知')}<br>
ID: {item.get('n_id', '未知')}
</div>""")
            else:
                # 下架商品 - 红色边框
                content_parts.append(f"""
<div style="border-left: 3px solid #f44336; padding-left: 10px; margin: 10px 0;">
<strong>🚫 {item.get('title', '未知商品')}</strong><br>
供应商: {item.get('name', '未知')}<br>
时间: {item.get('nu_time', '未知')}<br>
ID: {item.get('n_id', '未知')}
</div>""")

        content = "".join(content_parts)
        return title, content

    def send_notification(self, title: str, content: str):
        """发送推送通知"""
        if not self.config["push"]["enabled"]:
            logging.info("推送功能已禁用")
            return

        try:
            wxpusher(title, content, self.config["push"]["content_type"])
            logging.info(f"推送通知成功: {title}")
        except Exception as e:
            logging.error(f"推送通知失败: {e}")

    def cleanup_history(self):
        """清理过期的历史数据"""
        max_days = self.config["storage"]["max_history_days"]
        cutoff_date = datetime.now() - timedelta(days=max_days)

        to_remove = []
        for n_id, data in self.history.items():
            try:
                first_seen = datetime.fromisoformat(data.get("first_seen", ""))
                if first_seen < cutoff_date:
                    to_remove.append(n_id)
            except (ValueError, TypeError):
                # 如果日期格式错误，也删除
                to_remove.append(n_id)

        for n_id in to_remove:
            del self.history[n_id]

        if to_remove:
            logging.info(f"清理了 {len(to_remove)} 条过期历史记录")

    def run_single_check(self):
        """执行单次监控检查（单次执行模式）"""
        logging.info("开始执行单次监控检查...")

        # 获取第一页数据
        current_items = self.fetch_first_page_data()
        if not current_items:
            logging.warning("未获取到任何数据，检查结束")
            return False

        # 分析变化
        new_listings, new_delistings = self.analyze_changes(current_items)

        # 发送通知
        if new_listings or new_delistings:
            title, content = self.format_message(new_listings, new_delistings)
            if title and content:
                self.send_notification(title, content)

            logging.info(f"发现变化: {len(new_listings)} 个上架, {len(new_delistings)} 个下架")
        else:
            logging.info("未发现商品状态变化")

        # 保存历史数据
        self.save_history()

        # 清理过期数据
        self.cleanup_history()

        logging.info("单次监控检查完成")
        return True

def main():
    """主函数（单次执行模式）"""
    try:
        monitor = ProductMonitor()
        success = monitor.run_single_check()
        return 0 if success else 1
    except KeyboardInterrupt:
        logging.info("程序被用户中断")
        return 0
    except Exception as e:
        logging.error(f"程序执行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

import requests


def wxpusher(title: str, content: str, content_type: int) -> None:
    """
    发送消息到微信推送服务。

    参数:
        title (str): 消息标题。
        content (str): 消息内容。
        content_type (int): 内容类型。
            1 - 文字
            2 - HTML（仅发送<body>标签内部的数据，不包括<body>标签，推荐使用）
            3 - Markdown
    """
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": content,
        "summary": title,
        "contentType": content_type,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"]
    }
    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        result = response.json()
        print(result)
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP错误发生: {http_err}")
    except Exception as err:
        print(f"其他错误发生: {err}")

#!/bin/bash
# 商品监控系统定时任务示例脚本
# 作者: Claude 4.0 sonnet

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 记录执行时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始执行商品监控检查" >> cron.log

# 执行监控脚本
python3 monitor.py >> cron.log 2>&1

# 记录完成时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 监控检查完成" >> cron.log
echo "----------------------------------------" >> cron.log

# 清理过期日志（保留最近100行）
tail -n 100 cron.log > cron.log.tmp && mv cron.log.tmp cron.log

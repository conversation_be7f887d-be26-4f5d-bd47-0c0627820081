# 号卡平台商品监控系统

## 功能介绍

这是一个轻量级的商品监控系统，用于监控91号卡平台的商品上下架状态变化，并自动推送通知。

**运行模式**: 单次执行模式 - 每次运行脚本执行一次检查后自动退出，适合通过定时任务定期调用。

### 主要功能
- 🔍 单次检查商品上架/下架状态（仅检查第一页数据）
- 📱 智能推送通知（支持批量推送）
- 🚫 智能去重，避免重复推送
- 📊 完善的日志记录
- ⚙️ 可配置化管理
- 🔄 自动重试和错误处理
- ⚡ 快速执行，适合频繁调用

## 文件说明

- `monitor.py` - 主监控程序
- `config.json` - 配置文件
- `message_push.py` - 消息推送模块（已存在）
- `requirements.txt` - Python依赖包
- `history.json` - 历史数据存储（自动生成）
- `monitor.log` - 运行日志（自动生成）

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置设置
编辑 `config.json` 文件，主要配置项：

- **API配置**: 包含请求URL、参数、Cookie等
- **监控配置**: 轮询间隔、重试次数等
- **推送配置**: 是否启用推送、消息格式等

### 3. 运行程序
```bash
# 单次执行（推荐）
python start.py

# 或直接运行监控脚本
python monitor.py

# 测试功能
python test.py
```

### 4. 定时任务设置
由于程序采用单次执行模式，建议通过系统定时任务定期调用：

**Linux/Mac (crontab)**:
```bash
# 每5分钟执行一次
*/5 * * * * cd /path/to/号卡平台商品监控 && python monitor.py

# 每10分钟执行一次
*/10 * * * * cd /path/to/号卡平台商品监控 && python monitor.py
```

**Windows (任务计划程序)**:
- 创建基本任务
- 设置触发器为"每天"或"每小时"
- 操作：启动程序 `python.exe`
- 参数：`monitor.py`
- 起始于：脚本所在目录

## 配置说明

### API配置
- `cookies`: 从浏览器复制的Cookie字符串
- `xsrf_token`: CSRF令牌，需要与Cookie中的XSRF-TOKEN保持一致

### 监控配置
- `request_timeout`: 请求超时时间
- `retry_attempts`: 重试次数
- `retry_delay`: 重试间隔时间

### 推送配置
- `enabled`: 是否启用推送功能
- `content_type`: 消息格式（1=文字，2=HTML，3=Markdown）
- `batch_notifications`: 是否批量推送
- `max_batch_size`: 单次推送最大商品数量

## 消息格式

### 混合状态变化通知
```
📱 2个商品上架 | 🚫 1个商品下架

📱 联通专享卡 9元130g流量+500分钟通话+会员
供应商: sijia
时间: 2025-07-23 18:21:12
ID: 78324420

🚫 联通盛世卡29包100G通用 200分钟（免费副卡）
供应商: sijia
时间: 2025-07-23 14:29:12
ID: 78357511

📱 电信魔都卡29元240G通用+30G定向+100分钟
供应商: 派大星卡号
时间: 2025-07-23 13:56:25
ID: 78348880
```

**说明**:
- 所有商品变化按时间顺序混合显示（最新的在前）
- 📱 绿色边框表示上架商品
- 🚫 红色边框表示下架商品
- 标题显示变化统计，内容按原始数据顺序排列

## 注意事项

1. **Cookie更新**: Cookie可能会过期，需要定期从浏览器更新
2. **执行频率**: 建议通过定时任务每5-10分钟执行一次，避免过于频繁
3. **网络环境**: 确保网络连接稳定，程序会自动重试失败的请求
4. **日志监控**: 定期查看日志文件，了解程序运行状态
5. **单次执行**: 程序每次只检查第一页数据，执行完毕后自动退出

## 故障排除

### 常见问题
1. **API请求失败**: 检查Cookie是否过期，网络是否正常
2. **推送失败**: 检查message_push.py中的推送配置
3. **程序崩溃**: 查看monitor.log日志文件获取详细错误信息

### 日志级别
- INFO: 正常运行信息
- WARNING: 警告信息，程序可继续运行
- ERROR: 错误信息，可能影响功能

## 技术支持

如有问题，请查看日志文件或联系开发者。

---
*开发者: Claude 4.0 sonnet*  
*版本: 1.0.0*

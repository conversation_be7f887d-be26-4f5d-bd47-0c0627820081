{"api": {"base_url": "https://www.91haoka.cn/api/system/notice/center", "params": {"page": 1, "page_size": 20, "type": 1, "on_shelf": "", "supplier_id": ""}, "headers": {"Accept": "application/json, text/plain, */*", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6", "Cache-Control": "no-cache", "Connection": "keep-alive", "Pragma": "no-cache", "Referer": "https://www.91haoka.cn/gth/", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "same-origin", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"}, "cookies": "Hm_lvt_fb02bd00f4c43c61dcc04d06bd80fb5d=**********; Hm_lvt_6a2cc1e72ce15ae483eccb4455431eba=**********; HMACCOUNT=38AEDE5530DCA8FB; Hm_lvt_fb02bd00f4c43c61dcc04d06bd80fb5d=**********; Hm_lvt_6a2cc1e72ce15ae483eccb4455431eba=**********; Hm_lvt_6fb1cdb2b70e9e2f602787e9d9156774=**********; Hm_lpvt_6fb1cdb2b70e9e2f602787e9d9156774=**********; Hm_lpvt_fb02bd00f4c43c61dcc04d06bd80fb5d=**********; Hm_lpvt_6a2cc1e72ce15ae483eccb4455431eba=**********; XSRF-TOKEN=eyJpdiI6IkxLTDZEQ1FBUzJybVBORFRZQlB3dlE9PSIsInZhbHVlIjoiVnJxRm9YZ2JRdFNmU1c3MnVFUWZ6dnQ0RklFdE1mUHNxOEFUclRlM1ZQcFJTSm55Y0M2SVFYWGkxRW9Xa25pa01yQlJVa0FWV0hFcFBDUW5OclZ6d2c9PSIsIm1hYyI6IjdkZDZlNTY2ZDExNTBhZmE5NzAzODQ1NzJkMTEwMGRhNDFlNjYzZmM5ZDAwYTFhY2NjOTEzMmYyODZlMzY4MjcifQ%3D%3D; laravel_session=eyJpdiI6IjdzSzlBMDFGV2orbjgzclBsb25XaHc9PSIsInZhbHVlIjoiS05kU3B0VTlYeDhKYjg3Sks2K3ZzM0JSUFZXZWRXZWJsNHE3ckhZa0NaK2F1RUV1Znp3SDlTQXJBUkVNSjlYSUZnMW9JMjNTd3gyNWJua1RUN1wvNlBBPT0iLCJtYWMiOiIyNjJhNTc0NDZlNzQzN2QyNGI0Nzc1ZDM4NGNkNWQ4ZDliOGNmMjRkOTIxZDQ5Mjc5MjNiNmQyZjMwYzE5MGE0In0%3D", "xsrf_token": "eyJpdiI6IkxLTDZEQ1FBUzJybVBORFRZQlB3dlE9PSIsInZhbHVlIjoiVnJxRm9YZ2JRdFNmU1c3MnVFUWZ6dnQ0RklFdE1mUHNxOEFUclRlM1ZQcFJTSm55Y0M2SVFYWGkxRW9Xa25pa01yQlJVa0FWV0hFcFBDUW5OclZ6d2c9PSIsIm1hYyI6IjdkZDZlNTY2ZDExNTBhZmE5NzAzODQ1NzJkMTEwMGRhNDFlNjYzZmM5ZDAwYTFhY2NjOTEzMmYyODZlMzY4MjcifQ=="}, "monitor": {"request_timeout": 10, "retry_attempts": 3, "retry_delay": 5}, "storage": {"history_file": "history.json", "log_file": "monitor.log", "max_history_days": 7}, "push": {"enabled": true, "content_type": 2, "batch_notifications": true, "max_batch_size": 10}}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品监控系统启动脚本
提供友好的启动界面和配置检查

作者: Claude 4.0 sonnet
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    try:
        import requests
        print("✅ 依赖包检查通过")
        return True
    except ImportError:
        print("❌ 缺少依赖包，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✅ 依赖包安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动执行: pip install -r requirements.txt")
            return False

def check_config():
    """检查配置文件"""
    config_file = "config.json"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件 {config_file} 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查关键配置
        required_keys = [
            "api.base_url",
            "api.cookies",
            "api.xsrf_token",
            "monitor.request_timeout"
        ]
        
        for key in required_keys:
            keys = key.split('.')
            value = config
            for k in keys:
                if k not in value:
                    print(f"❌ 配置项 {key} 缺失")
                    return False
                value = value[k]
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError:
        print("❌ 配置文件格式错误")
        return False
    except Exception as e:
        print(f"❌ 配置文件检查失败: {e}")
        return False

def check_message_push():
    """检查消息推送模块"""
    if not os.path.exists("message_push.py"):
        print("❌ message_push.py 文件不存在")
        return False
    
    try:
        from message_push import wxpusher
        print("✅ 消息推送模块检查通过")
        return True
    except ImportError as e:
        print(f"❌ 消息推送模块导入失败: {e}")
        return False

def show_banner():
    """显示启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    号卡平台商品监控系统                        ║
║                                                              ║
║  🔍 自动监控商品上下架状态                                     ║
║  📱 智能推送通知                                              ║
║  🚫 智能去重避免重复推送                                       ║
║  📊 完善的日志记录                                            ║
║                                                              ║
║  开发者: Claude 4.0 sonnet                                   ║
║  版本: 1.0.0                                                ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def main():
    """主函数"""
    show_banner()
    
    print("正在进行启动前检查...")
    print("-" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 启动失败：依赖包检查未通过")
        return 1
    
    # 检查配置
    if not check_config():
        print("\n❌ 启动失败：配置文件检查未通过")
        print("请检查 config.json 文件是否存在且格式正确")
        return 1
    
    # 检查消息推送
    if not check_message_push():
        print("\n❌ 启动失败：消息推送模块检查未通过")
        return 1
    
    print("-" * 50)
    print("✅ 所有检查通过，正在执行单次监控检查...")
    print("\n提示:")
    print("- 程序将执行一次检查后自动退出")
    print("- 日志文件: monitor.log")
    print("- 历史数据: history.json")
    print("- 可通过定时任务定期执行此脚本")
    print("\n" + "=" * 50)
    
    # 执行单次检查
    try:
        from monitor import main as monitor_main
        result = monitor_main()
        if result == 0:
            print("\n✅ 监控检查完成")
        else:
            print("\n❌ 监控检查失败")
        return result
    except KeyboardInterrupt:
        print("\n\n程序被中断")
        return 0
    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())

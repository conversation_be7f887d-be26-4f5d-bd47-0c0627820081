#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酷安用户搜索脚本
根据 user.txt 中的用户名搜索酷安用户信息
提取匹配用户的 uid、username、status、block_status、logintime
按登录时间排序并保存到 data.json 和 data.txt
"""

import requests
import json
import urllib.parse
from datetime import datetime
import time
import os
import hashlib
import base64
import re
import bcrypt
import sys

def get_v2_token():
    """
    生成动态的X-App-Token，用于保持请求状态有效
    """
    device_code = "gN4YWYwMmYiJjYiBTZjdTMgszc5V2atQ3clRHIxADMuUDM5AjMy4SMRtEVgszQ2MzSSdjMxIjMgsTat9WYphHI7kWbvFWa4ByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR"  # 抓包headers有，固定即可
    format_base64 = re.compile('\\r\\n|\\r|\\n|=')
    token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
    device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
    timestamp = int(datetime.now().timestamp())
    timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
    timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
    token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
    token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
    token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
    token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
    arg = f'$2y$10${timestamp_base64}/{token_md5}'
    salt = (arg[:28] + 'u').encode('utf-8')
    crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
    crypt_base64 = base64.b64encode(crypt).decode()
    return f'v2{crypt_base64}'

class CoolapkUserSearch:
    def __init__(self, mode='merge'):
        """
        初始化搜索器

        Args:
            mode (str): 运行模式
                - 'merge': 合并模式，保留已有数据并添加新数据（默认）
                - 'overwrite': 覆盖模式，完全重新开始
                - 'update': 更新模式，更新已有用户的信息
        """
        self.session = requests.Session()
        self.user_data = {}  # 使用 uid 作为 key 避免重复
        self.success_count = 0
        self.error_count = 0
        self.updated_count = 0
        self.skipped_count = 0
        self.mode = mode
        self.existing_users = set()  # 存储已有用户的用户名，避免重复搜索
        self.existing_errors = set()  # 存储已有的错误用户名，避免重复记录

        # 生成动态Token
        dynamic_token = get_v2_token()
        print(f"🔐 生成动态Token: {dynamic_token[:20]}...")

        # 从 example.txt 中提取的 headers
        self.headers = {
            'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; xiaomi; 22127RK36C; TKQ1.220905.001 test-keys; 13) +CoolMarket/13.3.6-2310232-universal',
            'X-Requested-With': 'XMLHttpRequest',
            'X-Sdk-Int': '33',
            'X-Sdk-Locale': 'zh-CN',
            'X-App-Id': 'com.coolapk.market',
            'X-App-Token': dynamic_token,  # 使用动态生成的Token
            'X-App-Version': '13.3.6',
            'X-App-Code': '2310232',
            'X-Api-Version': '13',
            'X-App-Device': 'gN4YWYwMmYiJjYiBTZjdTMgszc5V2atQ3clRHIxADMuUDM5AjMy4SMRtEVgszQ2MzSSdjMxIjMgsTat9WYphHI7kWbvFWa4ByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR',
            'X-Dark-Mode': '0',
            'X-App-Channel': 'coolapk',
            'X-App-Mode': 'universal',
            'X-App-Supported': '2310232',
            'Host': 'api.coolapk.com',
            'Connection': 'Keep-Alive',
            'Accept-Encoding': 'gzip',
            'Cookie': 'uid=38008049; username=%E4%BD%90%E5%85%89;token=b10e99eePgd16Sv6rMlFgTQJjyZ9ckOFmslqrNc8dyA4tav04HAzvvDRdalHjJnqB8p1bNXOERn_7jNF2Ph6ITBE8HayvP3ybVbcgCooe3asqGEdgIOYno3HD-80tsCVHqG_Pq_YM4p8KyCJF3PGv4ZWt1-9elj8BGT9M0u1IFJWW2oKdEQpfWbOuTw8ePw028K3kUOX'
        }
        
        # 配置参数
        self.base_url = 'https://api.coolapk.com/v6/search'
        self.user_space_url = 'https://api.coolapk.com/v6/user/space'  # 新增：UID 查询 API
        self.request_timeout = 10
        self.request_interval = 1  # 请求间隔（秒）

        # Token管理
        self.token_generated_time = datetime.now()
        self.token_refresh_interval = 3600  # Token刷新间隔（秒），1小时

    def refresh_token_if_needed(self):
        """
        检查并在需要时刷新Token
        """
        current_time = datetime.now()
        time_diff = (current_time - self.token_generated_time).total_seconds()

        if time_diff > self.token_refresh_interval:
            print("🔄 Token即将过期，正在刷新...")
            new_token = get_v2_token()
            self.headers['X-App-Token'] = new_token
            self.token_generated_time = current_time
            print(f"✅ Token已刷新: {new_token[:20]}...")

    def load_existing_data(self):
        """
        加载现有的 data.json 文件

        Returns:
            int: 加载的用户数量
        """
        if not os.path.exists('data.json'):
            print("未找到现有的 data.json 文件，将创建新文件")
            return 0

        try:
            with open('data.json', 'r', encoding='utf-8') as f:
                existing_data = json.load(f)

            if not isinstance(existing_data, list):
                print("警告: data.json 格式不正确，将重新创建")
                return 0

            # 将现有数据加载到 user_data 字典中
            for user in existing_data:
                if 'uid' in user and 'username' in user:
                    uid = str(user['uid'])
                    # 添加 logintime_timestamp 用于排序
                    if 'logintime' in user:
                        try:
                            # 尝试解析时间字符串回时间戳
                            dt = datetime.strptime(user['logintime'], '%Y-%m-%d %H:%M:%S')
                            user['logintime_timestamp'] = int(dt.timestamp())
                        except ValueError:
                            user['logintime_timestamp'] = 0
                    else:
                        user['logintime_timestamp'] = 0

                    # 确保新字段存在（向后兼容）
                    if 'loginip' not in user:
                        user['loginip'] = ''
                    if 'regip' not in user:
                        user['regip'] = ''
                    if 'mobilestatus' not in user:
                        user['mobilestatus'] = ''

                    self.user_data[uid] = user
                    self.existing_users.add(user['username'])

            print(f"✓ 成功加载 {len(self.user_data)} 条现有用户数据")
            return len(self.user_data)

        except json.JSONDecodeError as e:
            print(f"警告: data.json 文件损坏 - {str(e)}")
            return 0
        except Exception as e:
            print(f"警告: 读取 data.json 失败 - {str(e)}")
            return 0


        
    def search_user(self, username):
        """
        搜索单个用户

        Args:
            username (str): 要搜索的用户名

        Returns:
            dict or None: 匹配的用户信息，如果没有匹配则返回 None
        """
        try:
            # 检查并刷新Token
            self.refresh_token_if_needed()

            # URL 编码用户名
            encoded_username = urllib.parse.quote(username)
            
            # 构造请求 URL（使用新的 API）
            url = f"{self.base_url}?type=all&searchValue={encoded_username}&page=1&showAnonymous=-1"
            
            print(f"正在搜索用户: {username}")
            
            # 发送请求
            response = self.session.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()
            
            # 解析 JSON 响应
            data = response.json()
            # print(data)
            # 检查是否有数据
            if 'data' not in data or not data['data']:
                print(f"  未找到用户: {username}")
                return None

            # 查找用户数据卡片（大多数情况下在data[0]，极少数情况下需要遍历）
            user_card = None
            for card in data['data']:
                if (card.get('entityTemplate') == 'iconScrollCard' and
                    card.get('title') == '用户' and
                    'entities' in card and
                    card['entities']):
                    user_card = card
                    break

            if not user_card:
                print(f"  未找到用户: {username}")
                return None

            # 获取第一个用户（从 entities[0] 中）
            first_user = user_card['entities'][0]
            
            # 检查用户名是否匹配
            found_username = first_user.get('username')
            found_uid = str(first_user.get('uid'))

            # 更新模式下的特殊处理：如果 UID 匹配但用户名不匹配，说明用户改名了
            if self.mode == 'update' and found_uid in self.user_data:
                if found_username != username:
                    print(f"  🔄 检测到用户改名: '{username}' -> '{found_username}' (UID: {found_uid})")
                    return first_user
                else:
                    print(f"  🔄 更新用户信息: {username} (UID: {found_uid})")
                    return first_user

            # 非更新模式或新用户：严格匹配用户名
            if found_username == username:
                # 检查是否已存在该用户
                if found_uid in self.user_data:
                    if self.mode == 'merge':
                        print(f"  ⚠ 用户已存在，跳过: {username} (UID: {found_uid})")
                        self.skipped_count += 1
                        return None

                print(f"  ✓ 匹配成功: {username} (UID: {found_uid})")
                return first_user
            else:
                print(f"  ✗ 用户名不匹配: 搜索 '{username}', 找到 '{found_username}'")
                self.save_error_username(username, found_username)
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"  ✗ 网络请求失败: {username} - {str(e)}")
            self.error_count += 1
            return None
        except json.JSONDecodeError as e:
            print(f"  ✗ JSON 解析失败: {username} - {str(e)}")
            self.error_count += 1
            return None
        except Exception as e:
            print(f"  ✗ 未知错误: {username} - {str(e)}")
            self.error_count += 1
            return None

    def get_user_by_uid(self, uid):
        """
        通过 UID 直接获取用户基本信息（使用 example2.txt UID API）
        用于更新模式的第一步，检查用户名是否变更

        Args:
            uid (str): 用户 UID

        Returns:
            dict or None: 用户基本信息，如果获取失败则返回 None
        """
        try:
            # 检查并刷新Token
            self.refresh_token_if_needed()

            # 构造请求 URL (example2.txt UID API)
            url = f"{self.user_space_url}?uid={uid}"

            # 发送请求
            response = self.session.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()

            # 解析 JSON 响应
            data = response.json()

            # 检查是否有数据
            if 'data' not in data or not data['data']:
                print(f"  ❌ 用户不存在或已被删除: UID {uid}")
                return None

            user_info = data['data']
            current_username = user_info.get('username', '未知')

            return user_info

        except requests.exceptions.RequestException as e:
            print(f"  ❌ UID API 请求失败: UID {uid} - {str(e)}")
            return None
        except json.JSONDecodeError as e:
            print(f"  ❌ UID API JSON 解析失败: UID {uid} - {str(e)}")
            return None
        except Exception as e:
            print(f"  ❌ UID API 未知错误: UID {uid} - {str(e)}")
            return None

    def search_user_by_name_for_complete_data(self, username, expected_uid):
        """
        通过用户名搜索获取完整数据（使用 example3.txt 搜索 API）
        用于更新模式的第二步，获取包含 loginip、regip、mobilestatus 等完整字段的数据

        Args:
            username (str): 用户名
            expected_uid (str): 期望的UID

        Returns:
            dict or None: 完整的用户信息，如果获取失败则返回 None
        """
        try:
            # 检查并刷新Token
            self.refresh_token_if_needed()

            # URL 编码用户名
            encoded_username = urllib.parse.quote(username)

            # 构造请求 URL（使用搜索API）
            url = f"{self.base_url}?type=all&searchValue={encoded_username}&page=1&showAnonymous=-1"

            # 发送请求
            response = self.session.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()

            # 解析 JSON 响应
            data = response.json()

            # 检查是否有数据
            if 'data' not in data or not data['data']:
                print(f"    ❌ 搜索无结果: {username}")
                return None

            # 查找用户数据卡片（大多数情况下在data[0]，极少数情况下需要遍历）
            user_card = None
            for card in data['data']:
                if (card.get('entityTemplate') == 'iconScrollCard' and
                    card.get('title') == '用户' and
                    'entities' in card and
                    card['entities']):
                    user_card = card
                    break

            if not user_card:
                print(f"    ❌ 搜索无用户数据: {username}")
                return None

            # 查找匹配的用户（通过UID匹配）
            for entity in user_card['entities']:
                user_info = entity.get('userInfo', {})
                entity_uid = str(user_info.get('uid', entity.get('uid', '')))

                if entity_uid == expected_uid:
                    print(f"    ✅ 获取完整数据成功: {username}")
                    return entity

            print(f"    ❌ 未找到匹配的UID: {expected_uid}")
            return None

        except requests.exceptions.RequestException as e:
            print(f"    ❌ 网络请求失败: {username} - {str(e)}")
            return None
        except json.JSONDecodeError as e:
            print(f"    ❌ JSON 解析失败: {username} - {str(e)}")
            return None
        except Exception as e:
            print(f"    ❌ 未知错误: {username} - {str(e)}")
            return None

    def extract_user_info_from_uid_api(self, user_data):
        """
        从 UID API (example2.txt) 返回的数据中提取用户信息
        注意：example2.txt 中的数据类型为数字类型

        Args:
            user_data (dict): UID API 返回的用户数据

        Returns:
            dict: 提取的用户信息
        """
        # example2.txt 格式：数字类型
        uid = user_data.get('uid')  # 数字类型
        username = user_data.get('username')  # 字符串类型
        status = user_data.get('status')  # 数字类型
        block_status = user_data.get('block_status', 0)  # 数字类型
        logintime_timestamp = user_data.get('logintime')  # 数字类型

        # 转换登录时间
        logintime = self.convert_timestamp(logintime_timestamp)

        return {
            'uid': str(uid) if uid is not None else '',  # 统一转为字符串
            'username': username if username else '',
            'status': str(status) if status is not None else '0',  # 统一转为字符串
            'block_status': str(block_status) if block_status is not None else '0',  # 统一转为字符串
            'logintime': logintime,
            'logintime_timestamp': int(logintime_timestamp) if logintime_timestamp else 0,
            'loginip': '',  # UID API 不提供这些字段
            'regip': '',
            'mobilestatus': ''
        }

    def search_user_for_update(self, username, expected_uid):
        """
        为更新模式搜索用户（使用搜索API获取完整数据）

        Args:
            username (str): 用户名
            expected_uid (str): 期望的UID

        Returns:
            dict or None: 用户信息，如果获取失败则返回 None
        """
        try:
            # URL 编码用户名
            encoded_username = urllib.parse.quote(username)

            # 构造请求 URL（使用搜索API）
            url = f"{self.base_url}?type=all&searchValue={encoded_username}&page=1&showAnonymous=-1"

            print(f"正在更新用户: {username} (UID: {expected_uid})")

            # 发送请求
            response = self.session.get(url, headers=self.headers, timeout=self.request_timeout)
            response.raise_for_status()

            # 解析 JSON 响应
            data = response.json()

            # 检查是否有数据
            if 'data' not in data or not data['data']:
                print(f"  ❌ 用户不存在: {username}")
                return None

            # 查找用户数据卡片（大多数情况下在data[0]，极少数情况下需要遍历）
            user_card = None
            for card in data['data']:
                if (card.get('entityTemplate') == 'iconScrollCard' and
                    card.get('title') == '用户' and
                    'entities' in card and
                    card['entities']):
                    user_card = card
                    break

            if not user_card:
                print(f"  ❌ 用户不存在: {username}")
                return None

            # 查找匹配的用户（可能用户改名了，需要通过UID匹配）
            found_user = None
            for entity in user_card['entities']:
                user_info = entity.get('userInfo', {})
                entity_uid = str(user_info.get('uid', entity.get('uid', '')))

                if entity_uid == expected_uid:
                    found_user = entity
                    break

            if not found_user:
                # 如果通过UID没找到，尝试用户名匹配（可能是新用户）
                first_user = user_card['entities'][0]
                user_info = first_user.get('userInfo', {})
                found_username = user_info.get('username', first_user.get('username', ''))

                if found_username == username:
                    found_user = first_user
                else:
                    print(f"  ❌ 用户UID不匹配: 期望 {expected_uid}, 搜索到其他用户")
                    return None

            current_username = found_user.get('userInfo', {}).get('username', found_user.get('username', '未知'))
            print(f"  📡 获取到用户数据: {current_username}")

            return found_user

        except requests.exceptions.RequestException as e:
            print(f"  ❌ 网络请求失败: {username} - {str(e)}")
            self.error_count += 1
            return None
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON 解析失败: {username} - {str(e)}")
            self.error_count += 1
            return None
        except Exception as e:
            print(f"  ❌ 未知错误: {username} - {str(e)}")
            self.error_count += 1
            return None

    def update_users_by_uid(self, uids_to_update):
        """
        通过 UID 列表更新用户信息
        新的两步更新流程：
        1. 先用 UID API (example2.txt) 检查用户名变更
        2. 再用搜索 API (example3.txt) 获取完整数据更新

        Args:
            uids_to_update (list): 需要更新的 UID 列表
        """
        total_users = len(uids_to_update)
        print(f"🚀 开始通过 UID 更新 {total_users} 个用户...")
        print("💡 新流程：先检查用户名变更，再获取完整数据")
        print()

        # 逐个更新用户
        for i, uid in enumerate(uids_to_update, 1):
            print(f"[{i}/{total_users}] ", end="")

            # 获取旧数据进行对比
            old_data = self.user_data[uid]
            old_username = old_data.get('username', '')

            # 第一步：使用 UID API 检查用户名变更
            print(f"正在检查用户: {old_username} (UID: {uid})")
            basic_user_info = self.get_user_by_uid(uid)

            if not basic_user_info:
                print(f"  ❌ 无法获取用户信息，跳过")
                self.error_count += 1
                if i < total_users:
                    time.sleep(self.request_interval)
                continue

            # 检查用户名是否变更
            current_username = basic_user_info.get('username', '')
            username_changed = old_username != current_username

            if username_changed:
                print(f"  🔄 检测到用户改名: '{old_username}' -> '{current_username}'")
            else:
                print(f"  ✅ 用户名未变更: {current_username}")

            # 第二步：使用搜索 API 获取完整数据
            print(f"  📡 获取完整数据...")
            complete_user_info = self.search_user_by_name_for_complete_data(current_username, uid)

            if complete_user_info:
                # 提取完整用户信息
                extracted_info = self.extract_user_info(complete_user_info)

                # 检测各种变化
                self.detect_user_changes(old_data, extracted_info, uid)

                # 更新用户信息
                self.user_data[uid] = extracted_info
                self.updated_count += 1
            else:
                # 如果搜索失败，使用基本信息更新
                print(f"  ⚠️  搜索失败，使用基本信息更新")
                basic_extracted_info = self.extract_user_info_from_uid_api(basic_user_info)

                # 检测变化（主要是用户名变更）
                if username_changed:
                    print(f"  🔄 用户改名: '{old_username}' -> '{current_username}'")

                # 更新用户信息（保留原有的额外字段）
                basic_extracted_info['loginip'] = old_data.get('loginip', '')
                basic_extracted_info['regip'] = old_data.get('regip', '')
                basic_extracted_info['mobilestatus'] = old_data.get('mobilestatus', '')

                self.user_data[uid] = basic_extracted_info
                self.updated_count += 1

            # 请求间隔
            if i < total_users:
                time.sleep(self.request_interval)

        print()
        print("=" * 60)
        self.print_summary()
        print("=" * 60)

        # 保存结果
        self.save_results()

        print("🎉 程序执行完成！")

    def analyze_login_ip(self):
        """
        分析登录IP相关情况
        1. 同一登录IP下的多个用户
        2. 登录IP与注册IP的匹配分析
        3. 注册IP重复分析
        """
        print("🔍 开始分析登录IP和IP匹配...")
        print()

        # 加载现有数据
        existing_count = self.load_existing_data()
        if existing_count == 0:
            print("❌ 没有数据可供分析")
            return

        # 1. 分析同一登录IP下的多个用户
        self.analyze_same_login_ip()

        # 2. 分析IP匹配情况
        print("\n" + "=" * 80)
        print("🔍 开始IP匹配分析...")
        self.analyze_ip_matches()

    def analyze_same_login_ip(self):
        """
        分析同一登录IP下的多个用户（原功能）
        """
        print("📋 同一登录IP分析:")
        print("-" * 60)

        # 按登录IP分组用户
        ip_groups = {}
        users_without_ip = []

        for uid, user_data in self.user_data.items():
            loginip = user_data.get('loginip', '').strip()

            if not loginip:
                users_without_ip.append(user_data)
                continue

            if loginip not in ip_groups:
                ip_groups[loginip] = []

            ip_groups[loginip].append({
                'uid': uid,
                'username': user_data.get('username', '未知'),
                'logintime': user_data.get('logintime', '未知'),
                'status': user_data.get('status', '未知'),
                'block_status': user_data.get('block_status', '未知')
            })

        # 筛选出有多个用户的IP
        multi_user_ips = {ip: users for ip, users in ip_groups.items() if len(users) > 1}

        if not multi_user_ips:
            print("✅ 没有发现同一登录IP下有多个用户的情况")
            print(f"📊 分析了 {len(ip_groups)} 个不同的登录IP")
            if users_without_ip:
                print(f"⚠️  有 {len(users_without_ip)} 个用户没有登录IP信息")
        else:
            # 显示结果
            print(f"🚨 发现 {len(multi_user_ips)} 个登录IP下有多个用户:")

            # 按用户数量排序（从多到少）
            sorted_ips = sorted(multi_user_ips.items(), key=lambda x: len(x[1]), reverse=True)

            for ip, users in sorted_ips:
                print(f"\n🌐 登录IP: {ip} ({len(users)} 个用户)")
                print("-" * 40)

                # 按登录时间排序用户（最新的在前）
                users.sort(key=lambda x: x.get('logintime', ''), reverse=True)

                for i, user in enumerate(users, 1):
                    status_icon = self.get_status_icon(user['status'], user['block_status'])
                    print(f"  {i}. {status_icon} {user['username']} (UID: {user['uid']})")
                    print(f"     最后登录: {user['logintime']}")
                    if i < len(users):
                        print()

            print(f"\n📊 同一登录IP统计:")
            print(f"   🔍 总计分析用户: {len(self.user_data)}")
            print(f"   🌐 不同登录IP数: {len(ip_groups)}")
            print(f"   🚨 多用户IP数量: {len(multi_user_ips)}")
            print(f"   👥 涉及用户总数: {sum(len(users) for users in multi_user_ips.values())}")

            if users_without_ip:
                print(f"   ⚠️  无IP信息用户: {len(users_without_ip)}")

    def analyze_ip_matches(self):
        """
        分析IP匹配情况
        1. 登录IP与注册IP的匹配（不同用户之间）
        2. 注册IP与注册IP的匹配（不同用户之间）
        """
        print("\n🌐 IP匹配分析:")
        print("-" * 60)

        # 收集所有用户的IP信息
        users_with_ip = []
        for uid, user_data in self.user_data.items():
            loginip = user_data.get('loginip', '').strip()
            regip = user_data.get('regip', '').strip()

            if loginip or regip:  # 至少有一个IP信息
                users_with_ip.append({
                    'uid': uid,
                    'username': user_data.get('username', '未知'),
                    'loginip': loginip,
                    'regip': regip,
                    'logintime': user_data.get('logintime', '未知'),
                    'status': user_data.get('status', '0'),
                    'block_status': user_data.get('block_status', '0')
                })

        if not users_with_ip:
            print("❌ 没有用户包含IP信息")
            return

        print(f"📊 分析 {len(users_with_ip)} 个有IP信息的用户")

        # 1. 分析登录IP与注册IP的匹配
        self.analyze_login_reg_ip_matches(users_with_ip)

        # 2. 分析注册IP与注册IP的匹配
        self.analyze_reg_reg_ip_matches(users_with_ip)

    def analyze_login_reg_ip_matches(self, users_with_ip):
        """
        分析不同用户之间登录IP与注册IP的匹配
        """
        print("\n🔍 登录IP与注册IP匹配分析:")
        print("-" * 40)

        matches = []

        # 双重循环比较所有用户对
        for i, user1 in enumerate(users_with_ip):
            for j, user2 in enumerate(users_with_ip):
                if i >= j:  # 避免重复比较和自己与自己比较
                    continue

                # 检查user1的登录IP是否与user2的注册IP匹配
                if (user1['loginip'] and user2['regip'] and
                    user1['loginip'] == user2['regip']):
                    matches.append({
                        'type': 'login_reg',
                        'user1': user1,
                        'user2': user2,
                        'ip': user1['loginip'],
                        'description': f"{user1['username']}的登录IP = {user2['username']}的注册IP"
                    })

                # 检查user2的登录IP是否与user1的注册IP匹配
                if (user2['loginip'] and user1['regip'] and
                    user2['loginip'] == user1['regip']):
                    matches.append({
                        'type': 'login_reg',
                        'user1': user2,
                        'user2': user1,
                        'ip': user2['loginip'],
                        'description': f"{user2['username']}的登录IP = {user1['username']}的注册IP"
                    })

        if matches:
            print(f"🚨 发现 {len(matches)} 个登录IP与注册IP匹配:")
            for i, match in enumerate(matches, 1):
                print(f"\n  {i}. {match['description']}")
                print(f"     匹配IP: {match['ip']}")
                print(f"     用户1: {match['user1']['username']} (UID: {match['user1']['uid']})")
                print(f"     用户2: {match['user2']['username']} (UID: {match['user2']['uid']})")

                # 显示状态信息
                status1 = self.get_status_icon(match['user1']['status'], match['user1']['block_status'])
                status2 = self.get_status_icon(match['user2']['status'], match['user2']['block_status'])
                print(f"     状态: {status1} vs {status2}")
        else:
            print("✅ 未发现登录IP与注册IP匹配的情况")

    def analyze_reg_reg_ip_matches(self, users_with_ip):
        """
        分析不同用户之间注册IP与注册IP的匹配
        """
        print("\n🔍 注册IP重复分析:")
        print("-" * 40)

        # 按注册IP分组
        regip_groups = {}
        for user in users_with_ip:
            regip = user['regip']
            if regip:  # 只处理有注册IP的用户
                if regip not in regip_groups:
                    regip_groups[regip] = []
                regip_groups[regip].append(user)

        # 筛选出有多个用户的注册IP
        duplicate_regips = {ip: users for ip, users in regip_groups.items() if len(users) > 1}

        if duplicate_regips:
            total_duplicates = sum(len(users) for users in duplicate_regips.values())
            print(f"🚨 发现 {len(duplicate_regips)} 个重复的注册IP，涉及 {total_duplicates} 个用户:")

            # 按用户数量排序（从多到少）
            sorted_regips = sorted(duplicate_regips.items(), key=lambda x: len(x[1]), reverse=True)

            for i, (regip, users) in enumerate(sorted_regips, 1):
                print(f"\n  {i}. 注册IP: {regip} ({len(users)} 个用户)")

                # 按注册时间排序（如果有的话）
                users.sort(key=lambda x: x.get('logintime', ''), reverse=True)

                for j, user in enumerate(users, 1):
                    status_icon = self.get_status_icon(user['status'], user['block_status'])
                    print(f"     {j}. {status_icon} {user['username']} (UID: {user['uid']})")
                    if user['loginip']:
                        print(f"        当前登录IP: {user['loginip']}")
        else:
            print("✅ 未发现重复的注册IP")

    def get_status_icon(self, status, block_status):
        """
        根据用户状态返回对应的图标

        Args:
            status: 用户状态
            block_status: 封禁状态

        Returns:
            str: 状态图标
        """
        status = str(status)
        block_status = str(block_status)

        if block_status != '0':
            return '🚫'  # 被封禁
        elif status == '0':
            return '⚠️'   # 被禁用
        elif status == '1':
            return '✅'  # 正常
        else:
            return '❓'  # 未知状态

    def check_abnormal_status(self):
        """
        检查状态不正常的用户
        条件：status≠1 或 block_status≠0 或 mobilestatus≠1
        """
        print("⚠️ 开始检查用户状态...")
        print()

        # 加载现有数据
        existing_count = self.load_existing_data()
        if existing_count == 0:
            print("❌ 没有数据可供检查")
            return

        # 筛选状态不正常的用户
        abnormal_users = []

        for uid, user_data in self.user_data.items():
            status = str(user_data.get('status', '0'))
            block_status = str(user_data.get('block_status', '0'))
            mobilestatus = str(user_data.get('mobilestatus', '0'))

            # 检查是否有异常状态
            issues = []
            if status != '1':
                issues.append(f"status: {status}")
            if block_status != '0':
                issues.append(f"block_status: {block_status}")
            if mobilestatus != '1':
                issues.append(f"mobilestatus: {mobilestatus}")

            if issues:
                abnormal_users.append({
                    'uid': uid,
                    'username': user_data.get('username', '未知'),
                    'logintime': user_data.get('logintime', '未知'),
                    'loginip': user_data.get('loginip', ''),
                    'regip': user_data.get('regip', ''),
                    'status': status,
                    'block_status': block_status,
                    'mobilestatus': mobilestatus,
                    'issues': issues
                })

        if not abnormal_users:
            print("✅ 所有用户状态正常")
            print(f"📊 检查了 {len(self.user_data)} 个用户")
            return

        # 按问题严重程度排序（封禁 > 禁用 > 手机验证）
        def get_severity(user):
            if user['block_status'] != '0':
                return 1  # 最严重：被封禁
            elif user['status'] != '1':
                return 2  # 中等：被禁用
            else:
                return 3  # 轻微：手机验证问题

        abnormal_users.sort(key=get_severity)

        # 显示结果
        print(f"⚠️ 发现 {len(abnormal_users)} 个状态不正常的用户:")
        print("=" * 80)

        # 按问题类型分组显示
        blocked_users = [u for u in abnormal_users if u['block_status'] != '0']
        disabled_users = [u for u in abnormal_users if u['status'] != '1' and u['block_status'] == '0']
        mobile_users = [u for u in abnormal_users if u['mobilestatus'] != '1' and u['status'] == '1' and u['block_status'] == '0']

        # 显示被封禁用户
        if blocked_users:
            print(f"\n🚫 被封禁用户 ({len(blocked_users)} 个):")
            print("-" * 60)
            for i, user in enumerate(blocked_users, 1):
                self.display_abnormal_user(i, user)

        # 显示被禁用用户
        if disabled_users:
            print(f"\n⚠️ 被禁用用户 ({len(disabled_users)} 个):")
            print("-" * 60)
            for i, user in enumerate(disabled_users, 1):
                self.display_abnormal_user(i, user)

        # 显示手机验证异常用户
        if mobile_users:
            print(f"\n📱 手机验证异常用户 ({len(mobile_users)} 个):")
            print("-" * 60)
            for i, user in enumerate(mobile_users, 1):
                self.display_abnormal_user(i, user)

        print("\n" + "=" * 80)
        print("📊 统计信息:")
        print(f"   🔍 总计检查用户: {len(self.user_data)}")
        print(f"   ⚠️ 异常用户数量: {len(abnormal_users)}")
        print(f"   🚫 被封禁用户: {len(blocked_users)}")
        print(f"   ⚠️ 被禁用用户: {len(disabled_users)}")
        print(f"   📱 手机验证异常: {len(mobile_users)}")
        print(f"   ✅ 正常用户比例: {((len(self.user_data) - len(abnormal_users)) / len(self.user_data) * 100):.1f}%")

    def display_abnormal_user(self, index, user):
        """
        显示异常用户信息

        Args:
            index (int): 序号
            user (dict): 用户信息
        """
        status_icon = self.get_status_icon(user['status'], user['block_status'])
        print(f"  {index}. {status_icon} {user['username']} (UID: {user['uid']})")
        print(f"     异常状态: {', '.join(user['issues'])}")
        print(f"     最后登录: {user['logintime']}")

        if user['loginip']:
            print(f"     登录IP: {user['loginip']}")
        if user['regip']:
            print(f"     注册IP: {user['regip']}")

        if index < len([u for u in [user] if True]):  # 如果不是最后一个
            print()

    def load_existing_errors(self):
        """
        加载现有的 error.txt 文件中的错误用户名

        Returns:
            int: 加载的错误用户名数量
        """
        if not os.path.exists('error.txt'):
            print("未找到现有的 error.txt 文件，将创建新文件")
            return 0

        try:
            with open('error.txt', 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # 解析现有的错误记录
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释行
                    # 提取用户名（格式：搜索用户名 -> 找到用户名）
                    if ' -> ' in line:
                        search_username = line.split(' -> ')[0].strip()
                        self.existing_errors.add(search_username)
                    else:
                        # 兼容简单格式（只有用户名）
                        self.existing_errors.add(line)

            if self.existing_errors:
                print(f"✓ 成功加载 {len(self.existing_errors)} 个已记录的错误用户名")

            # 记录初始错误数量，用于统计新增错误
            self._initial_errors = self.existing_errors.copy()
            return len(self.existing_errors)

        except Exception as e:
            print(f"警告: 读取 error.txt 失败 - {str(e)}")
            return 0

    def save_error_username(self, search_username, found_username):
        """
        保存用户名不匹配的记录到 error.txt

        Args:
            search_username (str): 搜索的用户名
            found_username (str): 实际找到的用户名
        """
        # 检查是否已经记录过这个错误
        if search_username in self.existing_errors:
            return

        try:
            # 准备要写入的内容
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            error_line = f"{search_username} -> {found_username} ({timestamp})\n"

            # 检查文件是否存在
            file_exists = os.path.exists('error.txt')

            # 追加写入文件
            with open('error.txt', 'a', encoding='utf-8') as f:
                # 如果是新文件，添加头部说明
                if not file_exists:
                    f.write("# 用户名不匹配记录\n")
                    f.write("# 格式: 搜索用户名 -> 找到用户名 (时间)\n")
                    f.write("# " + "=" * 50 + "\n")

                f.write(error_line)

            # 添加到已记录集合，避免重复
            self.existing_errors.add(search_username)

        except Exception as e:
            print(f"  警告: 保存错误记录失败 - {str(e)}")

    def detect_user_changes(self, old_data, new_data, uid):
        """
        检测用户信息的各种变化并输出提醒

        Args:
            old_data (dict): 旧的用户数据
            new_data (dict): 新的用户数据
            uid (str): 用户 UID
        """
        changes = []

        # 1. 检测用户名变化
        old_username = old_data.get('username', '未知')
        new_username = new_data.get('username', '未知')
        if old_username != new_username:
            changes.append(f"🔄 用户改名: '{old_username}' -> '{new_username}'")

        # 2. 检测状态变化
        old_status = str(old_data.get('status', '0'))
        new_status = str(new_data.get('status', '0'))
        if old_status != new_status:
            changes.append(f"🔄 status 变化: {old_status} -> {new_status}")

        # 3. 检测封禁状态变化
        old_block = str(old_data.get('block_status', '0'))
        new_block = str(new_data.get('block_status', '0'))
        if old_block != new_block:
            changes.append(f"🔄 block_status 变化: {old_block} -> {new_block}")

        # 4. 检测登录时间变化（超过3天提醒）
        old_logintime = old_data.get('logintime_timestamp', 0)
        new_logintime = new_data.get('logintime_timestamp', 0)

        if old_logintime and new_logintime:
            # 计算距离上次登录的天数
            current_time = int(time.time())
            days_since_last_login = (current_time - new_logintime) / (24 * 3600)

            if days_since_last_login > 3:
                days_text = f"{int(days_since_last_login)} 天"
                if days_since_last_login > 30:
                    months = int(days_since_last_login / 30)
                    days_text = f"{months} 个月"
                elif days_since_last_login > 365:
                    years = int(days_since_last_login / 365)
                    days_text = f"{years} 年"

                changes.append(f"⏰ 长时间未登录: 距离上次登录已超过 {days_text}")

            # 检测登录时间是否有更新
            if new_logintime > old_logintime:
                time_diff = new_logintime - old_logintime
                if time_diff > 3600:  # 超过1小时才提醒
                    hours = int(time_diff / 3600)
                    if hours < 24:
                        changes.append(f"🕐 最近有登录: {hours} 小时前更新了登录时间")
                    else:
                        days = int(hours / 24)
                        changes.append(f"🕐 最近有登录: {days} 天前更新了登录时间")

        # 5. 检测登录IP变化
        old_loginip = old_data.get('loginip', '')
        new_loginip = new_data.get('loginip', '')
        if old_loginip != new_loginip and new_loginip:
            if old_loginip:
                changes.append(f"🌐 登录IP变化: {old_loginip} -> {new_loginip}")
            else:
                changes.append(f"🌐 新增登录IP: {new_loginip}")

        # 6. 检测手机验证状态变化
        old_mobile = str(old_data.get('mobilestatus', '0'))
        new_mobile = str(new_data.get('mobilestatus', '0'))
        if old_mobile != new_mobile:
            changes.append(f"📱 手机验证状态变化: {old_mobile} -> {new_mobile}")

        # 输出所有检测到的变化
        if changes:
            for change in changes:
                print(f"  {change}")
        else:
            # 如果没有重要变化，只显示基本信息
            current_username = new_data.get('username', '未知')
            print(f"  ✅ 用户信息已更新: {current_username}")

            # 检查当前状态（即使没有变化也要提醒异常状态）
            current_status = str(new_data.get('status', '0'))
            current_block = str(new_data.get('block_status', '0'))

            if current_status != '1':
                print(f"  ⚠️  当前 status: {current_status}")
            if current_block != '0':
                print(f"  ⚠️  当前 block_status: {current_block}")
    
    def convert_timestamp(self, timestamp):
        """
        转换 Unix 时间戳为可读时间格式
        
        Args:
            timestamp (int or str): Unix 时间戳
            
        Returns:
            str: 格式化的时间字符串
        """
        try:
            timestamp = int(timestamp)
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, OSError) as e:
            print(f"  警告: 时间戳转换失败 {timestamp} - {str(e)}")
            return str(timestamp)
    
    def extract_user_info(self, user_data):
        """
        提取用户信息
        处理 example3.txt 搜索 API 的数据结构和数据类型

        Args:
            user_data (dict): 原始用户数据 (来自搜索API的entities[0])

        Returns:
            dict: 提取的用户信息
        """
        # example3.txt 格式分析：
        # - 核心字段从内层 userInfo 获取（数字类型）
        # - 仅 loginip、regip、mobilestatus 从外层 entities[0] 获取（字符串类型）

        user_info = user_data.get('userInfo', {})

        # 核心字段：从 userInfo 获取（数字类型）
        uid = user_info.get('uid')
        username = user_info.get('username')
        status = user_info.get('status')
        block_status = user_info.get('block_status', 0)
        logintime_timestamp = user_info.get('logintime')

        # 特殊字段：仅这三个字段从外层 entities[0] 获取（字符串类型）
        loginip = user_data.get('loginip', '')
        regip = user_data.get('regip', '')
        mobilestatus = user_data.get('mobilestatus', '')

        # 转换登录时间（userInfo 中是数字类型）
        logintime = self.convert_timestamp(logintime_timestamp)

        return {
            'uid': str(uid) if uid is not None else '',  # 统一转为字符串
            'username': username if username else '',
            'status': str(status) if status is not None else '0',  # 统一转为字符串
            'block_status': str(block_status) if block_status is not None else '0',  # 统一转为字符串
            'logintime': logintime,
            'logintime_timestamp': int(logintime_timestamp) if logintime_timestamp else 0,
            'loginip': loginip if loginip else '',
            'regip': regip if regip else '',
            'mobilestatus': mobilestatus if mobilestatus else ''
        }
    
    def load_usernames(self, filename='user.txt'):
        """
        从文件中加载用户名列表，并根据模式过滤

        Args:
            filename (str): 用户名文件路径

        Returns:
            tuple: (所有用户名列表, 需要搜索的用户名列表)
        """
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                all_usernames = [line.strip() for line in f if line.strip()]

            # 根据模式决定需要搜索的用户名
            if self.mode == 'merge':
                # 合并模式：只搜索不存在的用户
                usernames_to_search = [name for name in all_usernames if name not in self.existing_users]
                print(f"成功加载 {len(all_usernames)} 个用户名")
                print(f"合并模式：需要搜索 {len(usernames_to_search)} 个新用户")
                if len(usernames_to_search) < len(all_usernames):
                    print(f"跳过 {len(all_usernames) - len(usernames_to_search)} 个已存在的用户")
            elif self.mode == 'update':
                # 更新模式：这个分支不应该被执行，因为更新模式不读取 user.txt
                # 但为了代码完整性保留
                usernames_to_search = []
                print(f"⚠️  更新模式不应该读取 user.txt 文件")

            return all_usernames, usernames_to_search

        except FileNotFoundError:
            print(f"错误: 找不到文件 {filename}")
            return [], []
        except Exception as e:
            print(f"错误: 读取文件失败 {filename} - {str(e)}")
            return [], []
    
    def save_results(self):
        """保存结果到文件"""
        if not self.user_data:
            print("没有数据需要保存")
            return
            
        # 按登录时间排序（从旧到新）
        sorted_users = sorted(
            self.user_data.values(), 
            key=lambda x: x['logintime_timestamp']
        )
        
        # 移除用于排序的时间戳字段
        for user in sorted_users:
            user.pop('logintime_timestamp', None)
        
        # 保存到 data.json
        self.save_json_with_fallback(sorted_users)
        
        # 保存到 data.txt (只保存 uid)
        try:
            with open('data.txt', 'w', encoding='utf-8') as f:
                for user in sorted_users:
                    f.write(f"{user['uid']}\n")
            print(f"✓ 已保存 {len(sorted_users)} 个 UID 到 data.txt")
        except Exception as e:
            print(f"✗ 保存 data.txt 失败: {str(e)}")

    def save_json_with_fallback(self, sorted_users):
        """
        保存JSON文件，包含详细错误诊断和备用方案
        """
        import stat
        import tempfile
        from datetime import datetime

        # 尝试保存到 data.json
        try:
            # 检查文件是否存在及其状态
            if os.path.exists('data.json'):
                file_stat = os.stat('data.json')
                file_mode = stat.filemode(file_stat.st_mode)
                print(f"📁 data.json 文件状态: {file_mode}")

                # 检查是否为只读
                if not os.access('data.json', os.W_OK):
                    print("⚠️  data.json 文件为只读，尝试修改权限...")
                    try:
                        os.chmod('data.json', stat.S_IWRITE | stat.S_IREAD)
                        print("✅ 权限修改成功")
                    except Exception as perm_e:
                        print(f"❌ 权限修改失败: {perm_e}")

            # 尝试直接保存
            with open('data.json', 'w', encoding='utf-8') as f:
                json.dump(sorted_users, f, ensure_ascii=False, indent=2)
            print(f"✓ 已保存 {len(sorted_users)} 条记录到 data.json")
            return True

        except PermissionError as e:
            print(f"❌ 权限错误: {e}")
            print("💡 可能的解决方案:")
            print("   1. 关闭正在打开 data.json 的程序（如Excel、记事本）")
            print("   2. 以管理员身份运行程序")
            print("   3. 检查文件是否被设置为只读")

        except FileNotFoundError as e:
            print(f"❌ 文件路径错误: {e}")

        except OSError as e:
            print(f"❌ 系统错误: {e}")

        except Exception as e:
            print(f"❌ 未知错误: {e}")

        # 备用方案1：保存到临时文件
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"data_backup_{timestamp}.json"

            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(sorted_users, f, ensure_ascii=False, indent=2)
            print(f"✅ 备用方案：已保存到 {backup_filename}")
            return True

        except Exception as backup_e:
            print(f"❌ 备用保存也失败: {backup_e}")

        # 备用方案2：保存到用户临时目录
        try:
            temp_dir = tempfile.gettempdir()
            temp_filename = os.path.join(temp_dir, f"coolapk_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

            with open(temp_filename, 'w', encoding='utf-8') as f:
                json.dump(sorted_users, f, ensure_ascii=False, indent=2)
            print(f"✅ 临时保存：已保存到 {temp_filename}")
            return True

        except Exception as temp_e:
            print(f"❌ 临时保存失败: {temp_e}")

        print("❌ 所有保存方案都失败了！")
        return False

    def run(self):
        """运行主程序"""
        print("=" * 60)
        print("🔍 酷安用户搜索脚本启动")
        print(f"📋 运行模式: {self.mode.upper()}")
        print("=" * 60)

        # 根据模式处理现有数据
        if self.mode == 'merge':
            # 合并模式：加载现有数据
            existing_count = self.load_existing_data()
            if existing_count > 0:
                print("🔄 合并模式：将保留现有数据并添加新用户")

            # 加载现有的错误记录
            self.load_existing_errors()
        elif self.mode == 'update':
            # 更新模式：加载现有数据
            existing_count = self.load_existing_data()
            if existing_count > 0:
                print("🔄 更新模式：将更新已存在用户的信息")
        elif self.mode == 'analyze_ip':
            # IP分析模式：直接执行分析
            self.analyze_login_ip()
            return
        elif self.mode == 'check_status':
            # 状态检查模式：直接执行检查
            self.check_abnormal_status()
            return
        elif self.mode == 'uid_mode':
            # UID兼容模式：从 uid.txt 文件获取UID进行数据收集
            self.collect_data_from_uid_file()
            return

        # 根据模式决定搜索策略
        if self.mode == 'update':
            # 更新模式：直接基于现有数据中的 UID
            if not self.user_data:
                print("ℹ️  没有现有用户数据需要更新")
                return

            uids_to_update = list(self.user_data.keys())
            total_users = len(uids_to_update)
            print(f"🔄 更新模式：将通过 UID 更新 {total_users} 个已存在用户")
            print("💡 提示：使用 UID 查询，自动处理用户改名情况")

            # 执行 UID 更新逻辑
            self.update_users_by_uid(uids_to_update)
            return
        else:
            # 合并模式：从 user.txt 加载用户名
            all_usernames, usernames_to_search = self.load_usernames()
            if not usernames_to_search:
                if len(all_usernames) > 0:
                    print("✅ 所有用户都已存在，无需搜索新用户")
                else:
                    print("❌ 没有用户名需要搜索，程序退出")

                # 即使没有新搜索，也要保存现有数据（重新排序）
                if len(self.user_data) > 0:
                    self.save_results()
                return

            total_users = len(usernames_to_search)

        print(f"🚀 开始搜索 {total_users} 个用户...")
        print()

        # 逐个搜索用户
        for i, username in enumerate(usernames_to_search, 1):
            print(f"[{i}/{total_users}] ", end="")

            # 搜索用户
            user_info = self.search_user(username)

            if user_info:
                # 提取用户信息
                extracted_info = self.extract_user_info(user_info)
                uid = str(extracted_info['uid'])

                # 处理用户数据
                if uid in self.user_data:
                    if self.mode == 'update':
                        # 更新模式：更新用户信息
                        self.user_data[uid] = extracted_info
                        self.updated_count += 1
                        print(f"  ✅ 已更新用户信息")
                    # merge 模式在 search_user 中已经处理了跳过逻辑
                else:
                    # 新用户
                    self.user_data[uid] = extracted_info
                    self.success_count += 1

            # 请求间隔
            if i < total_users:
                time.sleep(self.request_interval)

        print()
        print("=" * 60)
        self.print_summary()
        print("=" * 60)

        # 保存结果
        self.save_results()

        print("🎉 程序执行完成！")

    def collect_data_from_uid_file(self):
        """
        UID兼容模式：从 uid.txt 文件读取UID并收集数据
        """
        print("🆔 UID兼容模式启动")
        print("📁 正在读取 uid.txt 文件...")

        # 读取UID列表
        try:
            with open('uid.txt', 'r', encoding='utf-8') as f:
                uid_lines = f.readlines()

            # 处理UID列表
            uids = []
            for line_num, line in enumerate(uid_lines, 1):
                uid = line.strip()
                if uid:  # 跳过空行
                    # 验证UID格式（应该是数字）
                    if uid.isdigit():
                        uids.append(uid)
                    else:
                        print(f"⚠️  第 {line_num} 行格式错误，跳过: {uid}")

            if not uids:
                print("❌ uid.txt 文件中没有有效的UID")
                return

            print(f"📊 从 uid.txt 读取到 {len(uids)} 个有效UID")
            print("🚀 开始收集用户数据...")
            print()

            # 使用两步更新流程收集数据
            self.collect_users_by_uid_list(uids)

        except FileNotFoundError:
            print("❌ 找不到 uid.txt 文件")
        except Exception as e:
            print(f"❌ 读取 uid.txt 文件失败: {e}")

    def collect_users_by_uid_list(self, uids):
        """
        通过UID列表收集用户数据（使用两步更新流程）

        Args:
            uids (list): UID列表
        """
        total_users = len(uids)
        print(f"🚀 开始收集 {total_users} 个用户的数据...")
        print("💡 使用两步流程：先获取基本信息，再获取完整数据")
        print()

        # 逐个收集用户数据
        for i, uid in enumerate(uids, 1):
            print(f"[{i}/{total_users}] ", end="")

            # 第一步：使用 UID API 获取基本信息
            print(f"正在收集用户数据: UID {uid}")
            basic_user_info = self.get_user_by_uid(uid)

            if not basic_user_info:
                print(f"  ❌ 无法获取用户信息，跳过")
                self.error_count += 1
                if i < total_users:
                    time.sleep(self.request_interval)
                continue

            # 获取用户名
            current_username = basic_user_info.get('username', '')
            print(f"  📝 用户名: {current_username}")

            # 第二步：使用搜索 API 获取完整数据
            print(f"  📡 获取完整数据...")
            complete_user_info = self.search_user_by_name_for_complete_data(current_username, uid)

            if complete_user_info:
                # 提取完整用户信息
                extracted_info = self.extract_user_info(complete_user_info)

                # 保存用户信息
                self.user_data[uid] = extracted_info
                self.success_count += 1
                print(f"  ✅ 数据收集成功")
            else:
                # 如果搜索失败，使用基本信息
                print(f"  ⚠️  搜索失败，使用基本信息")
                basic_extracted_info = self.extract_user_info_from_uid_api(basic_user_info)

                self.user_data[uid] = basic_extracted_info
                self.success_count += 1
                print(f"  ✅ 基本数据收集成功")

            # 请求间隔
            if i < total_users:
                time.sleep(self.request_interval)

        print()
        print("=" * 60)
        self.print_summary()
        print("=" * 60)

        # 保存结果
        self.save_results()

    def print_summary(self):
        """打印执行摘要"""
        print("📊 执行摘要:")
        print(f"   ✅ 新增用户: {self.success_count}")
        print(f"   🔄 更新用户: {self.updated_count}")
        print(f"   ⏭️  跳过用户: {self.skipped_count}")
        print(f"   ❌ 失败请求: {self.error_count}")
        print(f"   📝 总计记录: {len(self.user_data)}")

        # 显示错误记录统计
        if hasattr(self, 'existing_errors') and self.existing_errors:
            new_errors = len([name for name in self.existing_errors if name not in getattr(self, '_initial_errors', set())])
            if new_errors > 0:
                print(f"   📄 新增错误记录: {new_errors} 个（已保存到 error.txt）")

        if self.mode == 'merge':
            print(f"   💡 合并模式：保留了所有现有数据")
        elif self.mode == 'update':
            print(f"   💡 更新模式：更新了用户信息")

def select_mode():
    """交互式选择运行模式"""
    print("=" * 60)
    print("🔍 酷安用户搜索脚本")
    print("=" * 60)
    print("请选择运行模式:")
    print("1. 🔄 合并模式 - 保留现有数据，添加新用户")
    print("2. 🔄 更新模式 - 更新已存在用户的信息")
    print("3. 🔍 IP分析模式 - 查看同一登录IP下的多个用户")
    print("4. ⚠️  状态检查模式 - 查看状态不正常的用户")
    print("5. 🆔 UID兼容模式 - 从 uid.txt 文件获取UID进行数据收集")
    print("=" * 60)

    while True:
        try:
            choice = input("请输入选择 (1、2、3、4 或 5): ").strip()
            if choice == '1':
                return 'merge'
            elif choice == '2':
                return 'update'
            elif choice == '3':
                return 'analyze_ip'
            elif choice == '4':
                return 'check_status'
            elif choice == '5':
                return 'uid_mode'
            else:
                print("❌ 无效选择，请输入 1、2、3、4 或 5")
        except KeyboardInterrupt:
            print("\n❌ 操作已取消")
            sys.exit(0)

def main():
    """主函数"""
    # 交互式选择模式
    mode = select_mode()

    # 检查文件依赖
    if mode == 'merge':
        # 合并模式需要 user.txt
        if not os.path.exists('user.txt'):
            print("❌ 错误: 当前目录下找不到 user.txt 文件")
            print("📁 请确保脚本与 user.txt 文件在同一目录下")
            input("按回车键退出...")
            sys.exit(1)
    elif mode == 'update':
        # 更新模式需要 data.json
        if not os.path.exists('data.json'):
            print("❌ 错误: 更新模式需要现有的 data.json 文件")
            print("💡 请先运行合并模式创建初始数据")
            input("按回车键退出...")
            sys.exit(1)
    elif mode == 'analyze_ip':
        # IP分析模式需要 data.json
        if not os.path.exists('data.json'):
            print("❌ 错误: IP分析模式需要现有的 data.json 文件")
            print("💡 请先运行合并模式创建初始数据")
            input("按回车键退出...")
            sys.exit(1)
    elif mode == 'check_status':
        # 状态检查模式需要 data.json
        if not os.path.exists('data.json'):
            print("❌ 错误: 状态检查模式需要现有的 data.json 文件")
            print("💡 请先运行合并模式创建初始数据")
            input("按回车键退出...")
            sys.exit(1)
    elif mode == 'uid_mode':
        # UID兼容模式需要 uid.txt
        if not os.path.exists('uid.txt'):
            print("❌ 错误: UID兼容模式需要 uid.txt 文件")
            print("📁 请确保脚本与 uid.txt 文件在同一目录下")
            print("💡 uid.txt 文件格式：每行一个UID")
            input("按回车键退出...")
            sys.exit(1)

    # 显示选择的模式
    mode_descriptions = {
        'merge': '🔄 合并模式 - 保留现有数据，添加新用户',
        'update': '🔄 更新模式 - 更新已存在用户的信息',
        'analyze_ip': '🔍 IP分析模式 - 查看同一登录IP下的多个用户',
        'check_status': '⚠️ 状态检查模式 - 查看状态不正常的用户',
        'uid_mode': '🆔 UID兼容模式 - 从 uid.txt 文件获取UID进行数据收集'
    }

    print(f"\n🎯 已选择: {mode_descriptions[mode]}")
    print("🚀 程序即将开始运行...")
    print()

    # 创建搜索器并运行
    searcher = CoolapkUserSearch(mode=mode)
    searcher.request_interval = 0.5  # 固定1秒间隔

    try:
        searcher.run()
        print("\n🎉 程序执行完成！")
        input("按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        print("💾 正在保存已搜索的数据...")
        searcher.save_results()
        print("✅ 数据已保存，程序退出")
        input("按回车键退出...")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行出错: {str(e)}")
        print("💾 正在保存已搜索的数据...")
        searcher.save_results()
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()

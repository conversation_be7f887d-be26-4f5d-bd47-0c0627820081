import requests
import re
from datetime import datetime
import base64
import hashlib
import bcrypt
import json
import urllib.parse
import os
import time

def select_cookie_from_json():
    """从cookies.json文件中选择账号cookie"""
    cookies_file = 'cookies.json'

    # 检查文件是否存在
    if not os.path.exists(cookies_file):
        print(f"错误：找不到 {cookies_file} 文件")
        print("请确保cookies.json文件在脚本同目录下")
        return None

    try:
        # 读取cookies.json文件
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)

        if not cookies_data:
            print("错误：cookies.json文件为空")
            return None

        # 显示账号列表
        print("=" * 50)
        print("可用账号列表：")
        print("=" * 50)
        for i, account in enumerate(cookies_data, 1):
            username = account.get('username', '未知用户')
            uid = account.get('uid', '未知UID')
            print(f"{i}. {username} (UID: {uid})")

        print("=" * 50)

        # 获取用户选择
        while True:
            try:
                choice = input(f"请选择账号 (1-{len(cookies_data)})，输入0退出: ").strip()

                if choice == '0':
                    print("已退出")
                    return None

                choice_num = int(choice)
                if 1 <= choice_num <= len(cookies_data):
                    selected_account = cookies_data[choice_num - 1]
                    break
                else:
                    print(f"请输入1到{len(cookies_data)}之间的数字")
            except ValueError:
                print("请输入有效的数字")

        # 提取必要的cookie数据
        uid = selected_account.get('uid')
        username = selected_account.get('username')
        token = selected_account.get('token')

        if not all([uid, username, token]):
            print("错误：选中的账号缺少必要的数据 (uid, username, token)")
            return None

        # URL编码username
        username_encoded = urllib.parse.quote(username, safe='')

        # 构造cookies字典
        cookies = {
            'uid': uid,
            'username': username_encoded,
            'token': token
        }

        print(f"已选择账号：{username} (UID: {uid})")
        return cookies

    except json.JSONDecodeError:
        print("错误：cookies.json文件格式不正确")
        return None
    except Exception as e:
        print(f"读取cookies.json时发生错误：{e}")
        return None

def get_v2_token():
    """生成动态v2 token"""
    device_code = "xIDZwUTY1MTY5IjN0UzN2AyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR"
    format_base64 = re.compile('\\r\\n|\\r|\\n|=')
    token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
    device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
    timestamp = int(datetime.now().timestamp())
    timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
    timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
    token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
    token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
    token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
    token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
    arg = f'$2y$10${timestamp_base64}/{token_md5}'
    salt = (arg[:28] + 'u').encode('utf-8')
    crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
    crypt_base64 = base64.b64encode(crypt).decode()
    return f'v2{crypt_base64}'

def select_operation_mode():
    """选择运行模式"""
    print("\n" + "=" * 50)
    print("🎯 智能黑名单管理系统")
    print("=" * 50)
    print("请选择运行模式：")
    print("1. ⚡ 快速批量模式 - 自动添加，高效处理")
    print("2. 🎯 精确交互模式 - 逐个确认，精细控制")
    print("=" * 50)

    while True:
        try:
            choice = input("请选择模式 (1-2)，输入0退出: ").strip()

            if choice == '0':
                print("已退出")
                return None
            elif choice == '1':
                print("✅ 已选择：快速批量模式")
                return 'batch'
            elif choice == '2':
                print("✅ 已选择：精确交互模式")
                return 'interactive'
            else:
                print("请输入1或2")
        except ValueError:
            print("请输入有效的数字")

def get_full_blacklist(cookies, headers):
    """获取完整的黑名单列表"""
    print("\n🔍 正在获取当前黑名单列表...")

    blacklist_users = []
    blacklist_uids = set()
    page = 1
    capacity_info = None

    url = "https://api.coolapk.com/v6/user/blackList"

    while True:
        try:
            params = {'page': page}
            response = requests.get(url, headers=headers, params=params, cookies=cookies)
            time.sleep(0.5)  # 添加延迟
            response.raise_for_status()
            data = response.json()
            data_list = data.get('data', [])

            # 检查是否到达最后一页
            if len(data_list) == 1 and data_list[0].get('entityType') == 'configCard':
                capacity_info = data_list[0].get('extraData')
                print(f"📊 黑名单容量信息：{capacity_info}")
                break

            # 如果data_list为空，也退出循环
            if not data_list:
                break

            # 收集黑名单用户信息
            for user in data_list:
                if user.get('uid'):
                    blacklist_users.append(user)
                    blacklist_uids.add(str(user.get('uid')))

            print(f"📄 已获取第 {page} 页，当前收集到 {len(blacklist_users)} 个用户")
            page += 1

        except Exception as e:
            print(f"❌ 获取黑名单第 {page} 页时出错：{e}")
            break

    print(f"✅ 黑名单获取完成，共 {len(blacklist_users)} 个用户")
    return blacklist_users, blacklist_uids, capacity_info

def parse_blacklist_capacity(capacity_info):
    """解析黑名单容量信息"""
    if not capacity_info:
        print("⚠️ 无法获取容量信息，使用默认值")
        return 200, 0, 200

    try:
        capacity_data = json.loads(capacity_info)
        total = capacity_data.get('total', 200)
        current = capacity_data.get('current', 0)
        remaining = total - current

        print(f"📊 容量分析：总容量 {total}，已使用 {current}，剩余 {remaining}")
        return total, current, remaining
    except Exception as e:
        print(f"❌ 解析容量信息失败：{e}")
        return 200, 0, 200

def load_and_sort_data():
    """加载并排序data.json数据"""
    print("\n📂 正在加载 data.json 文件...")

    data_file = 'data.json'
    if not os.path.exists(data_file):
        print(f"❌ 找不到 {data_file} 文件")
        return []

    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"📊 成功加载 {len(data)} 个用户数据")

        # 按登录时间排序（从新到旧）
        def parse_login_time(user):
            try:
                login_time_str = user.get('logintime', '1970-01-01 00:00:00')
                return datetime.strptime(login_time_str, '%Y-%m-%d %H:%M:%S')
            except:
                return datetime(1970, 1, 1)

        sorted_data = sorted(data, key=parse_login_time, reverse=True)
        print("✅ 数据按登录时间排序完成（最近登录优先）")

        return sorted_data

    except Exception as e:
        print(f"❌ 加载 data.json 失败：{e}")
        return []

def filter_duplicates(data_users, blacklist_uids):
    """过滤重复用户"""
    print("\n🔍 正在进行去重处理...")

    candidates = []
    duplicates = 0

    for user in data_users:
        uid = str(user.get('uid', ''))
        if uid and uid not in blacklist_uids:
            candidates.append(user)
        else:
            duplicates += 1

    print(f"✅ 去重完成：找到 {len(candidates)} 个候选用户，跳过 {duplicates} 个重复用户")
    return candidates

# ANSI颜色代码
class Colors:
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"
    RESET = "\033[0m"
    BOLD = "\033[1m"

def add_to_blacklist(uid, cookies, headers, username=""):
    """添加用户到黑名单"""
    url = "https://api.coolapk.com/v6/user/addToBlackList"

    try:
        # 更新token
        headers['X-App-Token'] = get_v2_token()

        params = {'uid': uid}
        response = requests.post(url, headers=headers, params=params, cookies=cookies)
        time.sleep(0.5)  # 添加延迟
        response.raise_for_status()

        result = response.json()
        message = result.get('data', '未知结果')

        if '成功' in str(message):
            print(f"  {Colors.GREEN}✅ {username} (UID: {uid}) - {message}{Colors.RESET}")
            return True
        else:
            print(f"  {Colors.YELLOW}⚠️ {username} (UID: {uid}) - {message}{Colors.RESET}")
            return False

    except Exception as e:
        print(f"  {Colors.RED}❌ {username} (UID: {uid}) - 添加失败：{e}{Colors.RESET}")
        return False

def get_user_status_info(user):
    """获取用户状态信息"""
    status = user.get('status', '1')
    block_status = user.get('block_status', '0')
    mobilestatus = user.get('mobilestatus', '1')
    logintime = user.get('logintime', '未知')

    # 判断用户状态是否正常
    is_normal = status == '1' and block_status == '0' and mobilestatus == '1'

    status_text = ""
    if not is_normal:
        status_parts = []
        if status != '1':
            status_parts.append(f"status:{status}")
        if block_status != '0':
            status_parts.append(f"block:{block_status}")
        if mobilestatus != '1':
            status_parts.append(f"mobile:{mobilestatus}")
        status_text = f" [{', '.join(status_parts)}]"

    return is_normal, status_text

def batch_mode_add(candidates, remaining_capacity, cookies, headers):
    """批量模式添加"""
    print(f"\n⚡ 开始批量添加模式")
    print(f"📊 候选用户：{len(candidates)} 个，可添加容量：{remaining_capacity} 个")

    if len(candidates) > remaining_capacity:
        print(f"⚠️ 候选用户数量超过剩余容量，将只添加前 {remaining_capacity} 个用户")
        candidates = candidates[:remaining_capacity]

    success_count = 0
    failed_count = 0
    skipped_count = 0

    print(f"\n🚀 开始处理 {len(candidates)} 个用户...")

    for i, user in enumerate(candidates, 1):
        uid = user.get('uid')
        username = user.get('username', '未知用户')
        logintime = user.get('logintime', '未知')

        # 检查用户状态
        is_normal, status_text = get_user_status_info(user)

        print(f"\n[{i}/{len(candidates)}] 处理用户：{username} (UID: {uid})")
        print(f"  登录时间：{logintime}{status_text}")

        # 如果用户状态异常，询问是否继续
        if not is_normal:
            print(f"  {Colors.YELLOW}⚠️ 用户状态异常{status_text}{Colors.RESET}")
            choice = input("  是否仍要添加此用户？(y/n，默认n): ").strip().lower()
            if choice != 'y':
                print(f"  {Colors.BLUE}⏭️ 跳过此用户{Colors.RESET}")
                skipped_count += 1
                continue

        # 添加到黑名单
        if add_to_blacklist(uid, cookies, headers, username):
            success_count += 1
        else:
            failed_count += 1

    # 显示统计结果
    print(f"\n" + "=" * 50)
    print(f"📊 批量添加完成统计：")
    print(f"✅ 成功添加：{Colors.GREEN}{success_count}{Colors.RESET} 个")
    print(f"❌ 添加失败：{Colors.RED}{failed_count}{Colors.RESET} 个")
    print(f"⏭️ 跳过用户：{Colors.BLUE}{skipped_count}{Colors.RESET} 个")
    print(f"📈 总处理数：{success_count + failed_count + skipped_count} 个")
    print("=" * 50)

def interactive_mode_add(candidates, remaining_capacity, cookies, headers):
    """交互模式添加"""
    print(f"\n🎯 开始精确交互模式")
    print(f"📊 候选用户：{len(candidates)} 个，可添加容量：{remaining_capacity} 个")

    if len(candidates) > remaining_capacity:
        print(f"⚠️ 候选用户数量超过剩余容量，建议优先选择前 {remaining_capacity} 个用户")

    success_count = 0
    failed_count = 0
    skipped_count = 0
    added_count = 0

    print(f"\n🚀 开始逐个确认 {len(candidates)} 个用户...")
    print("💡 提示：输入 'y' 添加，'n' 跳过，'q' 退出，'a' 自动添加剩余所有用户")

    for i, user in enumerate(candidates, 1):
        # 检查是否已达到容量限制
        if added_count >= remaining_capacity:
            print(f"\n{Colors.YELLOW}⚠️ 已达到黑名单容量上限，停止添加{Colors.RESET}")
            break

        uid = user.get('uid')
        username = user.get('username', '未知用户')
        logintime = user.get('logintime', '未知')
        loginip = user.get('loginip', '未知')
        regip = user.get('regip', '未知')

        # 检查用户状态
        is_normal, status_text = get_user_status_info(user)

        print(f"\n" + "-" * 40)
        print(f"[{i}/{len(candidates)}] 用户详情：")
        print(f"👤 用户名：{Colors.CYAN}{username}{Colors.RESET}")
        print(f"🆔 UID：{uid}")
        print(f"⏰ 登录时间：{logintime}")
        print(f"🌐 登录IP：{loginip}")
        print(f"📍 注册IP：{regip}")

        if not is_normal:
            print(f"⚠️ 状态：{Colors.YELLOW}异常{status_text}{Colors.RESET}")
        else:
            print(f"✅ 状态：{Colors.GREEN}正常{Colors.RESET}")

        print("-" * 40)

        while True:
            choice = input("是否添加此用户到黑名单？(y/n/q/a): ").strip().lower()

            if choice == 'q':
                print(f"{Colors.BLUE}🛑 用户选择退出{Colors.RESET}")
                break
            elif choice == 'a':
                print(f"{Colors.MAGENTA}🚀 切换到自动模式，添加剩余所有正常用户{Colors.RESET}")
                # 自动添加剩余用户
                remaining_users = candidates[i-1:]
                for auto_user in remaining_users:
                    if added_count >= remaining_capacity:
                        break
                    auto_is_normal, _ = get_user_status_info(auto_user)
                    if auto_is_normal:  # 只自动添加状态正常的用户
                        auto_uid = auto_user.get('uid')
                        auto_username = auto_user.get('username', '未知用户')
                        print(f"\n自动添加：{auto_username} (UID: {auto_uid})")
                        if add_to_blacklist(auto_uid, cookies, headers, auto_username):
                            success_count += 1
                            added_count += 1
                        else:
                            failed_count += 1
                    else:
                        skipped_count += 1
                break
            elif choice == 'y':
                print(f"  正在添加用户：{username}")
                if add_to_blacklist(uid, cookies, headers, username):
                    success_count += 1
                    added_count += 1
                else:
                    failed_count += 1
                break
            elif choice == 'n':
                print(f"  {Colors.BLUE}⏭️ 跳过用户：{username}{Colors.RESET}")
                skipped_count += 1
                break
            else:
                print("请输入 y、n、q 或 a")

        if choice == 'q' or choice == 'a':
            break

    # 显示统计结果
    print(f"\n" + "=" * 50)
    print(f"📊 交互添加完成统计：")
    print(f"✅ 成功添加：{Colors.GREEN}{success_count}{Colors.RESET} 个")
    print(f"❌ 添加失败：{Colors.RED}{failed_count}{Colors.RESET} 个")
    print(f"⏭️ 跳过用户：{Colors.BLUE}{skipped_count}{Colors.RESET} 个")
    print(f"📈 总处理数：{success_count + failed_count + skipped_count} 个")
    print("=" * 50)

def main():
    """主程序"""
    print(f"{Colors.BOLD}{Colors.MAGENTA}🎯 智能黑名单管理系统 v1.0{Colors.RESET}")
    print(f"{Colors.CYAN}作者：Claude 4.0 sonnet{Colors.RESET}")
    print("=" * 50)

    # 1. 选择账号
    cookies = select_cookie_from_json()
    if cookies is None:
        return

    # 2. 选择运行模式
    mode = select_operation_mode()
    if mode is None:
        return

    # 3. 设置请求头
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) +CoolMarket/13.3.6-2310232-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-Sdk-Int": "33",
        "X-Sdk-Locale": "zh-CN",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": get_v2_token(),
        "X-App-Version": "13.3.6",
        "X-App-Code": "2310232",
        "X-Api-Version": "13",
        "X-App-Device": "xIDZwUTY1MTY5IjN0UzN2AyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
        "X-Dark-Mode": "0",
        "X-App-Channel": "coolapk",
        "X-App-Mode": "universal",
        "X-App-Supported": "2310232",
        "Host": "api.coolapk.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }

    # 4. 获取黑名单信息
    blacklist_users, blacklist_uids, capacity_info = get_full_blacklist(cookies, headers)

    # 5. 解析容量信息
    total, current, remaining = parse_blacklist_capacity(capacity_info)

    if remaining <= 0:
        print(f"{Colors.RED}❌ 黑名单已满，无法添加更多用户{Colors.RESET}")
        return

    # 6. 加载并排序数据
    data_users = load_and_sort_data()
    if not data_users:
        print(f"{Colors.RED}❌ 没有可用的用户数据{Colors.RESET}")
        return

    # 7. 去重处理
    candidates = filter_duplicates(data_users, blacklist_uids)

    if not candidates:
        print(f"{Colors.YELLOW}⚠️ 没有可添加的新用户（所有用户都已在黑名单中）{Colors.RESET}")
        return

    # 8. 根据模式执行添加操作
    if mode == 'batch':
        batch_mode_add(candidates, remaining, cookies, headers)
    elif mode == 'interactive':
        interactive_mode_add(candidates, remaining, cookies, headers)

    print(f"\n{Colors.BOLD}{Colors.GREEN}🎉 程序执行完成！{Colors.RESET}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}⚠️ 程序被用户中断{Colors.RESET}")
    except Exception as e:
        print(f"\n{Colors.RED}❌ 程序执行出错：{e}{Colors.RESET}")
    finally:
        print(f"{Colors.CYAN}感谢使用智能黑名单管理系统！{Colors.RESET}")
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
酷安消息监控脚本 - 检测外部用户发来的未读消息
功能：遍历cookies.json中的账号，检测是否收到其他人发来的未读消息
"""

import requests
import json
import time
import hashlib
import bcrypt
import base64
import re
import urllib.parse
from datetime import datetime
import os

def get_v2_token():
    """生成动态X-App-Token"""
    try:
        device_code = "gN4YWYwMmYiJjYiBTZjdTMgszc5V2atQ3clRHIxADMuUDM5AjMy4SMRtEVgszQ2MzSSdjMxIjMgsTat9WYphHI7kWbvFWa4ByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR"
        format_base64 = re.compile('\\r\\n|\\r|\\n|=')
        token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
        device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
        timestamp = int(datetime.now().timestamp())
        timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
        timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
        token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
        token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
        token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
        token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
        arg = f'$2y$10${timestamp_base64}/{token_md5}'
        salt = (arg[:28] + 'u').encode('utf-8')
        crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
        crypt_base64 = base64.b64encode(crypt).decode()
        return f'v2{crypt_base64}'
    except Exception as e:
        print(f"⚠️ Token生成失败: {e}")
        # 返回一个固定的token作为备用
        return "v2JDJ5JDEwJE1UYzFNVGczTXprMU53LzY3MTFkOU94eVNQQkpHL0pRUUlOaUpDemVSN3dpYWhrSDRYSzZ1"

def load_cookies():
    """加载cookies.json文件"""
    try:
        with open('cookies.json', 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)
        return cookies_data
    except FileNotFoundError:
        print("❌ 错误：找不到 cookies.json 文件")
        return None
    except json.JSONDecodeError:
        print("❌ 错误：cookies.json 文件格式错误")
        return None

def get_messages(account):
    """获取指定账号的私信消息列表"""
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; xiaomi; 22127RK36C; TKQ1.220905.001 test-keys; 13) +CoolMarket/13.3.6-2310232-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-Sdk-Int": "33",
        "X-Sdk-Locale": "zh-CN",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": get_v2_token(),
        "X-App-Version": "13.3.6",
        "X-App-Code": "2310232",
        "X-Api-Version": "13",
        "X-App-Device": "gN4YWYwMmYiJjYiBTZjdTMgszc5V2atQ3clRHIxADMuUDM5AjMy4SMRtEVgszQ2MzSSdjMxIjMgsTat9WYphHI7kWbvFWa4ByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
        "X-Dark-Mode": "0",
        "X-App-Channel": "coolapk",
        "X-App-Mode": "universal",
        "X-App-Supported": "2310232",
        "Host": "api.coolapk.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }

    # 构造Cookie字符串 - 使用URL编码处理中文用户名
    cookie_parts = []
    if 'uid' in account:
        cookie_parts.append(f"uid={account['uid']}")
    if 'username' in account:
        # 对中文用户名进行URL编码
        encoded_username = urllib.parse.quote(account['username'], safe='')
        cookie_parts.append(f"username={encoded_username}")
    if 'token' in account:
        cookie_parts.append(f"token={account['token']}")

    headers["Cookie"] = "; ".join(cookie_parts)

    url = "https://api.coolapk.com/v6/message/list?page=1"

    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"⚠️  账号 {account.get('username', account.get('uid', 'Unknown'))} API请求失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"⚠️  账号 {account.get('username', account.get('uid', 'Unknown'))} 网络请求错误: {e}")
        return None

def get_notifications(account):
    """获取指定账号的帖子回复通知列表"""
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; xiaomi; 22127RK36C; TKQ1.220905.001 test-keys; 13) +CoolMarket/13.3.6-2310232-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-Sdk-Int": "33",
        "X-Sdk-Locale": "zh-CN",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": get_v2_token(),
        "X-App-Version": "13.3.6",
        "X-App-Code": "2310232",
        "X-Api-Version": "13",
        "X-App-Device": "gN4YWYwMmYiJjYiBTZjdTMgszc5V2atQ3clRHIxADMuUDM5AjMy4SMRtEVgszQ2MzSSdjMxIjMgsTat9WYphHI7kWbvFWa4ByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
        "X-Dark-Mode": "0",
        "X-App-Channel": "coolapk",
        "X-App-Mode": "universal",
        "X-App-Supported": "2310232",
        "Host": "api.coolapk.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }

    # 构造Cookie字符串 - 使用URL编码处理中文用户名
    cookie_parts = []
    if 'uid' in account:
        cookie_parts.append(f"uid={account['uid']}")
    if 'username' in account:
        # 对中文用户名进行URL编码
        encoded_username = urllib.parse.quote(account['username'], safe='')
        cookie_parts.append(f"username={encoded_username}")
    if 'token' in account:
        cookie_parts.append(f"token={account['token']}")

    headers["Cookie"] = "; ".join(cookie_parts)

    url = "https://api.coolapk.com/v6/notification/list?page=1"

    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"⚠️  账号 {account.get('username', account.get('uid', 'Unknown'))} 通知API请求失败，状态码: {response.status_code}")
            return None
    except requests.exceptions.RequestException as e:
        print(f"⚠️  账号 {account.get('username', account.get('uid', 'Unknown'))} 通知网络请求错误: {e}")
        return None

def format_message_time(timestamp):
    """格式化消息时间戳"""
    try:
        dt = datetime.fromtimestamp(int(timestamp))
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return "未知时间"

def truncate_message(message, max_length=50):
    """截断消息内容"""
    if len(message) > max_length:
        return message[:max_length] + "..."
    return message

def extract_reply_content(note):
    """从note字段中提取回复内容"""
    try:
        # note格式: "回复了你的动态回复：<a href="/feed/xxx">回复内容</a>"
        import re
        # 使用正则表达式提取<a>标签中的内容
        match = re.search(r'<a[^>]*>([^<]+)</a>', note)
        if match:
            return match.group(1)
        else:
            # 如果没有找到<a>标签，返回整个note（去掉HTML标签）
            clean_note = re.sub(r'<[^>]+>', '', note)
            return clean_note
    except:
        return note

def check_unread_messages():
    """检测所有未读私信消息和帖子回复"""
    print("🔍 开始检测未读消息...")
    print("📧 包括：私信消息 + 帖子回复")
    print("=" * 60)

    # 加载账号信息
    cookies_data = load_cookies()
    if not cookies_data:
        return

    total_unread_messages = 0
    total_unread_replies = 0
    accounts_with_messages = 0
    accounts_with_replies = 0

    for i, account in enumerate(cookies_data):
        account_name = account.get('username', f"UID:{account.get('uid', 'Unknown')}")
        print(f"\n📱 检查账号: {account_name}")
        print("   " + "=" * 50)

        # === 检测私信消息 ===
        print("   📧 检查私信消息...")
        messages_data = get_messages(account)
        unread_messages = []

        if messages_data and 'data' in messages_data:
            current_username = account.get('username', '')

            for msg in messages_data['data']:
                # 检查是否为未读消息且不是自己发送的消息
                if (msg.get('isnew') == 1 and
                    msg.get('fromusername') and
                    msg.get('fromusername') != current_username):
                    unread_messages.append(msg)

        # 显示私信结果
        if unread_messages:
            accounts_with_messages += 1
            print(f"   📩 发现 {len(unread_messages)} 条未读私信:")
            print("   " + "-" * 45)

            for msg in unread_messages:
                sender = msg.get('fromusername', '未知发送者')
                message_content = truncate_message(msg.get('message', ''), 35)
                message_time = format_message_time(msg.get('dateline', 0))

                print(f"   👤 来自: {sender}")
                print(f"   ⏰ 时间: {message_time}")
                print(f"   💬 内容: {message_content}")
                print("   " + "-" * 45)

                total_unread_messages += 1
        else:
            print("   ✅ 无未读私信")

        # === 检测帖子回复 ===
        print("   🔔 检查帖子回复...")
        notifications_data = get_notifications(account)
        unread_replies = []

        if notifications_data and 'data' in notifications_data:
            for notification in notifications_data['data']:
                # 检查是否为未读的帖子回复
                if (notification.get('isnew') == 1 and
                    notification.get('type') == 'feed_reply' and
                    notification.get('fromusername')):
                    unread_replies.append(notification)

        # 显示帖子回复结果
        if unread_replies:
            accounts_with_replies += 1
            print(f"   🔔 发现 {len(unread_replies)} 条未读帖子回复:")
            print("   " + "-" * 45)

            for reply in unread_replies:
                sender = reply.get('fromusername', '未知回复者')
                reply_content = extract_reply_content(reply.get('note', ''))
                reply_content = truncate_message(reply_content, 35)
                reply_time = format_message_time(reply.get('dateline', 0))

                print(f"   👤 回复者: {sender}")
                print(f"   ⏰ 时间: {reply_time}")
                print(f"   💭 回复: {reply_content}")
                print("   " + "-" * 45)

                total_unread_replies += 1
        else:
            print("   ✅ 无未读帖子回复")

        # 限流延迟
        if i < len(cookies_data) - 1:
            time.sleep(0.1)

    # 显示总结
    print("\n" + "=" * 60)
    print(f"🎯 检测完成!")
    print(f"📊 私信统计: {accounts_with_messages} 个账号有未读私信，共 {total_unread_messages} 条")
    print(f"🔔 回复统计: {accounts_with_replies} 个账号有未读帖子回复，共 {total_unread_replies} 条")
    print(f"📈 总计: {total_unread_messages + total_unread_replies} 条未读消息")

    if total_unread_messages == 0 and total_unread_replies == 0:
        print("🎉 恭喜！所有账号都没有未读消息")

def main():
    """主程序入口"""

    print("🚀 酷安消息监控脚本启动")
    print("📋 功能：检测未读私信消息 + 帖子回复")
    print("🔧 基于cookies.json中的账号进行检测")
    print("💡 包括来自其他cookies账号的消息")
    print("🔔 新增：帖子回复通知监控")
    print()

    try:
        check_unread_messages()
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

    print("\n👋 程序结束")

if __name__ == "__main__":
    main()
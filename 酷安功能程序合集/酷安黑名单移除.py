import requests
import re
from datetime import datetime
import base64
import hashlib
import bcrypt
import json
import urllib.parse
import os
import time

def select_cookie_from_json():
    """从cookies.json文件中选择账号cookie"""
    cookies_file = 'cookies.json'

    # 检查文件是否存在
    if not os.path.exists(cookies_file):
        print(f"错误：找不到 {cookies_file} 文件")
        print("请确保cookies.json文件在脚本同目录下")
        return None

    try:
        # 读取cookies.json文件
        with open(cookies_file, 'r', encoding='utf-8') as f:
            cookies_data = json.load(f)

        if not cookies_data:
            print("错误：cookies.json文件为空")
            return None

        # 显示账号列表
        print("=" * 50)
        print("可用账号列表：")
        print("=" * 50)
        for i, account in enumerate(cookies_data, 1):
            username = account.get('username', '未知用户')
            uid = account.get('uid', '未知UID')
            print(f"{i}. {username} (UID: {uid})")

        print("=" * 50)

        # 获取用户选择
        while True:
            try:
                choice = input(f"请选择账号 (1-{len(cookies_data)})，输入0退出: ").strip()

                if choice == '0':
                    print("已退出")
                    return None

                choice_num = int(choice)
                if 1 <= choice_num <= len(cookies_data):
                    selected_account = cookies_data[choice_num - 1]
                    break
                else:
                    print(f"请输入1到{len(cookies_data)}之间的数字")
            except ValueError:
                print("请输入有效的数字")

        # 提取必要的cookie数据
        uid = selected_account.get('uid')
        username = selected_account.get('username')
        token = selected_account.get('token')

        if not all([uid, username, token]):
            print("错误：选中的账号缺少必要的数据 (uid, username, token)")
            return None

        # URL编码username
        username_encoded = urllib.parse.quote(username, safe='')

        # 构造cookies字典
        cookies = {
            'uid': uid,
            'username': username_encoded,
            'token': token
        }

        print(f"已选择账号：{username} (UID: {uid})")
        return cookies

    except json.JSONDecodeError:
        print("错误：cookies.json文件格式不正确")
        return None
    except Exception as e:
        print(f"读取cookies.json时发生错误：{e}")
        return None

# 选择cookie
cookies = select_cookie_from_json()
if cookies is None:
    exit(1)
dayss = 3  # 举报天数
def get_v2_token():
    device_code = "xIDZwUTY1MTY5IjN0UzN2AyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR"  # 抓包headers有，固定即可
    format_base64 = re.compile('\\r\\n|\\r|\\n|=')
    token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
    device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
    timestamp = int(datetime.now().timestamp())
    timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
    timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
    token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
    token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
    token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
    token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
    arg = f'$2y$10${timestamp_base64}/{token_md5}'
    salt = (arg[:28] + 'u').encode('utf-8')
    crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
    crypt_base64 = base64.b64encode(crypt).decode()
    return f'v2{crypt_base64}'

# 根据提供的curl命令设置请求头，但不包含敏感信息
headers = {
    "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) +CoolMarket/13.3.6-2310232-universal",
    "X-Requested-With": "XMLHttpRequest",
    "X-Sdk-Int": "33",
    "X-Sdk-Locale": "zh-CN",
    "X-App-Id": "com.coolapk.market",
    "X-App-Token": get_v2_token(),
    "X-App-Version": "13.3.6",
    "X-App-Code": "2310232",
    "X-Api-Version": "13",
    "X-App-Device": "xIDZwUTY1MTY5IjN0UzN2AyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
    "X-Dark-Mode": "0",
    "X-App-Channel": "coolapk",
    "X-App-Mode": "universal",
    "X-App-Supported": "2310232",
    "Host": "api.coolapk.com",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip"
}


url = "https://api.coolapk.com/v6/user/blackList"
remove_url = "https://api.coolapk.com/v6/user/removeFromBlackList"

page = 1

# ANSI转义序列，用于设置文本颜色
RED = "\033[31m"
RESET = "\033[0m"

while True:
    params = {'page': page}
    response = requests.get(url, headers=headers, params=params, cookies=cookies)
    time.sleep(0.5)  # 添加0.5秒延迟
    response.raise_for_status()
    data = response.json()
    data_list = data.get('data', [])

    # 检查data_list是否仅包含一个entityType为configCard的项
    if len(data_list) == 1 and data_list[0].get('entityType') == 'configCard':
        print(data_list[0].get('extraData'))
        break  # 没有更多数据，退出循环

    # 如果data_list为空，也退出循环
    if not data_list:
        break

    for user in data_list:
        uid = user.get('uid')
        username = user.get('username')
        logintime = user.get('logintime')
        if username and logintime:
            # 将logintime（以秒为单位的时间戳）转换为datetime对象
            user_login_datetime = datetime.fromtimestamp(logintime)
            now = datetime.now()
            delta = now - user_login_datetime
            total_seconds = abs(int(delta.total_seconds()))
            days = total_seconds // (24 * 3600)
            hours = (total_seconds % (24 * 3600)) // 3600
            if delta.total_seconds() >= 0:
                output = f"{username} 上次登录距今 {days} 天 {hours} 小时前"
            else:
                output = f"{username} 上次登录距今 {days} 天 {hours} 小时后"
            # 如果天数大于3，输出红色文本并移除黑名单
            if days >= dayss:
                print(f"{RED}{output}，将其从黑名单中移除...{RESET}")
                # 发送POST请求移除黑名单
                remove_params = {'uid': uid}
                remove_response = requests.post(remove_url, headers=headers, params=remove_params, cookies=cookies)
                time.sleep(0.5)  # 添加0.5秒延迟
                remove_response.raise_for_status()
                remove_data = remove_response.json()
                # 输出移除结果
                remove_message = remove_data.get('data', '未知错误')
                print(f"移除结果：{remove_message}")
            else:
                print(output)
    page += 1
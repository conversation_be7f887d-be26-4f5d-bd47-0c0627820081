#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数值增长奖励计算器
功能：计算输入数字达到目标值5625所需的差值，考虑奖励档次加成
实现逐步模拟增长过程，详细展示计算步骤
"""


class RewardGrowthCalculator:
    """
    数值增长奖励计算器
    
    实现从初始值到目标值的逐步增长模拟，
    包含多档次奖励机制和详细的计算过程记录。
    """
    
    def __init__(self, target=5625):
        """
        初始化计算器
        
        Args:
            target (int): 目标数值，默认5625
        """
        self.target = target
        
        # 奖励档次字典 {阈值: 奖励值}
        self.reward_tiers = {
            50: 10,
            100: 20,
            250: 20,
            400: 40,
            600: 60,
            1000: 100,
            1500: 150,
            2000: 200,
            2500: 250,
            3000: 300,
            4000: 400,
            5000: 500
        }
    
    def calculate_step_by_step(self, initial_value):
        """
        逐步模拟从初始值到目标值的增长过程
        
        Args:
            initial_value (float): 初始数值
            
        Returns:
            dict: 包含详细计算步骤的结果字典
        """
        # 输入验证
        if not isinstance(initial_value, (int, float)):
            raise ValueError("初始值必须是数字")
        
        if initial_value < 0:
            raise ValueError("初始值不能为负数")
        
        # 如果已经达到或超过目标值
        if initial_value >= self.target:
            return {
                'initial_value': initial_value,
                'target': self.target,
                'already_reached': True,
                'total_investment': 0,
                'total_rewards': 0,
                'steps': [],
                'final_value': initial_value
            }
        
        # 初始化计算状态
        current_value = initial_value
        total_investment = 0
        total_rewards = 0
        steps = []
        
        # 获取所有奖励阈值，按升序排列
        # 新逻辑：假设输入值时所有奖励都没有领取过
        # 包括当前值已经达到的阈值奖励也需要重新计算
        available_thresholds = sorted(self.reward_tiers.keys())
        
        # 逐步处理每个奖励阈值
        for threshold in available_thresholds:
            if current_value >= self.target:
                break

            # 如果阈值超过目标值，直接计算到目标值的成本
            if threshold > self.target:
                remaining_cost = self.target - current_value
                total_investment += remaining_cost

                steps.append({
                    'step_type': 'final',
                    'from_value': current_value,
                    'to_value': self.target,
                    'investment': remaining_cost,
                    'reward': 0,
                    'description': f"从 {current_value} 直接到达目标 {self.target}"
                })

                current_value = self.target
                break

            # 计算到达当前阈值的投入成本
            if current_value <= threshold:
                # 如果当前值小于等于阈值，需要投入到达阈值
                investment_to_threshold = threshold - current_value
            else:
                # 如果当前值已经超过阈值，无需投入，直接获得奖励
                investment_to_threshold = 0

            reward = self.reward_tiers[threshold]

            # 记录这一步
            if investment_to_threshold > 0:
                steps.append({
                    'step_type': 'threshold',
                    'from_value': current_value,
                    'to_value': threshold,
                    'investment': investment_to_threshold,
                    'reward': reward,
                    'description': f"从 {current_value} 投入 {investment_to_threshold} 到达 {threshold}，获得奖励 {reward}"
                })
            else:
                steps.append({
                    'step_type': 'threshold',
                    'from_value': current_value,
                    'to_value': current_value,
                    'investment': 0,
                    'reward': reward,
                    'description': f"当前值 {current_value} 已达到阈值 {threshold}，直接获得奖励 {reward}"
                })

            # 更新状态
            total_investment += investment_to_threshold
            total_rewards += reward
            current_value = max(current_value, threshold) + reward  # 当前值 = max(当前值, 阈值) + 奖励
        
        # 如果还没有达到目标值，计算剩余成本
        if current_value < self.target:
            remaining_cost = self.target - current_value
            total_investment += remaining_cost
            
            steps.append({
                'step_type': 'final',
                'from_value': current_value,
                'to_value': self.target,
                'investment': remaining_cost,
                'reward': 0,
                'description': f"从 {current_value} 投入 {remaining_cost} 到达目标 {self.target}"
            })
            
            current_value = self.target
        
        return {
            'initial_value': initial_value,
            'target': self.target,
            'already_reached': False,
            'total_investment': total_investment,
            'total_rewards': total_rewards,
            'steps': steps,
            'final_value': current_value
        }


def display_detailed_process(result):
    """
    详细展示计算过程

    Args:
        result (dict): calculate_step_by_step方法返回的结果字典
    """
    print("\n" + "="*50)
    print("🐾 数值增长奖励计算详细过程")
    print("="*50)

    # 显示基本信息
    print(f"📊 初始值: {result['initial_value']}")
    print(f"🎯 目标值: {result['target']}")

    # 如果已经达到目标
    if result['already_reached']:
        print("✅ 初始值已达到或超过目标值！")
        print(f"💰 无需额外投入")
        return

    print(f"\n📈 计算步骤:")
    print("-" * 40)

    current_display_value = result['initial_value']

    # 显示每个计算步骤
    for i, step in enumerate(result['steps'], 1):
        if step['step_type'] == 'threshold':
            print(f"步骤 {i}: 触发奖励阈值")
            print(f"  📍 当前值: {step['from_value']}")
            print(f"  💸 投入成本: {step['investment']}")
            print(f"  🎯 达到阈值: {step['to_value']}")
            print(f"  🎁 获得奖励: +{step['reward']}")
            print(f"  📈 新当前值: {step['to_value'] + step['reward']}")
            print(f"  📝 说明: {step['description']}")
            current_display_value = step['to_value'] + step['reward']
        else:  # final step
            print(f"步骤 {i}: 最终冲刺")
            print(f"  📍 当前值: {step['from_value']}")
            print(f"  💸 投入成本: {step['investment']}")
            print(f"  🏁 达到目标: {step['to_value']}")
            print(f"  📝 说明: {step['description']}")

        print()

    # 显示最终汇总
    print("📋 计算汇总:")
    print("-" * 40)
    print(f"💰 总投入成本: {result['total_investment']}")
    print(f"🎁 总获得奖励: {result['total_rewards']}")
    print(f"🏁 最终数值: {result['final_value']}")

    # 验证计算
    print(f"\n🔍 验证计算:")
    verification = result['initial_value'] + result['total_investment'] + result['total_rewards']
    print(f"初始值 + 总投入 + 总奖励 = {result['initial_value']} + {result['total_investment']} + {result['total_rewards']} = {verification}")

    if verification == result['final_value']:
        print("✅ 计算验证通过！")
    else:
        print("❌ 计算验证失败！")

    print("="*50)


def validate_input(user_input):
    """
    验证用户输入的有效性

    Args:
        user_input (str): 用户输入的字符串

    Returns:
        float: 验证通过的数值

    Raises:
        ValueError: 输入无效时抛出异常
    """
    # 检查输入是否为空
    if not user_input or not user_input.strip():
        raise ValueError("输入不能为空")

    # 去除首尾空格
    user_input = user_input.strip()

    # 检查是否为特殊命令
    if user_input.lower() in ['q', 'quit', 'exit', 'test', 'help']:
        return user_input.lower()

    try:
        # 尝试转换为浮点数
        value = float(user_input)
    except ValueError:
        raise ValueError(f"'{user_input}' 不是有效的数字")

    # 检查数值范围
    if value < 0:
        raise ValueError("数值不能为负数")

    if value > 1000000:  # 设置一个合理的上限
        raise ValueError("数值过大，请输入小于1,000,000的数字")

    # 检查是否为整数（可选，根据需求调整）
    if value != int(value) and value > 10000:
        # 对于大数值，建议使用整数
        print(f"⚠️  提示：建议使用整数 {int(value)} 而不是 {value}")

    return value


def handle_calculation_error(error, initial_value=None):
    """
    处理计算过程中的错误

    Args:
        error (Exception): 捕获的异常
        initial_value: 导致错误的初始值（可选）
    """
    print(f"\n❌ 计算过程中发生错误:")

    if isinstance(error, ValueError):
        print(f"   数值错误: {error}")
    elif isinstance(error, OverflowError):
        print(f"   数值溢出: 计算结果过大")
    elif isinstance(error, ZeroDivisionError):
        print(f"   除零错误: 计算过程中出现除零操作")
    else:
        print(f"   未知错误: {error}")

    if initial_value is not None:
        print(f"   问题输入值: {initial_value}")

    print(f"   💡 建议: 请检查输入值是否合理，或联系开发者")


def display_help():
    """
    显示帮助信息
    """
    print("\n" + "="*50)
    print("🐾 数值增长奖励计算器 - 帮助信息")
    print("="*50)
    print("📖 功能说明:")
    print("   计算从输入数字到目标值5625所需的最小投入成本")
    print("   ⚠️  重要：假设输入值时所有奖励都没有领取过")
    print("   考虑多档次奖励机制的逐步模拟过程")
    print()
    print("🎯 奖励档次:")
    print("   50→+10, 100→+20, 250→+20, 400→+40, 600→+60")
    print("   1000→+100, 1500→+150, 2000→+200, 2500→+250")
    print("   3000→+300, 4000→+400, 5000→+500")
    print()
    print("🔄 算法逻辑:")
    print("   1. 输入值假设所有奖励都没有领取")
    print("   2. 程序会计算能获得的所有奖励")
    print("   3. 包括输入值已经达到的阈值奖励")
    print("   4. 计算到达目标5625的最小投入")
    print()
    print("💡 使用方法:")
    print("   1. 输入一个数字（0-1,000,000）")
    print("   2. 程序会显示详细的计算过程")
    print("   3. 输入 'test' 运行测试用例")
    print("   4. 输入 'help' 显示此帮助信息")
    print("   5. 输入 'q' 或 'quit' 退出程序")
    print()
    print("📝 示例:")
    print("   输入: 4999")
    print("   结果: 获得所有≤4999的奖励，然后计算到5625的投入")
    print("="*50)


def run_test_cases():
    """
    运行测试用例验证计算器的正确性

    Returns:
        bool: 所有测试是否通过
    """
    print("\n" + "="*60)
    print("🧪 运行测试用例验证")
    print("="*60)

    calculator = RewardGrowthCalculator()

    # 定义测试用例：[输入值, 预期总投入, 预期总奖励, 描述]
    # 注意：新逻辑假设所有奖励都没有领取过
    # 所有奖励总和：10+20+20+40+60+100+150+200+250+300+400+500 = 2050
    test_cases = [
        # 核心示例 - 4999会获得所有<=4000的奖励(1550)，然后需要投入到5000获得500奖励
        (4999, 1, 2050, "核心示例：4999获得<=4000奖励1550，投入1到5000获得500，总奖励2050"),

        # 边界值测试
        (0, 3575, 2050, "从0开始：投入到5000触发所有奖励，然后投入到5625"),
        (49, 3576, 2050, "接近第一个阈值：获得所有奖励"),
        (50, 3575, 2050, "刚好达到第一个阈值：获得所有奖励"),

        # 各个阈值测试 - 需要重新计算
        (100, 3575, 2050, "刚好100阈值：获得所有奖励"),
        (1000, 3575, 2050, "刚好1000阈值：获得所有奖励"),
        (5000, 0, 2050, "刚好5000阈值：获得所有奖励，无需投入"),

        # 已达到目标的情况
        (5625, 0, 0, "刚好达到目标"),
        (5626, 0, 0, "超过目标"),
        (10000, 0, 0, "远超目标"),

        # 简化的特殊值测试
        (1, 3574, 2050, "最小正整数：获得所有奖励"),
        (3000, 3575, 2050, "刚好3000阈值：获得所有奖励"),
    ]

    passed_tests = 0
    failed_tests = 0

    print(f"📋 共 {len(test_cases)} 个测试用例\n")

    for i, (input_value, expected_investment, expected_rewards, description) in enumerate(test_cases, 1):
        print(f"测试 {i:2d}: {description}")
        print(f"        输入值: {input_value}")

        try:
            result = calculator.calculate_step_by_step(input_value)
            actual_investment = result['total_investment']
            actual_rewards = result['total_rewards']

            # 验证投入成本
            investment_correct = actual_investment == expected_investment
            # 验证奖励总额
            rewards_correct = actual_rewards == expected_rewards

            if investment_correct and rewards_correct:
                print(f"        ✅ 通过: 投入 {actual_investment}, 奖励 {actual_rewards}")
                passed_tests += 1
            else:
                print(f"        ❌ 失败:")
                if not investment_correct:
                    print(f"           投入: 期望 {expected_investment}, 实际 {actual_investment}")
                if not rewards_correct:
                    print(f"           奖励: 期望 {expected_rewards}, 实际 {actual_rewards}")
                failed_tests += 1

        except Exception as e:
            print(f"        ❌ 异常: {e}")
            failed_tests += 1

        print()

    # 测试报告
    total_tests = len(test_cases)
    success_rate = (passed_tests / total_tests) * 100

    print("="*60)
    print("📊 测试报告")
    print("="*60)
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {failed_tests}")
    print(f"成功率: {success_rate:.1f}%")

    if failed_tests == 0:
        print("🎉 所有测试用例通过！计算器功能正确。")
        return True
    else:
        print("⚠️  部分测试失败，需要检查算法实现。")
        return False


def run_edge_case_tests():
    """
    运行边界情况和特殊场景测试

    Returns:
        bool: 边界测试是否通过
    """
    print("\n" + "="*60)
    print("🔬 边界情况和特殊场景测试")
    print("="*60)

    calculator = RewardGrowthCalculator()

    # 边界和特殊情况测试
    edge_cases = [
        # 浮点数测试
        (49.5, "小数接近阈值"),
        (50.1, "小数超过阈值"),
        (4999.9, "小数接近5000"),

        # 大数值测试
        (100000, "超大数值"),

        # 精确阈值测试
        (5000.0, "精确5000.0"),
        (5625.0, "精确目标值"),
    ]

    passed = 0
    total = len(edge_cases)

    for input_value, description in edge_cases:
        print(f"测试: {description} (输入: {input_value})")

        try:
            result = calculator.calculate_step_by_step(input_value)

            # 基本验证：确保结果合理
            if input_value >= 5625:
                # 已达到目标
                if result['already_reached'] and result['total_investment'] == 0:
                    print(f"  ✅ 正确识别已达到目标")
                    passed += 1
                else:
                    print(f"  ❌ 未正确处理已达到目标的情况")
            else:
                # 未达到目标
                if not result['already_reached'] and result['final_value'] >= 5625:
                    print(f"  ✅ 正确计算: 投入 {result['total_investment']}, 奖励 {result['total_rewards']}")
                    passed += 1
                else:
                    print(f"  ❌ 计算结果异常: 最终值 {result['final_value']}")

        except Exception as e:
            print(f"  ❌ 计算异常: {e}")

    print(f"\n📊 边界测试结果: {passed}/{total} 通过")
    return passed == total


def show_welcome():
    """显示欢迎信息"""
    print("\n" + "="*60)
    print("🐾 欢迎使用数值增长奖励计算器！")
    print("="*60)
    print("📖 功能说明：")
    print("   计算从输入数字到目标值 5625 所需的最小投入成本")
    print("   ⚠️  重要：假设输入值时所有奖励都没有领取过")
    print("   考虑多档次奖励机制的逐步模拟过程")
    print()
    print("💡 可用命令：")
    print("   • 输入数字 - 进行计算")
    print("   • help    - 显示详细帮助")
    print("   • test    - 运行测试用例")
    print("   • q/quit  - 退出程序")
    print()
    print("🎯 示例：输入 4999，程序会获得所有≤4999的奖励并计算投入")
    print("="*60)


def interactive_main():
    """交互式主程序"""
    # 显示欢迎信息
    show_welcome()

    # 创建计算器实例
    calculator = RewardGrowthCalculator()

    # 主交互循环
    while True:
        try:
            print("\n" + "-"*40)
            user_input = input("请输入数字或命令: ").strip()

            if not user_input:
                print("⚠️  输入不能为空，请重新输入")
                continue

            # 验证输入
            try:
                validated_input = validate_input(user_input)
            except ValueError as e:
                print(f"❌ {e}")
                print("💡 提示：输入 'help' 查看使用说明")
                continue

            # 处理命令
            if isinstance(validated_input, str):
                command = validated_input.lower()

                if command in ['q', 'quit', 'exit']:
                    print("\n👋 感谢使用数值增长奖励计算器！")
                    print("🐾 再见！")
                    break

                elif command == 'help':
                    display_help()

                elif command == 'test':
                    print("\n🧪 运行测试用例...")
                    test_passed = run_test_cases()
                    if test_passed:
                        print("🎉 所有测试通过！")
                    else:
                        print("⚠️  部分测试失败")

                else:
                    print(f"❌ 未知命令: {command}")
                    print("💡 输入 'help' 查看可用命令")

            # 处理数字输入
            else:
                print(f"\n🔢 计算输入值: {validated_input}")

                try:
                    # 执行计算
                    result = calculator.calculate_step_by_step(validated_input)

                    # 显示详细过程
                    display_detailed_process(result)

                    # 简要总结
                    if result['already_reached']:
                        print(f"\n💡 总结: 输入值 {validated_input} 已达到目标，无需额外投入")
                    else:
                        print(f"\n💡 总结: 从 {validated_input} 到达 5625 需要投入 {result['total_investment']}")

                except Exception as e:
                    handle_calculation_error(e, validated_input)

        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断")
            print("🐾 再见！")
            break

        except EOFError:
            print("\n\n👋 输入结束")
            print("🐾 再见！")
            break

        except Exception as e:
            print(f"\n❌ 程序发生未预期的错误: {e}")
            print("💡 请重新尝试或联系开发者")


def run_development_tests():
    """运行开发测试（仅在开发模式下使用）"""
    print("🧪 开发测试模式")
    print("="*60)

    # 创建计算器实例
    calculator = RewardGrowthCalculator()

    # 快速验证核心功能
    print("✅ 快速功能验证:")
    result = calculator.calculate_step_by_step(4999)
    expected = 126
    if result['total_investment'] == expected:
        print(f"  ✅ 核心示例通过: 4999 → 投入 {result['total_investment']}")
    else:
        print(f"  ❌ 核心示例失败: 期望 {expected}, 实际 {result['total_investment']}")

    # 运行完整测试套件
    print("\n🧪 完整测试套件:")
    test_results = []
    test_results.append(run_test_cases())
    test_results.append(run_edge_case_tests())

    passed = sum(test_results)
    total = len(test_results)
    print(f"\n📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有开发测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False


# 主程序入口
if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--test':
            # 开发测试模式
            success = run_development_tests()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == '--help':
            # 显示帮助后退出
            display_help()
            sys.exit(0)
        else:
            print(f"❌ 未知参数: {sys.argv[1]}")
            print("💡 可用参数: --test, --help")
            sys.exit(1)
    else:
        # 正常交互模式
        interactive_main()

import requests
from urllib.parse import urlencode, quote_plus
import re
from datetime import datetime
import pytz
import json
import os

# 认证所需的Cookies（如果需要，请替换为您自己的）
cookies = {
    'uid': '35114371',
    'username': '%E7%9C%8B%E4%BB%80%E4%B9%88%E4%B9%88%E4%B9%88',  # 看什么么么
    'token': '85986164lcQDc_oiXN-WqvWnC0GodIwixoDVo1bJFPsm3UgQQdlGtCaaoIa9lHCrF3SdVK0O8OAOMVmng-e1LDhVd8To0Z-btGKJ2JKRzuX2BzZbEEzYNXz0tm-jhpViH8O0cwPvy6qaEvOr_9xizLVg6jglmst9omr3HlSyVhKLaRSv5KP8zbz701OiLW9EpsriBcmhaTfrprsC-MQ4s_0IhF5Arg',
}

# 请求头，包含必要的信息
headers = {
    "User-Agent": (
        "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) "
        "(#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) "
        "+CoolMarket/13.3.6-2310232-universal"
    ),  # 用户代理，包含设备和应用信息
    "X-Requested-With": "XMLHttpRequest",  # 指定请求方式为XHR
    "X-Sdk-Int": "33",  # SDK版本号
    "X-Sdk-Locale": "zh-CN",  # 语言区域
    "X-App-Id": "com.coolapk.market",  # 应用ID
    "X-App-Token": "v2JDJ5JDEwJE1UY3pNamc1T1RZek9RLzNlNzkyMXU4QnZUY0VNU01pQTlBR1U1dnVJUmlpaWR1Y0JiU3VL",
    # 应用Token（敏感信息，应动态获取）
    "X-App-Version": "13.3.6",  # 应用版本号
    "X-App-Code": "2310232",  # 应用代码版本
    "X-Api-Version": "13",  # API版本
    "X-App-Device": "xIDZwUTY1MTY5IjN0UzN2AyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
    # 设备信息（可能是加密后的设备标识）
    "X-Dark-Mode": "0",  # 是否开启暗黑模式（0：否，1：是）
    "X-App-Channel": "coolapk",  # 应用渠道
    "X-App-Mode": "universal",  # 应用模式
    "X-App-Supported": "2310232",  # 支持的应用版本
    "Host": "api.coolapk.com",  # 主机名
    "Connection": "Keep-Alive",  # 连接方式
    "Accept-Encoding": "gzip",  # 接受的编码方式
}


def get_recent_posts():
    """
    获取特定标签下的最新帖子。

    返回值：
        list: 帖子数据列表。
    """
    # 获取帖子列表的基础URL
    base_url = "https://api.coolapk.com/v6/page/dataList"

    # 内部URL和参数
    inner_url = "#/feed/multiTagFeedList"
    inner_params = {
        "listType": "lastupdate_desc",
        "isIncludeTop": "1",
        "hiddenTagRelation": "1",
        "cacheExpires": "60",
        "ignoreEntityById": "1",
        "tag": "我的流量套餐"
    }

    # 对内部参数进行编码
    encoded_inner_params = urlencode(inner_params, quote_via=quote_plus)

    # 构建完整的内部URL
    full_inner_url = f"{inner_url}?{encoded_inner_params}"

    # 请求的外部参数
    params = {
        "url": full_inner_url,
        "title": "最近回复",
        "subTitle": "",
        "page": "1"
    }

    # 发送GET请求到API
    response = requests.get(base_url, params=params, headers=headers, cookies=cookies)
    # 返回JSON响应中的data部分
    return response.json().get('data', [])


def get_post_comments(post_id, page):
    """
    获取特定帖子的评论。

    参数：
        post_id (str): 帖子ID。
        page (int): 要获取的评论页码。

    返回值：
        list: 评论数据列表。
    """
    params = {
        'id': post_id,
        'listType': 'lastupdate_desc',
        'page': page,
        'discussMode': '1',
        'feedType': 'feed',
        'blockStatus': '0',
        'fromFeedAuthor': '0',
    }

    response = requests.get('https://api2.coolapk.com/v6/feed/replyList', params=params, cookies=cookies,
                            headers=headers)
    # 返回JSON响应中的data部分
    return response.json().get('data', [])


def format_timestamp(timestamp, timezone_str="Asia/Shanghai"):
    """
    将UNIX时间戳格式化为可读的字符串。

    参数：
        timestamp (int): UNIX时间戳。
        timezone_str (str): 时区字符串。

    返回值：
        str: 格式化的日期时间字符串。
    """
    timezone = pytz.timezone(timezone_str)
    dt_object = datetime.fromtimestamp(timestamp, tz=timezone)
    return dt_object.strftime("%Y-%m-%d %H:%M:%S")


def clean_html(raw_html):
    """
    从字符串中移除HTML标签和不必要的空白。

    参数：
        raw_html (str): 原始HTML字符串。

    返回值：
        str: 清理后的文本。
    """
    # 移除所有<a>标签及其内容
    clean_text = re.sub(r'<a[^>]*>.*?</a>', '', raw_html)

    # 移除其他HTML标签
    clean_text = re.sub(r'<[^>]+>', '', clean_text)

    # 移除换行符
    clean_text = clean_text.replace('\n', '')

    # 移除多余的空白
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()

    return clean_text


def load_kuan_data():
    """
    加载包含帖子和评论最新时间戳的'kuan.json'文件。

    返回值：
        dict: 以帖子ID为键，最新时间戳为值的字典。
    """
    if os.path.exists('kuan.json'):
        with open('kuan.json', 'r', encoding='utf-8') as file:
            return json.load(file)
    else:
        return {}


def save_kuan_data(data):
    """
    将帖子和评论的最新时间戳保存到'kuan.json'。

    参数：
        data (dict): 以帖子ID为键，最新时间戳为值的字典。
    """
    with open('kuan.json', 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def main():
    # 从'kuan.json'加载现有数据
    kuan_data = load_kuan_data()
    # 获取最新帖子
    posts = get_recent_posts()

    for post in posts:
        if 'id' in post:
            post_id = post['id']
            post_dateline = int(post['dateline'])
            post_username = post.get('username', '未知用户')
            post_message = post.get('message', '')
            formatted_post_time = format_timestamp(post_dateline)

            # 从kuan.json中获取此帖子的最新记录时间戳
            last_post_timestamp = kuan_data.get(post_id, 0)

            # 如果是新帖或者帖子有更新
            if post_dateline > last_post_timestamp:
                # 更新此帖子的最新时间戳
                kuan_data[post_id] = post_dateline

                # 输出帖子信息
                print(f"帖子发布时间：{formatted_post_time}")
                if post_message:
                    print(f'{post_username} 发表动态：{clean_html(post_message)}')

                # 初始化评论页码
                page = 1
                max_comment_timestamp = last_post_timestamp

                while True:
                    # 获取帖子的评论
                    comments = get_post_comments(post_id, page)
                    if comments:
                        for comment in comments:
                            if 'id' in comment:
                                comment_id = comment['id']
                                comment_dateline = int(comment['dateline'])
                                comment_username = comment.get('username', '未知用户')
                                comment_message = comment.get('message', '')
                                formatted_comment_time = format_timestamp(comment_dateline)

                                # 只输出比上次记录时间新的评论
                                if comment_dateline > last_post_timestamp:
                                    print(f"评论时间：{formatted_comment_time}")
                                    if comment_message:
                                        print(f'{comment_username} 回复楼主：{clean_html(comment_message)}')

                                    # 如果该评论是目前为止最新的，更新max_comment_timestamp
                                    if comment_dateline > max_comment_timestamp:
                                        max_comment_timestamp = comment_dateline

                                    # 检查此评论是否有回复（楼中楼）
                                    if comment.get('replyRowsCount', 0):
                                        reply_rows = comment.get('replyRows', [])
                                        for reply in reply_rows:
                                            reply_dateline = int(reply['dateline'])
                                            reply_username = reply.get('username', '未知用户')
                                            reply_rusername = reply.get('rusername', '未知用户')
                                            reply_message = reply.get('message', '')
                                            formatted_reply_time = format_timestamp(reply_dateline)

                                            # 只输出比上次记录时间新的回复
                                            if reply_dateline > last_post_timestamp:
                                                print(f"楼中楼回复时间：{formatted_reply_time}")
                                                print(f'{reply_username} 回复 {reply_rusername}：{clean_html(reply_message)}')

                                                # 如果该回复是目前为止最新的，更新max_comment_timestamp
                                                if reply_dateline > max_comment_timestamp:
                                                    max_comment_timestamp = reply_dateline
                        # 进入下一页评论
                        page += 1
                    else:
                        # 没有更多评论
                        break

                # 在处理完所有评论后，更新kuan_data中帖子的时间戳
                kuan_data[post_id] = max(max_comment_timestamp, kuan_data[post_id])
                print('\n')
            else:
                # 该帖子没有新的更新
                continue

    # 将更新后的kuan_data保存回'kuan.json'
    save_kuan_data(kuan_data)


if __name__ == "__main__":
    main()

import requests
from bs4 import BeautifulSoup  # 需要安装该库
import json
import time
import os


def report_feed(feed_id, account_cookies, feed_type):
    session = requests.Session()

    # GET请求的URL（用于获取requestHash和举报页面）
    get_url = f'https://m.coolapk.com/mp/do?c=feed&m=report&type={feed_type}&id={feed_id}'

    # GET请求的Headers
    get_headers = {
        'Host': 'm.coolapk.com',
        'Connection': 'keep-alive',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Android WebView";v="133", "Chromium";v="133"',
        'sec-ch-ua-mobile': '?1',
        'sec-ch-ua-platform': '"Android"',
        'Upgrade-Insecure-Requests': '1',
        'User-Agent': 'Mozilla/5.0 (Linux; Android 11; Build/RKQ1.201217.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36 +CoolMarket/11.2',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-User': '?1',
        'Sec-Fetch-Dest': 'document',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
    }

    # 更新会话的Cookies，只使用uid、username、token
    session.cookies.update(account_cookies)

    # 发送GET请求，获取requestHash和举报页面内容
    response = session.get(get_url, headers=get_headers)
    # 检查请求是否成功
    if response.status_code == 200:
        html_content = response.text

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 提取requestHash
        request_hash_input = soup.find('input', {'name': 'requestHash'})
        if request_hash_input:
            requestHash = request_hash_input.get('value')
            print(f'获得requestHash: {requestHash}，动态ID: {feed_id}')
        else:
            print(f'未找到requestHash，动态ID: {feed_id}')
            requestHash = None

        # 提取被举报人用户名
        user_info_div = soup.find('div', class_='weui-cell',
                                  onclick=lambda x: x and x.startswith("window.location.href='/u/"))
        if user_info_div:
            user_name_p = user_info_div.find('p')
            if user_name_p:
                reported_user = user_name_p.get_text(strip=True)
            else:
                reported_user = '未知用户'
        else:
            reported_user = '未知用户'

        # 提取被举报内容
        feed_content_div = soup.find('div', class_='weui-cell__bd', style="padding-left: 30px")
        if feed_content_div:
            reported_content = feed_content_div.get_text(strip=True)
        else:
            reported_content = '无法获取内容'

        print(f'被举报人：{reported_user}')
        print(f'被举报内容：{reported_content}')
    else:
        # 如果状态码不是200，可能是已经举报过了，或者其他错误
        html_content = response.text  # 获取返回的HTML内容

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 尝试获取错误信息
        error_title = soup.find('h2', class_='weui-msg__title')
        error_desc = soup.find('p', class_='weui-msg__desc')
        if error_title and error_desc:
            error_title_text = error_title.get_text(strip=True)
            error_desc_text = error_desc.get_text(strip=True)
            print(f'{error_title_text}：{error_desc_text}，动态ID: {feed_id}')
        else:
            print(f'GET请求失败，状态码: {response.status_code}，动态ID: {feed_id}')
        requestHash = None
        reported_user = '未知用户'
        reported_content = '无法获取内容'

    # 如果成功获取到requestHash，则继续进行POST请求
    if requestHash:
        # POST请求的URL
        post_url = 'https://m.coolapk.com/mp/do?c=feed&m=report'

        # POST请求的Headers
        post_headers = {
            'Host': 'm.coolapk.com',
            'Connection': 'keep-alive',
            'sec-ch-ua': '"Chromium";v="128", "Not;A=Brand";v="24", "Android WebView";v="128"',
            'Accept': '*/*',
            'sec-ch-ua-platform': '"Android"',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua-mobile': '?1',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 11; Build/RKQ1.201217.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/89.0.4389.116 Mobile Safari/537.36 +CoolMarket/11.2',
            'Origin': 'https://m.coolapk.com',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': get_url,
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        }

        # POST请求的表单数据
        files = {
            'requestHash': (None, requestHash),
            'submit': (None, '1'),
            'report_reason': (None, '流量卡推广'),
            'custom_report_reason': (None, ''),
            'id': (None, str(feed_id)),  # 使用传入的feed_id
            'type': (None, feed_type),  # 使用与GET请求相同的类型
            'pic': (None, ''),
        }

        # 发送POST请求
        post_response = session.post(post_url, headers=post_headers, files=files)

        # 检查POST请求结果
        if post_response.status_code == 200:
            try:
                response_json = post_response.json()
                status = int(response_json.get('status'))
                message = response_json.get('message')
                username_response = response_json.get('SESSION', {}).get('username', '未知用户')
                if status == 200:
                    print(f"账号 {username_response} 举报成功！")
                    return True
                else:
                    print(f"账号 {username_response} 举报失败！动态ID: {feed_id}")
                    print(f"错误信息：{message}")
                    return False
            except json.JSONDecodeError:
                print(f"账号 {account_cookies.get('username', '未知用户')} 举报失败，无法解析响应内容。动态ID: {feed_id}")
                print('响应内容：', post_response.text)
                return False
        else:
            print(f"账号 {account_cookies.get('username', '未知用户')} 举报失败，状态码：{post_response.status_code}，动态ID: {feed_id}")
            # 尝试解析错误信息
            try:
                response_json = post_response.json()
                message = response_json.get('message', '未知错误')
                print(f"错误信息：{message}")
            except json.JSONDecodeError:
                # 如果不是JSON格式，可能是已经举报过，或者其他HTML错误页面
                html_content = post_response.text
                soup = BeautifulSoup(html_content, 'html.parser')
                error_title = soup.find('h2', class_='weui-msg__title')
                error_desc = soup.find('p', class_='weui-msg__desc')
                if error_title and error_desc:
                    error_title_text = error_title.get_text(strip=True)
                    error_desc_text = error_desc.get_text(strip=True)
                    print(f'{error_title_text}：{error_desc_text}')
                else:
                    print('未能解析错误信息。')
            return False

    print('----------------------')  # 分隔符，方便查看
    return False  # 如果未获取到requestHash，则返回False


def main(feed_type, num_accounts_per_feed, reset_counts=False, max_reports_per_account=10):
    # 读取report_feeds.txt文件中的ID列表，每行一个ID
    try:
        with open('report_feeds.txt', 'r', encoding='utf-8') as f:
            feed_ids = f.readlines()
    except FileNotFoundError:
        print('错误：未找到report_feeds.txt文件。请确保该文件存在于脚本所在的目录。')
        exit()

    # 去除每行末尾的换行符，并过滤空行
    feed_ids = [line.strip() for line in feed_ids if line.strip()]

    # 读取cookies.json文件，包含多个账号的Cookies
    try:
        with open('cookies.json', 'r', encoding='utf-8') as f:
            accounts = json.load(f)
    except FileNotFoundError:
        print('错误：未找到cookies.json文件。请确保该文件存在于脚本所在的目录。')
        exit()
    except json.JSONDecodeError:
        print('错误：cookies.json文件格式不正确，请确保是有效的JSON格式。')
        exit()

    if not accounts:
        print('错误：cookies.json中没有找到任何账号信息。')
        exit()

    # 处理举报次数记录
    if reset_counts or not os.path.exists('report_count.json'):
        # 初始化举报次数
        report_counts = {account['username']: 0 for account in accounts}
    else:
        # 读取已有的举报次数
        with open('report_count.json', 'r', encoding='utf-8') as f:
            report_counts = json.load(f)
        # 确保所有账户都有对应的举报次数记录
        for account in accounts:
            username = account['username']
            if username not in report_counts:
                report_counts[username] = 0

    # 处理每个feed_id
    remaining_feed_ids = []
    for feed_id in feed_ids:
        if feed_id.isdigit():
            # 过滤掉已经达到举报次数上限的账号
            available_accounts = [acc for acc in accounts if report_counts.get(acc['username'], 0) < max_reports_per_account]

            if not available_accounts:
                print(f"所有账号都已达到举报次数上限，无法继续举报动态ID {feed_id}。")
                remaining_feed_ids.append(feed_id)
                continue

            # 按举报次数对可用账户排序，次数少的优先使用
            accounts_sorted = sorted(available_accounts, key=lambda acc: report_counts.get(acc['username'], 0))
            accounts_to_use = accounts_sorted[:num_accounts_per_feed]

            # 检查是否有足够的账号
            if len(accounts_to_use) < num_accounts_per_feed:
                print(f'可用账号数量不足以举报动态ID {feed_id}，需要 {num_accounts_per_feed} 个账号。')
                remaining_feed_ids.append(feed_id)
                continue

            success_count = 0
            for account_cookies in accounts_to_use:
                username = account_cookies['username']  # 用于打印的用户名

                # 创建一个浅拷贝，避免修改原始对象
                account_cookies_encoded = account_cookies.copy()
                # 对username进行URL编码（仅在cookies中，不影响report_counts的键）
                account_cookies_encoded['username'] = requests.utils.quote(account_cookies_encoded['username'])

                success = report_feed(feed_id, account_cookies_encoded, feed_type)

                if success:
                    report_counts[username] += 1
                    success_count += 1
                else:
                    print(f"账号 {username} 对动态ID {feed_id} 的举报未成功。")
                # 可根据需要添加延时，避免请求过于频繁
                time.sleep(1)

            if success_count == num_accounts_per_feed:
                print(f"动态ID {feed_id} 已成功被 {num_accounts_per_feed} 个账号举报。")
                print('----------------------')
            else:
                print(f"动态ID {feed_id} 未能被所有账号成功举报，将在下次继续。")
                print('----------------------')
                remaining_feed_ids.append(feed_id)
        else:
            print(f'无效的ID：{feed_id}，跳过该条目。')
            print('----------------------')
            # 仍然将无效的ID写回文件，以便检查
            remaining_feed_ids.append(feed_id)

    # 更新report_count.json文件
    with open('report_count.json', 'w', encoding='utf-8') as f:
        json.dump(report_counts, f, ensure_ascii=False, indent=4)

    # 将剩余的feed_id写回report_feeds.txt文件
    with open('report_feeds.txt', 'w', encoding='utf-8') as f:
        for feed_id in remaining_feed_ids:
            f.write(feed_id + '\n')


# 举报回复是'feed_reply'，举报动态是'feed'

if __name__ == '__main__':
    # 在此处指定每个feed_id需要多少个账号进行举报，示例中设置为1
    num_accounts_per_feed = 1
    # 设置是否重置举报次数计数
    reset_counts = False  # True表示重置，False表示不重置
    # 每个账号的最大举报次数
    max_reports_per_account = 10
    main('feed_reply', num_accounts_per_feed, reset_counts, max_reports_per_account)
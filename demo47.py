import requests
import sys

url = "https://hdapi.37.com/"

params = {
    "c": "api",
    "a": "status",
    "alias_info": "yxmyswxqd20250214",
    "f": "d202502/yxmyswxqd20250214",
    "game_id": "1051"
}

headers = {
    "pragma": "no-cache",
    "cache-control": "no-cache",
    "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Android WebView";v="128"',
    "sec-ch-ua-mobile": "?1",
    "user-agent": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/128.0.6613.40 Mobile Safari/537.36",
    "sec-ch-ua-platform": '"Android"',
    "accept": "*/*",
    "x-requested-with": "com.sqw.yxmys.yxmys_tap1",
    "sec-fetch-site": "same-site",
    "sec-fetch-mode": "no-cors",
    "sec-fetch-dest": "script",
    "referer": "https://activity.37.com/",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
    "cookie": (
        "ispass_37wan_com=e690c006%7C1%7C7b014cf425a52d530bbff9b1dbe6d6d3%7C1%7C2; "
        "passport_37wan_com=3562275630%7C13927009231%7C1743328108000%7Cdc7a9b93ef65097fa40036d5249f4ba0%7C2; "
    ),
}

try:
    response = requests.get(url, headers=headers, params=params, timeout=10)
    response.raise_for_status()  # 检查HTTP请求是否成功
except requests.exceptions.RequestException as e:
    print(f"请求出现错误：{e}")
    sys.exit(1)

try:
    data = response.json()
except ValueError:
    print("响应不是有效的JSON格式")
    sys.exit(1)

# 检查响应中的关键字段
if not data.get('result') == 1:
    print(f"接口调用失败，返回信息：{data.get('msg', '未知错误')}")
    sys.exit(1)

if 'list' not in data:
    print("响应中缺少'list'字段")
    sys.exit(1)

list_data = data['list']

required_keys = ['gift_status', 'rule_arr', 'sign_total', 'gift_need_days', 'check_sign']
for key in required_keys:
    if key not in list_data:
        print(f"响应中缺少'{key}'字段")
        sys.exit(1)

# 提取数据
gift_status = list_data['gift_status']
rule_arr = list_data['rule_arr']
sign_total = list_data['sign_total']
gift_need_days = list_data['gift_need_days']
check_sign = list_data['check_sign']

# 打印签到天数
print(f"当前签到天数：{sign_total}")

# 根据gift_status和gift_need_days的值来判断签到和礼包领取情况
if check_sign:
    print("今日已签到")
    if gift_status[0] == 0:
        if gift_need_days[0] == 1:
            print("今天已经签到并领取了每日礼包")
        elif gift_need_days[0] == 0:
            print("今天已经签到，但还未领取每日礼包")
            # 在此处可以添加领取每日礼包的代码
    else:
        print("今日签到，但每日礼包尚未领取")
        # 在此处可以添加领取每日礼包的代码
else:
    print("今日尚未签到")
    # 在此处可以添加签到的代码

# 检查5天和30天的礼包领取情况
for i in range(1, len(gift_status)):
    required_days = rule_arr[i]
    need_days = gift_need_days[i]
    status = gift_status[i]
    if status == 0:
        print(f"已领取{required_days}天礼包")
    else:
        if need_days == 0:
            print(f"已满足{required_days}天礼包领取条件，但尚未领取")
            # 在此处可以添加领取对应礼包的代码
        else:
            print(f"距离领取{required_days}天礼包还需签到{need_days}天")

# 如果有其他逻辑需要处理，可以在此处继续添加
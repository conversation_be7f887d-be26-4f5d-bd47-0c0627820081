#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件智能合并工具
功能：将new_kuan.json合并到kuan.json中，重复键时保留新文件的值
作者：Claude 4.0 sonnet
适配：Windows 系统使用 py 命令，Linux/Mac 使用 python 命令
"""

import json
import os
from pathlib import Path


def read_json_file(file_path):
    """
    读取JSON文件并返回数据
    
    Args:
        file_path (str): JSON文件路径
        
    Returns:
        dict: JSON数据，如果文件不存在则返回空字典
    """
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"✅ 读取 {os.path.basename(file_path)}: {len(data)} 条数据")
                return data
        else:
            print(f"⚠️  文件不存在: {file_path}")
            return {}
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误 {file_path}: {e}")
        return {}
    except Exception as e:
        print(f"❌ 读取文件失败 {file_path}: {e}")
        return {}


def save_json_file(data, file_path):
    """
    保存数据到JSON文件
    
    Args:
        data (dict): 要保存的数据
        file_path (str): 保存路径
        
    Returns:
        bool: 保存是否成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        abs_path = os.path.abspath(file_path)
        print(f"✅ 文件已保存到: {abs_path}")
        return True
    except PermissionError:
        print(f"❌ 权限不足，无法写入文件: {file_path}")
        return False
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False


def merge_json_data(old_data, new_data):
    """
    合并两个JSON数据字典
    
    Args:
        old_data (dict): 旧数据
        new_data (dict): 新数据
        
    Returns:
        tuple: (合并后的数据, 统计信息字典)
    """
    # 计算重复键
    old_keys = set(old_data.keys())
    new_keys = set(new_data.keys())
    duplicate_keys = old_keys & new_keys
    
    # 执行合并（new_data会覆盖old_data中的重复键）
    merged_data = old_data.copy()
    merged_data.update(new_data)
    
    # 计算统计信息
    stats = {
        'old_count': len(old_data),
        'new_count': len(new_data),
        'duplicate_count': len(duplicate_keys),
        'merged_count': len(merged_data),
        'added_count': len(new_keys - old_keys),
        'updated_count': len(duplicate_keys)
    }
    
    return merged_data, stats


def print_merge_statistics(stats):
    """
    打印合并统计信息
    
    Args:
        stats (dict): 统计信息字典
    """
    print("\n" + "="*50)
    print("📊 合并统计报告")
    print("="*50)
    print(f"📁 原kuan.json数据条数: {stats['old_count']:,}")
    print(f"📁 new_kuan.json数据条数: {stats['new_count']:,}")
    print(f"🔄 发现重复键(帖子ID): {stats['duplicate_count']:,} 个")
    print(f"➕ 新增数据条数: {stats['added_count']:,}")
    print(f"🔄 更新数据条数: {stats['updated_count']:,}")
    print(f"📊 合并后总计: {stats['merged_count']:,} 条")
    print("="*50)


def main():
    """主函数"""
    print("🐾 JSON文件智能合并工具启动中...")
    print("作者：Claude 4.0 sonnet\n")
    
    # 获取当前脚本所在目录
    script_dir = Path(__file__).parent
    
    # 定义文件路径
    old_file = script_dir / "kuan.json"
    new_file = script_dir / "new_kuan.json"
    output_file = script_dir / "kuan.json"  # 保存回原文件
    
    print(f"📂 工作目录: {script_dir.absolute()}")
    print(f"📄 旧文件: {old_file.name}")
    print(f"📄 新文件: {new_file.name}")
    print(f"💾 输出文件: {output_file.name}\n")
    
    # 读取文件
    print("📖 开始读取文件...")
    old_data = read_json_file(str(old_file))
    new_data = read_json_file(str(new_file))
    
    if not new_data:
        print("⚠️  new_kuan.json为空或不存在，无需合并")
        return
    
    # 执行合并
    print("\n🔄 开始合并数据...")
    merged_data, stats = merge_json_data(old_data, new_data)
    
    # 保存结果
    print("\n💾 保存合并结果...")
    if save_json_file(merged_data, str(output_file)):
        print_merge_statistics(stats)
        print("\n🎉 合并完成！数据已成功保存")
    else:
        print("\n❌ 合并失败，请检查文件权限")


if __name__ == "__main__":
    main()

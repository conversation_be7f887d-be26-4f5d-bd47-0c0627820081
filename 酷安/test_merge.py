import json
import os

# 简单测试版本
def main():
    print("开始合并JSON文件...")
    
    # 读取旧文件
    try:
        with open('kuan.json', 'r', encoding='utf-8') as f:
            old_data = json.load(f)
        print(f"读取 kuan.json: {len(old_data)} 条数据")
    except:
        old_data = {}
        print("kuan.json 读取失败或不存在")
    
    # 读取新文件
    try:
        with open('new_kuan.json', 'r', encoding='utf-8') as f:
            new_data = json.load(f)
        print(f"读取 new_kuan.json: {len(new_data)} 条数据")
    except:
        new_data = {}
        print("new_kuan.json 读取失败或不存在")
        return
    
    # 计算统计
    old_count = len(old_data)
    new_count = len(new_data)
    
    # 找重复键
    old_keys = set(old_data.keys())
    new_keys = set(new_data.keys())
    duplicate_keys = old_keys & new_keys
    duplicate_count = len(duplicate_keys)
    
    # 合并
    merged_data = old_data.copy()
    merged_data.update(new_data)
    
    # 统计结果
    merged_count = len(merged_data)
    added_count = len(new_keys - old_keys)
    updated_count = duplicate_count
    
    # 保存
    abs_path = os.path.abspath('kuan.json')
    with open('kuan.json', 'w', encoding='utf-8') as f:
        json.dump(merged_data, f, indent=4, ensure_ascii=False)
    
    # 输出统计
    print("\n合并统计报告:")
    print(f"原kuan.json数据条数: {old_count}")
    print(f"new_kuan.json数据条数: {new_count}")
    print(f"发现重复键: {duplicate_count} 个")
    print(f"新增数据条数: {added_count}")
    print(f"更新数据条数: {updated_count}")
    print(f"合并后总计: {merged_count} 条")
    print(f"文件已保存到: {abs_path}")

if __name__ == "__main__":
    main()

from flask import Flask, render_template, request, redirect, url_for
import os
import json
from datetime import datetime
import requests
from urllib.parse import urlencode, quote_plus
import re
import pytz
import base64
import hashlib
import bcrypt
from time import sleep

app = Flask(__name__)

headers = {}
sleep_time = 0.5


def get_v2_token():
    device_code = "wGb15GI7MXeltWLlNXYlxWZyByT3QzRI5EI7wWZ4lGUgsTZsd2bvdEI7UGbn92bHByO3YkO0QjOEdjO1YjO1QjO4QEI7AyOgsjMhVjZmVzYiFTZ4UWN0gDO"  # 抓包headers有，固定即可
    format_base64 = re.compile('\\r\\n|\\r|\\n|=')
    token_part1 = "token://com.coolapk.market/dcf01e569c1e3db93a3d0fcf191a622c?"
    device_code_md5 = hashlib.md5(device_code.encode('utf-8')).hexdigest()
    timestamp = int(datetime.now().timestamp())
    timestamp_md5 = hashlib.md5(str(timestamp).encode('utf-8')).hexdigest()
    timestamp_base64 = re.sub(format_base64, '', base64.b64encode(str(timestamp).encode('utf-8')).decode())
    token = f'{token_part1}{timestamp_md5}${device_code_md5}&com.coolapk.market'
    token_base64 = re.sub(format_base64, '', base64.b64encode(token.encode('utf-8')).decode())
    token_base64_md5 = hashlib.md5(token_base64.encode('utf-8')).hexdigest()
    token_md5 = hashlib.md5(token.encode('utf-8')).hexdigest()
    arg = f'$2y$10${timestamp_base64}/{token_md5}'
    salt = (arg[:28] + 'u').encode('utf-8')
    crypt = bcrypt.hashpw(token_base64_md5.encode('utf-8'), salt)
    crypt_base64 = base64.b64encode(crypt).decode()
    return f'v2{crypt_base64}'


# 认证所需的Cookies（如果需要，请替换为您自己的）
cookies = {
    'uid': '38008208',
    'username': '%E7%88%B1%E5%90%83%E7%93%9C11',  # 爱吃瓜11
    'token': '91575e12nCk7UmRdYnWkLJN7QWged0lAm_mPU5PzckuHWILumGbdA6onW7gBVAPo1CNIjrYH_uti4xCclarVsOL_dsu_hT1yynMz0LVNV03HEkVoJ90x14cayN6JFyROVTYEQ4aBhEok20ZZ8X36grTEakKmlxuWEc8ICOJlX8M2dpSV23SLdylkazKrY32D75ofj3NL3KqLPi-22zjJwOktRylSaA',
}


def get_recent_posts(tags, page):
    """
    获取多个标签下的最新帖子。

    参数：
        tags (list): 要抓取的标签列表。

    返回值：
        list: 帖子数据列表，其中每个帖子都包含一个 'tag' 键，表示其所属的标签。
    """
    # 获取帖子列表的基础URL
    base_url = "https://api.coolapk.com/v6/page/dataList"

    all_posts = []
    processed_post_ids = set()  # 用于记录已处理的帖子ID，防止重复

    for tag in tags:
        # 内部URL和参数
        inner_url = "#/feed/multiTagFeedList"
        inner_params = {
            "listType": "lastupdate_desc",
            "isIncludeTop": "1",
            "hiddenTagRelation": "1",
            "cacheExpires": "60",
            "ignoreEntityById": "1",
            "tag": tag
        }

        # 对内部参数进行编码
        encoded_inner_params = urlencode(inner_params, quote_via=quote_plus)

        # 构建完整的内部URL
        full_inner_url = f"{inner_url}?{encoded_inner_params}"

        # 请求的外部参数
        params = {
            "url": full_inner_url,
            "title": "最近回复",
            "subTitle": "",
            "page": f"{page}"
        }

        # 发送GET请求到API
        response = requests.get(base_url, params=params, headers=headers, cookies=cookies)

        # 检查响应是否成功
        if response.status_code == 200:
            # 返回JSON响应中的data部分
            data = response.json().get('data', [])
            for post in data:
                post_id = post.get('id')
                if post_id:
                    # 为防止重复，如果一个帖子已经处理过，就不再添加
                    if post_id not in processed_post_ids:
                        # 在帖子数据中添加 'tag' 键，记录该帖子所属的标签
                        post['tag'] = tag
                        all_posts.append(post)
                        processed_post_ids.add(post_id)
                    else:
                        # 如果帖子已经存在于 processed_post_ids，说明重复，跳过
                        continue
        else:
            print(f"获取标签 '{tag}' 下的帖子时发生错误，状态码：{response.status_code}")

    return all_posts


def get_post_comments(post_id, page):
    """
    获取特定帖子的评论。

    参数：
        post_id (str): 帖子ID。
        page (int): 要获取的评论页码。

    返回值：
        list: 评论数据列表。
    """
    params = {
        'id': post_id,
        'listType': 'lastupdate_desc',
        'page': page,
        'discussMode': '1',
        'feedType': 'feed',
        'blockStatus': '0',
        'fromFeedAuthor': '0',
    }

    response = requests.get('https://api2.coolapk.com/v6/feed/replyList', params=params, cookies=cookies,
                            headers=headers)
    sleep(sleep_time)
    # print(post_id)
    # 返回JSON响应中的data部分
    return response.json().get('data', [])


def get_comment_comments(post_id, page):
    """
    获取特定评论的评论。

    参数：
        post_id (str): 帖子回复ID。
        page (int): 要获取的评论页码。

    返回值：
        list: 评论数据列表。
    """
    params = {
        'id': post_id,
        'listType': '',
        'page': page,
        'discussMode': '0',
        'feedType': 'feed_reply',
        'blockStatus': '0',
        'fromFeedAuthor': '0',
    }

    response = requests.get('https://api2.coolapk.com/v6/feed/replyList', params=params, cookies=cookies,
                            headers=headers)
    sleep(sleep_time)
    # print(post_id)
    # 返回JSON响应中的data部分
    return response.json().get('data', [])


def format_timestamp(timestamp, timezone_str="Asia/Shanghai"):
    """
    将UNIX时间戳格式化为可读的字符串。

    参数：
        timestamp (int): UNIX时间戳。
        timezone_str (str): 时区字符串。

    返回值：
        str: 格式化的日期时间字符串。
    """
    timezone = pytz.timezone(timezone_str)
    dt_object = datetime.fromtimestamp(timestamp, tz=timezone)
    return dt_object.strftime("%Y-%m-%d %H:%M:%S")


def clean_html(raw_html):
    """
    从字符串中移除HTML标签和不必要的空白。

    参数：
        raw_html (str): 原始HTML字符串。

    返回值：
        str: 清理后的文本。
    """
    # 移除所有<a>标签及其内容
    clean_text = re.sub(r'<a[^>]*>.*?</a>', '', raw_html)

    # 移除其他HTML标签
    clean_text = re.sub(r'<[^>]+>', '', clean_text)

    # 移除换行符
    clean_text = clean_text.replace('\n', '')

    # 移除多余的空白
    clean_text = re.sub(r'\s+', ' ', clean_text).strip()

    return clean_text


def load_kuan_data():
    """
    加载包含帖子和评论最新时间戳的'kuan.json'文件。

    返回值：
        dict: 以帖子ID为键，最新时间戳为值的字典。
    """
    if os.path.exists('kuan.json'):
        with open('kuan.json', 'r', encoding='utf-8') as file:
            return json.load(file)
    else:
        return {}


def save_kuan_data(data):
    """
    将帖子和评论的最新时间戳保存到'kuan.json'。

    参数：
        data (dict): 以帖子ID为键，最新时间戳为值的字典。
    """
    with open('kuan.json', 'w', encoding='utf-8') as file:
        json.dump(data, file, ensure_ascii=False, indent=4)


def wxpusher(title: str, content: str, content_type: int) -> None:
    """
    发送消息到微信推送服务。

    参数:
        title (str): 消息标题。
        content (str): 消息内容。
        content_type (int): 内容类型。
            1 - 文字
            2 - HTML（仅发送<body>标签内部的数据，不包括<body>标签，推荐使用）
            3 - Markdown
    """
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": content,
        "summary": title,
        "contentType": content_type,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"]
    }
    headers = {
        "Content-Type": "application/json"
    }

    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()  # 检查请求是否成功
        result = response.json()
        print(result)
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP错误发生: {http_err}")
    except Exception as err:
        print(f"其他错误发生: {err}")


# 这里插入你提供的所有处理函数（get_v2_token, get_recent_posts等）
# 保持原有处理代码不变，只需要在main函数前添加@app.route相关逻辑

# 修改输出文件路径为Linux兼容路径
# 统一使用脚本所在目录


OUTPUT_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "kuan_data.txt")


def read_result_file():
    """读取处理结果文件内容"""
    try:
        if not os.path.exists(OUTPUT_FILE):
            return "暂无数据", "未更新"
        with open(OUTPUT_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
        # 获取文件的最后修改时间
        last_modified_timestamp = os.path.getmtime(OUTPUT_FILE)
        last_modified_time = datetime.fromtimestamp(last_modified_timestamp).strftime("%Y-%m-%d %H:%M:%S")
        return content if content else "暂无数据", last_modified_time
    except Exception as e:
        return f"读取数据失败：{str(e)}", "未知时间"


@app.route('/get_kuan', methods=['GET', 'POST'])
def handle_kuan():
    data_not_updated = False  # 添加一个标志来指示数据是否未更新
    # **获取当天的日期，格式为 YYYY-MM-DD**
    today_date = datetime.now().strftime("%Y-%m-%d")
    highlight_text = today_date  # 默认高亮内容为当天日期
    page = 1  # 默认页码

    if request.method == 'POST':
        action = request.form.get('action')
        page = request.form.get('page', 1)
        # **获取用户提交的高亮内容，如果没有，则使用当天日期**
        highlight_text = request.form.get('highlight_text', today_date).strip()
        try:
            page = int(page)
            if page < 1:
                page = 1
        except ValueError:
            page = 1  # 如果转换失败，默认设置为 1

        if action == 'refresh':
            # **在刷新之前读取旧内容**
            old_content, _ = read_result_file()
            # 执行数据处理
            try:
                main(page)  # 调用 main 函数，传入 page 参数
                content, last_refresh = read_result_file()
            except Exception as e:
                return f"处理数据时发生错误：{str(e)}"
            # **比较新旧内容**
            if content.strip() == old_content.strip():
                data_not_updated = True
            # **在处理完 POST 请求后，重定向到 GET 请求**
            # 将必要的参数传递给 GET 请求
            return redirect(
                url_for('handle_kuan', page=page, highlight_text=highlight_text, data_not_updated=data_not_updated))
        elif action == 'highlight':
            # **高亮操作也重定向到 GET 请求**
            return redirect(url_for('handle_kuan', page=page, highlight_text=highlight_text))
        else:
            # 未知操作，重定向到首页
            return redirect(url_for('handle_kuan', page=page))

    elif request.method == 'GET':
        # 从查询参数中获取 'page' 和 'highlight_text'
        page = request.args.get('page', 1)
        highlight_text = request.args.get('highlight_text', today_date).strip()
        data_not_updated = request.args.get('data_not_updated', 'False') == 'True'
        try:
            page = int(page)
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        # 读取数据文件
        content, last_refresh = read_result_file()

        # **在渲染之前处理高亮**
        if highlight_text:
            content = highlight_content(content, highlight_text)

        return render_template('kuan.html',
                               content=content,
                               last_refresh=last_refresh,
                               page=page,
                               highlight_text=highlight_text,
                               data_not_updated=data_not_updated)


def highlight_content(content, highlight_text):
    """在内容中高亮匹配的文本"""
    # 转义特殊字符，防止正则表达式错误
    escaped_text = re.escape(highlight_text)
    # 使用正则替换，加上 <span> 标签
    pattern = re.compile(escaped_text, re.IGNORECASE)
    highlighted_content = pattern.sub(r'<span class="highlight">\g<0></span>', content)
    return highlighted_content


if __name__ == '__main__':
    # 确保输出文件存在
    if not os.path.exists(OUTPUT_FILE):
        open(OUTPUT_FILE, 'w').close()


    # 修改 main 函数中的文件路径和参数获取
    def main(page):
        # ... 您原有的处理代码 ...
        # 确保使用统一的输出文件路径
        global headers
        # 请求头部信息
        headers = {
            "User-Agent": (
                "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) "
                "(#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) "
                "+CoolMarket/13.3.6-2310232-universal"
            ),  # 用户代理，包含设备和应用信息
            "X-Requested-With": "XMLHttpRequest",  # 指定请求方式为XHR
            "X-Sdk-Int": "33",  # SDK版本号
            "X-Sdk-Locale": "zh-CN",  # 语言区域
            "X-App-Id": "com.coolapk.market",  # 应用ID
            "X-App-Token": get_v2_token(),
            # 应用Token（敏感信息，应动态获取）
            "X-App-Version": "13.3.6",  # 应用版本号
            "X-App-Code": "2310232",  # 应用代码版本
            "X-Api-Version": "13",  # API版本
            "X-App-Device": "wGb15GI7MXeltWLlNXYlxWZyByT3QzRI5EI7wWZ4lGUgsTZsd2bvdEI7UGbn92bHByO3YkO0QjOEdjO1YjO1QjO4QEI7AyOgsjMhVjZmVzYiFTZ4UWN0gDO",
            # 设备信息（可能是加密后的设备标识）
            "X-Dark-Mode": "0",  # 是否开启暗黑模式（0：否，1：是）
            "X-App-Channel": "coolapk",  # 应用渠道
            "X-App-Mode": "universal",  # 应用模式
            "X-App-Supported": "2310232",  # 支持的应用版本
            "Host": "api.coolapk.com",  # 主机名
            "Connection": "Keep-Alive",  # 连接方式
            "Accept-Encoding": "gzip",  # 接受的编码方式
        }
        # 从'kuan.json'加载现有数据
        kuan_data = load_kuan_data()

        # 定义要抓取的标签列表
        tags = [
            "我的流量套餐",
            "流量卡",
            "中国联通",
            "中国移动",
            "中国广电",
            "电信流量卡",
            "广东联通",
        ]  # 请替换为您想要抓取的标签

        # 获取最新帖子
        posts = get_recent_posts(tags, page)

        # 初始化内容列表，用于收集要推送的内容
        content_list = []

        for post in posts:
            if 'id' in post:
                post_id = str(post['id'])
                post_dateline = int(post['dateline'])
                post_lastupdate = int(post['lastupdate'])
                post_username = post.get('username', '未知用户')
                post_message = post.get('message', '')
                formatted_post_time = format_timestamp(post_dateline)
                post_tag = post.get('tag', '未知标签')

                # 从kuan.json中获取此帖子的最新记录时间戳
                last_post_timestamp = kuan_data.get(post_id, 0)

                # 如果是新帖或者帖子有更新
                if post_lastupdate > last_post_timestamp:

                    # 收集帖子信息
                    post_content = f"帖子发布时间：{formatted_post_time}\n"
                    post_content += f"所属标签：{post_tag}\n"
                    if post_message:
                        post_content += f'{post_id}  {post_username} 发表动态：{clean_html(post_message)}\n'
                    content_list.append('*********************************************************\n')
                    content_list.append('*********************************************************\n')
                    content_list.append(post_content)

                    # 初始化评论页码
                    post_comments_page = 1

                    while True:
                        # 获取帖子的评论
                        comments = get_post_comments(post_id, post_comments_page)
                        if comments:
                            for comment in comments:
                                if 'id' in comment:
                                    comment_id = str(comment['id'])
                                    comment_dateline = int(comment['dateline'])
                                    comment_lastupdate = int(comment['lastupdate'])
                                    comment_username = comment.get('username', '未知用户')
                                    comment_message = comment.get('message', '')
                                    formatted_comment_time = format_timestamp(comment_dateline)

                                    # 只输出比上次记录时间新的评论
                                    if comment_lastupdate > last_post_timestamp:
                                        comment_content = f"评论时间：{formatted_comment_time}\n"
                                        if comment_message:
                                            comment_content += f'{comment_id}  {comment_username} 回复楼主：{clean_html(comment_message)}\n'
                                        content_list.append('————————————————————————————————\n')
                                        content_list.append(comment_content)

                                        # 检查此评论是否有回复（楼中楼）
                                        if comment.get('replynum', 0):
                                            comment_comments_page = 1
                                            while True:
                                                reply_rows = get_comment_comments(comment_id, comment_comments_page)
                                                if reply_rows:
                                                    for reply in reply_rows:
                                                        if 'id' in reply:
                                                            reply_id = str(reply['id'])
                                                            reply_dateline = int(reply['dateline'])
                                                            reply_username = reply.get('username', '未知用户')
                                                            reply_rusername = reply.get('rusername', '未知用户')
                                                            reply_message = reply.get('message', '')
                                                            formatted_reply_time = format_timestamp(reply_dateline)

                                                        # 只输出比上次记录时间新的回复
                                                        if reply_dateline > last_post_timestamp:
                                                            reply_content = f"楼中楼回复时间：{formatted_reply_time}\n"
                                                            reply_content += f'{reply_id}  {reply_username} 回复 {reply_rusername}：{clean_html(reply_message)}\n'
                                                            content_list.append(reply_content)
                                                    comment_comments_page += 1
                                                else:
                                                    # 没有更多回复
                                                    break
                                                    # 进入下一页楼中楼

                            # 进入下一页评论
                            post_comments_page += 1
                        else:
                            # 没有更多评论
                            break

                    # 在处理完所有评论后，更新kuan_data中帖子的时间戳
                    kuan_data[post_id] = post_lastupdate
                else:
                    # 该帖子没有新的更新
                    continue

        # 如果有要推送的内容，写入指定的文件
        if content_list:
            # 将内容列表合并成一个字符串
            content = '\n'.join(content_list)
            # 指定输出文件的路径
            output_file = OUTPUT_FILE

            # 将内容写入文件，处理编码为UTF-8
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(content)

            print(f"已将内容写入 '{output_file}' 文件。")
            # 将更新后的kuan_data保存回'kuan.json'
            save_kuan_data(kuan_data)
        else:
            print("没有新的内容需要推送。")
        # 示例：使用 page 参数来获取数据
        # api_url = f"https://api.example.com/data?page={page}"
        # response = requests.get(api_url)
        # 处理响应并写入文件
        # with open(output_file, 'w', encoding='utf-8') as f:
        #     f.write(response.text)


    # 启动 Flask 应用
    app.run(host='0.0.0.0', port=5689, debug=False)

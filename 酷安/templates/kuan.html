<!DOCTYPE html>
<html>
<head>
    <title>酷安数据监控</title>
    <style>
        /* 您已有的样式 */
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .content-box {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            white-space: pre-wrap;
            background-color: #fffde7;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .input-field {
            margin-right: 10px;
        }
        /* 链接样式 */
        .content-box a {
            word-break: break-all;
        }
        /* 高亮样式 */
        .highlight {
            background-color: yellow;
            font-weight: bold;
        }
        /* 确保内容框可以正确显示长文本 */
        .content-box {
            max-width: 100%;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            word-break: break-all;
        }
        /* 提示信息样式 */
        .alert {
            color: red;
            font-weight: bold;
            margin-left: 15px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>酷安数据监控</h1>
    <form method="POST">
        <label for="page">页码：</label>
        <input type="number" id="page" name="page" value="{{ page }}" min="1" class="input-field" />

        <label for="highlight_text">高亮内容：</label>
        <input type="text" id="highlight_text" name="highlight_text" value="{{ highlight_text }}" class="input-field" />

        <button type="submit" name="action" value="refresh" class="button">立即刷新数据</button>
        <button type="submit" name="action" value="highlight" class="button">高亮</button>
    </form>
    <div class="timestamp">
        数据最后更新时间：{{ last_refresh }}
        {% if data_not_updated %}
        <span class="alert">数据未更新</span>
        {% endif %}
    </div>
    <div class="content-box">{{ content|safe }}</div>
</div>
</body>
</html>

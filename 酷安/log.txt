nohup: 忽略输入
 * Serving Flask app 'app' (lazy loading)
 * Environment: production
   WARNING: This is a development server. Do not use it in a production deployment.
   Use a production WSGI server instead.
 * Debug mode: off
 * Running on all addresses.
   WARNING: This is a development server. Do not use it in a production deployment.
 * Running on http://**************:5689/ (Press CTRL+C to quit)
************** - - [11/Mar/2025 01:58:41] "GET /get_kuan?page=1&highlight_text=2025-03-10&data_not_updated=False HTTP/1.1" 200 -
************** - - [11/Mar/2025 01:58:46] "GET /get_kuan HTTP/1.1" 200 -
************** - - [11/Mar/2025 02:00:03] "[32mPOST /get_kuan HTTP/1.1[0m" 302 -
************** - - [11/Mar/2025 02:00:03] "GET /get_kuan?page=1&highlight_text=2025-03-11&data_not_updated=False HTTP/1.1" 200 -
************** - - [13/Mar/2025 05:09:30] "[33mGET / HTTP/1.0[0m" 404 -
************** - - [13/Mar/2025 05:15:41] "[33mGET / HTTP/1.0[0m" 404 -
************** - - [13/Mar/2025 05:15:42] "[33mPOST /sdk HTTP/1.1[0m" 404 -
************** - - [13/Mar/2025 05:15:50] "[33mGET /nmaplowercheck1741814140 HTTP/1.1[0m" 404 -
************** - - [13/Mar/2025 05:15:50] "[33mGET /HNAP1 HTTP/1.1[0m" 404 -
************** - - [13/Mar/2025 05:15:51] "[33mGET /evox/about HTTP/1.1[0m" 404 -
************** - - [13/Mar/2025 05:15:56] code 400, message Bad request version ("::o\x11/Tfx\x84²]²ÐÃÑä®î-³S½!\\A?â\x1bR´Ï\x9b\x00\x9c\x13\x02\x13\x03\x13\x01\x003\x009\x005\x00/À,À0\x00£\x00\x9fÌ©Ì¨ÌªÀ¯À\xadÀ£À\x9fÀ]ÀaÀWÀSÀ+À/\x00¢\x00\x9eÀ®À¬À¢À\x9eÀ\\À`ÀVÀRÀ$À(\x00k\x00jÀsÀw\x00Ä\x00ÃÀ#À'\x00g\x00@ÀrÀv\x00¾\x00½À")
************** - - [13/Mar/2025 05:15:56] "[35m[1m
import requests
from datetime import datetime, timezone, timedelta


def convert_timestamp_to_china_time(timestamp):
    """
    将 Unix 时间戳转换为中国时间（北京时间，UTC+8）

    :param timestamp: Unix 时间戳
    :return: 转换后的中国时间，格式为 'YYYY-MM-DD HH:MM:SS'
    """
    # 将 Unix 时间戳转换为 UTC 时间
    utc_time = datetime.utcfromtimestamp(timestamp)

    # 设置中国时间 (UTC + 8小时)
    china_timezone = timezone(timedelta(hours=8))

    # 将 UTC 时间转换为中国时间
    china_time = utc_time.replace(tzinfo=timezone.utc).astimezone(china_timezone)

    # 返回格式化后的时间字符串
    return china_time.strftime("%Y-%m-%d %H:%M:%S")




headers = {
    'xy-direction': '87',
    'x-mini-mua': 'eyJhIjoiRUNGQUFGMDEiLCJjIjoyLCJrIjoiMDcyNTE1YTRlODA5ZmEzOWIwYzQwNGM0ZjI5MDNlN2Q5NGZkNjhkYzA4MmUxODYwODdhN2QwYzMwMzg2YTU3OSIsInAiOiJhIiwicyI6ImFmNTVmMWMzNGVjNTAxZDJkMWNkOGQwNDI3M2FlMjZmIiwidSI6IjAwMDAwMDAwNTAyZTVjNTQ4Y2U1MzI4ZDg3ZWMwMzExYjQ5ZGY4OGQiLCJ2IjoiMi42LjQifQ.9Z3AE1O2FkC0cWjXmAW67CSoU4t2FTis0iXL-1-vI4Oijpl5Spo1CzQPAOLLbv98XrKCXPiDDAu3hDk5If-9Q7Yp5bOg7iFcbgu4gSU9U6vdEMjZ7bDypVxqo2jQubkfzKNeS51YmJxtXNyADAL5A3wNzIdBtYKMu-g13Tt8pomTQbxt_e9aik1P6lI3_mi7kp3rdstdueVa7Jb7RnrOy8VdKLjtbGz3lEcKVlegKBVgaWEZ2dmZoge0MfBrH5OWJyf_DhuSL54RV1wpjakkt9IlXy48akzZ1yxU3bh5FtuAiimSyJA-9VfBQl5_uqoXa2oIWmsWpc9t71yTUdEfTYBeX6kCUvk9aLBfXvUy4k2tBwKZNeBfjZjjWmBV1DhhsUbkzrxtFR000i_r0CH9ZS-NzGx6rJTlpq6XPvTuQUUdtKgica0RL9gMUjZgu0G3NIO6mu_raLVi5AmpMd0v1Ax6blQ9tCWwgDMZ9H3B-GgQpLk2OnSeVE6lqGjOXGwtjw3-gusMwuNu4fkgVVns-U1T8jA6Oz4OB43eV62WR7BGmhpK5LEn3_ZLZ43uzgvrDP2NZ24mn4hzuhX-8etqJ4E2QKVIQbZ4BIrN8I7RjXEEnK7WjSAh4047T7iXAOb-uO628L0iV868rrkKDZVZUEPzBfnazFaZFXBycECzYt3gW_cuKvvrardeOO4avVnoMWeKQA001H79OG_NQIYQuTAUdq4LpTZU7fXujvYUC0pLtRFrjcVozTgJWD1QtFsjD1BJbVozcRsmzf8lsA0R8aNwhJDSMV9ks4-lkhQnjKC_4MZReRhU1P8KPDIWnF2yv0F8hrTAuOnYeSwHY6BnLz0kfCWEyNtlKrv5Goj3a7q8JXnuLsoZqbWi6u3gQl2IpkR3rJvZRrcsw92IvQn2qdvevTXZ70q580pe33ut42Qkl0LY_UmnzMYtdQWLQkZU_i76Tut4qFehVBRBlMwiOw93iX_1F4LSf_ElM7BugeOGnPDdzkcZ6we8GeNNv31BMpdQDa4nZtTOjGD8i25Csb3bZy-_USeSBgQUl3JFKUYEPG2IcIzwoI4TePMkygbrzdQMq6zv0SxEeoyO_aUZVxBhoUOi4-bjIFGmPhOf9Ct02qLWlOo-awKB4FIihsyd.',
    'xy-common-params': 'fid=172550414510a687e65a98f43963f184dbb9901b406b&device_fingerprint=20240904174945f66597c73b8f647e73e2a796a82f6d110114cd550e0f9402&device_fingerprint1=20240904174945f66597c73b8f647e73e2a796a82f6d110114cd550e0f9402&cpu_name=qcom&gid=7c5ff4f4817655d1f60505abe9249bf691bc507947359a097782418d&device_model=phone&launch_id=1730913446&tz=Asia%2FShanghai&channel=XiaomiPreload2022&versionName=8.57.0&overseas_channel=0&deviceId=ba27ddbe-74cd-3f9e-9944-4d4e58daa285&platform=android&sid=session.1725504165660788634157&identifier_flag=4&t=1730913447&project_id=ECFAAF&build=8570985&x_trace_page_current=app_loading_page&lang=zh-Hans&app_id=ECFAAF01&uis=light&teenager=0',
    'user-agent': 'Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) Resolution/1080*2400 Version/8.57.0 Build/8570985 Device/(Xiaomi;22127RK46C) discover/8.57.0 NetType/CellNetwork',
    'referer': 'https://app.xhs.cn/',
    'shield': 'XYAAAAAQAAAAEAAABTAAAAUzUWEe0xG1IbD9/c+qCLOlKGmTtFa+lG434Le+FfTKgQlIa3yuI1GZ3/+7MLz8N81Zh+2Ks2FAxKRWHdYbOgjXpijuGymO2Dv3zArgANRzIxEV+I',
    'xy-platform-info': 'platform=android&build=8570985&deviceId=ba27ddbe-74cd-3f9e-9944-4d4e58daa285',
    # 'accept-encoding': 'gzip',
}

params = {
    'oid': 'homefeed_recommend',
    'cursor_score': '',
    'geo': 'eyJsYXRpdHVkZSI6MjMuMDI4MjQ2LCJsb25naXR1ZGUiOjExMi4xODgzNzd9\n',
    'trace_id': 'aed5934c-e5fd-3df3-9929-603ba425d86b',
    'note_index': '0',
    'refresh_type': '2',
    'client_volume': '0.00',
    'known_signal': '{"hp_con":0,"hp_type":0,"m_active":0,"device_level":8,"device_model":"22127RK46C","nqe_level":6}',
    'unread_begin_note_id': '672ac8570000000019019cb2',
    'unread_end_note_id': '',
    'unread_note_count': '3',
    'preview_ad': '',
    'preview_type': '',
    'loaded_ad': '',
    'home_ads_id': '',
    'user_action': '0',
    'personalization': '1',
    'is_break_down': '0',
    'orientation': '',
    'launch_scenario': '1',
    'last_card_position': '0',
    'last_live_position': '0',
    'last_live_id': '',
    'enable_live_shooting': 'false',
    'enable_location_permission': 'true',
}

response = requests.get('https://edith.xiaohongshu.com/api/sns/v6/homefeed', params=params, headers=headers)
data = response.json()
for i in data['data']:
    if i.get('type') == 'normal':
        if i.get('recommend').get('category_name') == '科技数码':
            print(i.get('id'))         # 文章id
            print(convert_timestamp_to_china_time(i.get('timestamp')))   # 文章时间戳
            print(i.get('model_type'))   # 帖子类型
            print(i.get('recommend').get('category_name'))     # 帖子分类
            print(i.get('display_title'))   # 帖子标题
            print(i.get('desc'))   # 帖子内容
            print(i.get('recommend').get('predict_click_ratio'))   # 影响度

            print()

"""
小米运动步数自动更新脚本
功能：自动为指定账号更新随机步数到小米运动平台
作者：[作者名]
创建时间：[创建时间]
"""

import requests  # 用于发送HTTP请求
import random    # 用于生成随机步数
import time      # 用于延时等待
from message_push import wxpusher  # 用于消息推送通知

# 定义账号和密码对
# 格式：(账号, 密码) - 可以是手机号或邮箱
accounts = [
    ("***********", "1234qwer"),        # 手机号账号
    ("<EMAIL>", "aG!7tL[3aB") # 邮箱账号
]

# 定义API的基础URL
# 这是第三方小米运动API接口地址
base_url = "https://steps.api.030101.xyz/api"

# 定义步数范围
# 设置合理的步数范围，避免数值过于异常
min_steps = 66666  # 最小步数
max_steps = 88888  # 最大步数


def send_steps(account, password, steps, max_retries=10):
    """
    向小米运动API发送步数更新请求

    参数:
        account (str): 用户账号（手机号或邮箱）
        password (str): 用户密码
        steps (int): 要更新的步数
        max_retries (int): 最大重试次数，默认10次

    功能:
        1. 构造请求参数并发送GET请求
        2. 检查响应状态和内容
        3. 失败时自动重试，最多重试max_retries次
        4. 最终失败时通过wxpusher发送通知
    """
    # 构造API请求参数
    params = {
        "account": account,   # 用户账号
        "password": password, # 用户密码
        "steps": steps        # 步数
    }

    retries = 0  # 重试计数器

    # 重试循环：最多重试max_retries次
    while retries < max_retries:
        try:
            # 发送GET请求到小米运动API（添加超时设置：连接超时10秒，读取超时30秒）
            response = requests.get(base_url, params=params, timeout=(10, 30))

            # 检查HTTP响应状态码
            if response.status_code == 200:
                # 解析JSON响应数据
                response_data = response.json()

                # 检查API返回的业务状态
                if response_data.get("status") == "success":
                    # 成功：打印成功信息并退出函数
                    print(f"✅ 账号: {account} | 步数: {steps} | 响应: {response.json()}")
                    return  # 成功后直接返回，不再重试
                else:
                    # API返回失败消息
                    print(f"❌ 账号: {account} | 步数: {steps} | 响应失败: {response.json()}")
            else:
                # HTTP请求失败（非200状态码）
                print(f"❌ 账号: {account} | 步数: {steps} | 请求失败，状态码: {response.status_code}")

        except requests.exceptions.Timeout as e:
            # 捕获超时异常
            print(f"⏰ 账号: {account} | 步数: {steps} | 请求超时: {e}")
        except requests.exceptions.RequestException as e:
            # 捕获其他网络请求异常（如网络连接问题等）
            print(f"❌ 账号: {account} | 步数: {steps} | 请求异常: {e}")

        # 增加重试计数
        retries += 1
        print(f"🔄 账号: {account} | 步数: {steps} | 重试第 {retries} 次...")
        time.sleep(10)  # 等待10秒后重试，避免频繁请求

    # 所有重试都失败后的处理
    failure_message = f"账号: {account} | 步数: {steps} | 最终失败，已重试 {max_retries} 次。"
    print(f"💥 {failure_message}")

    # 通过wxpusher发送失败通知
    # 参数：标题、内容、消息类型（1表示文本消息）
    wxpusher("步数更新失败", failure_message, 1)


def main():
    """
    主函数：遍历所有账号并为每个账号更新随机步数

    功能流程:
        1. 遍历accounts列表中的所有账号
        2. 为每个账号生成随机步数
        3. 调用send_steps函数发送更新请求
    """
    print("🚀 开始执行小米运动步数更新任务...")
    print(f"📊 步数范围: {min_steps} - {max_steps}")
    print(f"👥 账号数量: {len(accounts)}")
    print("-" * 50)

    # 遍历所有配置的账号
    for index, (account, password) in enumerate(accounts, 1):
        print(f"\n🔄 处理第 {index}/{len(accounts)} 个账号: {account}")

        # 在指定范围内生成随机步数
        steps = random.randint(min_steps, max_steps)
        print(f"🎲 生成随机步数: {steps}")

        # 调用发送函数更新步数
        send_steps(account, password, steps)

        # 如果不是最后一个账号，添加间隔时间避免频繁请求
        if index < len(accounts):
            print("⏳ 等待5秒后处理下一个账号...")
            time.sleep(5)

    print("\n" + "=" * 50)
    print("✅ 所有账号处理完成！")


# 程序入口点
# 当脚本被直接运行时（而不是被导入时）执行main函数
if __name__ == "__main__":
    main()

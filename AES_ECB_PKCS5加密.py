"""加密
算法类型：AES/ECB/PKCS5Padding

Key类型：javax.crypto.spec.SecretKeySpec

加密秘钥（文本）：2%!9vn8(&MK*49)_
加密秘钥（Base64）：MiUhOXZuOCgmTUsqNDkpXw==
加密秘钥（Hex）：********************************

加密Iv（文本）：null
加密Iv（Base64）：bnVsbA==
加密Iv（Hex）：6e756c6c
"""
import base64
from Cryptodome.Cipher import AES

# 定义填充函数，使被加密数据的字节码长度是16的整数倍
def pad(text):
    count = len(text.encode('utf-8'))
    add = 16 - (count % 16)
    entext = text + (chr(add) * add)
    return entext

# 定义加密函数
def encrypt(encrData):
    # 将base64编码的密钥转换为字节码
    key = base64.b64decode("MiUhOXZuOCgmTUsqNDkpXw==")
    # 创建AES对象，使用ECB模式和SecretKeySpec类型的密钥
    aes = AES.new(key, AES.MODE_ECB)
    # 填充普通文本
    res = pad(encrData)
    # 加密并转换为base64编码
    msg = base64.b64encode(aes.encrypt(res.encode("utf-8")))
    return msg

# 测试加密函数
encrData = '{"member_id":"13640532","name":"CumulativeSignIn"}'
print(encrypt(encrData).decode("utf-8"))
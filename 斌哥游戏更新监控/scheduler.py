#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时监控调度器
支持定时执行游戏监控任务
"""

import schedule
import time
import json
import os
from datetime import datetime
from game_monitor import GameMonitor

class MonitorScheduler:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
        self.monitor = GameMonitor()
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return {
                "monitor_settings": {
                    "check_interval_minutes": 30,
                    "max_retries": 3
                }
            }
    
    def run_scheduled_check(self):
        """执行定时检查"""
        print(f"\n{'='*60}")
        print(f"定时检查开始 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        max_retries = self.config.get("monitor_settings", {}).get("max_retries", 3)
        
        for attempt in range(max_retries):
            try:
                has_changes = self.monitor.run_monitor()
                
                if has_changes:
                    print("🔔 发现游戏更新！")
                    self.send_notification()
                else:
                    print("✅ 无更新")
                
                break  # 成功执行，退出重试循环
                
            except Exception as e:
                print(f"❌ 第 {attempt + 1} 次尝试失败: {e}")
                if attempt < max_retries - 1:
                    print(f"⏳ 等待 30 秒后重试...")
                    time.sleep(30)
                else:
                    print("❌ 所有重试都失败了")
    
    def send_notification(self):
        """发送通知（可扩展）"""
        if not self.config.get("monitor_settings", {}).get("enable_notifications", False):
            return
            
        # 这里可以添加各种通知方式
        # 例如：邮件、微信、钉钉、Telegram等
        print("📧 通知功能待实现...")
    
    def start_scheduler(self):
        """启动定时调度器"""
        interval = self.config.get("monitor_settings", {}).get("check_interval_minutes", 30)
        
        print(f"🚀 启动游戏监控调度器")
        print(f"⏰ 检查间隔: {interval} 分钟")
        print(f"📁 数据目录: 当前目录")
        print(f"{'='*60}")
        
        # 立即执行一次
        print("🔍 执行初始检查...")
        self.run_scheduled_check()
        
        # 设置定时任务
        schedule.every(interval).minutes.do(self.run_scheduled_check)
        
        print(f"\n⏰ 定时任务已设置，每 {interval} 分钟检查一次")
        print("按 Ctrl+C 停止监控")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次是否有待执行的任务
                
        except KeyboardInterrupt:
            print("\n\n👋 监控已停止")

def main():
    """主函数"""
    scheduler = MonitorScheduler()
    scheduler.start_scheduler()

if __name__ == "__main__":
    main()

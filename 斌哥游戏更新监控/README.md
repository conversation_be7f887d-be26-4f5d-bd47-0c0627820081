# 斌哥游戏平台更新监控

这是一个用于监控斌哥游戏平台新游戏发布和历史游戏更新的Python脚本。

## 功能特性

### 🎮 智能游戏监控
- **新游戏检测**: 监控ID递增的新发布游戏
- **历史更新检测**: 监控ID乱序出现的历史游戏重新发布
- **批量发布检测**: 识别平台一次性上架多个游戏的情况
- **广告过滤**: 自动过滤游戏广告和外部推广链接
- **下载链接提取**: 自动提取新游戏的网盘下载链接
- **详细变化分析**: 提供游戏名称、特性、评论数、浏览量等详细信息

### 📊 智能监控逻辑

#### 🧠 智能游戏识别算法
系统使用先进的算法来区分真正的新游戏和重新上架的历史游戏：

1. **真正的新游戏** ✨
   - 条件：`游戏ID > 历史最大ID` **且** `从未在历史中出现过`
   - 例如：历史最大ID是883，检测到ID 885且从未见过 → 真正的新游戏

2. **重新上架的历史游戏** 🔄
   - 条件：`游戏ID在历史记录中存在` 或 `ID > 历史最大ID但曾经见过`
   - 例如：检测到ID 640，虽然 > 当前最大ID，但历史上见过 → 历史游戏重新上架

3. **批量发布检测** 📦
   - 一次检查发现多个真正的新游戏时会特别标注
   - 区分批量新游戏发布和批量历史游戏重新上架

#### 🎯 解决的问题
- **避免误判**：防止将重新上架的历史游戏误认为新游戏
- **精确识别**：即使历史游戏的ID比当前最大ID大，也能正确识别
- **完整记录**：维护所有见过的游戏ID的完整历史记录

### 🔧 技术特性
- 自动重试机制
- 详细日志记录
- 历史数据持久化
- 可配置监控间隔
- 支持定时调度
- 智能广告过滤
- 完整的HTTP头模拟
- 多网盘下载链接提取
- 自动密码识别

## 文件结构

```
斌哥游戏更新监控/
├── game_monitor.py           # 🔍 核心监控脚本（集成下载提取功能）
├── scheduler.py              # ⏰ 定时调度器
├── download_monitor.py       # 📊 下载监控器
├── test_monitor.py           # 🧪 基础测试脚本
├── test_download_extraction.py  # 🧪 下载提取测试
├── test_integrated_monitor.py   # 🧪 集成功能测试
├── config.json              # ⚙️ 配置文件
├── requirements.txt         # 📦 依赖包
├── README.md               # 📖 详细说明
├── run_*.bat               # 🏃 各种运行器
├── response.html           # 📄 游戏列表响应
├── 领主.html               # 📄 游戏详情响应
├── game_history.json       # 💾 游戏历史数据（自动生成）
├── game_downloads.json     # 💾 下载链接数据（自动生成）
└── monitor_log.txt         # 📝 监控日志（自动生成）
```

## 安装和使用

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行方式

#### Windows用户（推荐）
- **测试功能**: 双击 `run_test.bat`
- **单次检查**: 双击 `run_monitor.bat`
- **定时监控**: 双击 `run_scheduler.bat`
- **下载链接监控**: 双击 `run_download_monitor.bat`
- **测试下载提取**: 双击 `run_download_test.bat`
- **测试重新上架提取**: 双击 `run_reupload_test.bat`
- **测试集成功能**: 双击 `run_integrated_test.bat`

#### 命令行方式
```bash
# 测试功能
python test_monitor.py

# 单次检查
python game_monitor.py

# 定时监控
python scheduler.py

# 下载链接监控
python download_monitor.py

# 测试下载提取
python test_download_extraction.py

# 测试集成功能
python test_integrated_monitor.py
```

## 配置说明

编辑 `config.json` 文件来自定义监控设置：

```json
{
  "monitor_settings": {
    "check_interval_minutes": 30,  // 检查间隔（分钟）
    "max_retries": 3,              // 最大重试次数
    "timeout_seconds": 30,         // 请求超时时间
    "enable_notifications": true   // 是否启用通知
  }
}
```

## 监控报告示例

```
==================================================
斌哥游戏平台监控报告
检查时间: 2024-01-15T10:30:00
==================================================

🎮 新游戏发布 (2个):
⚠️  检测到批量发布!

  📱 快来当领主 (ID: 885)
     特性: 无敌
     评论: 20 | 浏览: 393
     链接: https://bg.denwq.cn/appshop/info/id/885.html

  📱 太空异形生存者 (ID: 884)
     特性: 秒杀
     评论: 105 | 浏览: 6204
     链接: https://bg.denwq.cn/appshop/info/id/884.html

🔄 历史游戏更新 (1个):
  📱 生存传奇 (ID: 636)
     特性: 无敌 秒杀
     评论: 262 | 浏览: 22578
     链接: https://bg.denwq.cn/appshop/info/id/636.html

📊 统计信息:
  总游戏数: 724
  最大ID: 885 (上次: 883)
```

## 监控逻辑详解

### ID分析示例
假设历史最大ID为883，当前检测到的游戏：
- ID 885 (快来当领主) → **新游戏** ✅
- ID 884 (太空异形生存者) → **新游戏** ✅  
- ID 636 (生存传奇) → **历史更新** 🔄
- ID 883 (异境英雄) → 无变化
- ID 882 (末日冲突) → 无变化

### 批量发布检测
如果一次检查中发现多个新游戏（ID > 历史最大ID），则标记为批量发布。

### 广告过滤机制
系统会自动过滤以下类型的广告内容：
1. **游戏广告**: 包含 `<div class="ribbon">游戏广告</div>` 标签的条目
2. **外部推广**: 链接到 `youyogame.com` 或包含 `spread/plat` 的外部推广
3. **其他广告**: 不包含游戏ID的非游戏条目

过滤后的内容不会计入游戏统计，确保监控结果的准确性。

## 🔗 下载链接提取功能

### 📦 支持的网盘类型
- **百度网盘**: 自动识别并提取密码
- **奶牛网盘**: 支持直链提取
- **360网盘**: 完整链接解析
- **蓝奏云**: 高速下载链接
- **阿里云盘**: 新兴网盘支持
- **腾讯微云**: 完整功能支持
- **123网盘**: 多种格式支持

### 🎯 提取功能
1. **新游戏自动提取**: 监控到新游戏时自动提取下载链接
2. **重新上架自动提取**: 重新上架的游戏也自动提取（新安装包链接可能不同）
3. **手动提取**: 支持指定游戏ID进行提取
4. **批量提取**: 一次性提取多个游戏的下载链接
5. **密码识别**: 自动识别并提取网盘密码
6. **链接验证**: 确保提取的链接有效性

### 💡 重要说明
**为什么重新上架的游戏也要提取下载链接？**
- 🔄 重新上架通常意味着游戏有更新
- 📦 新版本的安装包下载链接会发生变化
- 🔗 旧的下载链接可能已失效
- ✨ 确保始终获取最新的有效下载链接

### 📊 下载数据管理
- **数据存储**: 所有下载链接保存在 `game_downloads.json`
- **搜索功能**: 按网盘类型搜索下载链接
- **历史记录**: 完整的提取历史和时间戳
- **数据导出**: 支持JSON格式数据导出

### 🚀 使用示例

#### 自动监控并提取下载链接
```bash
python download_monitor.py
# 选择选项 1: 运行完整监控
```

#### 提取指定游戏下载链接
```bash
python download_monitor.py
# 选择选项 2: 提取指定游戏
# 输入游戏ID: 885
```

#### 批量提取下载链接
```bash
python download_monitor.py
# 选择选项 3: 批量提取
# 输入游戏ID: 885,884,883
```

## 注意事项

1. **网络稳定性**: 确保网络连接稳定，脚本包含重试机制
2. **访问频率**: 默认30分钟检查一次，避免过于频繁的请求
3. **数据备份**: 定期备份 `game_history.json` 文件
4. **日志管理**: 定期清理 `monitor_log.txt` 文件

## 扩展功能

可以根据需要添加以下功能：
- 邮件/微信通知
- Web界面展示
- 数据库存储
- API接口
- 更多平台支持

## 故障排除

### 常见问题
1. **网络连接失败**: 检查网络连接和URL是否正确
2. **解析失败**: 网站结构可能发生变化，需要更新解析逻辑
3. **权限错误**: 确保脚本有读写当前目录的权限

### 调试模式
在脚本中添加更详细的日志输出来调试问题。

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成的游戏监控和下载链接提取功能
"""

from game_monitor import GameMonitor
import json
import os

def test_integrated_functionality():
    """测试集成功能"""
    print("🧪 测试集成的游戏监控和下载链接提取功能")
    print("=" * 70)
    
    # 创建启用下载提取的监控器
    monitor = GameMonitor(extract_downloads=True)
    
    print("✅ 成功创建集成监控器")
    print(f"  - 游戏监控: 启用")
    print(f"  - 下载提取: 启用")
    print(f"  - 数据目录: {monitor.data_dir}")
    print(f"  - 历史文件: {monitor.history_file}")
    print(f"  - 下载文件: {monitor.downloads_file}")
    
    return True

def test_download_extraction_methods():
    """测试下载提取方法"""
    print("\n🧪 测试下载提取方法")
    print("=" * 70)
    
    monitor = GameMonitor(extract_downloads=True)
    
    # 测试网盘类型识别
    test_urls = [
        ("https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2", "百度云盘", "百度网盘"),
        ("https://www.feijipan.com/s/Z00RumBg", "奶牛网盘", "奶牛网盘"),
        ("https://www.yunpan.com/surl_ypSSF3IYh4e", "360网盘", "360网盘"),
    ]
    
    print("📊 测试网盘类型识别:")
    for url, display_name, expected in test_urls:
        result = monitor.identify_pan_type(url, display_name)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {result} <- {display_name}")
    
    # 测试密码提取
    print("\n🔑 测试密码提取:")
    password_tests = [
        ("https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2", "qlv2"),
        ("https://example.com/file?password=abc123", "abc123"),
        ("https://example.com/file", None),
    ]
    
    for url, expected in password_tests:
        result = monitor.extract_password(url)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {result} <- {url}")
    
    return True

def test_local_html_extraction():
    """测试本地HTML文件提取"""
    print("\n🧪 测试本地HTML文件提取")
    print("=" * 70)
    
    html_file = "领主.html"
    if not os.path.exists(html_file):
        print(f"⚠️ 文件 {html_file} 不存在，跳过测试")
        return False
    
    monitor = GameMonitor(extract_downloads=True)
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 提取下载链接
    game_id = 885
    result = monitor.extract_download_links(html_content, game_id)
    
    print(f"📊 提取结果:")
    print(f"  游戏ID: {game_id}")
    print(f"  游戏名称: {result['game_info'].get('name', '未知')}")
    print(f"  下载链接数量: {result['total_links']}")
    
    if result['download_links']:
        print(f"\n📦 下载链接:")
        for i, link in enumerate(result['download_links'], 1):
            print(f"  {i}. {link['pan_type']}")
            print(f"     🔗 {link['url']}")
            if link.get('password'):
                print(f"     🔑 密码: {link['password']}")
    
    return True

def test_game_monitoring_with_downloads():
    """测试游戏监控与下载提取的集成"""
    print("\n🧪 测试游戏监控与下载提取的集成")
    print("=" * 70)
    
    # 创建启用下载提取的监控器
    monitor = GameMonitor(extract_downloads=True)
    
    # 模拟历史数据
    history = {
        "games": {
            "880": {"id": 880, "name": "吃不到我呀", "features": "免广告"}
        },
        "max_id": 880,
        "all_seen_ids": [880],
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 模拟当前游戏列表（包含新游戏和重新上架）
    current_games = [
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 23, "views": 567, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},  # 新游戏
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22593, "url": "https://bg.denwq.cn/appshop/info/id/636.html"},  # 重新上架
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3834, "url": "https://bg.denwq.cn/appshop/info/id/880.html"}  # 无变化
    ]
    
    print("📅 模拟场景:")
    print("  历史最大ID: 880")
    print("  新游戏: 885 (快来当领主)")
    print("  重新上架: 636 (生存传奇)")
    print("  无变化: 880 (吃不到我呀)")
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    print(f"\n📊 分析结果:")
    print(f"  新游戏: {len(analysis['new_games'])} 个")
    print(f"  重新上架: {len(analysis['updated_games'])} 个")
    print(f"  检测到变化: {analysis['changes_detected']}")
    
    if analysis['new_games']:
        print(f"\n🆕 新游戏:")
        for game in analysis['new_games']:
            print(f"  - {game['name']} (ID: {game['id']})")
    
    if analysis['updated_games']:
        print(f"\n🔄 重新上架:")
        for game in analysis['updated_games']:
            print(f"  - {game['name']} (ID: {game['id']})")
    
    print(f"\n💡 下载链接提取策略:")
    print(f"  ✅ 新游戏: 自动提取下载链接")
    print(f"  ✅ 重新上架: 自动提取下载链接（可能包含新安装包）")
    
    return analysis

def test_download_data_management():
    """测试下载数据管理"""
    print("\n🧪 测试下载数据管理")
    print("=" * 70)
    
    monitor = GameMonitor(extract_downloads=True)
    
    # 模拟下载数据
    sample_data = {
        'game_info': {
            'id': 885,
            'name': '快来当领主',
            'features': ['无敌']
        },
        'download_links': [
            {
                'pan_type': '百度网盘',
                'url': 'https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2',
                'display_name': '百度云盘',
                'password': 'qlv2',
                'extracted_time': '2024-01-15T10:30:00'
            }
        ],
        'total_links': 1,
        'extraction_time': '2024-01-15T10:30:00'
    }
    
    # 测试保存数据
    monitor.save_download_data(885, sample_data, "测试保存")
    
    # 检查文件是否创建
    if os.path.exists(monitor.downloads_file):
        print("✅ 下载数据文件创建成功")
        
        # 读取并验证数据
        with open(monitor.downloads_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        if '885' in saved_data:
            print("✅ 数据保存成功")
            print(f"  游戏名称: {saved_data['885']['game_info']['name']}")
            print(f"  下载链接: {saved_data['885']['total_links']} 个")
            print(f"  提取原因: {saved_data['885']['extraction_reason']}")
        else:
            print("❌ 数据保存失败")
    else:
        print("❌ 下载数据文件创建失败")
    
    return True

def main():
    """运行所有测试"""
    print("🚀 开始测试集成的游戏监控和下载链接提取功能")
    print("=" * 80)
    
    try:
        # 测试集成功能
        test_integrated_functionality()
        
        # 测试下载提取方法
        test_download_extraction_methods()
        
        # 测试本地HTML文件提取
        test_local_html_extraction()
        
        # 测试游戏监控与下载提取的集成
        analysis = test_game_monitoring_with_downloads()
        
        # 测试下载数据管理
        test_download_data_management()
        
        print("\n🎉 所有测试完成！")
        print("✅ 游戏监控和下载链接提取功能已成功集成")
        print("✅ 系统可以自动监控游戏更新并提取下载链接")
        print("✅ 支持新游戏和重新上架游戏的下载链接提取")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试广告过滤功能
使用本地的response.html文件测试解析和过滤逻辑
"""

import os
from bs4 import BeautifulSoup
import re
from game_monitor import GameMonitor

def test_local_html_parsing():
    """测试本地HTML文件解析"""
    print("🧪 测试本地HTML文件解析和广告过滤")
    print("=" * 50)
    
    # 读取本地HTML文件
    html_file = "response.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    games = []
    ads_filtered = []
    
    # 查找游戏条目
    game_divs = soup.find_all('div', class_='d-flex flex-row shadow border')
    print(f"📊 找到 {len(game_divs)} 个条目")
    
    for i, div in enumerate(game_divs):
        # 检查是否是广告项
        ribbon = div.find('div', class_='ribbon')
        if ribbon and '游戏广告' in ribbon.get_text():
            ad_name = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
            ad_name_text = ad_name.text.strip() if ad_name else "未知广告"
            ads_filtered.append({
                'type': '游戏广告',
                'name': ad_name_text,
                'position': i + 1
            })
            print(f"🚫 过滤广告项 #{i+1}: {ad_name_text}")
            continue
        
        onclick = div.get('onclick', '')
        if 'appshop/info/id/' in onclick:
            # 提取游戏ID
            id_match = re.search(r'id/(\d+)\.html', onclick)
            if id_match:
                game_id = int(id_match.group(1))
                
                # 提取游戏名称
                name_div = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                game_name = name_div.text.strip() if name_div else f"游戏_{game_id}"
                
                # 提取游戏特性
                feature_div = div.find('div', style=lambda x: x and 'color: #878787' in x)
                features = feature_div.text.strip() if feature_div else ""
                
                # 提取浏览量和评论数
                badges = div.find_all('span', class_='badge')
                comments = views = 0
                for badge in badges:
                    text = badge.text.strip()
                    if '评论' in text:
                        comments = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0
                    elif '浏览' in text:
                        views = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0
                
                games.append({
                    'id': game_id,
                    'name': game_name,
                    'features': features,
                    'comments': comments,
                    'views': views,
                    'position': i + 1
                })
                print(f"✅ 游戏 #{i+1}: {game_name} (ID: {game_id})")
                
        elif 'youyogame.com' in onclick or 'spread/plat' in onclick:
            # 其他类型的广告链接
            ad_name = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
            ad_name_text = ad_name.text.strip() if ad_name else "未知外部广告"
            ads_filtered.append({
                'type': '外部广告',
                'name': ad_name_text,
                'position': i + 1
            })
            print(f"🚫 过滤外部广告 #{i+1}: {ad_name_text}")
    
    print("\n" + "=" * 50)
    print("📊 解析结果统计:")
    print(f"  总条目数: {len(game_divs)}")
    print(f"  有效游戏: {len(games)}")
    print(f"  过滤广告: {len(ads_filtered)}")
    
    if ads_filtered:
        print("\n🚫 过滤的广告项:")
        for ad in ads_filtered:
            print(f"  - 位置 #{ad['position']}: {ad['name']} ({ad['type']})")
    
    if games:
        print("\n🎮 检测到的游戏:")
        # 按ID排序显示
        games_sorted = sorted(games, key=lambda x: x['id'], reverse=True)
        for game in games_sorted[:10]:  # 只显示前10个
            print(f"  - ID {game['id']}: {game['name']}")
            print(f"    特性: {game['features']}")
            print(f"    评论: {game['comments']} | 浏览: {game['views']}")
        
        if len(games) > 10:
            print(f"  ... 还有 {len(games) - 10} 个游戏")
        
        # 显示ID范围
        max_id = max(game['id'] for game in games)
        min_id = min(game['id'] for game in games)
        print(f"\n📈 游戏ID范围: {min_id} - {max_id}")
    
    return games, ads_filtered

def test_monitor_with_local_file():
    """测试监控器使用本地文件"""
    print("\n🧪 测试监控器解析本地文件")
    print("=" * 50)
    
    # 创建监控器实例
    monitor = GameMonitor()
    
    # 模拟从本地文件获取游戏列表
    html_file = "response.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 使用BeautifulSoup解析
    soup = BeautifulSoup(html_content, 'html.parser')
    games = []
    
    # 查找游戏条目
    game_divs = soup.find_all('div', class_='d-flex flex-row shadow border')
    
    for div in game_divs:
        # 检查是否是广告项
        ribbon = div.find('div', class_='ribbon')
        if ribbon and '游戏广告' in ribbon.get_text():
            continue
        
        onclick = div.get('onclick', '')
        if 'appshop/info/id/' in onclick:
            # 提取游戏ID
            id_match = re.search(r'id/(\d+)\.html', onclick)
            if id_match:
                game_id = int(id_match.group(1))
                
                # 提取游戏名称
                name_div = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                game_name = name_div.text.strip() if name_div else f"游戏_{game_id}"
                
                # 提取游戏特性
                feature_div = div.find('div', style=lambda x: x and 'color: #878787' in x)
                features = feature_div.text.strip() if feature_div else ""
                
                # 提取浏览量和评论数
                badges = div.find_all('span', class_='badge')
                comments = views = 0
                for badge in badges:
                    text = badge.text.strip()
                    if '评论' in text:
                        comments = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0
                    elif '浏览' in text:
                        views = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0
                
                games.append({
                    'id': game_id,
                    'name': game_name,
                    'features': features,
                    'comments': comments,
                    'views': views,
                    'url': f"https://bg.denwq.cn/appshop/info/id/{game_id}.html"
                })
        elif 'youyogame.com' in onclick or 'spread/plat' in onclick:
            # 其他类型的广告链接也跳过
            continue
    
    print(f"✅ 成功解析 {len(games)} 个有效游戏")
    
    # 创建模拟的历史数据
    history = {
        "games": {
            "880": {"id": 880, "name": "吃不到我呀", "features": "免广告"},
            "881": {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀"},
            "882": {"id": 882, "name": "末日冲突", "features": "秒杀 定怪"},
            "883": {"id": 883, "name": "异境英雄", "features": "无敌 秒杀"}
        },
        "max_id": 883,
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 分析变化
    analysis = monitor.analyze_changes(games, history)
    
    # 生成报告
    report = monitor.generate_report(analysis)
    print(report)
    
    return games, analysis

def main():
    """主函数"""
    try:
        # 测试本地HTML解析
        games, ads = test_local_html_parsing()
        
        # 测试监控器
        games2, analysis = test_monitor_with_local_file()
        
        print("\n🎉 测试完成！")
        print("广告过滤功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

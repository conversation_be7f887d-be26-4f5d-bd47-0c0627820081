<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta content="width=device-width,initial-scale=1.0,maximum-scale=1.0,user-scalable=0" name="viewport"/>
    <title>免费游戏----斌哥游戏宝盒 -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  -  - </title>
    <meta name="keywords" content="斌哥游戏宝盒">
    <meta name="description" content="斌哥游戏宝盒">
    <!--苹果相关-->
    <meta name="format-detection" content="telephone=no"/>
    <meta name="format-detection" content="email=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="apple-mobile-web-app-title" content="斌哥游戏宝盒">
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <link rel="apple-touch-icon-precomposed" sizes="57x57" href="https://bg.denwq.cn/View/xr_fresh/static/image/icon/app57.png">
    <link rel="apple-touch-icon-precomposed" sizes="114x114" href="https://bg.denwq.cn/View/xr_fresh/static/image/icon/app114.png">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="https://bg.denwq.cn/View/xr_fresh/static/image/icon/app152.png">
    <link rel="icon" sizes="114x114" href="https://bg.denwq.cn/View/xr_fresh/static/image/icon/app114.png" />

    <link rel="shortcut icon" href="https://bg.denwq.cn/View/xr_fresh/static/image/icon/favicon.ico">
    <link rel="stylesheet" href="https://bg.denwq.cn/View/xr_fresh/static/lib/ydui/style/ydui.css"/>
    <link rel="stylesheet" href="https://bg.denwq.cn/View/xr_fresh/static/wap/style/common.css?v=0.51"/>
    <link rel="stylesheet" href="/Plugin/xr_gold_top_views/tag.css"/>
    <link rel="stylesheet" data-type="fresh_style_color" href="https://bg.denwq.cn/View/xr_fresh/static/wap/style/default.css?v=0.5"/>
    <link rel="stylesheet" href="//at.alicdn.com/t/font_1473752_nzxgp0rhp4.css"/>
    <link rel="stylesheet" href="https://bg.denwq.cn/View/xr_fresh/static/lib/remixicon/remixicon.css">

    <script src="https://bg.denwq.cn/View/xr_fresh/static/lib/jquery/jquery.min.js?v=3.5.1"></script>
    <script src="//at.alicdn.com/t/font_1473752_nzxgp0rhp4.js"></script>

    <script>
        var www = "https://bg.denwq.cn/",
            WWW = "https://bg.denwq.cn/",
            exp = "/",
            STATIC = "https://bg.denwq.cn/View/xr_fresh/static/",FRESH_VERSION = '0.5';
        window.IS_LOGIN = false;
        window.hy_user = '';
        window.hy_avatar = '';
        window.hy_user_info = null;
        //2019-12-18日添加
        window.fresh_conf = {"upload":{"save":"file","host":null,"exts":{"image":"jpg,gif,png,jpeg,bmp,gif","file":"zip,rar,apk","video":"mp4,MP4","audio":"mp3"},"size":{"image":"5","file":"10","video":"30","audio":"10"}},"wallet":{"money_name":"RMB"},"verify_code":{"is_open":"0","type":"image_verify"}};

        window.onerror = function(){
            return true;
        };
    </script>
    <style>

        .vip{

            display: inline-flex;
            width: 58px;
            line-height: 13px;
            vertical-align: middle;
            position: relative;

        }
        .vip div{
            width: 52px;
            height: 15px;
            animation: gno1 1.3s ease-in infinite;
            -webkit-animation: gno1 1.3s linear infinite;
            -moz-animation: gno1 1.3s linear infinite;
            -ms-animation: gno1 1.3s linear infinite;
            -o-animation: gno1 1.3s linear infinite;
            position: absolute;
            left: 4px;
            top: 3px;    border-radius: 100px;
            display: block;
            border-bottom-left-radius: 150px;
        }

        .gno1 {
            height: 22px !important;
            width: 58px !important;
            display: inline-flex;

            vertical-align: middle;
            z-index:1;
        }

        @keyframes gno1 {
            0%{ box-shadow: 0 0 4px #fe7c23}
            50%{ box-shadow: 0 0 40px #ff900a}
            100%{ box-shadow: 0 0 4px #ffd98b}
        }
        /* 添加兼容性前缀 */
        @-webkit-keyframes gno1 {
            0%{ box-shadow: 0 0 4px #fe7c23}
            50%{ box-shadow: 0 0 40px #ff900a}
            100%{ box-shadow: 0 0 4px #ffd98b}
        }
        @-moz-keyframes gno1 {
            0%{ box-shadow: 0 0 4px #fe7c23}
            50%{ box-shadow: 0 0 40px #ff900a}
            100%{ box-shadow: 0 0 4px #ffd98b}
        }
        @-ms-keyframes gno1 {
            0%{ box-shadow: 0 0 4px #fe7c23}
            50%{ box-shadow: 0 0 40px #ff900a}
            100%{ box-shadow: 0 0 4px #ffd98b}
        }
    </style>
    <script src="https://bg.denwq.cn/View/xr_fresh/static/wap/js/app.js?v=20.50.08"></script>
</head>
<body>
<div class="fresh_loading_page" style="display: none">
    <div class="loading_box">
        <div class="loader">
            <div class="outer"></div>
            <div class="inner"></div>
        </div>
    </div>
</div>
<div class="fresh_reload_page">
    <i class="ri-refresh-line"></i>
</div>
<div class="fresh_user_chat_box">
    <!--好友列表-->
    <div class="m-actionsheet bg_f" id="fresh_chat_friend_list">
        <header class="m-navbar navbar-fixed">
            <a href="javascript:void (0);" class="navbar-item" onclick="fresh_chat.close_friend()">
                <i class="iconfont icon-icon3"></i>
            </a>
            <div class="navbar-center">
                <span class="navbar-title">消息列表</span>
            </div>
        </header>
        <div class="g-view">
            <div class="m-tab fresh____chat_friend_tab">
                <ul class="tab-nav navbar-fixed fresh_scrollTop_box">
                    <li class="tab-nav-item tab-active"><a href="javascript:;">关注列表</a></li>
                    <li class="tab-nav-item" style="display: none"><a href="javascript:;">粉丝消息</a></li>
                    <li class="tab-nav-item"><a href="javascript:;">用户消息</a></li>
                </ul>
                <div style="height: 40px"></div>
                <div class="tab-panel">
                    <div class="tab-panel-item fresh_friend_list fresh_chat____friend_list_tab_1 tab-active">
                        1
                    </div>
                    <div class="tab-panel-item fresh_friend_list fresh_chat____friend_list_tab_2" style="display: none">
                        2
                    </div>
                    <div class="tab-panel-item fresh_friend_list fresh_chat____friend_list_tab_3">
                        3
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--聊天框-->
    <div class="m-actionsheet bg_f fresh-popup-right" id="fresh_chat_mess_box">
        <header class="m-navbar navbar-fixed">
            <a href="javascript:void (0);" class="navbar-item" onclick="fresh_chat.close_chat()">
                <i class="iconfont icon-zuo"></i>
            </a>
            <div class="navbar-center">
                <span class="navbar-title fresh_chat____friend_name">name</span>
            </div>
        </header>
        <div class="g-view">
            <div class="mask" style="display: none; width: 100%; height: 100%; position: fixed; top: 0px; left: 0px; background: rgb(0, 0, 0); opacity: 0.7; z-index: 20;"></div>
            <div class="fresh_chat_box">
                <div class="speak_box fresh____speak_box" data-speak-id="">
                </div>
            </div>
            <div class="fresh_chat_emoticon bg_f"></div>
        </div>
        <footer class="m-tabbar tabbar-fixed fresh_chat_write_box">
            <div class="write_text left">
                <div class="write_box bg_f5">
                    <input type="text" class="f_c left" name="fresh_chat____content">
                </div>
            </div>
            <div class="write_bq fresh_chat____smiley" onclick="fresh_chat.smiley()">
                <i class="iconfont icon-xiaolian f_c"></i>
            </div>
            <div class="write_send right">
                <button class="right blue_shadow" onclick="fresh_chat.send_chat(this)">发送</button>
            </div>
        </footer>
    </div>
</div>

<div id="pjax">
    <!--侧边栏菜单-->
    <div class="m-actionsheet fresh-popup-left fresh_sidenv_box" id="fresh_sidenv_box">
        <div class="fresh_sidenv_top f_f">
            <div class="sidenv_exit">
                <a href="https://bg.denwq.cn/user/add.html"  data-pjax>
                    <span><i class="iconfont icon-zhuce"></i>立即注册</span>
                </a>
            </div>
            <a href="https://bg.denwq.cn/user/login.html" data-pjax class="sidenv_user">
                <em><img src="https://bg.denwq.cn/public/images/user.gif" /></em>
                <p>
                <span class="user_tit fyy">
                  <script language="javascript">
                    var myDate = new Date();
                    var i = myDate.getHours();
                    if(i < 12)
                        $(".user_tit").text("早上好!请登录");
                    else if(i >=12 && i < 14)
                        $(".user_tit").text("中午好!请登录");
                    else if(i >= 14 && i < 18)
                        $(".user_tit").text("下午好!请登录");
                    else if(i >= 18)
                        $(".user_tit").text("晚上好!请登录");
                    </script>
                </span>
                </p>
                <p class="fyy mt5">登录后更精彩...</p>
            </a>
            <div class="fresh_svg_box">
                <div class="fresh_svg_a"></div>
                <div class="fresh_svg_b"></div>
            </div>
        </div>
        <div class="fresh_sidenv_middle">
            <ul class="m-grids-3">
                <li class="grids-item">
                    <a href="https://bg.denwq.cn/fresh/sign.html" data-pjax>
                        <div class="grids-icon">
                            <i class="iconfont ri-calendar-check-line"style="color:#83d6fa"></i>
                        </div>
                        <div class="grids-txt">每日签到</div>
                    </a>
                </li>
            </ul>
        </div>
        <div class="fresh_sidenv_list">
            <div class="m-cell">
                <a href="https://bg.denwq.cn/?index.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont ri-home-smile-line"style="color:#333333"></i>
                        首页                </div>
                    <div class="cell-right cell-arrow"></div>
                </a>
                <a href="https://bg.denwq.cn/?f.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont ri-question-answer-line"style="color:#333333"></i>
                        社区                </div>
                    <div class="cell-right cell-arrow"></div>
                </a>
                <a href="https://bg.denwq.cn/?fresh/medal.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont ri-medal-line"style="color:#333333"></i>
                        荣誉勋章                </div>
                    <div class="cell-right cell-arrow"></div>
                </a>
                <a href="https://bg.denwq.cn/?fresh/ranking.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont ri-bar-chart-2-line"style="color:#333333"></i>
                        排行榜                </div>
                    <div class="cell-right cell-arrow"></div>
                </a>
            </div>
        </div>
        <div class="fresh_sidenv_bottom"></div>
    </div>



    <header class="m-navbar navbar-fixed fresh_my_navbar" style="opacity: 0;">
        <a href="javascript:history.back(-1);" data-pjax class="navbar-item">
            <i class="iconfont ri-arrow-left-s-line"></i>
        </a>
        <div class="navbar-center">
            <span class="navbar-title">免费游戏</span>
        </div>
        <div class="navbar-item">
            <a href="javascript:void (0);" data-ydui-actionsheet="{target:'#fresh_search_box',closeElement:'#cancel'}">
                <i class="iconfont ri-search-line y"></i>
            </a>
        </div>
    </header>
    <div class="g-view">
        <header class="fresh_forum_thread_top" style="background-image:url(https://bg.denwq.cn/View/xr_fresh/static/image/banner/11.png);">
            <div class="m-navbar" style="background-image: none;background-color: rgba(0,0,0,0)">
                <a href="javascript:history.back(-1);" data-pjax class="navbar-item">
                    <i class="iconfont ri-arrow-left-s-line"></i>
                </a>
                <div class="navbar-center">
                    <span class="navbar-title">免费游戏</span>
                </div>
                <div class="navbar-item">
                    <a href="javascript:void (0);" data-ydui-actionsheet="{target:'#fresh_search_box',closeElement:'#cancel'}">
                        <i class="iconfont ri-search-line y"></i>
                    </a>
                </div>
            </div>
            <div class="info">
                <img class="forun_icon z" src="https://bg.denwq.cn/upload/forum1.png?t=1749494502" alt="" onerror="this.src='https://bg.denwq.cn/upload/de.png'">
                <div class="title">免费游戏</div>
                <div class="html"></div>
                <div class="bottom">
                    <div class="m-grids m-grids-2" style="width: 100%;">
                        <a href="#" class="grids-item">
                            <div class="grids-txt num f_f">
                                <span>724</span>
                            </div>
                            <div class="grids-txt f_f">
                                <span>游戏数量</span>
                            </div>
                        </a>

                        <!--
                        <a href="#" class="grids-item">
                            <div class="grids-txt num f_f">
                                <span>500</span>
                            </div>
                            <div class="grids-txt f_f">
                                <span>关注</span>
                            </div>
                        </a>
                        -->
                    </div>
                    <!--
                    <div class="btn-block y f_f blue_shadow">+ 关注</div>
                    -->
                </div>
            </div>
            <div class="fresh_svg_box">
                <div class="fresh_svg_a"></div>
                <div class="fresh_svg_b"></div>
            </div>
        </header>
        <div style="height:35px;">
            <div class="fresh_top_sub bg_f">
                <div class="fresh_topnv">
                    <ul class="fresh_flex">
                        <li class="flex f_h">
                            <em class="bg_h"></em>                            <a href="https://bg.denwq.cn/f/1.html" data-pjax="">全部</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <script>
            $(function () {
                var $navbar = $('.fresh_my_navbar');
                $navbar.attr('style', 'opacity: 0 !important;display: none;');
                $navbar.css('display','none');

                function scroll(){
                    if ($(window).scrollTop() > 150){
                        $('.fresh_my_navbar').attr('style', 'opacity: 1 !important');
                        $('.fresh_top_sub').css({
                            position: 'fixed',
                            top: '45px',
                            left: 0,
                            right: 0,
                            'z-index': '10',
                            'border-top':'1px solid #e7e7e7'
                        });
                    }else{
                        var i = $(window).scrollTop() / 150;
                        $('.fresh_my_navbar').attr('style', 'opacity: '+i+' !important');
                        $('.fresh_top_sub').attr('style','');
                    }
                }
                $(window).scroll(scroll);
                scroll();
                new Swiper('#fresh_sub', {
                    freeMode : true,
                    slidesPerView : 'auto',
                });
            });
        </script>
        <!-- 版块置顶部分 -->
        <div class="fresh_thread_list_top bg_f b_t b_b cl">
            <div class="m-cell">
                <a href="https://bg.denwq.cn/t/55269.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont icon-gonggao fresh_badge golden_shadow"></i>
                        使用条款与免责说明                    </div>
                </a>
                <a href="https://bg.denwq.cn/t/55270.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont icon-gonggao fresh_badge golden_shadow"></i>
                        金币和积分获得攻略                    </div>
                </a>
                <a href="https://bg.denwq.cn/t/55271.html" data-pjax class="cell-item">
                    <div class="cell-left">
                        <i class="iconfont icon-gonggao fresh_badge golden_shadow"></i>
                        关于赞助成为会员说明                    </div>
                </a>
            </div>
        </div>
        <style>
            #list {
                padding-top: 5px;
            }
            .flex-column {
                -ms-flex-direction: column !important;
                flex-direction: column !important;
            }
            .d-flex {
                display: -ms-flexbox !important;
                display: flex !important;
            }
            .shadow {
                /* box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important; */
                box-shadow: 4px 5px 1px rgba(0,0,0,.15)!important;
            }
            .flex-row {
                -ms-flex-direction: row !important;
                flex-direction: row !important;
            }
            .border {
                border: 1px solid #dee2e6 !important;
            }
            #list img {
                vertical-align: middle;
                border-style: none;
            }
            .justify-content-center {
                -ms-flex-pack: center !important;
                justify-content: center !important;
            }
            .mt-1, .my-1 {
                margin-top: .25rem !important;
            }
            .mr-2, .mx-2 {
                margin-right: .5rem !important;
            }
            .badge {
                display: inline-block;
                padding: .25em .4em;
                font-size: 75%;
                font-weight: 700;
                line-height: 1;
                text-align: center;
                white-space: nowrap;
                vertical-align: baseline;
                border-radius: .25rem;
                transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out
            }

            @media (prefers-reduced-motion:reduce) {
                .badge {
                    transition: none
                }
            }

            a.badge:focus,a.badge:hover {
                text-decoration: none
            }

            .badge:empty {
                display: none
            }

            .btn .badge {
                position: relative;
                top: -1px
            }

            .badge-pill {
                padding-right: .6em;
                padding-left: .6em;
                border-radius: 10rem
            }

            .badge-primary {
                color: #fff;
                background-color: #007bff
            }

            a.badge-primary:focus,a.badge-primary:hover {
                color: #fff;
                background-color: #0062cc
            }

            a.badge-primary.focus,a.badge-primary:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(0,123,255,.5)
            }

            .badge-secondary {
                color: #fff;
                background-color: #6c757d
            }

            a.badge-secondary:focus,a.badge-secondary:hover {
                color: #fff;
                background-color: #545b62
            }

            a.badge-secondary.focus,a.badge-secondary:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(108,117,125,.5)
            }

            .badge-success {
                color: #fff;
                background-color: #28a745
            }

            a.badge-success:focus,a.badge-success:hover {
                color: #fff;
                background-color: #1e7e34
            }

            a.badge-success.focus,a.badge-success:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(40,167,69,.5)
            }

            .badge-info {
                color: #fff;
                background-color: #17a2b8
            }

            a.badge-info:focus,a.badge-info:hover {
                color: #fff;
                background-color: #117a8b
            }

            a.badge-info.focus,a.badge-info:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(23,162,184,.5)
            }

            .badge-warning {
                color: #212529;
                background-color: #ffc107
            }

            a.badge-warning:focus,a.badge-warning:hover {
                color: #212529;
                background-color: #d39e00
            }

            a.badge-warning.focus,a.badge-warning:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(255,193,7,.5)
            }

            .badge-danger {
                color: #fff;
                background-color: #dc3545
            }

            a.badge-danger:focus,a.badge-danger:hover {
                color: #fff;
                background-color: #bd2130
            }

            a.badge-danger.focus,a.badge-danger:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(220,53,69,.5)
            }

            .badge-light {
                color: #212529;
                background-color: #f8f9fa
            }

            a.badge-light:focus,a.badge-light:hover {
                color: #212529;
                background-color: #dae0e5
            }

            a.badge-light.focus,a.badge-light:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(248,249,250,.5)
            }

            .badge-dark {
                color: #fff;
                background-color: #343a40
            }

            a.badge-dark:focus,a.badge-dark:hover {
                color: #fff;
                background-color: #1d2124
            }

            a.badge-dark.focus,a.badge-dark:focus {
                outline: 0;
                box-shadow: 0 0 0 .2rem rgba(52,58,64,.5)
            }
            .align-items-center {
                -ms-flex-align: center !important;
                align-items: center !important;
            }
            .ml-auto, .mx-auto {
                margin-left: auto !important;
            }
            #aa .dropdown-toggle::after{
                display: none
            }
            .shadow {
                /* box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important; */
                box-shadow: 4px 5px 1px rgba(0,0,0,.15)!important;
            }
            .ribbon {
                position: absolute;
                right: -5px;
                top: -1px;
                z-index: 1;
                overflow: hidden;
                width: 75px;
                height: 75px;
                text-align: right;
            }

            .ribbon span {
                font-size: 10px;
                font-weight: bold;
                color: #FFF;
                text-transform: uppercase;
                text-align: center;
                line-height: 20px;
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
                width: 80px;
                display: block;
                background: #79A70A;
                background: linear-gradient(#9BC90D 0%, #79A70A 100%);
                box-shadow: 0 3px 10px -5px rgba(0, 0, 0, 0.5);
                position: absolute;
                top: 12px; right: -18px;
            }



            .ribbon span::after {
                content: "";
                position: absolute; right: 0px; top: 100%;
                z-index: -1;
                border-left: 3px solid transparent;
                border-right: 3px solid #79A70A;
                border-bottom: 3px solid transparent;
                border-top: 3px solid #79A70A;
            }



            .ribbon-info span {
                color: #FFF;
                background: #1faad4;
                background: linear-gradient(#28b9e3 0%, #1faad4 100%);
            }


            .ribbon-info span::before {

                border-left: 3px solid #1faad4;
                border-top: 3px solid #1faad4;
            }
            .ribbon-info span::after {
                border-right: 3px solid #1faad4;
                border-top: 3px solid #1faad4;
            }



            .ribbon-success span {
                color: #FFF;
                background: #2dab49;
                background: linear-gradient(#33c052 0%, #2dab49 100%);
            }


            .ribbon-success span::before {

                border-left: 3px solid #2dab49;
                border-top: 3px solid #2dab49;
            }

            .ribbon-success span::after {
                border-right: 3px solid #2dab49;
                border-top: 3px solid #2dab49;
            }


            .ribbon-danger span {
                color: #FFF;
                background: #ec383c;
                background: linear-gradient(#ee4f52 0%, #ec383c 100%);
            }


            .ribbon-danger span::before {

                border-left: 3px solid #ec383c;
                border-top: 3px solid #ec383c;
            }

            .ribbon-danger span::after {
                border-right: 3px solid #ec383c;
                border-top: 3px solid #ec383c;
            }



            .ribbon-warning span {
                color: #212529;
                background: #ffc107;
                background: linear-gradient(#ffd129 0%, #ffc107 100%);
            }


            .ribbon-warning span::before {

                border-left: 3px solid #ffc107;
                border-top: 3px solid #ffc107;
            }

            .ribbon-warning span::after {
                border-right: 3px solid #ffc107;
                border-top: 3px solid #ffc107;
            }
        </style>
        <section class="d-flex flex-column" id="list">
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/885.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20250509/15364056785">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">快来当领主</div>
                    <div style="color: #878787;font-size: 14px;">无敌</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 23</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 552</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="padding: 13px;margin: 10px;border-radius:5px;background-color: #FFF;    position: relative;" onclick="location.href='http://yyhtml.youyogame.com/spread/plat/500677/2.html'">
                <div class="ribbon ribbon-warning"><span>游戏广告</span></div>
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://yyhtml.youyogame.com/static/image/bt/btgo-logo.png?v=20200828?v=20231030">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">yoyo专游盒子</div>
                    <div style="color: #878787;font-size: 14px;">绿色专服长久耐玩（内置1万+游戏）</div>
                    <div class="d-flex flex-wrap mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow mb-2" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">绿色专服</span>
                        <span class="badge badge-pill badge-warning mr-2 shadow mb-2" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">长久耐玩</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/884.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20241008/17381177621">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">太空异形生存者</div>
                    <div style="color: #878787;font-size: 14px;">秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 105</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 6207</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/636.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20240304/14555737674">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">生存传奇</div>
                    <div style="color: #878787;font-size: 14px;">无敌 秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 262</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 22590</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/883.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20250215/13503944774">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">异境英雄</div>
                    <div style="color: #878787;font-size: 14px;">无敌 秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 42</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 5598</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/882.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx/gicon/105380/20240309-00.png">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">末日冲突</div>
                    <div style="color: #878787;font-size: 14px;">秒杀 定怪</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 44</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 9909</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/431.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20230503/10444822154">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">疾风骑士</div>
                    <div style="color: #878787;font-size: 14px;">无敌 秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 51289</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 304578</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/691.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://pic.kuaizhan.com/g3/24/c2/b2f0-cfda-48d5-8556-97db606a460d07">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">伏魔觉</div>
                    <div style="color: #878787;font-size: 14px;">无敌 秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 4642</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 67764</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/881.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20250318/21120732881">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">疯狂农场植物僵尸</div>
                    <div style="color: #878787;font-size: 14px;">无敌 秒杀</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 70</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 3348</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/880.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20240710/14055692758">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">吃不到我呀</div>
                    <div style="color: #878787;font-size: 14px;">免广告</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 36</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 3834</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
            <div class="d-flex flex-row shadow border " style="background-color: #FFF;padding: 13px;margin: 10px;border-radius:5px" onclick="location.href='https://bg.denwq.cn/appshop/info/id/879.html'">
                <div style="width: 56px;min-width: 56px;height: 56px;margin-right: 10px">
                    <img class="shadow" style="width: 100%;height: 100%;border-radius: 12px;" src="https://img.71acg.net/kbyx~sykb/20250428/16552219953">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <div style="color: #1c1c1c;font-weight: 500;">保卫向日葵</div>
                    <div style="color: #878787;font-size: 14px;">无敌</div>
                    <div class="d-flex mt-1">
                        <span class="badge badge-pill badge-primary mr-2 shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">评论 137</span>
                        <span class="badge badge-pill badge-warning shadow" style="border-radius:5px;font-size: 12px;font-weight: 400;padding: 5px;">浏览 5652</span>
                    </div>
                </div>
                <div class="ml-auto d-flex align-items-center" style="min-width: 62px;">
                    <div style="background-color: #ecfafb;color: #19bccc;display: inline-block;padding: 4px 15px;border-radius: 20px;font-size: 14px;" class="shadow">查看</div>
                </div>
            </div>
        </section>
        <script>


            $(function(){

                $(window).scroll(function () {
                    if ($(this).scrollTop() + $(this).height() + 100
                        >=  $(document).height()) {
                        loadList();
                    }
                });



            });
            window.pageid = 2;
            window.loadIng = false;
            window.noData = false;
            window.adid='120,';
            function loadList(){
                if(window.loadIng)
                    return;
                window.loadIng=true;

                $.ajax({
                    url:"https://bg.denwq.cn/f/1/new/" + (window.pageid),
                    type:'get',
                    dataType:'html',
                    success:function(data){
                        window.pageid++;

                        window.loadIng=false;


                        $("#list").append(data.match(/<section class="d-flex flex-column" id="list">([\s\S]*?)<\/section>/)[1]);



                    },
                    error:function(){}
                });


            }





        </script>


    </div>


    <!--搜索页-->
    <div class="m-actionsheet fresh-popup-right" id="fresh_search_box" style="height: 100%;">
        <header class="m-navbar navbar-fixed fresh_search_navbar" style="opacity: 0;">
            <a href="javascript:void (0);" id="cancel" data-pjax class="navbar-item">
                <i class="iconfont icon-zuo"></i>
            </a>
            <div class="navbar-center">
                <span class="navbar-title">搜索</span>
            </div>
        </header>
        <div class="g-view">
            <header class="fresh_search_top bg_h">
                <div class="m-navbar" style="background-image: none;background-color: rgba(0,0,0,0)">
                    <a href="javascript:void (0);" id="cancel" data-pjax class="navbar-item">
                        <i class="iconfont icon-zuo"></i>
                    </a>
                    <div class="navbar-center">
                        <span class="navbar-title"></span>
                    </div>
                </div>
                <div class="title f_f">
                    搜索
                </div>
                <div class="tip f_f">
                    在下面输入您的关键词回车即可搜索
                </div>
                <div class="date">
                    <div class="time f_f">x月x</div>
                    <div class="week f_c bg_f">星期x</div>
                </div>
                <form class="fresh_search_input">
                    <input type="text" value="" id="fresh_top_search_view" placeholder="搜索您感兴趣的...">
                    <button type="submit" id="fresh_top_search_button">
                        <i class="iconfont icon-sousuo1 f_c f_s3"></i>
                    </button>
                </form>
            </header>
            <div class="fresh_search_content bg_f">
                <div class="top bg_f m-grids">
                    <div class="m-grids-3">
                        <div href="#" class="grids-item">
                            <div class="grids-txt icon br"><i class="iconfont icon-guanjianci"></i></div>
                            <div class="grids-txt"><span>关键词</span></div>
                        </div>
                        <div href="#" class="grids-item">
                            <div class="grids-txt icon br"><i class="iconfont icon-shouye"></i></div>
                            <div class="grids-txt"><span>用户名</span></div>
                        </div>
                        <div href="#" class="grids-item">
                            <div class="grids-txt icon br"><i class="iconfont icon-wodebankuai"></i></div>
                            <div class="grids-txt"><span>版块</span></div>
                        </div>
                    </div>
                    <div class="fresh_box fresh_search_hot b_ok">
                        <div class="title">
                            <i class="iconfont icon-tuijian2" style="color: #ffad2f;font-size: 18px;margin-right: 2px;"></i>
                            热搜关键词
                        </div>
                        <div class="list cl">
                            <a href="https://bg.denwq.cn/search/index/key/破解.html" data-pjax class="comiis_xifont color1">
                                <span class="f_b">破解</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/游戏.html" data-pjax class="comiis_xifont">
                                <span class="f_b">游戏</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/活动.html" data-pjax class="comiis_xifont">
                                <span class="f_b">活动</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/原创.html" data-pjax class="comiis_xifont color1">
                                <span class="f_b">原创</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/动漫.html" data-pjax class="comiis_xifont color1">
                                <span class="f_b">动漫</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/源码.html" data-pjax class="comiis_xifont color4">
                                <span class="f_b">源码</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/资源分享.html" data-pjax class="comiis_xifont color1">
                                <span class="f_b">资源分享</span>
                            </a>
                            <a href="https://bg.denwq.cn/search/index/key/源码分享.html" data-pjax class="comiis_xifont color3">
                                <span class="f_b">源码分享</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script>
            $(function () {
                var date = fresh.date();
                $(".time").text(date.month+'月'+date.day);
                $(".week").text('星期'+date.cnWeek);

                $("#fresh_top_search_button").click(function () {
                    var $input = $("#fresh_top_search_view");
                    var val = $input.val();
                    var url = "https://bg.denwq.cn/search/index/key/"+val+".html";
                    if (val == '' || val == null) {
                        fresh.toast('请输入要搜索的内容！');
                        $input.focus();
                        return false;
                    }
                    YDUI.dialog.loading.open('寻找中...');
                    fresh.pjax.open(url);
                    setTimeout(function () {
                        YDUI.dialog.loading.close();
                    },1500);
                    return false;
                });
            });
        </script>
    </div>


    <footer class="m-tabbar tabbar-fixed">
        <a href="https://bg.denwq.cn/index.html" data-pjax class="tabbar-item">
                <span class="tabbar-icon">
                    <i class="iconfont ri-home-smile-line"></i>
                </span>
            <span class="tabbar-txt">首页</span>
        </a>
        <a href="https://bg.denwq.cn/page/5.html" data-pjax class="tabbar-item">
                <span class="tabbar-icon">
                    <i class="iconfont ri-question-answer-line"></i>
                </span>
            <span class="tabbar-txt">最新发布</span>
        </a>
        <a class="tabbar-item plus" href="https://bg.denwq.cn/vip">
            <div class="plus_box">

                    <span><img src="https://bg.denwq.cn/555.gif" style="
    width: 60px;
    height: 60px;
"></span>
            </div>
        </a>
        <a href="javascript:void (0);" class="tabbar-item" onclick="fresh_chat.open_friend();">
                    <span class="tabbar-icon">
                        <span class="badge badge-danger hide">
                            <em class="fresh_chat_xx_num show"></em>
                        </span>
                        <i class="iconfont ri-notification-2-line"></i>
                    </span>
            <span class="tabbar-txt">消息</span>
        </a>
        <a href="https://bg.denwq.cn/my.html" data-pjax class="tabbar-item">
                <span class="tabbar-icon">
                    <i class="iconfont ri-user-5-line"></i>
                </span>
            <span class="tabbar-txt">我</span>
        </a>
    </footer>
    <!--菜单-->
    <div class="m-actionsheet fresh-popup-fadeIn" id="fresh_fnav_menu">
        <div class="fnav_top cl">
            <div class="fresh_date_box">
                <div class="date_day f_c z">10<em>日</em></div>
                <div class="date_list z f_d">
                    <span>星期二</span>
                    <span>2025年06月</span>
                </div>
            </div>
        </div>
        <div class="fresh_box_flat">
            <div class="fresh_ad_box">
                <a href="https://shimo.im/docs/GHH3dqWWjRyHGYJc">
                    <img src="/View/xr_fresh/static/image/pic/6.gif">
                    <span>广告</span>
                </a>
            </div>
        </div>
        <div class="fresh_box_flat fnav_menu">
            <div class="m-grids-4">
                <a href="https://bg.denwq.cn/?post.html" class="grids-item" data-pjax>
                    <div class="grids-txt icon mnav f_f" style="background:#73c7ef;">
                        <i class="iconfont ri-edit-box-line"></i>
                    </div>
                    <div class="grids-txt">发布主题</div>
                </a>
                <a href="https://bg.denwq.cn/?fresh/sign.html" class="grids-item" data-pjax>
                    <div class="grids-txt icon mnav f_f" style="background:#bdf2d5;">
                        <i class="iconfont ri-calendar-check-line"></i>
                    </div>
                    <div class="grids-txt">每日签到</div>
                </a>
                <a href="https://bg.denwq.cn/?fresh/medal.html" class="grids-item" data-pjax>
                    <div class="grids-txt icon mnav f_f" style="background:#333333;">
                        <i class="iconfont ri-medal-line"></i>
                    </div>
                    <div class="grids-txt">我的勋章</div>
                </a>
                <a href="https://bg666.kuaizhan.com/98/58/p445358541a3b6b" class="grids-item" data-pjax>
                    <div class="grids-txt icon mnav f_f" style="background:#333333;">
                        <i class="iconfont #icon-xihuan"></i>
                    </div>
                    <div class="grids-txt">赞助我们</div>
                </a>
            </div>
            <div class="fresh_fnav_bottom">
                <i class="iconfont ri-close-line f_d" onclick="$('#fresh_fnav_menu').actionSheet('close')"></i>
            </div>
        </div>
    </div>

    <div class="fresh_footer_scroll" style="bottom:82px;">
        <div class="fresh_lrmenu">
            <a href="javascript:;" title="顶部" onclick="$('body,html').animate({scrollTop:0}, 800);"
               class="fresh_scrolltops f_f" style="display:none;">
                <i class="iconfont ri-upload-line"></i>
                <span><em>顶部</em></span>
            </a>
            <a href="javascript:;" title="导航" class="f_f" data-ydui-actionsheet="{target:'#fresh_sidenv_box',closeElement:'#cancel'}">
                <i class="iconfont ri-menu-2-line"></i>
                <span><em>导航</em></span>
            </a>
            <a href="javascript:;" onclick="history.go(0);" title="刷新" class="f_f">
                <i class="iconfont ri-refresh-line"></i>
                <span><em>刷新</em></span>
            </a>
        </div>
        <a href="javascript:;" title="菜单" class="fresh_lrmenukey bg_a f_f">
            <i class="iconfont ri-apps-line"></i>
            <span><em>菜单</em></span>
        </a>
        <a href="javascript:;" title="信息" onclick="fresh_chat.open_friend();" class="hide bg_a f_f" style="background-color: #00bcd4 !important">
            <i class="iconfont ri-mail-add-line"></i>
            <span><em>信息</em></span>
            <em class="badge badge-danger fresh_chat_xx_num hide">0</em>
        </a>
    </div>
</div>

<!--通用弹窗-->
<div class="m-actionsheet" id="fresh_popup_page"></div>
<!--音乐播放器-->
<div class="m-actionsheet fresh_music_player">
    <header class="m-navbar navbar-fixed">
        <a href="javascript:void (0);" class="navbar-item fresh____music_player_close">
            <i class="iconfont icon-xia"></i>
        </a>
        <div class="navbar-center"><span class="navbar-title">音乐播放器</span></div>
    </header>
    <div class="g-view">
        <div class="logo">
            <img src="https://bg.denwq.cn/View/xr_fresh/static/image/music_player_logo.jpg" class="rotate">
        </div>
        <div class="strip fresh____music_strip bg_f">
            <span class="time_load fresh____music_strip_time_load f_f z"></span>
            <span class="spot fresh____music_strip_spot"></span>
            <span class="fresh____music_strip_bg bg_h"></span>
            <span class="time fresh____music_strip_time f_f y"></span>
        </div>
        <div class="control f_f">
            <span class="next fresh____music_up_btn">
                <i class="iconfont icon-shangyishou"></i>
            </span>
            <span class="play fresh____music_play_btn">
                <i class="iconfont icon-bofang"></i>
            </span>
            <span class="next fresh____music_next_btn">
                <i class="iconfont icon-xiayishou"></i>
            </span>
        </div>
        <audio id="fresh_music_player_audio" data-music="false"></audio>
    </div>
    <div class="bg" style="background-image: url('https://bg.denwq.cn/View/xr_fresh/static/image/music_player_logo.jpg')"></div>
</div>
<script src="https://bg.denwq.cn/View/xr_fresh/static/lib/ydui/js/ydui.min.js"></script>
<script src="https://bg.denwq.cn/View/xr_fresh/static/wap/js/common.js?v=0.52"></script>





</body>
</html>


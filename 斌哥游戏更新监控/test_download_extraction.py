#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载链接提取功能
使用本地的领主.html文件测试解析逻辑
"""

import os
from bs4 import BeautifulSoup
from game_monitor import GameMonitor

def test_local_html_extraction():
    """测试本地HTML文件的下载链接提取"""
    print("🧪 测试本地HTML文件下载链接提取")
    print("=" * 60)
    
    # 读取本地HTML文件
    html_file = "领主.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # 创建监控器实例
    monitor = GameMonitor(extract_downloads=True)

    # 提取下载链接
    game_id = 885  # 快来当领主
    result = monitor.extract_download_links(html_content, game_id)
    
    print(f"📊 提取结果:")
    print(f"  游戏ID: {game_id}")
    print(f"  游戏名称: {result['game_info'].get('name', '未知')}")
    print(f"  游戏特性: {result['game_info'].get('features', [])}")
    print(f"  下载链接数量: {result['total_links']}")
    print(f"  提取时间: {result['extraction_time']}")
    
    if result['download_links']:
        print(f"\n📦 下载链接详情:")
        for i, link in enumerate(result['download_links'], 1):
            print(f"  {i}. {link['pan_type']}")
            print(f"     🔗 链接: {link['url']}")
            print(f"     📝 显示名称: {link['display_name']}")
            if link.get('password'):
                print(f"     🔑 密码: {link['password']}")
            print(f"     ⏰ 提取时间: {link['extracted_time']}")
            print()
    else:
        print("⚠️ 未找到下载链接")
    
    return True

def test_pan_type_identification():
    """测试网盘类型识别"""
    print("🧪 测试网盘类型识别")
    print("=" * 60)
    
    monitor = GameMonitor(extract_downloads=True)

    test_cases = [
        ("https://www.feijipan.com/s/Z00RumBg", "奶牛网盘", "奶牛网盘"),
        ("https://www.yunpan.com/surl_ypSSF3IYh4e", "360网盘", "360网盘"),
        ("https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2", "百度云盘", "百度网盘"),
        ("https://lanzou.com/abc123", "蓝奏云", "蓝奏云"),
        ("https://aliyundrive.com/s/abc", "阿里云盘", "阿里云盘"),
    ]

    print("测试网盘类型识别:")
    for url, display_name, expected in test_cases:
        result = monitor.identify_pan_type(url, display_name)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {url} -> {result} (期望: {expected})")

    print("\n测试密码提取:")
    password_cases = [
        ("https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2", "qlv2"),
        ("https://example.com/file?password=abc123", "abc123"),
        ("https://example.com/file", None),
    ]

    for url, expected in password_cases:
        result = monitor.extract_password(url)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {url} -> {result} (期望: {expected})")

def test_game_info_extraction():
    """测试游戏信息提取"""
    print("\n🧪 测试游戏信息提取")
    print("=" * 60)
    
    html_file = "领主.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    monitor = GameMonitor(extract_downloads=True)

    game_info = monitor.extract_game_info(soup, 885)
    
    print("📊 游戏信息提取结果:")
    for key, value in game_info.items():
        print(f"  {key}: {value}")
    
    return True

def test_download_links_structure():
    """测试下载链接结构解析"""
    print("\n🧪 测试下载链接结构解析")
    print("=" * 60)
    
    html_file = "领主.html"
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return False
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找网盘链接容器
    wangpan_div = soup.find('div', id='wangpan')
    if wangpan_div:
        print("✅ 找到网盘链接容器")
        
        # 查找所有下载链接
        link_elements = wangpan_div.find_all('a', href=True)
        print(f"📊 找到 {len(link_elements)} 个链接元素")
        
        for i, link in enumerate(link_elements, 1):
            href = link.get('href')
            text = link.get_text(strip=True)
            print(f"  {i}. 链接: {href}")
            print(f"     文本: {text}")
            print()
    else:
        print("❌ 未找到网盘链接容器")
        
        # 查找所有可能的下载链接
        all_links = soup.find_all('a', href=True)
        download_links = []
        
        for link in all_links:
            href = link.get('href')
            text = link.get_text(strip=True)
            
            if href and any(keyword in href.lower() for keyword in ['pan.', 'drive.', 'yunpan', 'feijipan']):
                download_links.append((href, text))
        
        if download_links:
            print(f"🔍 在页面中找到 {len(download_links)} 个可能的下载链接:")
            for href, text in download_links:
                print(f"  - {text}: {href}")
        else:
            print("❌ 未找到任何下载链接")

def main():
    """运行所有测试"""
    print("🚀 开始测试下载链接提取功能")
    print("=" * 80)
    
    try:
        # 测试本地HTML文件提取
        if not test_local_html_extraction():
            return
        
        # 测试网盘类型识别
        test_pan_type_identification()
        
        # 测试游戏信息提取
        test_game_info_extraction()
        
        # 测试下载链接结构
        test_download_links_structure()
        
        print("\n🎉 所有测试完成！")
        print("下载链接提取功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
斌哥游戏平台更新监控脚本
功能：
1. 监控新游戏发布（ID递增）
2. 监控历史游戏更新（ID乱序出现）
3. 监控批量游戏上架
4. 生成详细的更新报告
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import os
from datetime import datetime
from typing import Dict, List, Set, Tuple, Optional
import time
from urllib.parse import urlparse
import sys

# 添加父目录到路径以导入 message_push 模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from message_push import wxpusher
    MESSAGE_PUSH_AVAILABLE = True
except ImportError:
    MESSAGE_PUSH_AVAILABLE = False
    print("⚠️ 消息推送模块不可用，将跳过推送功能")

class GameMonitor:
    def __init__(self, data_dir=".", extract_downloads=False, enable_push=True):
        self.data_dir = data_dir
        self.history_file = os.path.join(data_dir, "game_history.json")
        self.log_file = os.path.join(data_dir, "monitor_log.txt")
        self.downloads_file = os.path.join(data_dir, "game_downloads.json")
        self.url = "https://bg.denwq.cn/f/1.html"  # 游戏列表页面
        self.extract_downloads = extract_downloads  # 是否提取下载链接
        self.enable_push = enable_push and MESSAGE_PUSH_AVAILABLE  # 是否启用消息推送

        # 确保目录存在（当前目录不需要创建）
        if data_dir != ".":
            os.makedirs(data_dir, exist_ok=True)

        # 推送功能状态提示
        if enable_push and not MESSAGE_PUSH_AVAILABLE:
            self.log("⚠️ 消息推送功能不可用，请检查 message_push 模块")
        elif self.enable_push:
            self.log("✅ 消息推送功能已启用")
        
    def load_history(self) -> Dict:
        """加载历史游戏数据"""
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.log(f"加载历史数据失败: {e}")
                return {}
        return {}
    
    def save_history(self, data: Dict):
        """保存历史游戏数据"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log(f"保存历史数据失败: {e}")
    
    def log(self, message: str):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_message + "\n")
        except Exception as e:
            print(f"写入日志失败: {e}")

    def send_push_notification(self, title: str, content: str, content_type: int = 1):
        """发送推送通知"""
        if not self.enable_push:
            return

        try:
            self.log(f"📤 发送推送通知: {title}")
            wxpusher(title, content, content_type)
            self.log("✅ 推送通知发送成功")
        except Exception as e:
            self.log(f"❌ 推送通知发送失败: {e}")
    
    def fetch_game_list(self) -> List[Dict]:
        """获取当前游戏列表"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1'
            }
            cookies = {
                'hyphp_lang': 'zh-CN',
                'xr_fullscreen_cookies': '1'
            }
            response = requests.get(self.url, headers=headers, cookies=cookies, timeout=30)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'html.parser')
            games = []

            # 查找游戏条目
            game_divs = soup.find_all('div', class_='d-flex flex-row shadow border')

            for div in game_divs:
                # 检查是否是广告项 - 广告项包含 ribbon 标签且内容为"游戏广告"
                ribbon = div.find('div', class_='ribbon')
                if ribbon and '游戏广告' in ribbon.get_text():
                    self.log(f"跳过广告项: {ribbon.get_text()}")
                    continue

                onclick = div.get('onclick', '')
                if 'appshop/info/id/' in onclick:
                    # 提取游戏ID
                    id_match = re.search(r'id/(\d+)\.html', onclick)
                    if id_match:
                        game_id = int(id_match.group(1))

                        # 提取游戏名称
                        name_div = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                        game_name = name_div.text.strip() if name_div else f"游戏_{game_id}"

                        # 提取游戏特性
                        feature_div = div.find('div', style=lambda x: x and 'color: #878787' in x)
                        features = feature_div.text.strip() if feature_div else ""

                        # 提取浏览量和评论数
                        badges = div.find_all('span', class_='badge')
                        comments = views = 0
                        for badge in badges:
                            text = badge.text.strip()
                            if '评论' in text:
                                comments = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0
                            elif '浏览' in text:
                                views = int(re.search(r'\d+', text).group()) if re.search(r'\d+', text) else 0

                        games.append({
                            'id': game_id,
                            'name': game_name,
                            'features': features,
                            'comments': comments,
                            'views': views,
                            'url': f"https://bg.denwq.cn/appshop/info/id/{game_id}.html"
                        })
                elif 'youyogame.com' in onclick or 'spread/plat' in onclick:
                    # 其他类型的广告链接也跳过
                    ad_name = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                    ad_name_text = ad_name.text.strip() if ad_name else "未知广告"
                    self.log(f"跳过外部广告: {ad_name_text}")
                    continue

            return games
            
        except Exception as e:
            self.log(f"获取游戏列表失败: {e}")
            return []
    
    def analyze_changes(self, current_games: List[Dict], history: Dict) -> Dict:
        """分析游戏变化"""
        current_time = datetime.now().isoformat()
        last_games = history.get('games', {})
        last_max_id = history.get('max_id', 0)

        # 当前游戏ID集合和最大ID
        current_ids = {game['id'] for game in current_games}
        current_max_id = max(current_ids) if current_ids else 0
        last_ids = set(map(int, last_games.keys())) if last_games else set()

        # 获取历史上见过的所有游戏ID（包括已下架的）
        all_historical_ids = history.get('all_seen_ids', set())
        if isinstance(all_historical_ids, list):
            all_historical_ids = set(all_historical_ids)

        # 分析结果
        analysis = {
            'timestamp': current_time,
            'total_games': len(current_games),
            'max_id': current_max_id,
            'last_max_id': last_max_id,
            'new_games': [],           # 真正的新发布游戏
            'updated_games': [],       # 重新上架的历史游戏
            'batch_release': False,    # 是否批量发布
            'changes_detected': False,
            'truly_new_threshold': last_max_id  # 真正新游戏的阈值
        }

        # 找出新增的游戏ID（本次检查中新出现的）
        new_ids = current_ids - last_ids

        # 智能分析：区分真正的新游戏和重新上架的历史游戏
        for game in current_games:
            if game['id'] in new_ids:
                # 判断是否为真正的新游戏
                if game['id'] > last_max_id and game['id'] not in all_historical_ids:
                    # 真正的新发布游戏：ID大于历史最大值且从未见过
                    analysis['new_games'].append(game)
                    self.log(f"检测到真正的新游戏: {game['name']} (ID: {game['id']})")
                elif game['id'] > last_max_id and game['id'] in all_historical_ids:
                    # ID虽然大于last_max_id，但历史上见过，可能是重新上架
                    analysis['updated_games'].append(game)
                    self.log(f"检测到重新上架的历史游戏: {game['name']} (ID: {game['id']}) - 历史上见过")
                else:
                    # ID小于等于last_max_id的重新上架游戏
                    analysis['updated_games'].append(game)
                    self.log(f"检测到重新上架的历史游戏: {game['name']} (ID: {game['id']})")
        
        # 判断是否批量发布（一次性新增多个游戏）
        if len(analysis['new_games']) > 1:
            analysis['batch_release'] = True
        
        # 判断是否有变化
        analysis['changes_detected'] = bool(analysis['new_games'] or analysis['updated_games'])
        
        return analysis
    
    def generate_report(self, analysis: Dict) -> str:
        """生成更新报告"""
        report = []
        report.append("=" * 50)
        report.append(f"斌哥游戏平台监控报告")
        report.append(f"检查时间: {analysis['timestamp']}")
        report.append("=" * 50)

        if not analysis['changes_detected']:
            report.append("✅ 未检测到游戏更新")
            return "\n".join(report)

        # 新游戏发布
        if analysis['new_games']:
            report.append(f"\n🎮 新游戏发布 ({len(analysis['new_games'])}个):")
            if analysis['batch_release']:
                report.append("⚠️  检测到批量发布!")

            for game in analysis['new_games']:
                report.append(f"  📱 {game['name']} (ID: {game['id']})")
                report.append(f"     特性: {game['features']}")
                report.append(f"     评论: {game['comments']} | 浏览: {game['views']}")
                report.append(f"     链接: {game['url']}")
                report.append("")

        # 历史游戏更新
        if analysis['updated_games']:
            report.append(f"\n🔄 历史游戏更新 ({len(analysis['updated_games'])}个):")
            for game in analysis['updated_games']:
                report.append(f"  📱 {game['name']} (ID: {game['id']})")
                report.append(f"     特性: {game['features']}")
                report.append(f"     评论: {game['comments']} | 浏览: {game['views']}")
                report.append(f"     链接: {game['url']}")
                report.append("")

        # 统计信息
        report.append(f"\n📊 统计信息:")
        report.append(f"  总游戏数: {analysis['total_games']}")
        report.append(f"  最大ID: {analysis['max_id']} (上次: {analysis['last_max_id']})")

        return "\n".join(report)

    def generate_push_message(self, analysis: Dict) -> Tuple[str, str]:
        """生成推送消息的标题和内容"""
        if not analysis['changes_detected']:
            return None, None

        # 生成标题
        title_parts = []
        if analysis['new_games']:
            title_parts.append(f"🎮新游戏{len(analysis['new_games'])}个")
        if analysis['updated_games']:
            title_parts.append(f"🔄更新{len(analysis['updated_games'])}个")

        title = f"斌哥游戏平台 - {' | '.join(title_parts)}"

        # 生成内容
        content_parts = []
        content_parts.append(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 新游戏信息
        if analysis['new_games']:
            content_parts.append(f"\n🎮 新游戏发布 ({len(analysis['new_games'])}个):")
            for game in analysis['new_games']:
                content_parts.append(f"📱 {game['name']} (ID: {game['id']})")
                content_parts.append(f"   特性: {game['features']}")
                content_parts.append(f"   评论: {game['comments']} | 浏览: {game['views']}")
                content_parts.append(f"   🔗 {game['url']}")
                content_parts.append("")

        # 历史游戏更新信息
        if analysis['updated_games']:
            content_parts.append(f"\n🔄 历史游戏更新 ({len(analysis['updated_games'])}个):")
            for game in analysis['updated_games']:
                content_parts.append(f"📱 {game['name']} (ID: {game['id']})")
                content_parts.append(f"   特性: {game['features']}")
                content_parts.append(f"   评论: {game['comments']} | 浏览: {game['views']}")
                content_parts.append(f"   🔗 {game['url']}")
                content_parts.append("")

        content = "\n".join(content_parts)
        return title, content
    
    def run_monitor(self) -> bool:
        """运行监控检查"""
        self.log("开始游戏监控检查...")

        # 获取当前游戏列表
        current_games = self.fetch_game_list()
        if not current_games:
            self.log("获取游戏列表失败，跳过本次检查")
            return False

        # 加载历史数据
        history = self.load_history()

        # 分析变化
        analysis = self.analyze_changes(current_games, history)

        # 生成报告
        report = self.generate_report(analysis)
        self.log("监控报告:")
        self.log(report)

        # 发送推送通知（如果有更新）
        if analysis['changes_detected'] and self.enable_push:
            push_title, push_content = self.generate_push_message(analysis)
            if push_title and push_content:
                self.send_push_notification(push_title, push_content, content_type=1)

        # 提取新游戏的下载链接
        if self.extract_downloads and analysis['changes_detected']:
            download_summary = self.extract_new_game_downloads(analysis)

            # 如果提取到下载链接，发送下载链接推送
            if download_summary and self.enable_push:
                self.send_download_links_push(analysis, download_summary)

        # 更新历史数据
        # 获取历史上见过的所有游戏ID
        all_historical_ids = history.get('all_seen_ids', set())
        if isinstance(all_historical_ids, list):
            all_historical_ids = set(all_historical_ids)

        # 添加当前检测到的所有游戏ID到历史记录中
        current_ids = {game['id'] for game in current_games}
        all_historical_ids.update(current_ids)

        new_history = {
            'games': {str(game['id']): game for game in current_games},
            'max_id': analysis['max_id'],
            'last_check': analysis['timestamp'],
            'total_checks': history.get('total_checks', 0) + 1,
            'all_seen_ids': list(sorted(all_historical_ids))  # 保存所有见过的游戏ID
        }
        self.save_history(new_history)

        return analysis['changes_detected']

    # ==================== 下载链接提取功能 ====================

    def fetch_game_detail(self, game_id: int) -> Optional[str]:
        """获取游戏详情页面HTML"""
        url = f"https://bg.denwq.cn/appshop/info/id/{game_id}.html"

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
                'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'cache-control': 'no-cache',
                'pragma': 'no-cache',
                'referer': 'https://bg.denwq.cn/f/1.html',
                'sec-fetch-dest': 'document',
                'sec-fetch-mode': 'navigate',
                'sec-fetch-site': 'same-origin',
                'sec-fetch-user': '?1',
                'upgrade-insecure-requests': '1'
            }
            cookies = {
                'xr_fullscreen_cookies': '1',
                'hyphp_lang': 'zh-CN'
            }

            response = requests.get(url, headers=headers, cookies=cookies, timeout=30)
            response.raise_for_status()

            self.log(f"成功获取游戏详情页面: {url}")
            return response.text

        except Exception as e:
            self.log(f"获取游戏详情页面失败 {url}: {e}")
            return None

    def identify_pan_type(self, url: str, display_name: str) -> str:
        """识别网盘类型"""
        url_lower = url.lower()
        display_lower = display_name.lower()

        if 'baidu.com' in url_lower or 'pan.baidu' in url_lower or '百度' in display_lower:
            return '百度网盘'
        elif 'feijipan.com' in url_lower or '奶牛' in display_lower:
            return '奶牛网盘'
        elif 'yunpan.com' in url_lower or '360' in display_lower:
            return '360网盘'
        elif 'lanzou' in url_lower or '蓝奏' in display_lower:
            return '蓝奏云'
        elif 'aliyundrive.com' in url_lower or '阿里' in display_lower:
            return '阿里云盘'
        elif 'weiyun.com' in url_lower or '微云' in display_lower:
            return '腾讯微云'
        elif '123pan.com' in url_lower or '123' in display_lower:
            return '123网盘'
        else:
            return '其他网盘'

    def extract_password(self, url: str) -> Optional[str]:
        """从URL中提取密码"""
        # 百度网盘密码格式: ?pwd=xxxx
        pwd_match = re.search(r'[?&]pwd=([^&]+)', url)
        if pwd_match:
            return pwd_match.group(1)

        # 其他可能的密码格式
        password_patterns = [
            r'[?&]password=([^&]+)',
            r'[?&]code=([^&]+)',
            r'[?&]key=([^&]+)'
        ]

        for pattern in password_patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None

    def extract_game_info(self, soup: BeautifulSoup, game_id: int) -> Dict:
        """提取游戏基本信息"""
        game_info = {'id': game_id}

        try:
            # 提取游戏名称
            title_elem = soup.find('title')
            if title_elem:
                title = title_elem.get_text(strip=True)
                # 清理标题
                game_name = title.replace(' - 斌哥游戏宝盒', '').replace('斌哥游戏宝盒 - ', '').strip()
                game_info['name'] = game_name

            # 提取游戏图标
            icon_elem = soup.find('img', class_='shadow')
            if icon_elem and icon_elem.get('src'):
                game_info['icon_url'] = icon_elem.get('src')

            # 提取游戏特性/标签
            badges = soup.find_all('span', class_='badge')
            features = []
            for badge in badges:
                text = badge.get_text(strip=True)
                if text and not any(keyword in text for keyword in ['评论', '浏览', '下载']):
                    features.append(text)
            game_info['features'] = features

            # 提取游戏描述
            desc_elem = soup.find('div', class_='game-description')
            if desc_elem:
                game_info['description'] = desc_elem.get_text(strip=True)

        except Exception as e:
            self.log(f"提取游戏信息时出错: {e}")

        return game_info

    def extract_download_links(self, html_content: str, game_id: int) -> Dict:
        """从HTML中提取下载链接"""
        soup = BeautifulSoup(html_content, 'html.parser')

        # 提取游戏基本信息
        game_info = self.extract_game_info(soup, game_id)

        # 提取网盘下载链接
        download_links = []

        # 查找网盘链接容器（包括隐藏的）
        wangpan_div = soup.find('div', id='wangpan')
        if wangpan_div:
            self.log(f"找到网盘容器: {wangpan_div.get('class', [])}")

            # 查找所有下载链接
            link_elements = wangpan_div.find_all('a', href=True)
            self.log(f"在网盘容器中找到 {len(link_elements)} 个链接")

            for link in link_elements:
                href = link.get('href')
                if href and href.startswith('http'):
                    # 提取网盘名称
                    link_text = link.get_text(strip=True)

                    # 识别网盘类型
                    pan_type = self.identify_pan_type(href, link_text)

                    # 提取密码（如果有）
                    password = self.extract_password(href)

                    download_info = {
                        'pan_type': pan_type,
                        'url': href,
                        'display_name': link_text,
                        'password': password,
                        'extracted_time': datetime.now().isoformat()
                    }

                    download_links.append(download_info)
                    self.log(f"✅ 提取到下载链接: {pan_type} - {href}")
                    if password:
                        self.log(f"   🔑 密码: {password}")
        else:
            self.log("❌ 未找到网盘容器 (id='wangpan')")

        # 如果没有找到网盘容器，尝试查找其他可能的下载链接
        if not download_links:
            self.log("🔍 尝试查找其他下载链接...")
            other_links = self.extract_other_download_links(soup)
            download_links.extend(other_links)

        result = {
            'game_info': game_info,
            'download_links': download_links,
            'total_links': len(download_links),
            'extraction_time': datetime.now().isoformat()
        }

        return result

    def extract_other_download_links(self, soup: BeautifulSoup) -> List[Dict]:
        """提取其他可能的下载链接"""
        other_links = []

        # 查找页面中所有可能的下载链接
        all_links = soup.find_all('a', href=True)

        download_keywords = ['下载', 'download', 'pan.', 'drive.', '网盘', '云盘']

        for link in all_links:
            href = link.get('href')
            text = link.get_text(strip=True)

            if href and href.startswith('http'):
                # 检查是否包含下载相关关键词
                if any(keyword in href.lower() or keyword in text.lower() for keyword in download_keywords):
                    # 避免重复添加已经在wangpan区域的链接
                    if not any(existing['url'] == href for existing in other_links):
                        pan_type = self.identify_pan_type(href, text)
                        password = self.extract_password(href)

                        other_links.append({
                            'pan_type': pan_type,
                            'url': href,
                            'display_name': text,
                            'password': password,
                            'source': 'other_links',
                            'extracted_time': datetime.now().isoformat()
                        })

        return other_links

    def save_download_data(self, game_id: int, download_data: Dict, extraction_reason: str = "手动提取"):
        """保存下载数据到文件"""
        try:
            # 读取现有数据
            if os.path.exists(self.downloads_file):
                with open(self.downloads_file, 'r', encoding='utf-8') as f:
                    all_data = json.load(f)
            else:
                all_data = {}

            # 添加提取原因
            download_data['extraction_reason'] = extraction_reason

            # 更新数据
            all_data[str(game_id)] = download_data

            # 保存数据
            with open(self.downloads_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)

            self.log(f"下载数据已保存: 游戏ID {game_id} ({extraction_reason})")

        except Exception as e:
            self.log(f"保存下载数据失败: {e}")

    def extract_game_downloads(self, game_id: int, extraction_reason: str = "手动提取") -> Optional[Dict]:
        """提取指定游戏的下载链接"""
        self.log(f"开始提取游戏下载链接: ID {game_id} ({extraction_reason})")

        # 获取游戏详情页面
        html_content = self.fetch_game_detail(game_id)
        if not html_content:
            return None

        # 提取下载链接
        download_data = self.extract_download_links(html_content, game_id)

        # 保存数据
        self.save_download_data(game_id, download_data, extraction_reason)

        return download_data

    def extract_new_game_downloads(self, analysis: Dict):
        """提取新游戏和重新上架游戏的下载链接"""
        if not self.extract_downloads:
            return {}

        download_summary = {
            'new_games_with_links': [],
            'updated_games_with_links': [],
            'total_links_extracted': 0
        }

        # 提取真正的新游戏下载链接
        if analysis['new_games']:
            self.log(f"🆕 开始提取 {len(analysis['new_games'])} 个新游戏的下载链接...")

            for game in analysis['new_games']:
                try:
                    self.log(f"提取新游戏下载链接: {game['name']} (ID: {game['id']})")
                    download_data = self.extract_game_downloads(
                        game['id'],
                        extraction_reason="新游戏发布"
                    )

                    if download_data and download_data['total_links'] > 0:
                        self.log(f"✅ 成功提取 {game['name']} 的 {download_data['total_links']} 个下载链接")

                        # 添加到摘要
                        download_summary['new_games_with_links'].append({
                            'game': game,
                            'download_data': download_data
                        })
                        download_summary['total_links_extracted'] += download_data['total_links']

                        # 在日志中记录下载链接
                        for link in download_data['download_links']:
                            self.log(f"  📦 {link['pan_type']}: {link['url']}")
                            if link['password']:
                                self.log(f"     🔑 密码: {link['password']}")
                    else:
                        self.log(f"⚠️ 未找到 {game['name']} 的下载链接")

                except Exception as e:
                    self.log(f"❌ 提取 {game['name']} 下载链接失败: {e}")

        # 提取重新上架的历史游戏下载链接（重要：新安装包链接可能不同）
        if analysis['updated_games']:
            self.log(f"🔄 开始提取 {len(analysis['updated_games'])} 个重新上架游戏的下载链接...")
            self.log("💡 重新上架的游戏可能包含新的安装包，需要更新下载链接")

            for game in analysis['updated_games']:
                try:
                    self.log(f"提取重新上架游戏下载链接: {game['name']} (ID: {game['id']})")
                    download_data = self.extract_game_downloads(
                        game['id'],
                        extraction_reason="重新上架（可能包含新安装包）"
                    )

                    if download_data and download_data['total_links'] > 0:
                        self.log(f"✅ 成功提取 {game['name']} 的 {download_data['total_links']} 个下载链接")

                        # 添加到摘要
                        download_summary['updated_games_with_links'].append({
                            'game': game,
                            'download_data': download_data
                        })
                        download_summary['total_links_extracted'] += download_data['total_links']

                        # 在日志中记录下载链接
                        for link in download_data['download_links']:
                            self.log(f"  📦 {link['pan_type']}: {link['url']}")
                            if link['password']:
                                self.log(f"     🔑 密码: {link['password']}")
                    else:
                        self.log(f"⚠️ 未找到 {game['name']} 的下载链接")

                except Exception as e:
                    self.log(f"❌ 提取 {game['name']} 下载链接失败: {e}")

        return download_summary

    def send_download_links_push(self, analysis: Dict, download_summary: Dict):
        """发送下载链接推送通知"""
        if not self.enable_push or download_summary['total_links_extracted'] == 0:
            return

        # 生成下载链接推送标题
        title = f"🎮 游戏下载链接 - 共{download_summary['total_links_extracted']}个链接"

        # 生成下载链接推送内容
        content_parts = []
        content_parts.append(f"📦 下载链接提取完成")
        content_parts.append(f"⏰ 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_parts.append(f"🔗 总链接数: {download_summary['total_links_extracted']}")
        content_parts.append("")

        # 新游戏下载链接
        if download_summary['new_games_with_links']:
            content_parts.append(f"🆕 新游戏下载链接 ({len(download_summary['new_games_with_links'])}个):")
            for item in download_summary['new_games_with_links']:
                game = item['game']
                download_data = item['download_data']
                content_parts.append(f"📱 {game['name']} (ID: {game['id']})")

                for i, link in enumerate(download_data['download_links'], 1):
                    content_parts.append(f"  {i}. {link['pan_type']}")
                    content_parts.append(f"     🔗 {link['url']}")
                    if link.get('password'):
                        content_parts.append(f"     🔑 密码: {link['password']}")
                content_parts.append("")

        # 重新上架游戏下载链接
        if download_summary['updated_games_with_links']:
            content_parts.append(f"🔄 重新上架游戏下载链接 ({len(download_summary['updated_games_with_links'])}个):")
            for item in download_summary['updated_games_with_links']:
                game = item['game']
                download_data = item['download_data']
                content_parts.append(f"📱 {game['name']} (ID: {game['id']})")

                for i, link in enumerate(download_data['download_links'], 1):
                    content_parts.append(f"  {i}. {link['pan_type']}")
                    content_parts.append(f"     🔗 {link['url']}")
                    if link.get('password'):
                        content_parts.append(f"     🔑 密码: {link['password']}")
                content_parts.append("")

        content = "\n".join(content_parts)
        self.send_push_notification(title, content, content_type=1)

def main():
    """主函数"""
    monitor = GameMonitor(
        extract_downloads=True,  # 启用下载链接提取
        enable_push=True         # 启用消息推送
    )

    try:
        has_changes = monitor.run_monitor()
        if has_changes:
            print("\n🔔 检测到游戏更新！")
            if monitor.enable_push:
                print("📤 推送通知已发送")
        else:
            print("\n✅ 无游戏更新")

    except KeyboardInterrupt:
        print("\n用户中断监控")
    except Exception as e:
        print(f"\n❌ 监控过程中出现错误: {e}")
        # 发送错误推送通知
        if monitor.enable_push:
            monitor.send_push_notification(
                "斌哥游戏监控 - 错误通知",
                f"监控过程中出现错误: {str(e)}",
                content_type=1
            )

if __name__ == "__main__":
    main()

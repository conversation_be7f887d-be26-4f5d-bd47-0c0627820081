#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重新上架游戏的下载链接提取
验证系统能否正确提取重新上架游戏的下载链接
"""

from game_monitor import GameMonitor
import json
import os

def test_reupload_download_extraction():
    """测试重新上架游戏的下载链接提取"""
    print("🧪 测试重新上架游戏的下载链接提取")
    print("=" * 60)
    
    # 创建启用下载提取的监控器
    monitor = GameMonitor(extract_downloads=True)
    
    # 模拟历史数据（只有部分游戏）
    history = {
        "games": {
            "880": {"id": 880, "name": "吃不到我呀", "features": "免广告"},
            "881": {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀"}
        },
        "max_id": 881,
        "all_seen_ids": [431, 636, 691, 880, 881, 882, 883],  # 历史上见过的ID
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 模拟当前游戏列表（包含重新上架的游戏）
    current_games = [
        # 新游戏
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 23, "views": 567, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},
        {"id": 884, "name": "太空异形生存者", "features": "秒杀", "comments": 106, "views": 6216, "url": "https://bg.denwq.cn/appshop/info/id/884.html"},
        
        # 重新上架的历史游戏（可能有新安装包）
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22593, "url": "https://bg.denwq.cn/appshop/info/id/636.html"},
        {"id": 691, "name": "伏魔觉", "features": "无敌 秒杀", "comments": 4642, "views": 67770, "url": "https://bg.denwq.cn/appshop/info/id/691.html"},
        
        # 无变化的游戏
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3834, "url": "https://bg.denwq.cn/appshop/info/id/880.html"},
        {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3351, "url": "https://bg.denwq.cn/appshop/info/id/881.html"}
    ]
    
    print("📅 模拟场景:")
    print("  历史最大ID: 881")
    print("  新游戏: 885 (快来当领主), 884 (太空异形生存者)")
    print("  重新上架: 636 (生存传奇), 691 (伏魔觉)")
    print("  无变化: 880, 881")
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    print(f"\n📊 分析结果:")
    print(f"  新游戏: {len(analysis['new_games'])} 个")
    print(f"  重新上架: {len(analysis['updated_games'])} 个")
    print(f"  检测到变化: {analysis['changes_detected']}")
    
    # 显示具体游戏
    if analysis['new_games']:
        print(f"\n🆕 新游戏:")
        for game in analysis['new_games']:
            print(f"  - {game['name']} (ID: {game['id']})")
    
    if analysis['updated_games']:
        print(f"\n🔄 重新上架游戏:")
        for game in analysis['updated_games']:
            print(f"  - {game['name']} (ID: {game['id']})")
    
    # 验证下载链接提取逻辑
    print(f"\n💡 下载链接提取策略:")
    print(f"  ✅ 新游戏将自动提取下载链接")
    print(f"  ✅ 重新上架游戏也将自动提取下载链接（可能包含新安装包）")
    print(f"  📦 总共需要提取: {len(analysis['new_games']) + len(analysis['updated_games'])} 个游戏")
    
    # 模拟下载链接提取过程
    if analysis['changes_detected']:
        print(f"\n🔍 模拟下载链接提取过程:")
        
        # 模拟新游戏提取
        if analysis['new_games']:
            print(f"\n🆕 提取新游戏下载链接:")
            for game in analysis['new_games']:
                print(f"  📱 {game['name']} (ID: {game['id']})")
                print(f"     🔗 模拟提取: 百度网盘、奶牛网盘、360网盘")
                print(f"     🔑 模拟密码: 自动识别")
        
        # 模拟重新上架游戏提取
        if analysis['updated_games']:
            print(f"\n🔄 提取重新上架游戏下载链接:")
            for game in analysis['updated_games']:
                print(f"  📱 {game['name']} (ID: {game['id']})")
                print(f"     💡 原因: 重新上架可能包含新版本安装包")
                print(f"     🔗 模拟提取: 更新的下载链接")
                print(f"     🔑 模拟密码: 可能已更新")
    
    return analysis

def test_download_data_structure():
    """测试下载数据结构"""
    print("\n🧪 测试下载数据结构")
    print("=" * 60)
    
    # 模拟下载数据
    sample_download_data = {
        "885": {  # 新游戏
            "game_info": {
                "id": 885,
                "name": "快来当领主",
                "features": ["无敌"],
                "icon_url": "https://img.71acg.net/kbyx~sykb/20250509/15364056785"
            },
            "download_links": [
                {
                    "pan_type": "百度网盘",
                    "url": "https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2",
                    "display_name": "百度云盘",
                    "password": "qlv2",
                    "extracted_time": "2024-01-15T10:30:00"
                },
                {
                    "pan_type": "奶牛网盘",
                    "url": "https://www.feijipan.com/s/Z00RumBg",
                    "display_name": "奶牛网盘",
                    "password": None,
                    "extracted_time": "2024-01-15T10:30:00"
                }
            ],
            "total_links": 2,
            "extraction_time": "2024-01-15T10:30:00",
            "extraction_reason": "新游戏发布"
        },
        "636": {  # 重新上架游戏
            "game_info": {
                "id": 636,
                "name": "生存传奇",
                "features": ["无敌", "秒杀"],
                "icon_url": "https://img.71acg.net/kbyx~sykb/20240304/14555737674"
            },
            "download_links": [
                {
                    "pan_type": "百度网盘",
                    "url": "https://pan.baidu.com/s/1NewLinkForReupload?pwd=new1",
                    "display_name": "百度云盘",
                    "password": "new1",
                    "extracted_time": "2024-01-15T10:31:00"
                }
            ],
            "total_links": 1,
            "extraction_time": "2024-01-15T10:31:00",
            "extraction_reason": "重新上架（可能包含新安装包）"
        }
    }
    
    print("📊 下载数据结构示例:")
    print(json.dumps(sample_download_data, ensure_ascii=False, indent=2))
    
    print(f"\n💡 数据说明:")
    print(f"  🆕 新游戏 (ID: 885): 提取原因 - 新游戏发布")
    print(f"  🔄 重新上架 (ID: 636): 提取原因 - 重新上架（可能包含新安装包）")
    print(f"  🔗 下载链接: 包含网盘类型、链接、密码等完整信息")
    print(f"  ⏰ 时间戳: 记录提取时间，便于追踪")

def main():
    """运行所有测试"""
    print("🚀 开始测试重新上架游戏的下载链接提取")
    print("=" * 80)
    
    try:
        # 测试重新上架游戏的下载链接提取
        analysis = test_reupload_download_extraction()
        
        # 测试下载数据结构
        test_download_data_structure()
        
        print("\n🎉 测试完成！")
        print("✅ 系统将自动提取所有有变化游戏的下载链接")
        print("✅ 包括新游戏和重新上架的游戏")
        print("💡 重新上架的游戏可能包含新版本，下载链接需要更新")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏下载链接监控器
监控新游戏并自动提取下载链接
"""

from game_monitor import GameMonitor
import json
import os
from datetime import datetime

class DownloadMonitor:
    def __init__(self, data_dir=".", enable_push=True):
        self.data_dir = data_dir
        self.game_monitor = GameMonitor(data_dir, extract_downloads=True, enable_push=enable_push)
        
    def run_download_monitor(self):
        """运行下载链接监控"""
        print("🚀 启动游戏下载链接监控")
        print("=" * 50)
        print("💡 监控说明：")
        print("  - 🆕 新游戏：自动提取下载链接")
        print("  - 🔄 重新上架：自动提取下载链接（可能是新安装包）")
        print("  - 📦 支持多种网盘：百度、奶牛、360、蓝奏云等")
        print("  - 🔑 自动识别网盘密码")
        print()

        # 运行游戏监控
        has_changes = self.game_monitor.run_monitor()

        if has_changes:
            print("\n🔔 检测到游戏更新，下载链接已自动提取！")
            print("💡 重新上架的游戏也已提取，因为可能包含新的安装包")
            if self.game_monitor.enable_push:
                print("📤 推送通知已发送")
            self.show_download_summary()
        else:
            print("\n✅ 无游戏更新")

        return has_changes
    
    def show_download_summary(self):
        """显示下载链接摘要"""
        downloads_file = os.path.join(self.data_dir, "game_downloads.json")
        
        if not os.path.exists(downloads_file):
            print("📁 暂无下载数据")
            return
        
        try:
            with open(downloads_file, 'r', encoding='utf-8') as f:
                downloads_data = json.load(f)
            
            print(f"\n📊 下载链接数据库摘要:")
            print(f"  总游戏数: {len(downloads_data)}")
            
            # 显示最近的下载链接
            recent_games = []
            for game_id, data in downloads_data.items():
                if data.get('download_links'):
                    recent_games.append((game_id, data))
            
            # 按提取时间排序，显示最新的5个
            recent_games.sort(key=lambda x: x[1].get('extraction_time', ''), reverse=True)
            
            print(f"\n📦 最近提取的下载链接:")
            for i, (game_id, data) in enumerate(recent_games[:5], 1):
                game_name = data['game_info'].get('name', f'游戏_{game_id}')
                link_count = data['total_links']
                print(f"  {i}. {game_name} (ID: {game_id}) - {link_count} 个下载链接")
                
                # 显示下载链接详情
                for link in data['download_links'][:2]:  # 只显示前2个
                    print(f"     📎 {link['pan_type']}: {link['url']}")
                    if link.get('password'):
                        print(f"        🔑 密码: {link['password']}")
                
                if len(data['download_links']) > 2:
                    print(f"     ... 还有 {len(data['download_links']) - 2} 个链接")
                print()
                
        except Exception as e:
            print(f"❌ 读取下载数据失败: {e}")
    
    def extract_specific_game(self, game_id: int):
        """提取指定游戏的下载链接"""
        print(f"🔍 提取游戏 {game_id} 的下载链接...")

        result = self.game_monitor.extract_game_downloads(game_id, "手动提取")

        if result:
            print(f"\n✅ 提取成功!")
            print(f"游戏名称: {result['game_info'].get('name', '未知')}")
            print(f"游戏特性: {', '.join(result['game_info'].get('features', []))}")
            print(f"下载链接数量: {result['total_links']}")

            if result['download_links']:
                print(f"\n📦 下载链接:")
                for i, link in enumerate(result['download_links'], 1):
                    print(f"  {i}. {link['pan_type']}")
                    print(f"     🔗 链接: {link['url']}")
                    if link.get('password'):
                        print(f"     🔑 密码: {link['password']}")
                    print()
            else:
                print("⚠️ 未找到下载链接")
        else:
            print("❌ 提取失败")

        return result
    
    def batch_extract_games(self, game_ids: list):
        """批量提取多个游戏的下载链接"""
        print(f"🔍 批量提取 {len(game_ids)} 个游戏的下载链接...")

        results = {}
        for i, game_id in enumerate(game_ids, 1):
            print(f"\n进度: {i}/{len(game_ids)} - 游戏ID: {game_id}")
            result = self.game_monitor.extract_game_downloads(game_id, "批量提取")

            if result:
                results[game_id] = result
                game_name = result['game_info'].get('name', f'游戏_{game_id}')
                print(f"✅ {game_name}: {result['total_links']} 个下载链接")
            else:
                print(f"❌ 游戏 {game_id} 提取失败")

        print(f"\n📊 批量提取完成:")
        print(f"  成功: {len(results)} 个")
        print(f"  失败: {len(game_ids) - len(results)} 个")

        return results
    
    def search_downloads_by_pan_type(self, pan_type: str):
        """按网盘类型搜索下载链接"""
        downloads_file = os.path.join(self.data_dir, "game_downloads.json")
        
        if not os.path.exists(downloads_file):
            print("📁 暂无下载数据")
            return []
        
        try:
            with open(downloads_file, 'r', encoding='utf-8') as f:
                downloads_data = json.load(f)
            
            results = []
            for game_id, data in downloads_data.items():
                game_name = data['game_info'].get('name', f'游戏_{game_id}')
                
                for link in data.get('download_links', []):
                    if pan_type.lower() in link['pan_type'].lower():
                        results.append({
                            'game_id': game_id,
                            'game_name': game_name,
                            'link': link
                        })
            
            print(f"\n🔍 找到 {len(results)} 个 {pan_type} 下载链接:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. {result['game_name']} (ID: {result['game_id']})")
                print(f"     🔗 {result['link']['url']}")
                if result['link'].get('password'):
                    print(f"     🔑 密码: {result['link']['password']}")
                print()
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索失败: {e}")
            return []

def main():
    """主函数"""
    monitor = DownloadMonitor()
    
    print("🎮 斌哥游戏下载链接监控器")
    print("=" * 50)
    print("1. 运行完整监控（检测新游戏并提取下载链接）")
    print("2. 提取指定游戏下载链接")
    print("3. 批量提取游戏下载链接")
    print("4. 查看下载链接摘要")
    print("5. 按网盘类型搜索")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                monitor.run_download_monitor()
            elif choice == '2':
                game_id = input("请输入游戏ID: ").strip()
                if game_id.isdigit():
                    monitor.extract_specific_game(int(game_id))
                else:
                    print("❌ 请输入有效的游戏ID")
            elif choice == '3':
                ids_input = input("请输入游戏ID列表（用逗号分隔）: ").strip()
                try:
                    game_ids = [int(x.strip()) for x in ids_input.split(',') if x.strip().isdigit()]
                    if game_ids:
                        monitor.batch_extract_games(game_ids)
                    else:
                        print("❌ 请输入有效的游戏ID列表")
                except ValueError:
                    print("❌ 请输入有效的游戏ID列表")
            elif choice == '4':
                monitor.show_download_summary()
            elif choice == '5':
                pan_type = input("请输入网盘类型（如：百度、奶牛、360）: ").strip()
                if pan_type:
                    monitor.search_downloads_by_pan_type(pan_type)
                else:
                    print("❌ 请输入网盘类型")
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能游戏检测逻辑
验证系统能否正确区分真正的新游戏和重新上架的历史游戏
"""

from game_monitor import GameMonitor

def test_scenario_historical_reappearance():
    """测试场景：历史游戏重新出现但ID更大"""
    print("🧪 测试场景：历史游戏重新出现（ID更大）")
    print("=" * 60)
    
    monitor = GameMonitor()
    
    # 模拟第一次运行的历史数据
    print("📅 第一次运行：")
    history_1 = {
        "games": {
            "636": {"id": 636, "name": "生存传奇", "features": "无敌 秒杀"}
        },
        "max_id": 636,
        "all_seen_ids": [636],  # 历史上见过的所有ID
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 模拟第二次运行：出现了ID 640的游戏（假设是历史游戏重新上架）
    print("  - 检测到游戏: 生存传奇 (ID: 636)")
    print("  - 记录最大ID: 636")
    print("  - 历史ID列表: [636]")
    
    print("\n📅 第二次运行：")
    current_games_2 = [
        {"id": 640, "name": "某个历史游戏", "features": "无敌", "comments": 50, "views": 1000, "url": "https://bg.denwq.cn/appshop/info/id/640.html"},
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22593, "url": "https://bg.denwq.cn/appshop/info/id/636.html"}
    ]
    
    # 为了模拟这是历史游戏，我们假设ID 640在更早的历史中出现过
    history_2 = history_1.copy()
    history_2["all_seen_ids"] = [636, 640]  # 假设640是很久以前见过的
    
    analysis = monitor.analyze_changes(current_games_2, history_2)
    report = monitor.generate_report(analysis)
    
    print("  - 新出现游戏: 某个历史游戏 (ID: 640)")
    print("  - ID 640 > 636，但在历史记录中存在")
    print("\n📊 分析结果:")
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 0, "不应该有新游戏"
    assert len(analysis['updated_games']) == 1, "应该有1个重新上架的历史游戏"
    assert analysis['updated_games'][0]['id'] == 640, "应该是ID 640的游戏"
    
    print("✅ 测试通过：正确识别为历史游戏重新上架\n")

def test_scenario_truly_new_game():
    """测试场景：真正的新游戏"""
    print("🧪 测试场景：真正的新游戏")
    print("=" * 60)
    
    monitor = GameMonitor()
    
    # 历史数据
    history = {
        "games": {
            "636": {"id": 636, "name": "生存传奇", "features": "无敌 秒杀"}
        },
        "max_id": 636,
        "all_seen_ids": [431, 636, 640, 691],  # 历史上见过的ID
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 当前游戏：出现了从未见过的新ID
    current_games = [
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 23, "views": 567, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},  # 真正的新游戏
        {"id": 640, "name": "某个历史游戏", "features": "无敌", "comments": 50, "views": 1000, "url": "https://bg.denwq.cn/appshop/info/id/640.html"},  # 历史游戏重新上架
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22593, "url": "https://bg.denwq.cn/appshop/info/id/636.html"}
    ]
    
    print("📅 当前检测：")
    print("  - 出现游戏: 快来当领主 (ID: 885) - 从未见过")
    print("  - 出现游戏: 某个历史游戏 (ID: 640) - 历史上见过")
    print("  - 历史最大ID: 636")
    print("  - 历史ID列表: [431, 636, 640, 691]")
    
    analysis = monitor.analyze_changes(current_games, history)
    report = monitor.generate_report(analysis)
    
    print("\n📊 分析结果:")
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 1, "应该有1个真正的新游戏"
    assert analysis['new_games'][0]['id'] == 885, "应该是ID 885的游戏"
    assert len(analysis['updated_games']) == 1, "应该有1个重新上架的历史游戏"
    assert analysis['updated_games'][0]['id'] == 640, "应该是ID 640的游戏"
    
    print("✅ 测试通过：正确区分新游戏和历史游戏\n")

def test_scenario_mixed_complex():
    """测试场景：复杂混合情况"""
    print("🧪 测试场景：复杂混合情况")
    print("=" * 60)
    
    monitor = GameMonitor()
    
    # 历史数据
    history = {
        "games": {
            "880": {"id": 880, "name": "吃不到我呀", "features": "免广告"}
        },
        "max_id": 880,
        "all_seen_ids": [431, 636, 691, 880],  # 历史上见过的ID
        "last_check": "2024-01-15T09:00:00"
    }
    
    # 当前游戏：混合情况
    current_games = [
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 23, "views": 567, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},  # 真正新游戏
        {"id": 884, "name": "太空异形生存者", "features": "秒杀", "comments": 106, "views": 6216, "url": "https://bg.denwq.cn/appshop/info/id/884.html"},  # 真正新游戏
        {"id": 691, "name": "伏魔觉", "features": "无敌 秒杀", "comments": 4642, "views": 67770, "url": "https://bg.denwq.cn/appshop/info/id/691.html"},  # 历史游戏重新上架
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22593, "url": "https://bg.denwq.cn/appshop/info/id/636.html"},  # 历史游戏重新上架
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3834, "url": "https://bg.denwq.cn/appshop/info/id/880.html"}  # 无变化
    ]
    
    print("📅 当前检测：")
    print("  - ID 885: 快来当领主 (新) - 从未见过且 > max_id")
    print("  - ID 884: 太空异形生存者 (新) - 从未见过且 > max_id") 
    print("  - ID 691: 伏魔觉 (历史) - 见过且 < max_id")
    print("  - ID 636: 生存传奇 (历史) - 见过且 < max_id")
    print("  - ID 880: 吃不到我呀 (无变化)")
    print("  - 历史最大ID: 880")
    
    analysis = monitor.analyze_changes(current_games, history)
    report = monitor.generate_report(analysis)
    
    print("\n📊 分析结果:")
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 2, f"应该有2个新游戏，实际{len(analysis['new_games'])}个"
    assert len(analysis['updated_games']) == 2, f"应该有2个历史更新，实际{len(analysis['updated_games'])}个"
    
    new_game_ids = {game['id'] for game in analysis['new_games']}
    updated_game_ids = {game['id'] for game in analysis['updated_games']}
    
    assert 885 in new_game_ids, "ID 885应该是新游戏"
    assert 884 in new_game_ids, "ID 884应该是新游戏"
    assert 691 in updated_game_ids, "ID 691应该是历史更新"
    assert 636 in updated_game_ids, "ID 636应该是历史更新"
    
    print("✅ 测试通过：正确处理复杂混合情况\n")

def main():
    """运行所有测试"""
    print("🚀 开始测试智能游戏检测逻辑")
    print("=" * 80)
    
    try:
        test_scenario_historical_reappearance()
        test_scenario_truly_new_game()
        test_scenario_mixed_complex()
        
        print("🎉 所有测试通过！")
        print("智能检测逻辑工作正常，能够正确区分新游戏和历史游戏重新上架。")
        
    except AssertionError as e:
        print(f"❌ 测试失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

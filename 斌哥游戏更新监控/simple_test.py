#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的广告过滤测试
"""

import os
import sys

def test_basic():
    print("🧪 基础测试")
    print("Python版本:", sys.version)
    print("当前目录:", os.getcwd())
    
    # 检查文件是否存在
    files_to_check = [
        "response.html",
        "game_monitor.py",
        "test_ad_filter.py"
    ]
    
    for file in files_to_check:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
    
    # 测试导入
    try:
        from bs4 import BeautifulSoup
        print("✅ BeautifulSoup 导入成功")
    except ImportError:
        print("❌ BeautifulSoup 导入失败")
        return False
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError:
        print("❌ requests 导入失败")
        return False
    
    return True

def test_html_parsing():
    print("\n🧪 HTML解析测试")
    
    if not os.path.exists("response.html"):
        print("❌ response.html 文件不存在")
        return
    
    try:
        from bs4 import BeautifulSoup
        import re
        
        with open("response.html", 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 查找所有游戏条目
        game_divs = soup.find_all('div', class_='d-flex flex-row shadow border')
        print(f"📊 找到 {len(game_divs)} 个条目")
        
        games = []
        ads = []
        
        for i, div in enumerate(game_divs):
            # 检查广告
            ribbon = div.find('div', class_='ribbon')
            if ribbon and '游戏广告' in ribbon.get_text():
                ad_name = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                ad_name_text = ad_name.text.strip() if ad_name else "未知广告"
                ads.append(ad_name_text)
                print(f"🚫 广告 #{i+1}: {ad_name_text}")
                continue
            
            # 检查游戏
            onclick = div.get('onclick', '')
            if 'appshop/info/id/' in onclick:
                id_match = re.search(r'id/(\d+)\.html', onclick)
                if id_match:
                    game_id = int(id_match.group(1))
                    name_div = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                    game_name = name_div.text.strip() if name_div else f"游戏_{game_id}"
                    games.append((game_id, game_name))
                    print(f"✅ 游戏 #{i+1}: {game_name} (ID: {game_id})")
            elif 'youyogame.com' in onclick:
                ad_name = div.find('div', style=lambda x: x and 'color: #1c1c1c' in x)
                ad_name_text = ad_name.text.strip() if ad_name else "外部广告"
                ads.append(ad_name_text)
                print(f"🚫 外部广告 #{i+1}: {ad_name_text}")
        
        print(f"\n📊 统计结果:")
        print(f"  总条目: {len(game_divs)}")
        print(f"  有效游戏: {len(games)}")
        print(f"  过滤广告: {len(ads)}")
        
        if games:
            games.sort(key=lambda x: x[0], reverse=True)
            print(f"\n🎮 游戏ID范围: {games[-1][0]} - {games[0][0]}")
            print("前5个游戏:")
            for game_id, name in games[:5]:
                print(f"  - ID {game_id}: {name}")
        
        if ads:
            print(f"\n🚫 过滤的广告:")
            for ad in ads:
                print(f"  - {ad}")
        
        return True
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return False

def main():
    print("=" * 50)
    print("斌哥游戏监控 - 简化测试")
    print("=" * 50)
    
    if not test_basic():
        print("❌ 基础测试失败")
        return
    
    if not test_html_parsing():
        print("❌ HTML解析测试失败")
        return
    
    print("\n🎉 所有测试通过！")
    print("广告过滤功能正常工作。")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息推送配置文件
"""

# 推送功能配置
PUSH_CONFIG = {
    # 是否启用推送功能
    "enable_push": True,
    
    # 是否推送游戏更新通知
    "push_game_updates": True,
    
    # 是否推送下载链接
    "push_download_links": True,
    
    # 是否推送错误通知
    "push_errors": True,
    
    # 推送内容类型 (1: 文字, 2: HTML, 3: Markdown)
    "content_type": 1,
    
    # 推送标题前缀
    "title_prefix": "🎮 斌哥游戏监控",
    
    # 是否在推送中包含游戏链接
    "include_game_links": True,
    
    # 是否在推送中包含下载密码
    "include_passwords": True,
    
    # 推送消息的最大长度（超过会截断）
    "max_message_length": 4000,
    
    # 批量发布阈值（超过此数量认为是批量发布）
    "batch_release_threshold": 3
}

def get_push_config():
    """获取推送配置"""
    return PUSH_CONFIG.copy()

def update_push_config(**kwargs):
    """更新推送配置"""
    for key, value in kwargs.items():
        if key in PUSH_CONFIG:
            PUSH_CONFIG[key] = value
        else:
            print(f"⚠️ 未知配置项: {key}")

def print_push_config():
    """打印当前推送配置"""
    print("📋 当前推送配置:")
    print("=" * 40)
    for key, value in PUSH_CONFIG.items():
        status = "✅" if value else "❌" if isinstance(value, bool) else "📝"
        print(f"  {status} {key}: {value}")
    print("=" * 40)

if __name__ == "__main__":
    print_push_config()

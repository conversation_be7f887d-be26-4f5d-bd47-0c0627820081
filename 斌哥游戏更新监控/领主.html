<!doctype html>
<html lang="zh-CN">
<head>
    <!-- 必须的 meta 标签 -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Bootstrap 的 CSS 文件 -->
    <link rel="stylesheet" href="https://menu-1255602259.cos.ap-nanjing.myqcloud.com/public/Game/bootstrap.min.css">
    <script src="https://menu-1255602259.cos.ap-nanjing.myqcloud.com/public/Game/jquery.min.js"></script>
    <script src="https://menu-1255602259.cos.ap-nanjing.myqcloud.com/public/Game/bootstrap.bundle.min.js"></script>
    <title>快来当领主</title>
    <style type="text/css">
        body {
            background: transparent;
            background-color: transparent;
        }

        .vip {
            display: inline-flex;
            width: 58px;
            line-height: 13px;
            vertical-align: middle;
            position: relative;
        }

        .vip div {
            width: 52px;
            height: 15px;
            animation: gno1 1.3s ease-in infinite;
            -webkit-animation: gno1 1.3s linear infinite;
            -moz-animation: gno1 1.3s linear infinite;
            -ms-animation: gno1 1.3s linear infinite;
            -o-animation: gno1 1.3s linear infinite;
            position: absolute;
            left: 4px;
            top: 3px;
            border-radius: 100px;
            display: block;
            border-bottom-left-radius: 150px;
        }

        .gno1 {
            height: 22px !important;
            width: 58px !important;
            display: inline-flex;
            vertical-align: middle;
            z-index: 1;
        }

        @keyframes gno1 {
            0% {
                box-shadow: 0 0 4px #fe7c23
            }

            50% {
                box-shadow: 0 0 40px #ff900a
            }

            100% {
                box-shadow: 0 0 4px #ffd98b
            }
        }

        /* 添加兼容性前缀 */
        @-webkit-keyframes gno1 {
            0% {
                box-shadow: 0 0 4px #fe7c23
            }

            50% {
                box-shadow: 0 0 40px #ff900a
            }

            100% {
                box-shadow: 0 0 4px #ffd98b
            }
        }

        @-moz-keyframes gno1 {
            0% {
                box-shadow: 0 0 4px #fe7c23
            }

            50% {
                box-shadow: 0 0 40px #ff900a
            }

            100% {
                box-shadow: 0 0 4px #ffd98b
            }
        }

        @-ms-keyframes gno1 {
            0% {
                box-shadow: 0 0 4px #fe7c23
            }

            50% {
                box-shadow: 0 0 40px #ff900a
            }

            100% {
                box-shadow: 0 0 4px #ffd98b
            }
        }

        .nav-tabs .nav-link {
            color: #727272;
        }

        .nav-tabs .nav-link.active {
            color: #000;
        }

        .nav-tabs .nav-link.active .dd {
            background-color: #15c5ce;
            width: 50px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
<div class="d-flex flex-column">
    <div style="width: 100%;height: 300px;background-color: #DDD;position: relative;">
        <a href="javascript:history.back(-1)" style="position: absolute;left: 10px;top: 10px;width: 35px;height: 35px;background: #00000063;border-radius: 50%;display: flex;align-content: center;justify-content: center;padding-top: 7px;">
            <svg t="1655739362605" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3582" width="20" height="20" style="fill: #FFF;">
                <path d="M724.4 895.7c-9.6 0-19.3-3.2-27.4-9.6L273.2 545.2c-10.3-8.3-16.3-20.8-16.3-34.1 0-13.2 6-25.8 16.3-34l422-339.1c18.8-15.1 46.3-12.1 61.4 6.7 15.1 18.8 12.1 46.3-6.7 61.4l-379.6 305L751.8 818c18.8 15.1 21.8 42.6 6.7 61.4-8.7 10.7-21.3 16.3-34.1 16.3z" p-id="3583"></path>
            </svg>
        </a>
        <div style="background-position:50%;object-fit: cover;width: 100%;height: 100%;    background-size: cover;
    background-repeat: no-repeat;background-image:url(//v.3839video.com/video/upload/1745837140.jpg)"></div>
        <div style="position: absolute;bottom: 0;left: 0;right: 0;">
            <div style="position: absolute;bottom: 0;left: 0;right: 0;top: 0;background: #3b3b3b7d;background: linear-gradient(0deg,rgba(0,0,0,.2),rgba(0,0,0,.2)),rgba(114,102,104,.3);backdrop-filter: blur(15px);z-index: 1"></div>
            <div class="d-flex" style="z-index: 11;position: absolute;">
                <div class="" style="padding: 10px 15px;">
                    <img src="https://img.71acg.net/kbyx~sykb/20250509/15364056785" style="width: 60px;height: 60px;border-radius: 10px;background: transparent;">
                </div>
                <div class="d-flex flex-column justify-content-center">
                    <span style="color: #FFF;">快来当领主</span>
                    <span style="color: #FFF;font-size: 14px">无敌</span>
                </div>
            </div>
            <div class="clearfix" style="height: 80px;"></div>
        </div>
    </div>
    <div class="d-flex flex-column" style="padding: 10px">
        <div class="d-flex flex-row justify-content-between">
            <div class="d-flex flex-column" style="font-size: 14px;">
                <div class="d-flex align-items-center">
                    <span style="color: #aeaeae;margin-right: 5px;">版本号</span>
                    <span style="color: #15c5ce;font-weight: 700;">1</span>
                </div>
                <div class="d-flex align-items-center">
                    <span style="color: #aeaeae;margin-right: 5px;">应用大小</span>
                    <span style="color: #15c5ce;font-weight: 700;margin-right: 5px;">708</span>
                    <span style="color: #aeaeae;margin-right: 5px;">浏览量</span>
                    <span style="color: #15c5ce;font-weight: 700;">654</span>
                </div>
            </div>
            <div class="d-flex flex-column">
                <div class="d-flex flex-column align-items-center">
                    <div style="color: #15c5ce;font-size: 12px;font-weight: bold;">游戏评分</div>
                    <div style="color: #15c5ce;font-weight: 700;font-size: 24px">7.9</div>
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex flex-row" style="padding-left: 10px" id="one">
        <div class="flex-fill" style="padding-right: 10px;">
            <a onclick="clickDown()" class="shadow-sm d-flex align-items-center justify-content-center" style="text-decoration: none;background-color: #15c5ce;color: #FFF;height: 40px;border-radius: 20px;padding: 0 6px;width: 100%">
                <svg t="1656586804743" style="fill: #FFF" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3580" width="20" height="20">
                    <path d="M512 576 768 320 576 320 576 64 448 64 448 320 256 320zM744.736 471.264 672.992 543.008 933.056 640 512 797.024 90.944 640 351.008 543.008 279.264 471.264 0 576 0 832 512 1024 1024 832 1024 576z" p-id="3581"></path>
                </svg>
                下载应用

            </a>
        </div>
        <div class="flex-fill" style="padding-right: 10px;">
            <a onclick="openGx()" class="shadow-sm d-flex align-items-center justify-content-center" style="text-decoration: none;background-color: #15c5ce;color: #FFF;height: 40px;border-radius: 20px;padding: 0 6px;width: 100%">
                <svg style="fill: #FFF" t="1656586600113" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2000" width="18" height="18">
                    <path d="M323.072 100.864h377.856c28.16 0 50.688-22.528 50.688-50.688S728.576 0 700.928 0H323.072c-28.16 0-50.688 22.528-50.688 50.688s23.04 50.176 50.688 50.176zM189.952 286.72h643.584c28.16 0 50.688-22.528 50.688-50.688s-22.528-50.688-50.688-50.688H189.952c-28.16 0-50.688 22.528-50.688 50.688s23.04 50.688 50.688 50.688zM936.448 374.784H87.552C39.936 374.784 1.024 413.696 1.024 460.8v476.672c0 47.616 38.4 86.016 86.016 86.016h849.408c47.616 0 86.016-38.4 86.016-86.016V460.8c0.512-47.104-38.4-86.016-86.016-86.016z m-297.984 259.584c-8.704 8.704-20.48 13.312-31.744 13.312-11.776 0-23.04-4.608-31.744-13.312l-17.408-17.408V890.88c0 25.088-20.48 45.056-45.056 45.056s-45.056-20.48-45.056-45.056v-273.92l-17.408 17.408c-17.408 17.408-46.08 17.408-64 0-17.408-17.408-17.408-46.08 0-64L480.256 476.16c8.704-8.704 19.968-13.312 31.744-13.312s23.552 4.608 31.744 13.312l94.208 94.208c17.92 17.92 17.92 46.592 0.512 64z" p-id="2001"></path>
                </svg>
                请求更新

            </a>
        </div>
    </div>
    <div id="wangpan" class="d-none flex-row" style="padding-left: 10px;">
        <div class="flex-fill" style="padding-right: 10px;">
            <a href="https://www.feijipan.com/s/Z00RumBg" target="_blank" class="shadow-sm d-flex align-items-center justify-content-center" style="text-decoration: none;background-color: #15c5ce;color: #FFF;height: 40px;border-radius: 20px;padding: 0 6px;width: 100%">
                <svg style="fill:#FFF" t="1656067776471" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1718" width="20" height="20">
                    <path d="M112.5 401.8c-29.6 0-57.4-11.5-78.4-32.4-21.1-21-32.7-48.9-32.7-78.6 0-30 11.7-58.1 33-79.1s49.4-32.3 79.5-32.1l142.6 1.8-0.7 58.1-142.6-1.8c-14.3-0.4-27.8 5.3-37.9 15.3-10.2 10-15.7 23.5-15.7 37.7 0 14.2 5.6 27.5 15.6 37.5s23.3 15.4 37.4 15.4h0.2l95.2-0.3 0.2 58.1-95.2 0.3c-0.3 0.1-0.4 0.1-0.5 0.1zM912 401.8h-0.3l-95.2-0.3c-16 0-29-13.1-29-29.1s13-29 29.1-29h0.1l95.2 0.3h0.2c14.1 0 27.4-5.5 37.4-15.4 10-10 15.6-23.3 15.6-37.5 0-17.5-8.6-33.9-23.1-43.9-13.2-9.1-16.6-27.1-7.6-40.4 9.1-13.2 27.1-16.6 40.4-7.6 30.4 20.8 48.5 55.1 48.4 91.9 0 29.7-11.6 57.6-32.7 78.6-21.1 20.9-48.9 32.4-78.5 32.4zM768.4 239.5c-15.9 0-28.8-12.8-29-28.7-0.2-16 12.7-29.2 28.7-29.4l93-1.1c16 0.1 29.2 12.6 29.4 28.7 0.2 16-12.7 29.2-28.7 29.4l-93 1.1h-0.4zM670.7 959.6h-317c-110.4 0-200.3-89.8-200.3-200.3 0-72.4 39.5-139.4 103.1-175 14-7.8 31.7-2.8 39.5 11.2 7.8 14 2.8 31.7-11.2 39.5-45.2 25.3-73.3 72.9-73.3 124.2 0 78.4 63.8 142.2 142.2 142.2h317c78.4 0 142.1-63.8 142.1-142.2S749 617 670.7 617H415.9c-16 0-29.1-13-29.1-29.1s13-29.1 29.1-29.1h254.8C781.2 559 871 648.9 871 759.3s-89.8 200.3-200.3 200.3z" p-id="1719"></path>
                    <path d="M211.4 768.2l-57.9-5 43.2-496.8c0.3-3.5 0.8-7 1.4-10.5 6.2-35.8 30.2-65.9 64.1-80.6 35.5-15.4 75.9-11.9 108.1 9.4 31 20.4 80.6 44.8 140.9 44.8 61.8 0 113-25.4 145.1-46.7 28.7-19.1 64.1-23.7 97.1-12.7 31.9 10.6 56.5 34.5 67.6 65.6 3.5 9.9 5.8 20.2 6.7 30.8l43.2 496.9-57.9 5-43.2-496.8c-0.5-5.6-1.7-11.1-3.5-16.3-5-14.1-16.4-25-31.2-30-15.9-5.3-32.9-3.1-46.6 6-38.8 25.7-101 56.4-177.2 56.4-74.8 0-135.3-29.6-172.9-54.4-15.7-10.4-35.6-12.1-53-4.5-16.1 7-27 20.5-29.9 37.2-0.3 1.8-0.6 3.7-0.7 5.6l-43.4 496.6z" p-id="1720"></path>
                    <path d="M417.1 429.6m-31.7 0a31.7 31.7 0 1 0 63.4 0 31.7 31.7 0 1 0-63.4 0Z" p-id="1721"></path>
                    <path d="M594.6 429.6m-31.7 0a31.7 31.7 0 1 0 63.4 0 31.7 31.7 0 1 0-63.4 0Z" p-id="1722"></path>
                </svg>
                奶牛网盘

            </a>
        </div>
        <div class="flex-fill" style="padding-right: 10px;">
            <a href="https://www.yunpan.com/surl_ypSSF3IYh4e" target="_blank" class="shadow-sm d-flex align-items-center justify-content-center" style="text-decoration: none;background-color: #15c5ce;color: #FFF;height: 40px;border-radius: 20px;padding: 0 6px;width: 100%">
                <svg style="fill:#FFF" t="1656067852727" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2732" width="20" height="20">
                    <path d="M680.416757 459.07972 573.090357 459.07972 573.090357 351.753319c0-30.312376-24.800849-55.113225-55.113225-55.113225l0 0c-30.312376 0-55.113225 24.800849-55.113225 55.113225l0 107.326401L355.537506 459.07972c-30.312376 0-55.113225 24.800849-55.113225 55.113225l0 0c0 30.312376 24.800849 55.113225 55.113225 55.113225l107.326401 0 0 107.326401c0 30.312376 24.800849 55.113225 55.113225 55.113225l0 0c30.312376 0 55.113225-24.800849 55.113225-55.113225L573.090357 569.30617l107.326401 0c30.312376 0 55.113225-24.800849 55.113225-55.113225l0 0C735.529983 483.880569 710.729134 459.07972 680.416757 459.07972z" p-id="2733"></path>
                    <path d="M511.771803 76.893274c-239.630682 0-435.106726 195.47195-435.106726 435.103656 0 94.841042 30.642904 182.753277 82.499969 254.379595 13.024641 20.517289 32.243355 42.841737 59.010999 66.24168 77.483722 71.033823 180.62787 114.488521 293.595758 114.488521 103.163591 0 198.122313-36.252669 272.847202-96.638991 0.907673-0.728594 1.814322-1.456165 2.716878-2.192945 0.475837-0.38988 0.954745-0.775666 1.425466-1.165546 22.744004-18.662035 43.799552-39.940664 62.682621-63.755046 59.665915-74.495668 95.433536-168.876222 95.433536-271.356244C946.877505 272.370341 751.405555 76.893274 511.771803 76.893274zM829.009004 763.432473c-66.787102 37.028335-141.208069 57.060577-188.409091 66.422805-90.930989 18.152429-200.3654-12.568247-240.679571-31.591509-68.068282-32.116465-89.183182-64.577784-124.267235-97.739045l-20.070104-19.195178c-47.562249-44.911886-98.123808-39.531342-113.870444-4.776793-22.51069-50.329269-35.056424-106.024755-35.056424-164.555823 0.001023-92.624561 31.394011-178.147376 84.049255-246.521626 68.905346-40.120766 147.548474-61.442374 196.569958-71.193459 91.102904-18.147312 200.535269 12.568247 240.679571 31.592532 68.240197 32.115442 89.182158 64.576761 124.43915 97.91403l20.070104 19.023262c47.082318 44.544519 95.120404 38.818097 109.209284 4.267187 22.610974 50.424436 35.21299 106.243743 35.21299 164.91705C916.885424 606.854345 883.972826 694.26516 829.009004 763.432473z" p-id="2734"></path>
                </svg>
                360网盘

            </a>
        </div>
        <div class="flex-fill" style="padding-right: 10px;">
            <a href="https://pan.baidu.com/s/1Qgdi4QFaJN89uoxkSp6AjQ?pwd=qlv2" target="_blank" class="shadow-sm  d-flex align-items-center justify-content-center" style="text-decoration: none;background-color: #15c5ce;color: #FFF;height: 40px;border-radius: 20px;padding: 0 6px;width: 100%">
                <svg style="fill:#FFF" t="1656067872252" class="icon mr-1" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2914" width="20" height="20">
                    <path d="M689.641351 411.482679c0 6.046716 0.431835 12.133342-0.125867 18.130939-0.506537 5.436826 1.440815 6.835685 6.489808 6.237051 36.348859-4.299933 70.868047 2.424212 102.898555 19.59222 55.276954 29.623691 86.608543 76.366272 95.429443 138.264017 2.153036 15.09479-2.444679 27.982308-15.495926 36.601616-11.509125 7.604188-23.905456 8.066723-35.941584 1.172709-11.006681-6.30152-16.213263-16.301268-18.044982-28.886911-6.818289-46.828538-32.426527-79.198783-77.747735-93.074815-39.798425-12.183484-76.939323-3.77907-106.826004 25.578562-68.665893 67.454298-136.304386 135.953392-204.465788 203.923436-32.568766 32.475646-71.38891 50.953486-117.774357 54.386678-90.432639 6.697539-168.177304-56.065923-186.951903-138.084938-24.041556-105.017822 46.965661-208.337979 159.723771-220.48053 10.906397-1.174756 22.029736-1.291412 32.9259 0.38374 6.433526 0.988514 8.731872 0.207731 8.15575-7.231705-2.411933-31.147394 1.074472-61.550845 14.32731-90.239234 28.02017-60.654428 75.401294-96.17441 141.200906-106.171088 71.04201-10.791787 145.764851 27.092032 179.905416 89.940428C684.241364 352.662015 690.150958 376.044562 689.641351 411.482679zM620.501668 410.010141c1.044796-55.863309-44.008306-109.989043-110.020766-110.275569-60.398602-0.260943-109.216447 49.629327-109.476367 110.101607-0.24457 57.08616 45.0091 109.920482 109.706611 109.866247C574.836629 519.649214 620.900757 468.037742 620.501668 410.010141zM305.704029 505.531682c-61.21213-2.200108-110.138446 51.575656-109.564371 109.139699 0.583285 58.380642 46.017056 109.776196 109.74959 109.769033 65.567321-0.00921 109.310591-52.623521 109.573581-109.844757C415.748331 552.893362 364.195188 503.776711 305.704029 505.531682z" p-id="2915"></path>
                    <path d="M894.783545 759.206218c-0.00921 19.534915-14.94334 34.916231-34.019815 35.035958-19.166525 0.122797-35.289738-16.005532-35.226293-35.240619 0.062422-18.859533 15.766079-34.111912 35.080983-34.075073C880.607684 724.965369 894.793778 739.197512 894.783545 759.206218z" p-id="2916"></path>
                </svg>
                百度云盘

            </a>
        </div>
    </div>
    <div class="mt-3">
        <ul class="nav nav-tabs flex-fill mb-2" id="myTab" role="tablist" style="border:0">
            <li class="nav-item flex-fill align-items-center justify-content-center" style="    text-align: center;" role="presentation">
                <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true" style="border:0;display: inline-block;text-decoration: none;">
                    <div class="d-flex flex-column align-items-center justify-content-center">
                        <div>游戏详情</div>
                        <div class="dd" style="height: 3px;"></div>
                    </div>
                </a>
            </li>
            <li class="nav-item flex-fill align-items-center justify-content-center" style="    text-align: center;" role="presentation">
                <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false" style="border:0;display: inline-block;text-decoration: none;">
                    <div class="d-flex flex-column align-items-center justify-content-center">
                        <div>
                            评论列表
                            <small style="margin-left: 5px;color: #7b7b7b;">27</small>
                        </div>
                        <div class="dd" style="height: 3px;"></div>
                    </div>
                </a>
            </li>
            <li class="nav-item flex-fill align-items-center justify-content-center" style="    text-align: center;" role="presentation">
                <a class="nav-link" id="update-tab" data-toggle="tab" href="#update" role="tab" aria-controls="update" aria-selected="false" style="border:0;display: inline-block;text-decoration: none;">
                    <div class="d-flex flex-column align-items-center justify-content-center">
                        <div>更新日志</div>
                        <div class="dd" style="height: 3px;"></div>
                    </div>
                </a>
            </li>
        </ul>
        <!-- mp4 -->
        <div class="tab-content" id="myTabContent">
            <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                <div style="padding: 10px">
                    <div style="height: 220px;overflow-x: auto" class="d-flex ">
                        <video class=" mr-2" style="width:100%; height:100%;margin: 0 auto;" controls="controls" controlslist="nodownload" poster="//v.3839video.com/video/upload/1745837140.jpg" x5-video-player-type="h5">
                            <source src="//v2.3839video.com/vod-57f5a7/d0b285780a1e71f0bfbd5017f0e80402/e007c132cc854f15b86f16bc9a026084-60434750e3b58f77092f1116de9ea45f-hd.mp4" type="video/mp4">
                        </video>
                        <img style="max-height: 100%" src="https://img.71acg.net/kbdev/opensj/20250428/18432070146~thumb?720x1280" class="d-block mr-2" alt="...">
                        <img style="max-height: 100%" src="https://img.71acg.net/kbdev/opensj/20250428/18432117253~thumb?720x1280" class="d-block mr-2" alt="...">
                        <img style="max-height: 100%" src="https://img.71acg.net/kbdev/opensj/20250428/18432366991~thumb?720x1280" class="d-block mr-2" alt="...">
                        <img style="max-height: 100%" src="https://img.71acg.net/kbdev/opensj/20250428/18432487003~thumb?720x1280" class="d-block mr-2" alt="...">
                        <img style="max-height: 100%" src="https://img.71acg.net/kbdev/opensj/20250428/18432538393~thumb?720x1280" class="d-block mr-2" alt="...">
                    </div>
                </div>
                <!-- <div style="padding: 10px">
            <div id="carouselExampleSlidesOnly" class="carousel slide" data-ride="carousel" data-interval="false">
              <div class="carousel-inner" style="    border-radius: 4px;">

                                        <div class="carousel-item active">
                                                <video style="width:100%; height:100%;margin: 0 auto;" controls="controls" controlslist="nodownload" poster="//v.3839video.com/video/upload/1745837140.jpg" x5-video-player-type="h5">
                        <source src="//v2.3839video.com/vod-57f5a7/d0b285780a1e71f0bfbd5017f0e80402/e007c132cc854f15b86f16bc9a026084-60434750e3b58f77092f1116de9ea45f-hd.mp4" type="video/mp4">
                    </video>

                </div>
                                        <div class="carousel-item ">
                                                <img src="https://img.71acg.net/kbdev/opensj/20250428/18432070146~thumb?720x1280" class="d-block w-100" alt="...">

                </div>
                                        <div class="carousel-item ">
                                                <img src="https://img.71acg.net/kbdev/opensj/20250428/18432117253~thumb?720x1280" class="d-block w-100" alt="...">

                </div>
                                        <div class="carousel-item ">
                                                <img src="https://img.71acg.net/kbdev/opensj/20250428/18432366991~thumb?720x1280" class="d-block w-100" alt="...">

                </div>
                                        <div class="carousel-item ">
                                                <img src="https://img.71acg.net/kbdev/opensj/20250428/18432487003~thumb?720x1280" class="d-block w-100" alt="...">

                </div>
                                        <div class="carousel-item ">
                                                <img src="https://img.71acg.net/kbdev/opensj/20250428/18432538393~thumb?720x1280" class="d-block w-100" alt="...">

                </div>
                                      </div>
              <button class="carousel-control-prev" type="button" data-target="#carouselExampleSlidesOnly" data-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="sr-only">Previous</span>
              </button>
              <button class="carousel-control-next" type="button" data-target="#carouselExampleSlidesOnly" data-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="sr-only">Next</span>
              </button>

            </div>
        </div> -->
                <div class="d-flex flex-column" style="padding: 10px">
                    <div style="font-size: 18px;font-weight: 500;margin-bottom: 8px">
                        <div class="d-flex flex-column align-items-center justify-content-center">
                            <div>注意事项</div>
                            <div style="height: 3px;background-color: #15c5ce;width: 100px;margin-top: 5px;"></div>
                        </div>
                    </div>
                    <div class="shadow" style="border: 1px #DDD solid;padding: 10px;border-radius: 4px;">
                        <p>
                            <span style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">亲爱的领主们：</span>
                            <br style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">
                            <span style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">大家好，我是《快来当领主》的制作人，制作这款游戏的初衷是想做一款让玩家拥护快乐体验的游戏。不枯燥是游戏制作初期我给自己制定的必须实现的目标，虽然我认为目前基本达到了我的预期，但是还是需要通过大家体验后的反馈来评估，好玩的话可以多多鼓励哟，另外各位玩家的反馈和建议也可以帮助我们来不断优化。</span>
                            <br style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">
                            <span style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">除了游戏的快乐体验，游戏画面冬日的寒冷与挑战中体验者火热的内心形成了鲜明的对比。不出预料，每个深入体验的玩家最后都会内心出现一点点的涟漪，可能有挑战通关的兴奋，也可能有招募未达标的遗憾，总之，这是一款能让玩家燥起来的游戏。</span>
                            <br style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">
                            <span style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">简单却并不容易，（玩家可简单上手，体验中却需要玩家动脑去搭配站位，制定策略通关）可能这就是目前这款产品初期带给大家的感受。</span>
                            <br style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">
                            <span style="color: rgb(62, 64, 63); font-family: 微软雅黑, Verdana, arial; font-size: 14px;">游戏版本初步完成，可能存在许多我们未发现的bug或者一些特别需要优化的地方，还请各位玩家能多多的包涵。有任何问题或者想法建议，都可以私信我们或者论坛中给我们留言，您的宝贵建议是我们前进的动力。</span>
                            <br>
                        </p>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                <div class="d-flex justify-content-between" style="padding: 0 10px">
                    <div style="color: #000;font-weight: bold;font-size: 20px;">游戏评价</div>
                    <div>
                        <div class="d-flex align-items-center dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                            <svg t="1656312209277" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3588" width="20" height="20">
                                <path d="M438.634667 192v644.202667l-0.810667 5.781333c-1.152 7.210667-2.304 8.704-8.64 17.002667l-7.168 3.925333c-15.914667 8.490667-18.282667 7.168-38.4-3.925333L149.333333 624.533333l45.269334-45.226666 180.032 180.138666V192h64z m216.96 10.005333l231.04 231.146667-45.269334 45.248L661.333333 298.261333V865.706667h-64V226.133333a34.133333 34.133333 0 0 1 58.282667-24.128z" p-id="3589"></path>
                            </svg>
                            最新评论
                        </div>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item active" href="https://bg.denwq.cn/appshop/info/id/885.html">最新评论</a>
                            <a class="dropdown-item " href="https://bg.denwq.cn/appshop/info/id/885/order/atime.html">最早评论</a>
                        </div>
                    </div>
                </div>
                <div class="d-flex flex-column" style="padding: 10px" id="postbox">
                    <div class="d-flex align-items-center mb-2" id="pfbox">
                        <div class="mr-2" style="font-weight: bold;">评分</div>
                        <div style="line-height: 0;">
                            <svg data-id="1" t="1656334894473" class="icon sda" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="20" height="20" style="fill:#aeaeae">
                                <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                            </svg>
                            <svg data-id="2" t="1656334894473" class="icon sda" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="20" height="20" style="fill:#aeaeae">
                                <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                            </svg>
                            <svg data-id="3" t="1656334894473" class="icon sda" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="20" height="20" style="fill:#aeaeae">
                                <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                            </svg>
                            <svg data-id="4" t="1656334894473" class="icon sda" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="20" height="20" style="fill:#aeaeae">
                                <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                            </svg>
                            <svg data-id="5" t="1656334894473" class="icon sda" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="20" height="20" style="fill:#aeaeae">
                                <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                            </svg>
                        </div>
                        <script type="text/javascript">
                            window.click_id = 0;
                            $('.sda').click(function() {
                                var id = $(this).data('id');
                                window.click_id = id;
                                $('.sda').each(function() {
                                    if (parseInt($(this).data('id')) > id) {
                                        $(this).css('fill', '#aeaeae');

                                    } else {
                                        $(this).css('fill', '#15c5ce');
                                    }
                                });

                                // switch(id){
                                //     case '1':
                                //         $('.sda').each(function(){
                                //             if(parseInt($(this).data('id'))  > 1){
                                //                 $(this).css('fill','#aeaeae');

                                //             }else{
                                //                 $(this).css('fill','#15c5ce');
                                //             }
                                //         });
                                //         break;
                                //     case '2':
                                //         break;
                                //     case '3':
                                //         break;
                                //     case '4':
                                //         break;
                                //     case '5':
                                //         break;
                                // }

                            });
                        </script>
                    </div>
                    <div class="d-none mb-2" id="postUserBox">
                        <div class="mr-2" style="font-weight: bold;">回复</div>
                        <div id="postUser" class="mr-2"></div>
                        <div style="color: #2196f3;" onclick="closePostUserBox()">取消回复</div>
                    </div>
                    <div class="d-flex flex-column">
                        <div class="mb-2" style="font-weight: bold;">评论内容</div>
                        <textarea class="form-control" rows="4" id="content"></textarea>
                        <div>
                            <button class="btn btn-warning btn-block mt-2" onclick="sendPost(this)">发布</button>
                        </div>
                    </div>
                </div>
                <style type="text/css">
                    .dropdown-toggle::after {
                        display: none;
                    }
                </style>
                <div class="d-flex flex-column" id="list">
                    <div id="postbox-51745640" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/2315.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/88bfcf02e7f554f9e9ea350b699bc6a7-b.jpg?time=1628139410" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    清梦                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:2315</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">宝盒铁粉</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">8分钟前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">666666</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745640" data-user="清梦" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745639" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/4549.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/3eb2f1a06667bfb9daba7f7effa0284b-b.jpg?time=1626410843" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    xuhan88                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:4549</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">宝盒铁粉</div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">9分钟前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">？。。。。。。</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745639" data-user="xuhan88" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745638" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/48267.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/a76650df1a9ed5ae6c11bce464aa2710-b.jpg?time=1635215815" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    十五                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:48267</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">新用户</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">10分钟前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">666666</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745638" data-user="十五" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745637" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/37593.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/07da625d725f8d78daf4576f397c6e82-b.jpg?time=1654695176" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    这破网卡了                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:37593</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">金仙</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">20分钟前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#aeaeae">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">确实有点意思</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745637" data-user="这破网卡了" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745634" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/7375.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/076a8133735eb5d7552dc195b125a454-b.jpg?time=1628956820" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    普通公鸡                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:7375</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">大罗金仙</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">51分钟前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">666666</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745634" data-user="普通公鸡" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745633" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/65.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/fc490ca45c00b1249bbe3554a4fdf6fb-b.jpg?time=1627808166" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    小虫                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:65</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">新用户</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">1小时前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">123456</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745633" data-user="小虫" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745632" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/101260.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/674d9635e8a475e7dcad0d5f21529ffb-b.jpg?time=1719165318" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    豹子頭零充                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:101260</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">散仙</div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">1小时前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">那就紧急集合</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745632" data-user="豹子頭零充" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745631" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/1888.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/2d1b2a5ff364606ff041650887723470-b.jpg?time=1625930197" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    yotimaki                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:1888</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">新用户</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">1小时前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">支持支持</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745631" data-user="yotimaki" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745630" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/7588.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/7576182d0a84b1ba2207f8f061d48bc9-b.jpg?time=1626812082" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    帝瑜                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:7588</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">宝盒铁粉</div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">1小时前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">还是有d觉得惊喜哦</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745630" data-user="帝瑜" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                    <div id="postbox-51745629" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px;border-radius: 8px;padding: 13px">
                        <div class="d-flex" style="margin-bottom: 10px">
                            <div style="margin-right: 10px" onclick="location.href='https://bg.denwq.cn/Space/profile/uid/2435.html'">
                                <img src="https://bg.denwq.cn/upload/avatar/b1301141feffabac455e1f90a7de2054-b.jpg?time=1626955931" style="width: 38px;height: 38px;border-radius:50%">
                            </div>
                            <div class="d-flex flex-column">
                                <div class="d-flex" style="font-weight: bold;font-size: 15px">
                                    3522506770                                        <span class="d-flex ml-1"></span>
                                </div>
                                <div class="d-flex" style="font-size: 13px;color: #9a9a9a">
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">ID:2435</div>
                                    <div class="badge badge-primary mr-1" style="line-height:inherit;">宝盒铁粉</div>
                                    <div class="vip">
                                        <img width="58" height="22" class="gno1" src="/public/images/vip.png">
                                        <div></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">1小时前                                </div>
                            <div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>
                            <div style="line-height: 0;">
                                <svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                                <svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#15c5ce">
                                    <path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="d-flex">
                            <p class="mb-0" style="font-size: 14px">。。。。。。。。</p>
                        </div>
                        <div class="d-flex flex-column">
                            <div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="51745629" data-user="3522506770" onclick="huifu(this)">回复评论</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="update" role="tabpanel" aria-labelledby="update-tab">
                <div class="d-flex flex-column"></div>
            </div>
        </div>
    </div>
</div>
<div id="gxbox" style="position: fixed; inset: 0px; background: rgba(0, 0, 0, 0.58); z-index: 1000;display: none;    left: 0px;
    right: 0px;
    top: 0px;
    bottom: 0px;">
    <div id="gxbox2" style="
    position: absolute;
    transform: translateY(100%);
    background: #FFF;
    height: 80%;
    width: 100%;
    bottom: 0;
    transition: transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
">
        <div style="
    padding: 10px 15px;
    border-bottom: solid 1px #e9e9e9;
">
            请求更新

            <button type="button" class="close" onclick="hideGx()">
                <span aria-hidden="true">×</span>
            </button>
        </div>
        <div id="sdxcas" style="
    padding: 10px 15px;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 50px;
">
            <div class="d-flex flex-column">
                <div class="mb-2" style="font-weight: bold;">反馈内容</div>
                <textarea class="form-control" rows="3" id="fknr"></textarea>
                <div>
                    <button class="btn btn-warning btn-block mt-2" onclick="tjfknr(this)">提交</button>
                </div>
            </div>
            <div id="fknr-list"></div>
        </div>
    </div>
</div>
<style type="text/css">
    .showGx {
        transform: translateY(0%) !important;
    }
</style>
<link href="https://bg.denwq.cn/public/css/alert.css" rel="stylesheet">
<script src="https://bg.denwq.cn/public/js/sweet-alert.min.js"></script>
<script type="text/javascript">
    window.loadFkIng = false;
    window.pageid2 = 0;

    function loadFk() {
        if (window.loadFkIng)
            return;
        window.loadFkIng = true;
        $.ajax({
            url: "https://bg.denwq.cn/appshop/loadingFk.html",
            type: "POST",
            data: {
                id: 885,
                pageid: ++window.pageid2
            },
            dataType: 'json',
            success: function(e) {
                window.loadIng = false;
                if (e.error) {
                    var $list = $('#fknr-list');
                    for (var o in e.info) {

                        var xz = '<span class="d-flex ml-1">';
                        for (var oo in e.info[o].medal) {
                            xz += '<a href="javascript:;"><img src="https://bg.denwq.cn/upload/fresh/medal/medal' + e.info[o].medal[oo]['id'] + '.gif" class="vm" alt="' + e.info[o].medal[oo]['name'] + '" title="' + e.info[o].medal[oo]['name'] + '" style="height: 15px;"></a>'
                        }
                        xz += '</span>';
                        var zz = '';
                        if (e.info[o].user_info != undefined) {
                            zz = '<div class="d-flex" style="font-size: 13px;color: #9a9a9a">' + '<div class="badge badge-primary mr-1" style="line-height:inherit;">ID:' + e.info[o].uid + '</div>' + '<div class="badge badge-primary mr-1" style="line-height:inherit;">' + e.info[o].user_info.gname + '</div>' + (e.info[o].user_info.vip == "1" ? '<div class="vip"><img width="58" height="22" class="gno1" src="/public/images/vip.png"><div></div></div>' : '') +
                                '</div>';
                        }

                        var html = '<div id="postbox-' + e.info[o].id + '" class="shadow d-flex flex-column border" style="background-color: #fff;margin: 10px 0;border-radius: 8px;padding: 13px;">' + '<div class="d-flex" style="margin-bottom: 10px">' + '<div style="margin-right: 10px" onclick="location.href=\'https://yyufyrm.cn/Space/profile/uid/' + e.info[o].uid + '.html\'">' + '<img src="https://bg.denwq.cn/' + e.info[o].avatar.b + '" style="width: 38px; height: 38px; border-radius: 50%"/>' + ' </div>' + '<div class="d-flex flex-column">' + '<div class="d-flex" style="font-weight: bold; font-size: 15px">' + e.info[o].user + xz + '</div>' + zz + '</div>' +
                            '</div>' + '<div class="d-flex align-items-center mb-2">' + '<div style="font-size: 12px; color: #9a9a9a; margin-right: 5px">' + e.info[o].atime_str + '</div>' + '</div>' + '<div class="d-flex">' + '<p class="mb-0" style="font-size: 14px">' + e.info[o].content + '</p>' + '</div>' + '</div>';

                        $list.append(html);
                    }
                    $('.dropdown-toggle').dropdown()

                } else {
                    if (!window.noData) {
                        window.noData = true;
                        window.loadFkIng = true;
                        $('#list').append('<div class="border shadow p-3" style="text-align: center;">没有更多了</div>');
                    }

                }

            },
            danger: function() {
                window.loadFkIng = false;
            }
        })
    }

    $(function() {
        $('#sdxcas').scroll(function() {
            console.log($(this).scrollTop())
            // if ($(this).scrollTop() + $(this).height() + 100
            // >=  $(document).height()) {
            //     loadList();
            // }
        });

        $('#gxbox').click(function() {
            hideGx();
            console.log(this, $(this).parents('#gxbox2'));
            // if()
            // hideGx()
        })
        $('#gxbox2').click(function(e) {
            e.stopPropagation();
        });

        loadFk();
    });
    function openGx() {
        $('#gxbox').show();
        $('#gxbox2').addClass('showGx');
    }
    function hideGx() {
        $('#gxbox2').removeClass('showGx');
        setTimeout(function() {
            $('#gxbox').hide();
        }, 200)
    }
    function tjfknr(obj) {
        $btn = $(obj);
        //$btn.attr('disabled','disabled');

        swal({
            title: "请求更新",
            text: "请求更新APP需要消耗：100 金币，确认申请吗？",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "提交申请",
            cancelButtonText: '取消'
        }).then(function() {
            $.ajax({
                url: 'https://bg.denwq.cn/appshop/update1.html',
                type: "POST",
                data: {
                    id: 885,
                    content: $('#fknr').val()
                },
                cache: false,
                dataType: 'json'
            }).then(function(e) {
                setTimeout(function() {
                    swal(e.error ? "提示" : "提示", e.info, e.error ? "success" : "error");
                }, 100);
                if (e.error) {
                    $('#fknr-list').html('');
                    window.pageid2 = 0;
                    window.loadFkIng = false;
                    loadFk();
                }

            }, function() {
                swal("失败", "请尝试重新提交", "error");
            });
        }, function() {
        });

    }
    window.isPost = false;
    function sendPost(obj) {
        $btn = $(obj);
        $btn.attr('disabled', 'disabled');
        $.ajax({
            url: 'https://bg.denwq.cn/appshop/post.html',
            type: 'post',
            data: {
                app_id: 885,
                pf: window.click_id,
                content: $('#content').val(),
                pid: window.nowPostPid
            },
            dataType: 'json',
            success: function(e) {
                $btn.removeAttr('disabled');
                if (e.error) {
                    window.isPost = true;
                    swal("发表成功", e.info, "success");
                    $('#list').prepend(formumData({
                        uid: 0,
                        user: "",
                        pf: window.click_id,
                        atime_str: "刚刚",
                        content: $('#content').val(),
                        avatar: {
                            b: ""
                        },
                        user_info: {
                            "vip": null,
                            "gname": "\u6e38\u5ba2"
                        },
                        medal: []

                    }));
                    $('#content').val('');
                    $('.sda').css('fill', '#aeaeae');

                    window.click_id = 0;
                    closePostUserBox();

                } else {
                    swal("发表失败", e.info, "error")
                }
            },
            danger: function(e) {
                $btn.removeAttr('disabled');
            }
        })
    }

    $(function() {

        $(window).scroll(function() {
            if ($(this).scrollTop() + $(this).height() + 100 >= $(document).height()) {
                loadList();
            }
        });

    });

    window.pageid = 1;
    window.loadIng = false;
    window.noData = false;
    function loadList() {
        if (window.loadIng)
            return;
        window.loadIng = true;
        $.ajax({
            url: "",
            type: "POST",
            data: {
                pageid: ++window.pageid,
                order: ''
            },
            dataType: 'json',
            success: function(e) {
                window.loadIng = false;
                if (e.error) {
                    var $list = $('#list');
                    for (var o in e.info) {
                        var html = formumData(e.info[o]);
                        $list.append(html);
                    }

                } else {
                    if (!window.noData) {
                        window.noData = true;
                        window.loadIng = true;
                        $('#list').append('<div class="border shadow p-3" style="text-align: center;">没有更多了</div>');
                    }

                }

            },
            danger: function() {
                window.loadIng = false;
            }
        })
    }

    function formumData(data) {

        var pf = '<div class="d-flex align-items-center mb-2">' + '<div style="font-size: 12px;color: #9a9a9a;margin-right: 5px;">' + data.atime_str + '</div>' + '<div class="mr-2" style="font-size: 14px;font-weight: 400;color: #9b9b9b;">评分</div>' + '<div style="line-height: 0;">' + '<svg data-id="1" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#' + (data.pf >= 1 ? '15c5ce' : 'aeaeae') + '"><path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path></svg>' + '<svg data-id="2" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#' + (data.pf >= 2 ? '15c5ce' : 'aeaeae') + '"><path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path></svg>' + '<svg data-id="3" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#' + (data.pf >= 3 ? '15c5ce' : 'aeaeae') + '"><path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path></svg>' + '<svg data-id="4" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#' + (data.pf >= 4 ? '15c5ce' : 'aeaeae') + '"><path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path></svg>' + '<svg data-id="5" t="1656334894473" class="icon " viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2180" width="16" height="16" style="fill:#' + (data.pf >= 5 ? '15c5ce' : 'aeaeae') + '"><path d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3-12.3 12.7-12.1 32.9 0.6 45.3l183.7 179.1-43.4 252.9c-1.2 6.9-0.1 14.1 3.2 20.3 8.2 15.6 27.6 21.7 43.2 13.4L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z" p-id="2181"></path></svg>' + '</div>' + '</div>';
        if (parseInt(data.pid) != 0) {
            pf = '';
        }

        var xz = '<span class="d-flex ml-1">';
        for (var o in data.medal) {
            xz += '<a href="javascript:;"><img src="https://bg.denwq.cn/upload/fresh/medal/medal' + data.medal[o]['id'] + '.gif" class="vm" alt="' + data.medal[o]['name'] + '" title="' + data.medal[o]['name'] + '" style="height: 15px;"></a>'
        }
        xz += '</span>';

        var zz = '';
        if (data.user_info != undefined) {
            zz = '<div class="d-flex" style="font-size: 13px;color: #9a9a9a">' + '<div class="badge badge-primary mr-1" style="line-height:inherit;">ID:' + data.uid + '</div>' + '<div class="badge badge-primary mr-1" style="line-height:inherit;">' + data.user_info.gname + '</div>' + (data.user_info.vip == "1" ? '<div class="vip"><img width="58" height="22" class="gno1" src="/public/images/vip.png"><div></div></div>' : '') +
                '</div>';
        }

        return '<div id="postbox-' + data.id + '" class="shadow d-flex flex-column border" style="background-color: #f6f7f9;margin: 10px;border-radius: 5px;padding: 13px">' + '<div class="d-flex" style="margin-bottom: 10px">' + '<div style="margin-right: 10px">' + '<img src="https://bg.denwq.cn/' + data.avatar.b + '" style="width: 38px;height: 38px;border-radius:50%">' + '</div>' + '<div class="d-flex flex-column">' + '<div class="d-flex" style="font-weight: bold;font-size: 15px">' + data.user + xz +
            '</div>' + zz +
            '</div>' +
            '</div>' +
            (data.pidd == false || data.pidd == undefined ? '' : '<div class="d-flex mb-2"><div class="d-flex flex-column" style="background: #dedede;padding: 10px;width: 100%;border-radius: 10px;"><div class="d-flex"><div style="margin-right: 10px"><img src="https://bg.denwq.cn/' + data.pidd.avatar.b + '" style="width: 30px;height: 30px;border-radius:50%"></div><div class="d-flex flex-column"><div style="font-weight: bold;font-size: 14px;color: #505050">' + data.pidd.user + '</div><div style="font-size: 12px;color: #9a9a9a">' + data.pidd.atime_str + '</div></div></div><div style="font-size: 14px;color: #545454;">' + data.pidd.content + '</div></div></div>') +
            pf + '<div class="d-flex">' + '<p class="mb-0" style="font-size: 14px">' + data.content + '</p>' + '</div>' + (data.id != undefined ? '<div class="d-flex flex-column"><div style="font-size: 14px;color: #3f51b5;margin-top: 10px;" data-pid="' + data.id + '" data-user="' + data.user + '" onclick="huifu(this)">回复评论</div></div>' : '') + '</div>';

    }

    function clickDown() {

        return swal('下载失败', "需要登录后才能下载！<a href=\"https://bg.denwq.cn/user/login.html\">点击登录</a>", "error");

        $('#wangpan').removeClass('d-none').addClass('d-flex');
        $('#one').removeClass('d-flex').addClass('d-none');
    }
    function qingqiu() {
        return swal('请求失败', "需要登录后才能请求更新！", "error");
        swal({
            title: "请求更新",
            text: "请求更新APP需要消耗：100 金币，确认申请吗？",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "申请",
            cancelButtonText: '取消'
        }).then(function() {
            $.ajax({
                url: 'https://bg.denwq.cn/appshop/update.html',
                type: "POST",
                data: {
                    id: 885
                },
                cache: false,
                dataType: 'json'
            }).then(function(e) {
                setTimeout(function() {
                    swal(e.error ? "提示" : "提示", e.info, e.error ? "success" : "error");
                }, 100);

            }, function() {
                swal("失败", "请尝试重新提交", "error");
            });
        }, function() {
        });
    }
    window.nowPostPid = 0;
    function huifu(obj) {
        var $this = $(obj);
        var pid = $this.data('pid');
        window.nowPostPid = pid;
        var user = $this.data('user');
        location.href = "#postbox";
        $('#postUser').text('@' + user);
        $('#postUserBox').removeClass('d-none').addClass('d-flex');
        $('#pfbox').removeClass('d-flex').addClass('d-none');

    }
    function closePostUserBox() {
        $('#postUserBox').removeClass('d-flex').addClass('d-none');
        $('#pfbox').removeClass('d-none').addClass('d-flex');
        window.nowPostPid = 0;
    }
    function deletePost(id) {
        $.ajax({
            url: 'https://bg.denwq.cn/appshop/delete_post.html',
            type: "POST",
            data: {
                id: id
            },
            cache: false,
            dataType: 'json'
        }).then(function(e) {
            setTimeout(function() {
                swal(e.error ? "提示" : "提示", e.info, e.error ? "success" : "error");
            }, 100);
            if (e.error) {
                $('#postbox-' + id).remove();
            }

        }, function() {
            swal("失败", "请尝试重新提交", "error");
        });
    }
    function deleteFk(id) {
        $.ajax({
            url: 'https://bg.denwq.cn/appshop/delete_fk.html',
            type: "POST",
            data: {
                id: id
            },
            cache: false,
            dataType: 'json'
        }).then(function(e) {
            setTimeout(function() {
                swal(e.error ? "提示" : "提示", e.info, e.error ? "success" : "error");
            }, 100);
            if (e.error) {
                $('#postbox-' + id).remove();
            }

        }, function() {
            swal("失败", "请尝试重新提交", "error");
        });
    }
</script>
</body>
</html>

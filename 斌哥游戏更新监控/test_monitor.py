#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏监控测试脚本
用于测试监控功能和模拟各种场景
"""

import json
import os
from datetime import datetime
from game_monitor import GameMonitor

def create_test_data():
    """创建测试数据"""
    # 模拟历史游戏数据
    test_history = {
        "games": {
            "880": {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3825},
            "881": {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3345},
            "882": {"id": 882, "name": "末日冲突", "features": "秒杀 定怪", "comments": 44, "views": 9882},
            "883": {"id": 883, "name": "异境英雄", "features": "无敌 秒杀", "comments": 42, "views": 5592}
        },
        "max_id": 883,
        "last_check": "2024-01-15T09:00:00",
        "total_checks": 10
    }
    
    return test_history

def test_scenario_1_new_games():
    """测试场景1: 新游戏发布"""
    print("🧪 测试场景1: 新游戏发布")
    print("-" * 40)
    
    monitor = GameMonitor()
    
    # 创建历史数据
    history = create_test_data()
    
    # 模拟当前游戏列表（添加了新游戏）
    current_games = [
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 20, "views": 393, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},
        {"id": 884, "name": "太空异形生存者", "features": "秒杀", "comments": 105, "views": 6204, "url": "https://bg.denwq.cn/appshop/info/id/884.html"},
        {"id": 883, "name": "异境英雄", "features": "无敌 秒杀", "comments": 42, "views": 5592, "url": "https://bg.denwq.cn/appshop/info/id/883.html"},
        {"id": 882, "name": "末日冲突", "features": "秒杀 定怪", "comments": 44, "views": 9882, "url": "https://bg.denwq.cn/appshop/info/id/882.html"},
        {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3345, "url": "https://bg.denwq.cn/appshop/info/id/881.html"},
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3825, "url": "https://bg.denwq.cn/appshop/info/id/880.html"}
    ]
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    # 生成报告
    report = monitor.generate_report(analysis)
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 2, f"应该检测到2个新游戏，实际检测到{len(analysis['new_games'])}个"
    assert analysis['batch_release'] == True, "应该检测到批量发布"
    assert analysis['changes_detected'] == True, "应该检测到变化"
    
    print("✅ 测试场景1通过\n")

def test_scenario_2_historical_update():
    """测试场景2: 历史游戏更新"""
    print("🧪 测试场景2: 历史游戏更新")
    print("-" * 40)
    
    monitor = GameMonitor()
    
    # 创建历史数据
    history = create_test_data()
    
    # 模拟当前游戏列表（添加了历史游戏）
    current_games = [
        {"id": 883, "name": "异境英雄", "features": "无敌 秒杀", "comments": 42, "views": 5592, "url": "https://bg.denwq.cn/appshop/info/id/883.html"},
        {"id": 882, "name": "末日冲突", "features": "秒杀 定怪", "comments": 44, "views": 9882, "url": "https://bg.denwq.cn/appshop/info/id/882.html"},
        {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3345, "url": "https://bg.denwq.cn/appshop/info/id/881.html"},
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3825, "url": "https://bg.denwq.cn/appshop/info/id/880.html"},
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22578, "url": "https://bg.denwq.cn/appshop/info/id/636.html"}  # 历史游戏重新上架
    ]
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    # 生成报告
    report = monitor.generate_report(analysis)
    print(report)
    
    # 验证结果
    assert len(analysis['updated_games']) == 1, f"应该检测到1个历史更新，实际检测到{len(analysis['updated_games'])}个"
    assert analysis['updated_games'][0]['id'] == 636, "应该检测到ID为636的游戏更新"
    assert analysis['changes_detected'] == True, "应该检测到变化"
    
    print("✅ 测试场景2通过\n")

def test_scenario_3_mixed_changes():
    """测试场景3: 混合变化（新游戏+历史更新）"""
    print("🧪 测试场景3: 混合变化")
    print("-" * 40)
    
    monitor = GameMonitor()
    
    # 创建历史数据
    history = create_test_data()
    
    # 模拟当前游戏列表（新游戏+历史更新）
    current_games = [
        {"id": 885, "name": "快来当领主", "features": "无敌", "comments": 20, "views": 393, "url": "https://bg.denwq.cn/appshop/info/id/885.html"},  # 新游戏
        {"id": 884, "name": "太空异形生存者", "features": "秒杀", "comments": 105, "views": 6204, "url": "https://bg.denwq.cn/appshop/info/id/884.html"},  # 新游戏
        {"id": 883, "name": "异境英雄", "features": "无敌 秒杀", "comments": 42, "views": 5592, "url": "https://bg.denwq.cn/appshop/info/id/883.html"},
        {"id": 882, "name": "末日冲突", "features": "秒杀 定怪", "comments": 44, "views": 9882, "url": "https://bg.denwq.cn/appshop/info/id/882.html"},
        {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3345, "url": "https://bg.denwq.cn/appshop/info/id/881.html"},
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3825, "url": "https://bg.denwq.cn/appshop/info/id/880.html"},
        {"id": 636, "name": "生存传奇", "features": "无敌 秒杀", "comments": 262, "views": 22578, "url": "https://bg.denwq.cn/appshop/info/id/636.html"},  # 历史更新
        {"id": 431, "name": "疾风骑士", "features": "无敌 秒杀", "comments": 51289, "views": 304572, "url": "https://bg.denwq.cn/appshop/info/id/431.html"}  # 历史更新
    ]
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    # 生成报告
    report = monitor.generate_report(analysis)
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 2, f"应该检测到2个新游戏，实际检测到{len(analysis['new_games'])}个"
    assert len(analysis['updated_games']) == 2, f"应该检测到2个历史更新，实际检测到{len(analysis['updated_games'])}个"
    assert analysis['batch_release'] == True, "应该检测到批量发布"
    assert analysis['changes_detected'] == True, "应该检测到变化"
    
    print("✅ 测试场景3通过\n")

def test_scenario_4_no_changes():
    """测试场景4: 无变化"""
    print("🧪 测试场景4: 无变化")
    print("-" * 40)
    
    monitor = GameMonitor()
    
    # 创建历史数据
    history = create_test_data()
    
    # 模拟当前游戏列表（无变化）
    current_games = [
        {"id": 883, "name": "异境英雄", "features": "无敌 秒杀", "comments": 42, "views": 5592, "url": "https://bg.denwq.cn/appshop/info/id/883.html"},
        {"id": 882, "name": "末日冲突", "features": "秒杀 定怪", "comments": 44, "views": 9882, "url": "https://bg.denwq.cn/appshop/info/id/882.html"},
        {"id": 881, "name": "疯狂农场植物僵尸", "features": "无敌 秒杀", "comments": 70, "views": 3345, "url": "https://bg.denwq.cn/appshop/info/id/881.html"},
        {"id": 880, "name": "吃不到我呀", "features": "免广告", "comments": 36, "views": 3825, "url": "https://bg.denwq.cn/appshop/info/id/880.html"}
    ]
    
    # 分析变化
    analysis = monitor.analyze_changes(current_games, history)
    
    # 生成报告
    report = monitor.generate_report(analysis)
    print(report)
    
    # 验证结果
    assert len(analysis['new_games']) == 0, f"应该检测到0个新游戏，实际检测到{len(analysis['new_games'])}个"
    assert len(analysis['updated_games']) == 0, f"应该检测到0个历史更新，实际检测到{len(analysis['updated_games'])}个"
    assert analysis['changes_detected'] == False, "应该检测到无变化"
    
    print("✅ 测试场景4通过\n")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行游戏监控测试")
    print("=" * 50)
    
    try:
        test_scenario_1_new_games()
        test_scenario_2_historical_update()
        test_scenario_3_mixed_changes()
        test_scenario_4_no_changes()
        
        print("🎉 所有测试通过！")
        print("监控逻辑工作正常，可以开始使用。")
        
    except AssertionError as e:
        print(f"❌ 测试失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == "__main__":
    run_all_tests()

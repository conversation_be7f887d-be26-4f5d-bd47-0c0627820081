import requests
import re
import os

# 设置ua
ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36 Edg/114.0.1823.43'
session = requests.session()

# 签到函数
def main(username,password):
    headers={'User-Agent': ua}
    session.get('https://bbs.binmt.cc/member.php?mod=logging&action=login&infloat=yes&handlekey=login&inajax=1&ajaxtarget=fwin_content_login',headers=headers)
    chusihua = session.get('https://bbs.binmt.cc/member.php?mod=logging&action=login&infloat=yes&handlekey=login&inajax=1&ajaxtarget=fwin_content_login',headers=headers)
    #print(chusihua.text)
    #print(re.findall('loginhash=(.*?)">', chusihua.text))
    loginhash = re.findall('loginhash=(.*?)">', chusihua.text)[0]
    formhash = re.findall('formhash" value="(.*?)".*? />', chusihua.text)[0]
    denurl = f'https://bbs.binmt.cc/member.php?mod=logging&action=login&loginsubmit=yes&handlekey=login&loginhash={loginhash}&inajax=1'
    data = {'formhash': formhash,'referer': 'https://bbs.binmt.cc/forum.php','loginfield': 'username','username': username,'password': password,'questionid': '0','answer': '',}
    denlu = session.post(headers=headers, url=denurl, data=data).text
    #print(denlu)
    #print(formhash)
    if '手机号登录成功' in denlu:
        #获取formhash
        zbqd = session.get('https://bbs.binmt.cc/k_misign-sign.html', headers=headers).text
        formhash = re.findall('formhash" value="(.*?)".*? />', zbqd)[0]
        #签到
        qdurl=f'https://bbs.binmt.cc/plugin.php?id=k_misign:sign&operation=qiandao&format=text&formhash={formhash}'
        qd = session.get(url=qdurl, headers=headers).text
        qdyz = re.findall('<root><(.*?)</root>', qd)
        print(qdyz)
        sign_info = re.findall('<root><(.*?)</root>', qd)[0].strip()
    else:
        print('登录失败')

# 从环境变量中读取账号密码


main('13927009231','@zyh0820@')

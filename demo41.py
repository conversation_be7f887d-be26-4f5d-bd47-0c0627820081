import requests
import urllib3

# 关闭 InsecureRequestWarning 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 请求头
headers = {
    "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/128.0.6613.40 Mobile Safari/537.36 uni-app Html5Plus/1.0 (Immersed/34.285713)",
    "Host": "open.api.hengfengweiye.cn",
    "Connection": "Keep-Alive",
    "Accept-Encoding": "gzip",
}

# 请求参数
params = {
    "phone": "15643542976"
}

# 发送 GET 请求，关闭 SSL 证书验证
response = requests.get("https://open.api.hengfengweiye.cn/api/getPhoneNetStatus", headers=headers, params=params, verify=False)

# 输出响应内容
print(response.json())
# 2024.4.29 16.46pm

import requests
from time import sleep

num = 0
num_1_before_1 = 0
num_3_before_1 = 0
num_1_after_1 = 0
num_3_after_1 = 0
num_1_before_2 = 0
num_3_before_2 = 0
num_1_after_2 = 0
num_3_after_2 = 0
juan_name = '1元'
# 赠送者
source_id = {'userId': '659523488b5d352a24a1ba82','authorization': 'bearer 8a5d394a-abe5-4b87-8968-c46c0cac7064','name': '张浩锐'}
# 接收者
target_id = {'authorization': 'bearer 3efb4f24-bd20-47c7-af5e-afd73cc34fe4','name': '大号'}

# couponId为被赠送卷entityList里的couponId，shareUserId为赠送卷者id，couponGrantId为被赠送卷entityList里的id
# authorization为接受赠送者的authorization
def receive_coupon(authorization, couponId, shareUserId, couponGrantId):
    global num
    headers = {
        'authorization': authorization
    }

    params = {
        'couponId': couponId,
        'shareUserId': shareUserId,
        'type': 'share',
        'couponGrantId': couponGrantId,
    }

    json_data = {}

    response = requests.post(
        'https://ymixy.cloudyee.com/api/activity/coupon/receiveShareCoupon',
        params=params,
        headers=headers,
        json=json_data,
    )
    if response.json()['message'] == 'success':
        print('赠送成功')
        num +=1
    else:
        print('赠送失败')
        print(response.json()['description'])


# authorization为赠送者的authorization
def id_message(authorization):
    headers = {
        'authorization': authorization
    }

    params = {
        'pageSize': '3000', #每页数据大小
        'pageNo': '1', #当前页数
        'status': '0', #未知
        'tradeAreaId': '6569501e15720000b8008864', #地区id
    }

    json_data = {}

    response = requests.post(
        'https://ymixy.cloudyee.com/api/activity/coupon/getMyCouponLists',
        params=params,
        headers=headers,
        json=json_data,
    )
    return response.json()['data']

data = id_message(source_id['authorization'])
entityList1=data['entityList']
print(f"当前{source_id['name']}共有{data['rowCount']}张卷")
for i in entityList1:
    if '1元' in i['couponInfo']['name']:
        num_1_before_1+=1
    if '3元' in i['couponInfo']['name']:
        num_3_before_1+=1
print(f"{num_1_before_1}张一元卷，{num_3_before_1}张三元卷")

data = id_message(target_id['authorization'])
entityList=data['entityList']
print(f"当前{target_id['name']}共有{data['rowCount']}张卷")
for i in entityList:
    if '1元' in i['couponInfo']['name']:
        num_1_before_2+=1
    if '3元' in i['couponInfo']['name']:
        num_3_before_2+=1
print(f"{num_1_before_2}张一元卷，{num_3_before_2}张三元卷")


for i in entityList1:
    if juan_name in i['couponInfo']['name']:
        print(f"{source_id['name']}正在给{target_id['name']}赠送{juan_name}卷")
        receive_coupon(target_id['authorization'], i['couponInfo']['id'], source_id['userId'], i['id'])
        print(f"已赠送{num}张")
print(f"成功赠送{num}张{juan_name}卷")

data = id_message(source_id['authorization'])
entityList=data['entityList']
print(f"当前{source_id['name']}共有{data['rowCount']}张卷")
for i in entityList:
    if '1元' in i['couponInfo']['name']:
        num_1_after_1+=1
    if '3元' in i['couponInfo']['name']:
        num_3_after_1+=1
print(f"{num_1_after_1}张一元卷，{num_3_after_1}张三元卷")
print(f"-----赠送前后{source_id['name']}的卷的变化-----")
print(f"3元卷：{num_3_before_1}->{num_3_after_1}")
print(f"1元卷：{num_1_before_1}->{num_1_after_1}")

data = id_message(target_id['authorization'])
entityList=data['entityList']
print(f"当前{target_id['name']}共有{data['rowCount']}张卷")
for i in entityList:
    if '1元' in i['couponInfo']['name']:
        num_1_after_2+=1
    if '3元' in i['couponInfo']['name']:
        num_3_after_2+=1
print(f"{num_1_after_2}张一元卷，{num_3_after_2}张三元卷")
print(f"-----赠送前后{target_id['name']}的卷的变化-----")
print(f"3元卷：{num_3_before_2}->{num_3_after_2}")
print(f"1元卷：{num_1_before_2}->{num_1_after_2}")


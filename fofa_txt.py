import json

input_file_path = 'input_data.json'
output_file_path = r'C:\Users\<USER>\自用程序\fofa爬虫\LibreChat.txt'


def generate_link(asset):
    return asset['link']

with open(input_file_path, 'r', encoding='utf-8') as f:
    data = json.load(f)

links = [generate_link(asset) for asset in data["data"]["assets"]]

with open(output_file_path, 'a', encoding='utf-8') as f:
    f.write('\n'.join(links) + '\n')

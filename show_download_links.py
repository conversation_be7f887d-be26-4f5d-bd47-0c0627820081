#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示游戏的下载链接
"""

import sys
import os
import json
from bs4 import BeautifulSoup

def show_download_links_from_html():
    """从保存的HTML文件中提取下载链接"""
    html_file = "斌哥游戏更新监控/领主.html"
    
    if not os.path.exists(html_file):
        print("❌ HTML文件不存在")
        return
    
    print("🔍 从HTML文件中提取下载链接...")
    print("=" * 50)
    
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 提取游戏名称
    title_elem = soup.find('title')
    game_name = title_elem.get_text(strip=True) if title_elem else "未知游戏"
    print(f"🎮 游戏名称: {game_name}")
    
    # 查找网盘链接容器（即使是隐藏的）
    wangpan_div = soup.find('div', id='wangpan')
    if wangpan_div:
        print("✅ 找到网盘下载区域")
        
        # 查找所有下载链接
        links = wangpan_div.find_all('a', href=True)
        print(f"📦 找到 {len(links)} 个下载链接:")
        print()
        
        for i, link in enumerate(links, 1):
            href = link.get('href')
            text = link.get_text(strip=True)
            
            # 识别网盘类型
            pan_type = "未知网盘"
            if 'feijipan.com' in href:
                pan_type = "奶牛网盘"
            elif 'yunpan.com' in href:
                pan_type = "360网盘"
            elif 'baidu.com' in href or 'pan.baidu' in href:
                pan_type = "百度网盘"
            
            # 提取密码
            password = None
            if '?pwd=' in href:
                password = href.split('?pwd=')[1].split('&')[0]
            
            print(f"  {i}. {pan_type}")
            print(f"     🔗 链接: {href}")
            if password:
                print(f"     🔑 密码: {password}")
            print(f"     📝 显示文本: {text}")
            print()
    else:
        print("❌ 未找到网盘下载区域")

def show_download_links_from_json():
    """从JSON文件中显示下载链接"""
    json_file = "斌哥游戏更新监控/game_downloads.json"
    
    if not os.path.exists(json_file):
        print("❌ 下载数据文件不存在")
        return
    
    print("\n" + "=" * 50)
    print("📊 从下载数据库中查看最近的游戏:")
    print("=" * 50)
    
    with open(json_file, 'r', encoding='utf-8') as f:
        downloads_data = json.load(f)
    
    # 显示有下载链接的游戏
    games_with_links = []
    for game_id, data in downloads_data.items():
        if data.get('total_links', 0) > 0:
            games_with_links.append((game_id, data))
    
    if games_with_links:
        print(f"✅ 找到 {len(games_with_links)} 个有下载链接的游戏:")
        
        for game_id, data in games_with_links:
            game_name = data['game_info'].get('name', f'游戏_{game_id}')
            print(f"\n📱 {game_name} (ID: {game_id})")
            print(f"   下载链接数: {data['total_links']}")
            print(f"   提取时间: {data.get('extraction_time', '未知')}")
            print(f"   提取原因: {data.get('extraction_reason', '未知')}")
            
            for i, link in enumerate(data.get('download_links', []), 1):
                print(f"   {i}. {link['pan_type']}")
                print(f"      🔗 {link['url']}")
                if link.get('password'):
                    print(f"      🔑 密码: {link['password']}")
    else:
        print("⚠️ 数据库中暂无有效的下载链接")
        print("💡 这可能是因为:")
        print("   1. 网站结构发生了变化")
        print("   2. 下载链接被隐藏或需要JavaScript触发")
        print("   3. 提取逻辑需要更新")

if __name__ == "__main__":
    show_download_links_from_html()
    show_download_links_from_json()

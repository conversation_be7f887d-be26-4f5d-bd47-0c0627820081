import requests
import json

url = 'http://jwsys.gdpu.edu.cn/xsxxxggl/xsxxwh_cxXsxkxx.html?gnmkdm=N100801&su=2130506164'
data = {
    'queryModel.showCount': '45',
    'queryModel.currentPage': '1'
}
headers = {
    "Cookie": "JSESSIONID=5BB36C879608436F2B66D9117646CB83; route=979e8ca7ca476c5c3460db7fc11ce328"
}
req = requests.post(url, data=data, headers=headers)
a = req.json()
print('课程数:', len(a['items']))
for i in a['items']:
    b = i.get('sksj', '没有上课时间')
    print('课程名称:{}   任课老师:{}  上课时间:{}'.format(i['kcmc'], i['jsxm'], b))

"""
简单验证脚本
检查活跃度分析工具是否可以正常导入和运行
"""

import os
import sys

def check_files():
    """检查必要文件"""
    print("📁 检查文件...")
    
    files = [
        'config.py',
        'chat_activity_analyzer.py', 
        'quick_activity_check.py',
        'manage_chats.py',
        'start_activity_analyzer.py'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - 缺失")
            return False
    
    return True

def test_imports():
    """测试导入"""
    print("\n📦 测试导入...")
    
    try:
        print("导入 config...")
        from config import Config
        print("✅ config 导入成功")
        
        print("导入 chat_activity_analyzer...")
        from chat_activity_analyzer import ChatActivityAnalyzer
        print("✅ chat_activity_analyzer 导入成功")
        
        print("导入 quick_activity_check...")
        from quick_activity_check import QuickActivityChecker  
        print("✅ quick_activity_check 导入成功")
        
        print("导入 manage_chats...")
        from manage_chats import ChatManager
        print("✅ manage_chats 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def main():
    print("🔍 活跃度分析工具验证")
    print("=" * 40)
    
    if not check_files():
        print("\n❌ 文件检查失败")
        return
    
    if not test_imports():
        print("\n❌ 导入测试失败")
        return
    
    print("\n🎉 验证通过！")
    print("\n📋 使用方法:")
    print("1. 双击运行 start_activity_tools.bat")
    print("2. 或运行 python start_activity_analyzer.py")
    print("3. 或直接运行单个工具:")
    print("   - python chat_activity_analyzer.py")
    print("   - python quick_activity_check.py")

if __name__ == "__main__":
    main()

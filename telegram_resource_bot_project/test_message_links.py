"""
测试消息链接生成功能
验证不同类型频道的链接生成
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def generate_message_link(chat_entity, message) -> str:
    """生成消息链接"""
    try:
        # 获取频道/群组的用户名或ID
        if hasattr(chat_entity, 'username') and chat_entity.username:
            # 公开频道，使用用户名
            chat_identifier = chat_entity.username
            link = f"https://t.me/{chat_identifier}/{message.id}"
            logger.info(f"公开频道链接: {link}")
        else:
            # 私有频道或群组，使用ID
            # 对于私有频道，链接格式为 https://t.me/c/频道ID/消息ID
            # 需要去掉ID前面的-100前缀
            chat_id = str(chat_entity.id)
            if chat_id.startswith('-100'):
                chat_id = chat_id[4:]  # 去掉-100前缀
            link = f"https://t.me/c/{chat_id}/{message.id}"
            logger.info(f"私有频道链接: {link}")
        
        return link
        
    except Exception as e:
        logger.warning(f"生成消息链接失败: {e}")
        return None

async def test_message_links():
    """测试消息链接生成"""
    # 加载配置
    load_dotenv('.env')
    
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    bot_token = os.getenv('BOT_TOKEN')
    user_id = int(os.getenv('USER_ID'))
    
    # 连接客户端
    client = TelegramClient('test_links_session', api_id, api_hash)
    await client.start()
    
    bot = Bot(token=bot_token)
    
    try:
        # 获取对话列表
        dialogs = await client.get_dialogs()
        
        # 找到频道和群组
        channels_and_groups = []
        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channels_and_groups.append(dialog)
        
        logger.info(f"找到 {len(channels_and_groups)} 个频道和群组")
        
        # 测试前5个频道的链接生成
        test_results = []
        
        for i, dialog in enumerate(channels_and_groups[:5]):
            logger.info(f"\n=== 测试频道 {i+1}: {dialog.title} ===")
            
            try:
                # 获取频道信息
                entity = dialog.entity
                logger.info(f"频道ID: {entity.id}")
                logger.info(f"频道用户名: {getattr(entity, 'username', '无')}")
                logger.info(f"频道类型: {type(entity).__name__}")
                
                # 获取最新的一条消息
                async for message in client.iter_messages(entity, limit=1):
                    logger.info(f"消息ID: {message.id}")
                    logger.info(f"消息时间: {message.date}")
                    logger.info(f"消息内容: {message.text[:50] if message.text else '(无文本)'}...")
                    
                    # 生成链接
                    link = await generate_message_link(entity, message)
                    
                    if link:
                        test_results.append({
                            'channel': dialog.title,
                            'link': link,
                            'message_id': message.id,
                            'is_public': bool(getattr(entity, 'username', None))
                        })
                        
                        # 发送测试结果到用户
                        test_text = f"🧪 **链接测试 #{i+1}**\n"
                        test_text += f"📺 频道：{dialog.title}\n"
                        test_text += f"🆔 消息ID：{message.id}\n"
                        test_text += f"🔓 公开频道：{'是' if getattr(entity, 'username', None) else '否'}\n"
                        test_text += f"🔗 生成链接：{link}\n"
                        test_text += f"📝 消息预览：{message.text[:100] if message.text else '(无文本)'}..."
                        
                        await bot.send_message(
                            chat_id=user_id,
                            text=test_text,
                            parse_mode='Markdown'
                        )
                        
                        logger.info(f"✅ 链接生成成功: {link}")
                    else:
                        logger.warning(f"❌ 链接生成失败")
                    
                    break  # 只测试一条消息
                
            except Exception as e:
                logger.error(f"测试频道 {dialog.title} 时出错: {e}")
            
            await asyncio.sleep(1)
        
        # 发送测试总结
        summary = f"🧪 **链接生成测试完成**\n\n"
        summary += f"📊 测试了 {len(test_results)} 个频道\n"
        summary += f"✅ 成功生成 {len([r for r in test_results if r['link']])} 个链接\n\n"
        
        summary += "📋 **测试结果：**\n"
        for i, result in enumerate(test_results, 1):
            summary += f"{i}. {result['channel'][:20]}{'...' if len(result['channel']) > 20 else ''}\n"
            summary += f"   {'🔓 公开' if result['is_public'] else '🔒 私有'} | 消息ID: {result['message_id']}\n"
        
        summary += f"\n💡 **说明：**\n"
        summary += f"• 🔓 公开频道：可直接点击链接访问\n"
        summary += f"• 🔒 私有频道：需要先加入频道才能访问\n"
        summary += f"• 所有链接都已在上方消息中提供"
        
        await bot.send_message(
            chat_id=user_id,
            text=summary,
            parse_mode='Markdown'
        )
        
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    logger.info("🧪 开始测试消息链接生成...")
    await test_message_links()
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())

"""
频道/群组活跃度分析工具
用于分析所有频道/群组的最后消息时间和成员数量，识别不活跃的频道/群组
"""

import asyncio
import logging
import os
import csv
from datetime import datetime, timezone, timedelta
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ChatActivityAnalyzer:
    def __init__(self):
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.proxy_url = getattr(Config, 'PROXY_URL', None)
        self.client = None
        self.chat_data = []

        # 时区配置
        self.beijing_tz = timezone(timedelta(hours=8))  # 北京时间时区

    def utc_to_beijing(self, utc_time):
        """将UTC时间转换为北京时间"""
        if utc_time is None:
            return None

        # 确保输入时间有时区信息
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)

        # 转换为北京时间
        beijing_time = utc_time.astimezone(self.beijing_tz)
        return beijing_time

    def format_beijing_time(self, utc_time, format_str='%Y-%m-%d %H:%M:%S'):
        """格式化UTC时间为北京时间字符串"""
        if utc_time is None:
            return "未知时间"

        beijing_time = self.utc_to_beijing(utc_time)
        return beijing_time.strftime(format_str)

    async def init_client(self):
        """初始化客户端"""
        try:
            # 配置代理（使用与主程序相同的方式）
            proxy = None
            if self.proxy_url:
                logger.info(f"🌐 使用代理: {self.proxy_url}")
                try:
                    # 解析代理URL，支持格式：http://host:port 或 socks5://host:port
                    if self.proxy_url.startswith('http://'):
                        # 解析HTTP代理
                        proxy_part = self.proxy_url.replace('http://', '')
                        if '@' in proxy_part:
                            # 包含认证信息
                            auth_part, addr_part = proxy_part.split('@')
                            username, password = auth_part.split(':')
                            addr, port = addr_part.split(':')
                            proxy = {
                                'proxy_type': 'HTTP',
                                'addr': addr,
                                'port': int(port),
                                'username': username,
                                'password': password
                            }
                        else:
                            # 不包含认证信息
                            addr, port = proxy_part.split(':')
                            proxy = {
                                'proxy_type': 'HTTP',
                                'addr': addr,
                                'port': int(port)
                            }
                        logger.info("✅ HTTP代理配置成功")

                    elif self.proxy_url.startswith('socks5://'):
                        # 解析SOCKS5代理
                        proxy_part = self.proxy_url.replace('socks5://', '')
                        if '@' in proxy_part:
                            # 包含认证信息
                            auth_part, addr_part = proxy_part.split('@')
                            username, password = auth_part.split(':')
                            addr, port = addr_part.split(':')
                            proxy = {
                                'proxy_type': 'SOCKS5',
                                'addr': addr,
                                'port': int(port),
                                'username': username,
                                'password': password
                            }
                        else:
                            # 不包含认证信息
                            addr, port = proxy_part.split(':')
                            proxy = {
                                'proxy_type': 'SOCKS5',
                                'addr': addr,
                                'port': int(port)
                            }
                        logger.info("✅ SOCKS5代理配置成功")
                    else:
                        logger.warning(f"⚠️ 不支持的代理类型: {self.proxy_url}")

                except Exception as e:
                    logger.error(f"❌ 代理配置失败: {e}")
                    logger.info("🔄 将尝试直连")
                    proxy = None

            # 创建客户端（使用与主程序相同的方式）
            self.client = TelegramClient('user_session', self.api_id, self.api_hash, proxy=proxy)
            await self.client.start()
            logger.info("✅ 客户端初始化成功")

        except Exception as e:
            logger.error(f"❌ 客户端初始化失败: {e}")
            raise

    async def get_last_message_time(self, entity):
        """获取频道/群组的最后一条消息时间"""
        try:
            # 获取最后一条消息
            async for message in self.client.iter_messages(entity, limit=1):
                return message.date
            return None  # 没有消息
        except Exception as e:
            logger.warning(f"获取最后消息时间失败: {e}")
            return None

    async def analyze_all_chats(self):
        """分析所有频道和群组的活跃度"""
        try:
            logger.info("📋 正在获取所有对话...")
            dialogs = await self.client.get_dialogs()
            logger.info(f"📋 获取到 {len(dialogs)} 个对话")

            channels_and_groups = []
            for dialog in dialogs:
                if isinstance(dialog.entity, Channel) or isinstance(dialog.entity, Chat):
                    channels_and_groups.append(dialog)

            logger.info(f"🔍 找到 {len(channels_and_groups)} 个频道和群组")
            logger.info("📊 开始分析活跃度...")

            current_time = datetime.now(timezone.utc)
            
            for i, dialog in enumerate(channels_and_groups, 1):
                try:
                    logger.info(f"📈 分析进度: {i}/{len(channels_and_groups)} - {dialog.title}")
                    
                    # 确定类型
                    if isinstance(dialog.entity, Channel):
                        if dialog.entity.megagroup:
                            chat_type = "超级群组"
                            type_icon = "🔒"
                        else:
                            chat_type = "频道"
                            type_icon = "📺"
                    else:
                        chat_type = "普通群组"
                        type_icon = "👥"

                    # 获取成员数量
                    members_count = getattr(dialog.entity, 'participants_count', 'N/A')
                    
                    # 获取最后消息时间
                    last_message_time = await self.get_last_message_time(dialog.entity)
                    
                    # 计算距今天数
                    if last_message_time:
                        days_ago = (current_time - last_message_time).days
                        # 转换为北京时间显示
                        last_message_str = self.format_beijing_time(last_message_time)
                    else:
                        days_ago = float('inf')  # 无消息的设为无穷大
                        last_message_str = "无消息"

                    # 获取用户名信息
                    username = getattr(dialog.entity, 'username', None)
                    username_info = f"@{username}" if username else "私有"

                    # 存储数据
                    chat_info = {
                        'id': dialog.entity.id,
                        'title': dialog.title,
                        'type': chat_type,
                        'type_icon': type_icon,
                        'username': username_info,
                        'members': members_count,
                        'last_message_time': last_message_time,
                        'last_message_str': last_message_str,
                        'days_ago': days_ago
                    }
                    
                    self.chat_data.append(chat_info)
                    
                    # 添加延迟避免API限制
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"❌ 分析 {dialog.title} 时出错: {e}")
                    continue

            logger.info(f"✅ 分析完成，共分析了 {len(self.chat_data)} 个频道/群组")
            
        except Exception as e:
            logger.error(f"❌ 分析过程失败: {e}")
            raise

    def display_results(self, sort_by='activity'):
        """显示分析结果"""
        if not self.chat_data:
            print("❌ 没有数据可显示")
            return

        # 排序数据
        if sort_by == 'activity':
            # 按活跃度排序（最近活跃的在前）
            sorted_data = sorted(self.chat_data, key=lambda x: x['days_ago'])
        elif sort_by == 'members':
            # 按成员数排序（成员多的在前）
            sorted_data = sorted(self.chat_data, key=lambda x: x['members'] if isinstance(x['members'], int) else 0, reverse=True)
        elif sort_by == 'name':
            # 按名称排序
            sorted_data = sorted(self.chat_data, key=lambda x: x['title'])
        else:
            sorted_data = self.chat_data

        # 显示结果
        print(f"\n{'='*120}")
        print(f"📊 频道/群组活跃度分析报告")
        print(f"{'='*120}")
        print(f"📈 总计: {len(sorted_data)} 个频道/群组")
        # 显示北京时间
        current_beijing_time = self.format_beijing_time(datetime.now(timezone.utc))
        print(f"🕒 分析时间: {current_beijing_time} (北京时间)")
        sort_names = {'activity': '按活跃度', 'members': '按成员数', 'name': '按名称'}
        print(f"📋 排序方式: {sort_names[sort_by]}")
        print(f"{'='*120}")
        
        # 表头
        print(f"{'序号':<4} {'类型':<4} {'频道/群组名称':<30} {'用户名':<15} {'成员数':<8} {'最后消息时间(北京)':<22} {'距今天数':<8}")
        print(f"{'-'*120}")
        
        # 数据行
        for i, chat in enumerate(sorted_data, 1):
            title = chat['title'][:28] + '..' if len(chat['title']) > 30 else chat['title']
            username = chat['username'][:13] + '..' if len(chat['username']) > 15 else chat['username']
            members = str(chat['members']) if chat['members'] != 'N/A' else 'N/A'
            
            if chat['days_ago'] == float('inf'):
                days_str = "无消息"
            elif chat['days_ago'] == 0:
                days_str = "今天"
            elif chat['days_ago'] == 1:
                days_str = "1天前"
            else:
                days_str = f"{chat['days_ago']}天前"
            
            print(f"{i:<4} {chat['type_icon']:<4} {title:<30} {username:<15} {members:<8} {chat['last_message_str']:<22} {days_str:<8}")

        # 统计信息
        print(f"\n{'='*120}")
        print(f"📊 活跃度统计:")
        
        active_today = sum(1 for chat in self.chat_data if chat['days_ago'] == 0)
        active_week = sum(1 for chat in self.chat_data if chat['days_ago'] <= 7)
        active_month = sum(1 for chat in self.chat_data if chat['days_ago'] <= 30)
        inactive_month = sum(1 for chat in self.chat_data if chat['days_ago'] > 30)
        no_messages = sum(1 for chat in self.chat_data if chat['days_ago'] == float('inf'))
        
        print(f"🟢 今天活跃: {active_today} 个")
        print(f"🟡 一周内活跃: {active_week} 个")
        print(f"🟠 一月内活跃: {active_month} 个")
        print(f"🔴 超过一月未活跃: {inactive_month} 个")
        print(f"⚫ 无消息记录: {no_messages} 个")

    def export_to_csv(self, filename=None):
        """导出结果到CSV文件"""
        if not self.chat_data:
            print("❌ 没有数据可导出")
            return

        if filename is None:
            filename = f"chat_activity_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['序号', '类型', '频道群组名称', '用户名', 'ID', '成员数', '最后消息时间', '距今天数']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                
                # 按活跃度排序
                sorted_data = sorted(self.chat_data, key=lambda x: x['days_ago'])
                
                for i, chat in enumerate(sorted_data, 1):
                    days_str = "无消息" if chat['days_ago'] == float('inf') else f"{chat['days_ago']}天前"
                    
                    writer.writerow({
                        '序号': i,
                        '类型': chat['type'],
                        '频道群组名称': chat['title'],
                        '用户名': chat['username'],
                        'ID': chat['id'],
                        '成员数': chat['members'],
                        '最后消息时间': chat['last_message_str'],
                        '距今天数': days_str
                    })
            
            logger.info(f"✅ 数据已导出到: {filename}")
            print(f"✅ 数据已导出到: {filename}")
            
        except Exception as e:
            logger.error(f"❌ 导出失败: {e}")
            print(f"❌ 导出失败: {e}")

async def main():
    """主函数"""
    analyzer = ChatActivityAnalyzer()

    try:
        # 显示配置信息
        print("📊 Telegram 频道/群组活跃度分析工具")
        print("=" * 60)
        print(f"📋 配置信息:")
        print(f"   API ID: {analyzer.api_id}")
        print(f"   代理设置: {'已配置' if analyzer.proxy_url else '未配置'}")
        if analyzer.proxy_url:
            print(f"   代理地址: {analyzer.proxy_url}")
        print("=" * 60)

        await analyzer.init_client()
        await analyzer.analyze_all_chats()
        
        # 显示结果菜单
        while True:
            print(f"\n📊 分析结果显示选项:")
            print(f"1. 按活跃度排序显示（推荐）")
            print(f"2. 按成员数排序显示")
            print(f"3. 按名称排序显示")
            print(f"4. 导出到CSV文件")
            print(f"5. 退出")
            
            try:
                choice = input(f"\n请选择显示方式 (1-5): ").strip()
                
                if choice == '1':
                    analyzer.display_results('activity')
                elif choice == '2':
                    analyzer.display_results('members')
                elif choice == '3':
                    analyzer.display_results('name')
                elif choice == '4':
                    analyzer.export_to_csv()
                elif choice == '5':
                    print("👋 再见！")
                    break
                else:
                    print("❌ 无效选择，请输入 1-5")
                    
            except KeyboardInterrupt:
                print("\n\n❌ 用户中断操作")
                break
            except Exception as e:
                print(f"❌ 操作出错: {e}")

    except Exception as e:
        logger.error(f"❌ 程序运行失败: {e}")
    finally:
        if analyzer.client:
            await analyzer.client.disconnect()
            logger.info("🔌 客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main())

# Telegram资源搜索机器人项目

## 📁 项目文件结构

```
telegram_resource_bot_project/
├── telegram_resource_bot.py    # 主程序文件（核心功能）
├── config.py                   # 配置管理文件
├── start_bot.py                # 启动脚本（支持.env文件）
├── requirements.txt            # Python依赖包列表
├── .env.example               # 环境变量配置示例
├── start.bat                  # Windows一键启动脚本
├── README.md                  # 详细使用说明文档
├── test_connection.py         # 连接测试脚本
└── 项目说明.md                # 本文件（项目结构说明）
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd telegram_resource_bot_project
pip install -r requirements.txt
```

### 2. 配置环境变量
```bash
# 复制配置文件模板
copy .env.example .env

# 编辑 .env 文件，填入您的配置信息
```

### 3. 测试连接
```bash
# 先测试连接是否正常
python start_bot.py --test
```

### 4. 启动机器人
```bash
# 方法1：使用Python脚本
python start_bot.py

# 方法2：在Windows上双击
start.bat

# 方法3：直接运行主程序
python telegram_resource_bot.py
```

## 📋 必需配置项

在 `.env` 文件中需要配置以下信息：

- `USER_ID` - 您的Telegram用户ID
- `API_ID` - Telegram API ID
- `API_HASH` - Telegram API Hash
- `BOT_TOKEN` - 机器人Token

## 🎯 主要功能

- ✅ 搜索所有已加入频道的消息
- ✅ 支持文字和文件名匹配
- ✅ 可指定搜索时间范围（1-30天）
- ✅ 自动转发匹配结果
- ✅ 智能频率控制
- ✅ 权限验证
- ✅ 代理支持

## 📞 使用命令

- `/start` - 显示欢迎信息
- `/help` - 显示帮助文档
- `/search 关键词 天数` - 搜索资源

## 📖 详细说明

请查看 `README.md` 文件获取完整的使用说明和配置指南。

---
**注意**：请确保遵守Telegram的使用条款和相关法律法规。

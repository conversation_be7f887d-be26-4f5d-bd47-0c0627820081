@echo off
chcp 65001 >nul
title Telegram 频道/群组活跃度分析工具

echo.
echo ========================================
echo  Telegram 频道/群组活跃度分析工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "config.py" (
    echo ❌ 错误: 未找到 config.py
    echo 请确保所有文件都在当前目录中
    pause
    exit /b 1
)

if not exist ".env" (
    echo ⚠️  警告: 未找到 .env 配置文件
    echo 请确保已正确配置 .env 文件
    echo.
)

echo ✅ 环境检查通过
echo.
echo 🚀 启动活跃度分析工具...
echo.

REM 运行Python脚本
python start_activity_analyzer.py

echo.
echo 📊 分析工具已退出
pause

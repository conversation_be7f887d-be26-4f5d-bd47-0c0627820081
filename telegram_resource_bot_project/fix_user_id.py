"""
修复用户ID的脚本
用于更新.env文件中的USER_ID为当前登录用户的ID
"""

import os
import asyncio
import logging
from dotenv import load_dotenv, set_key
from telethon import TelegramClient
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def fix_user_id():
    """修复用户ID"""
    try:
        logger.info("🔍 检查当前登录用户...")
        
        # 初始化用户客户端
        client = TelegramClient('user_session', Config.API_ID, Config.API_HASH)
        await client.start()
        
        # 获取当前登录用户信息
        me = await client.get_me()
        current_user_id = me.id
        configured_user_id = Config.USER_ID
        
        logger.info(f"当前登录用户: {me.first_name} (@{me.username or 'N/A'})")
        logger.info(f"当前用户ID: {current_user_id}")
        logger.info(f"配置的用户ID: {configured_user_id}")
        
        if current_user_id == configured_user_id:
            logger.info("✅ 用户ID匹配，无需修复")
        else:
            logger.info("⚠️  用户ID不匹配，正在修复...")
            
            # 检查是否是机器人账号
            if me.bot:
                logger.error("❌ 当前登录的是机器人账号，不是用户账号！")
                logger.error("请删除 user_session.session 文件，然后用您的个人账号重新登录")
                await client.disconnect()
                return False
            
            # 更新.env文件
            if os.path.exists('.env'):
                set_key('.env', 'USER_ID', str(current_user_id))
                logger.info(f"✅ 已更新 .env 文件中的 USER_ID 为: {current_user_id}")
            else:
                logger.error("❌ 未找到 .env 文件")
                await client.disconnect()
                return False
        
        await client.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("🚀 开始修复用户ID...")
    
    # 加载配置
    if os.path.exists('.env'):
        load_dotenv('.env')
        logger.info("✅ 已加载 .env 配置文件")
    else:
        logger.error("❌ 未找到 .env 文件")
        return
    
    try:
        # 验证基本配置
        if not Config.API_ID or not Config.API_HASH:
            logger.error("❌ API_ID 或 API_HASH 未配置")
            return
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return
    
    # 修复用户ID
    success = await fix_user_id()
    
    if success:
        logger.info("🎉 修复完成！现在可以启动机器人了")
        logger.info("运行命令: python start_bot.py --test")
    else:
        logger.error("❌ 修复失败")
        logger.info("建议步骤:")
        logger.info("1. 删除 user_session.session 文件")
        logger.info("2. 确保使用您的个人Telegram账号（不是机器人）")
        logger.info("3. 重新运行此脚本")

if __name__ == "__main__":
    asyncio.run(main())

"""
验证搜索结果发送
模拟搜索结果的发送过程
"""

import os
import asyncio
import logging
from datetime import datetime, timezone
from dotenv import load_dotenv
from telegram import Bo<PERSON>
from telethon import TelegramClient

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def simulate_search_result():
    """模拟发送搜索结果"""
    # 加载配置
    load_dotenv('.env')
    
    bot_token = os.getenv('BOT_TOKEN')
    user_id = int(os.getenv('USER_ID'))
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    
    logger.info(f"🎯 目标用户ID: {user_id}")
    
    try:
        # 创建机器人和用户客户端
        bot = Bot(token=bot_token)
        client = TelegramClient('verify_session', api_id, api_hash)
        await client.start()
        
        logger.info("✅ 客户端连接成功")
        
        # 模拟搜索结果头部
        header = f"🎯 **#1** | 📺 豌豆鱼 | 📅 {datetime.now().strftime('%m-%d %H:%M')} | 🔍 文本消息"
        
        logger.info(f"📤 发送头部信息到用户 {user_id}")
        header_msg = await bot.send_message(
            chat_id=user_id,
            text=header,
            parse_mode='Markdown'
        )
        logger.info(f"✅ 头部信息发送成功，消息ID: {header_msg.message_id}")
        
        # 模拟搜索结果内容
        content = """🔍 这是一条模拟的搜索结果消息

📋 内容：包含关键词"一键"的测试消息
🔗 来源：豌豆鱼频道
⏰ 时间：刚刚

这条消息用于验证搜索结果是否能正确发送到您的私聊中。
如果您看到这条消息，说明转发功能工作正常！"""
        
        logger.info(f"📤 发送内容消息到用户 {user_id}")
        content_msg = await bot.send_message(
            chat_id=user_id,
            text=content
        )
        logger.info(f"✅ 内容消息发送成功，消息ID: {content_msg.message_id}")
        
        # 发送确认消息
        confirm = f"""✅ **验证完成**

📊 发送统计：
• 头部消息ID: {header_msg.message_id}
• 内容消息ID: {content_msg.message_id}
• 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• 目标用户: {user_id}

如果您看到上面的消息，说明搜索结果转发功能正常工作！"""
        
        confirm_msg = await bot.send_message(
            chat_id=user_id,
            text=confirm,
            parse_mode='Markdown'
        )
        logger.info(f"✅ 确认消息发送成功，消息ID: {confirm_msg.message_id}")
        
        # 获取聊天信息
        chat = await bot.get_chat(user_id)
        logger.info(f"📋 聊天信息:")
        logger.info(f"   Chat ID: {chat.id}")
        logger.info(f"   Chat Type: {chat.type}")
        logger.info(f"   First Name: {chat.first_name}")
        logger.info(f"   Username: @{chat.username if chat.username else 'N/A'}")
        
        await client.disconnect()
        
        logger.info("🎉 验证完成！请检查您与机器人的私聊")
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")
        
        if "Forbidden" in str(e):
            logger.error("🔒 权限问题：请确保您已与机器人开始对话")
        elif "Chat not found" in str(e):
            logger.error("👤 用户ID问题：请检查配置中的USER_ID")

async def main():
    """主函数"""
    logger.info("🧪 开始验证搜索结果发送...")
    await simulate_search_result()
    logger.info("✅ 验证完成")

if __name__ == "__main__":
    asyncio.run(main())

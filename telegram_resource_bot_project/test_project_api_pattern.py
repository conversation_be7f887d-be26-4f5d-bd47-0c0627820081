"""
测试这个项目的实际API请求模式
验证提前终止机制对API请求数量的影响
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta, timezone
from telethon import TelegramClient
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ProjectAPITester:
    def __init__(self):
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.client = None

    async def init_client(self):
        """初始化客户端"""
        self.client = TelegramClient('test_session', self.api_id, self.api_hash)
        await self.client.start()
        logger.info("✅ 客户端初始化成功")

    async def test_project_pattern(self, days=1, limit=1000):
        """测试项目的实际API请求模式"""
        try:
            # 计算时间范围
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days)
            
            logger.info(f"🧪 测试参数:")
            logger.info(f"   时间范围: {start_date} 到 {end_date}")
            logger.info(f"   消息限制: {limit}")
            
            # 获取测试频道
            dialogs = await self.client.get_dialogs(limit=10)
            test_dialog = None
            
            for dialog in dialogs:
                try:
                    # 检查是否有足够的消息
                    messages = await self.client.get_messages(dialog.entity, limit=1)
                    if messages:
                        test_dialog = dialog
                        break
                except:
                    continue
            
            if not test_dialog:
                logger.error("❌ 没有找到可测试的频道")
                return
            
            logger.info(f"📺 测试频道: {test_dialog.title}")
            
            # 模拟项目的消息获取逻辑
            messages = []
            checked_count = 0
            api_request_count = 0
            start_time = time.time()
            
            logger.info("🔍 开始获取消息（模拟项目逻辑）...")
            
            # 记录每100条消息的时间点（推测的API请求边界）
            last_batch_time = start_time
            
            async for message in self.client.iter_messages(test_dialog.entity, limit=limit):
                messages.append(message)
                checked_count += 1
                
                # 每100条记录一次（推测的API批次）
                if checked_count % 100 == 0:
                    current_time = time.time()
                    batch_time = current_time - last_batch_time
                    api_request_count += 1
                    
                    logger.info(f"📦 推测API请求 #{api_request_count}: 获取消息{checked_count-99}-{checked_count} (耗时: {batch_time:.2f}秒)")
                    logger.info(f"   最新消息时间: {message.date}")
                    
                    last_batch_time = current_time
                
                # 项目的关键逻辑：提前终止检查
                if message.date < start_date:
                    logger.info(f"🛑 提前终止: 消息时间 {message.date} < 搜索开始时间 {start_date}")
                    break
            
            # 处理最后一个不完整的批次
            if checked_count % 100 != 0:
                api_request_count += 1
                final_batch_time = time.time() - last_batch_time
                logger.info(f"📦 最后API请求 #{api_request_count}: 获取消息{(checked_count//100)*100+1}-{checked_count} (耗时: {final_batch_time:.2f}秒)")
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 分析时间范围内的消息
            in_range_count = 0
            for msg in messages:
                if msg.date >= start_date and msg.date <= end_date:
                    in_range_count += 1
            
            # 输出统计结果
            logger.info(f"\n{'='*60}")
            logger.info(f"📊 测试结果统计")
            logger.info(f"{'='*60}")
            logger.info(f"🔢 总检查消息数: {checked_count}")
            logger.info(f"📅 时间范围内消息数: {in_range_count}")
            logger.info(f"🌐 推测API请求次数: {api_request_count}")
            logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
            logger.info(f"📈 平均每次API请求: {checked_count/api_request_count:.1f} 条消息")
            logger.info(f"🚀 获取速度: {checked_count/total_time:.1f} 条/秒")
            
            # 分析提前终止的效果
            theoretical_requests = (limit + 99) // 100  # 理论上需要的API请求次数
            saved_requests = theoretical_requests - api_request_count
            
            logger.info(f"\n📈 提前终止效果分析:")
            logger.info(f"   理论API请求次数: {theoretical_requests}")
            logger.info(f"   实际API请求次数: {api_request_count}")
            logger.info(f"   节省API请求次数: {saved_requests}")
            logger.info(f"   节省比例: {(saved_requests/theoretical_requests)*100:.1f}%")
            
            # 显示消息时间分布
            if messages:
                logger.info(f"\n📅 消息时间分布:")
                logger.info(f"   最新消息: {messages[0].date}")
                logger.info(f"   最旧消息: {messages[-1].date}")
                logger.info(f"   时间跨度: {messages[0].date - messages[-1].date}")

        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")

    async def test_different_time_ranges(self):
        """测试不同时间范围的影响"""
        test_cases = [
            {"days": 1, "limit": 1000, "desc": "1天范围"},
            {"days": 3, "limit": 1000, "desc": "3天范围"},
            {"days": 7, "limit": 1000, "desc": "7天范围"},
            {"days": 1, "limit": 500, "desc": "1天范围(限制500)"},
        ]
        
        for case in test_cases:
            logger.info(f"\n{'='*80}")
            logger.info(f"🧪 测试案例: {case['desc']}")
            logger.info(f"{'='*80}")
            
            await self.test_project_pattern(case['days'], case['limit'])
            
            # 等待一下，避免API限制
            await asyncio.sleep(3)

async def main():
    """主函数"""
    tester = ProjectAPITester()
    
    try:
        await tester.init_client()
        
        # 测试不同时间范围
        await tester.test_different_time_ranges()
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        if tester.client:
            await tester.client.disconnect()
            logger.info("🔌 客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main())

"""
网络连接诊断脚本
用于检查Telegram API连接状态
"""

import os
import asyncio
import logging
import aiohttp
from dotenv import load_dotenv
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def check_basic_network():
    """检查基础网络连接"""
    logger.info("🌐 检查基础网络连接...")
    
    test_urls = [
        "https://www.google.com",
        "https://www.baidu.com",
        "https://api.telegram.org"
    ]
    
    for url in test_urls:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=10) as response:
                    if response.status == 200:
                        logger.info(f"✅ {url} - 连接正常")
                    else:
                        logger.warning(f"⚠️  {url} - 状态码: {response.status}")
        except Exception as e:
            logger.error(f"❌ {url} - 连接失败: {e}")

async def check_proxy_connection():
    """检查代理连接"""
    proxy_url = os.getenv('PROXY_URL')
    
    if not proxy_url:
        logger.info("ℹ️  未配置代理")
        return
    
    logger.info(f"🔧 检查代理连接: {proxy_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(
                "https://api.telegram.org", 
                proxy=proxy_url,
                timeout=10
            ) as response:
                if response.status == 200:
                    logger.info("✅ 代理连接正常")
                else:
                    logger.warning(f"⚠️  代理连接异常，状态码: {response.status}")
    except Exception as e:
        logger.error(f"❌ 代理连接失败: {e}")

async def check_telegram_bot():
    """检查Telegram机器人连接"""
    bot_token = os.getenv('BOT_TOKEN')
    
    if not bot_token:
        logger.error("❌ 未找到BOT_TOKEN")
        return
    
    logger.info("🤖 检查Telegram机器人连接...")
    
    try:
        # 不使用代理的机器人
        bot = Bot(token=bot_token)
        bot_info = await bot.get_me()
        logger.info(f"✅ 机器人连接正常: {bot_info.first_name} (@{bot_info.username})")
        
    except Exception as e:
        logger.error(f"❌ 机器人连接失败: {e}")
        
        # 如果有代理，尝试使用代理
        proxy_url = os.getenv('PROXY_URL')
        if proxy_url:
            logger.info("🔧 尝试使用代理连接机器人...")
            try:
                from telegram.request import HTTPXRequest
                request = HTTPXRequest(proxy_url=proxy_url)
                bot_with_proxy = Bot(token=bot_token, request=request)
                bot_info = await bot_with_proxy.get_me()
                logger.info(f"✅ 通过代理连接成功: {bot_info.first_name}")
            except Exception as e2:
                logger.error(f"❌ 代理连接也失败: {e2}")

async def check_telegram_api_endpoints():
    """检查Telegram API端点"""
    logger.info("🔗 检查Telegram API端点...")
    
    endpoints = [
        "https://api.telegram.org",
        "https://api.telegram.org/bot",
    ]
    
    proxy_url = os.getenv('PROXY_URL')
    
    for endpoint in endpoints:
        try:
            kwargs = {}
            if proxy_url:
                kwargs['proxy'] = proxy_url
            
            async with aiohttp.ClientSession() as session:
                async with session.get(endpoint, timeout=10, **kwargs) as response:
                    logger.info(f"✅ {endpoint} - 状态码: {response.status}")
        except Exception as e:
            logger.error(f"❌ {endpoint} - 连接失败: {e}")

def print_network_suggestions():
    """打印网络问题解决建议"""
    logger.info("\n" + "="*50)
    logger.info("💡 网络问题解决建议:")
    logger.info("1. 检查网络连接是否正常")
    logger.info("2. 如果在中国大陆，需要配置代理")
    logger.info("3. 检查防火墙设置")
    logger.info("4. 尝试更换代理服务器")
    logger.info("5. 检查代理配置格式是否正确")
    logger.info("   格式: http://host:port 或 socks5://host:port")
    logger.info("6. 重启网络连接或更换网络环境")
    logger.info("="*50)

async def main():
    """主函数"""
    logger.info("🔍 开始网络连接诊断...")
    
    # 加载配置
    if os.path.exists('.env'):
        load_dotenv('.env')
        logger.info("✅ 已加载 .env 配置文件")
    
    # 显示当前配置
    proxy_url = os.getenv('PROXY_URL')
    logger.info(f"📋 当前配置:")
    logger.info(f"   代理: {proxy_url if proxy_url else '未配置'}")
    
    # 执行各项检查
    await check_basic_network()
    await check_proxy_connection()
    await check_telegram_api_endpoints()
    await check_telegram_bot()
    
    # 显示建议
    print_network_suggestions()
    
    logger.info("🎉 网络诊断完成")

if __name__ == "__main__":
    asyncio.run(main())

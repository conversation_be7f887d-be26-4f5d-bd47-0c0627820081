"""
测试Markdown解析错误修复
"""

import asyncio
import logging
from telegram import Bo<PERSON>
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def clean_text_for_markdown(text: str) -> str:
    """清理文本中可能导致Markdown解析错误的字符"""
    if not text:
        return text
    
    import re
    
    # 移除或转义可能有问题的字符
    cleaned = text
    
    # 转义未配对的星号
    cleaned = re.sub(r'(?<!\*)\*(?!\*)', r'\\*', cleaned)
    
    # 转义未配对的下划线
    cleaned = re.sub(r'(?<!_)_(?!_)', r'\\_', cleaned)
    
    # 转义方括号
    cleaned = cleaned.replace('[', '\\[').replace(']', '\\]')
    
    # 转义反引号
    cleaned = cleaned.replace('`', '\\`')
    
    return cleaned

async def test_markdown_parsing():
    """测试Markdown解析修复"""
    try:
        # 验证配置
        Config.validate()
        
        # 创建机器人实例
        bot = Bot(token=Config.BOT_TOKEN)
        chat_id = Config.USER_ID
        
        logger.info("🧪 开始测试Markdown解析修复...")
        
        # 测试可能导致问题的文本
        test_texts = [
            "正常文本测试",
            "包含*单个星号的文本",
            "包含_单个下划线的文本",
            "包含[方括号]的文本",
            "包含`反引号的文本",
            "混合*_[`]特殊字符的文本",
            "APK文件名: app_v3.1.20_release.apk",
            "文件名包含特殊字符: test*file_[2024].zip",
            "复杂文本: **粗体** 和 *斜体* 混合 _下划线_ 和 `代码`",
        ]
        
        for i, test_text in enumerate(test_texts, 1):
            logger.info(f"测试 {i}: {test_text[:50]}...")
            
            try:
                # 测试原始文本（可能失败）
                header = f"🧪 **测试 #{i}** - 原始文本"
                await bot.send_message(chat_id=chat_id, text=header, parse_mode='Markdown')
                await bot.send_message(chat_id=chat_id, text=test_text, parse_mode='Markdown')
                logger.info(f"  ✅ 原始文本发送成功")
                
            except Exception as e:
                logger.warning(f"  ❌ 原始文本发送失败: {e}")
                
                # 测试清理后的文本
                try:
                    cleaned_text = clean_text_for_markdown(test_text)
                    header = f"🧪 测试 #{i} - 清理后文本"
                    await bot.send_message(chat_id=chat_id, text=header)
                    await bot.send_message(chat_id=chat_id, text=cleaned_text, parse_mode='Markdown')
                    logger.info(f"  ✅ 清理后文本发送成功")
                    
                except Exception as e2:
                    logger.warning(f"  ❌ 清理后文本也失败: {e2}")
                    
                    # 最后降级为纯文本
                    try:
                        plain_text = test_text.replace('**', '').replace('*', '')
                        header = f"测试 #{i} - 纯文本"
                        await bot.send_message(chat_id=chat_id, text=header)
                        await bot.send_message(chat_id=chat_id, text=plain_text)
                        logger.info(f"  ✅ 纯文本发送成功")
                        
                    except Exception as e3:
                        logger.error(f"  ❌ 纯文本也失败: {e3}")
            
            await asyncio.sleep(1)  # 避免发送过快
        
        # 发送测试完成通知
        await bot.send_message(
            chat_id=chat_id,
            text="🧪 **Markdown解析测试完成**\n\n"
                 "✅ 修复后的机器人应该能够正确处理各种特殊字符\n"
                 "📝 即使Markdown解析失败，也会自动降级为纯文本发送",
            parse_mode='Markdown'
        )
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

async def main():
    """主函数"""
    await test_markdown_parsing()

if __name__ == "__main__":
    asyncio.run(main())

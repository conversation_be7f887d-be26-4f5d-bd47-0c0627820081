# 故障排除指南

## 🔧 问题修复

### 问题1：搜索失败 - "The API access for bot users is restricted"

**原因**：机器人Token无法获取用户的对话列表

**解决方案**：
✅ 已修复！程序现在使用用户客户端进行搜索，机器人只负责接收命令和发送消息。

### 问题2：连接测试失败

**步骤**：
1. 运行连接测试：
   ```bash
   python start_bot.py --test
   ```

2. 根据测试结果进行修复：

#### 用户客户端连接失败
- 检查 `API_ID` 和 `API_HASH` 是否正确
- 确认网络连接正常
- 如果使用代理，检查代理配置

#### 机器人连接失败
- 检查 `BOT_TOKEN` 是否正确
- 确认机器人未被禁用

#### 用户ID不匹配
- 使用 [@userinfobot](https://t.me/userinfobot) 获取正确的用户ID
- 更新 `.env` 文件中的 `USER_ID`

### 问题3：无法获取对话列表

**可能原因**：
- 账号未登录
- 网络连接问题
- API限制

**解决方案**：
1. 删除 `user_session.session` 文件
2. 重新运行程序，重新登录
3. 确保网络连接稳定

### 问题4：搜索速度慢

**优化建议**：
1. 调整配置参数（在 `.env` 文件中）：
   ```env
   SEARCH_DELAY=1.0      # 减少搜索间隔
   CHANNEL_DELAY=0.5     # 减少频道切换间隔
   MAX_RESULTS=30        # 减少最大结果数量
   ```

2. 缩小搜索范围：
   - 使用更具体的关键词
   - 减少搜索天数

### 问题5：转发失败

**常见原因**：
- 频道限制转发
- 消息过大
- 网络问题

**自动处理**：
程序会自动改为发送消息内容而不是转发原消息。

## 🚀 最佳实践

### 1. 首次使用
```bash
# 1. 测试连接
python start_bot.py --test

# 2. 如果测试通过，启动机器人
python start_bot.py
```

### 2. 配置优化
- 根据网络情况调整延迟参数
- 根据需要调整最大结果数量
- 使用代理提高连接稳定性

### 3. 使用技巧
- 使用具体的关键词提高匹配精度
- 合理设置搜索天数（建议1-7天）
- 避免在高峰时段进行大量搜索

## 📞 获取帮助

如果问题仍然存在：

1. 查看详细日志输出
2. 检查网络连接
3. 确认所有配置正确
4. 尝试重新启动程序

## 🔄 重置程序

如果遇到严重问题，可以重置：

```bash
# 删除会话文件
rm user_session.session
rm test_session.session

# 清理缓存
rm -rf __pycache__

# 重新启动
python start_bot.py --test
```

## 📋 检查清单

启动前请确认：

- [ ] 已安装所有依赖 (`pip install -r requirements.txt`)
- [ ] 已创建并配置 `.env` 文件
- [ ] 网络连接正常
- [ ] Telegram API配置正确
- [ ] 用户ID匹配
- [ ] 机器人Token有效

---

**提示**：大多数问题都可以通过运行连接测试来诊断和解决。

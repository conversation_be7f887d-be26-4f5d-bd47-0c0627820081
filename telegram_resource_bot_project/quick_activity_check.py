"""
快速活跃度检查工具
快速检查所有频道/群组的活跃度，重点显示不活跃的频道/群组
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class QuickActivityChecker:
    def __init__(self):
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.proxy_url = getattr(Config, 'PROXY_URL', None)
        self.client = None

    async def init_client(self):
        """初始化客户端"""
        try:
            # 简化的代理配置
            proxy = None
            if self.proxy_url:
                logger.info(f"🌐 使用代理: {self.proxy_url}")
                try:
                    if self.proxy_url.startswith('http://'):
                        proxy_part = self.proxy_url.replace('http://', '')
                        addr, port = proxy_part.split(':')
                        proxy = {'proxy_type': 'HTTP', 'addr': addr, 'port': int(port)}
                    elif self.proxy_url.startswith('socks5://'):
                        proxy_part = self.proxy_url.replace('socks5://', '')
                        addr, port = proxy_part.split(':')
                        proxy = {'proxy_type': 'SOCKS5', 'addr': addr, 'port': int(port)}
                except Exception as e:
                    logger.error(f"❌ 代理配置失败: {e}")
                    proxy = None

            self.client = TelegramClient('user_session', self.api_id, self.api_hash, proxy=proxy)
            await self.client.start()
            logger.info("✅ 客户端初始化成功")

        except Exception as e:
            logger.error(f"❌ 客户端初始化失败: {e}")
            raise

    async def quick_check(self, inactive_days=30):
        """快速检查不活跃的频道/群组"""
        try:
            print(f"🔍 正在快速检查不活跃频道/群组（超过 {inactive_days} 天未活跃）...")
            
            dialogs = await self.client.get_dialogs()
            current_time = datetime.now(timezone.utc)
            cutoff_date = current_time - timedelta(days=inactive_days)
            
            inactive_chats = []
            no_message_chats = []
            total_checked = 0
            
            for dialog in dialogs:
                if isinstance(dialog.entity, Channel) or isinstance(dialog.entity, Chat):
                    total_checked += 1
                    
                    # 显示进度
                    if total_checked % 10 == 0:
                        print(f"📈 已检查: {total_checked} 个频道/群组...")
                    
                    try:
                        # 获取最后一条消息
                        last_message = None
                        async for message in self.client.iter_messages(dialog.entity, limit=1):
                            last_message = message
                            break
                        
                        # 确定类型
                        if isinstance(dialog.entity, Channel):
                            chat_type = "超级群组" if dialog.entity.megagroup else "频道"
                            type_icon = "🔒" if dialog.entity.megagroup else "📺"
                        else:
                            chat_type = "普通群组"
                            type_icon = "👥"
                        
                        members = getattr(dialog.entity, 'participants_count', 'N/A')
                        username = getattr(dialog.entity, 'username', None)
                        username_info = f"@{username}" if username else "私有"
                        
                        if last_message is None:
                            # 没有消息的频道/群组
                            no_message_chats.append({
                                'title': dialog.title,
                                'type': chat_type,
                                'type_icon': type_icon,
                                'username': username_info,
                                'members': members,
                                'id': dialog.entity.id
                            })
                        elif last_message.date < cutoff_date:
                            # 不活跃的频道/群组
                            days_ago = (current_time - last_message.date).days
                            inactive_chats.append({
                                'title': dialog.title,
                                'type': chat_type,
                                'type_icon': type_icon,
                                'username': username_info,
                                'members': members,
                                'last_message_date': last_message.date,
                                'days_ago': days_ago,
                                'id': dialog.entity.id
                            })
                        
                        # 添加小延迟
                        await asyncio.sleep(0.3)
                        
                    except Exception as e:
                        logger.warning(f"检查 {dialog.title} 时出错: {e}")
                        continue
            
            # 显示结果
            self.display_inactive_results(inactive_chats, no_message_chats, inactive_days, total_checked)
            
        except Exception as e:
            logger.error(f"❌ 快速检查失败: {e}")
            raise

    def display_inactive_results(self, inactive_chats, no_message_chats, inactive_days, total_checked):
        """显示不活跃频道/群组的结果"""
        print(f"\n{'='*100}")
        print(f"🔍 不活跃频道/群组检查报告")
        print(f"{'='*100}")
        print(f"📊 检查统计:")
        print(f"   总检查数量: {total_checked}")
        print(f"   不活跃标准: 超过 {inactive_days} 天未发消息")
        print(f"   不活跃数量: {len(inactive_chats)}")
        print(f"   无消息数量: {len(no_message_chats)}")
        print(f"   活跃数量: {total_checked - len(inactive_chats) - len(no_message_chats)}")
        
        if inactive_chats:
            print(f"\n🔴 不活跃频道/群组 ({len(inactive_chats)} 个):")
            print(f"{'序号':<4} {'类型':<4} {'名称':<35} {'用户名':<15} {'成员数':<8} {'距今天数':<8}")
            print(f"{'-'*80}")
            
            # 按不活跃天数排序
            inactive_chats.sort(key=lambda x: x['days_ago'], reverse=True)
            
            for i, chat in enumerate(inactive_chats, 1):
                title = chat['title'][:33] + '..' if len(chat['title']) > 35 else chat['title']
                username = chat['username'][:13] + '..' if len(chat['username']) > 15 else chat['username']
                members = str(chat['members']) if chat['members'] != 'N/A' else 'N/A'
                
                print(f"{i:<4} {chat['type_icon']:<4} {title:<35} {username:<15} {members:<8} {chat['days_ago']}天前")
        
        if no_message_chats:
            print(f"\n⚫ 无消息记录的频道/群组 ({len(no_message_chats)} 个):")
            print(f"{'序号':<4} {'类型':<4} {'名称':<35} {'用户名':<15} {'成员数':<8}")
            print(f"{'-'*70}")
            
            for i, chat in enumerate(no_message_chats, 1):
                title = chat['title'][:33] + '..' if len(chat['title']) > 35 else chat['title']
                username = chat['username'][:13] + '..' if len(chat['username']) > 15 else chat['username']
                members = str(chat['members']) if chat['members'] != 'N/A' else 'N/A'
                
                print(f"{i:<4} {chat['type_icon']:<4} {title:<35} {username:<15} {members:<8}")
        
        if not inactive_chats and not no_message_chats:
            print(f"\n🎉 太棒了！所有频道/群组都很活跃！")
        
        print(f"\n💡 建议:")
        if len(inactive_chats) > 0:
            print(f"   - 考虑退出长期不活跃的频道/群组以减少干扰")
            print(f"   - 特别关注超过 90 天未活跃的频道/群组")
        if len(no_message_chats) > 0:
            print(f"   - 检查无消息记录的频道/群组是否为空频道或权限受限")

async def main():
    """主函数"""
    checker = QuickActivityChecker()

    try:
        print("⚡ Telegram 频道/群组快速活跃度检查工具")
        print("=" * 60)
        
        # 获取用户输入的不活跃天数标准
        try:
            days_input = input("请输入不活跃天数标准 (默认30天，直接回车使用默认值): ").strip()
            inactive_days = int(days_input) if days_input else 30
            if inactive_days <= 0:
                inactive_days = 30
                print("⚠️ 无效输入，使用默认值 30 天")
        except ValueError:
            inactive_days = 30
            print("⚠️ 无效输入，使用默认值 30 天")
        
        print(f"📋 配置信息:")
        print(f"   API ID: {checker.api_id}")
        print(f"   代理设置: {'已配置' if checker.proxy_url else '未配置'}")
        print(f"   不活跃标准: {inactive_days} 天")
        print("=" * 60)

        await checker.init_client()
        await checker.quick_check(inactive_days)

    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 程序运行失败: {e}")
        print(f"❌ 程序运行失败: {e}")
    finally:
        if checker.client:
            await checker.client.disconnect()
            logger.info("🔌 客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main())

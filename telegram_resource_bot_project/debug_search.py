"""
调试搜索功能的脚本
用于测试消息获取和时间过滤
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def debug_search():
    """调试搜索功能"""
    # 加载配置
    load_dotenv('.env')
    
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    
    # 计算时间范围（最近3天）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=3)
    
    logger.info(f"搜索时间范围: {start_date} 到 {end_date}")
    
    # 连接客户端
    client = TelegramClient('debug_session', api_id, api_hash)
    await client.start()
    
    try:
        # 获取对话列表
        dialogs = await client.get_dialogs()
        logger.info(f"获取到 {len(dialogs)} 个对话")
        
        # 找到频道和群组
        channels_and_groups = []
        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channels_and_groups.append(dialog)
        
        logger.info(f"找到 {len(channels_and_groups)} 个频道和群组")
        
        # 测试前3个频道
        for i, dialog in enumerate(channels_and_groups[:3]):
            logger.info(f"\n=== 测试频道 {i+1}: {dialog.title} ===")
            
            try:
                # 获取最新的10条消息
                messages = []
                async for message in client.iter_messages(dialog.entity, limit=10):
                    messages.append(message)
                
                logger.info(f"获取到 {len(messages)} 条最新消息")
                
                if messages:
                    # 显示消息时间信息
                    latest_msg = messages[0]
                    oldest_msg = messages[-1]
                    
                    logger.info(f"最新消息时间: {latest_msg.date}")
                    logger.info(f"最旧消息时间: {oldest_msg.date}")
                    
                    # 检查时间范围内的消息
                    in_range_count = 0
                    for msg in messages:
                        if msg.date >= start_date:
                            in_range_count += 1
                            logger.info(f"  时间范围内消息: {msg.date} - {msg.text[:50] if msg.text else '(无文本)'}")
                    
                    logger.info(f"时间范围内消息数量: {in_range_count}")
                else:
                    logger.info("该频道没有消息")
                    
            except Exception as e:
                logger.error(f"测试频道 {dialog.title} 时出错: {e}")
            
            await asyncio.sleep(1)
    
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    logger.info("🔍 开始调试搜索功能...")
    await debug_search()
    logger.info("✅ 调试完成")

if __name__ == "__main__":
    asyncio.run(main())

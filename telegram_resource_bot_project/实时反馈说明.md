# 实时反馈功能说明

## 🎯 功能概述

机器人现在提供完整的实时反馈，让您随时了解搜索进度和状态。

## 📱 用户体验流程

### 1. 发送搜索命令
```
/search 关键词 天数
```

### 2. 立即收到状态消息
```
🔍 开始搜索任务
🎯 关键词：关键词
📅 时间范围：最近 X 天
📊 状态：正在初始化...
```

### 3. 实时状态更新
状态消息会自动更新，显示：
- 当前搜索进度（百分比）
- 正在搜索的频道名称
- 已找到的匹配消息数量

```
🔍 搜索进行中
🎯 关键词：关键词
📅 时间范围：最近 X 天
📊 进度：25/100 个频道 (25%)
📍 当前：频道名称
🎯 已找到：3 条匹配消息
```

### 4. 即时结果发送
每找到一条匹配消息，立即发送：
```
🎯 匹配结果 #1
📺 频道：频道名称
📅 时间：06-08 10:54
🔍 匹配类型：文本消息
━━━━━━━━━━━━━━━━━━━━━━━━━
[转发的原始消息]
```

### 5. 定期进度报告
每搜索一定数量的频道后，发送独立的进度报告：
```
📊 搜索进度报告 #1
✅ 已搜索: 20/100 个频道
🎯 已找到: 5 条匹配消息
📍 当前搜索: 频道名称
```

### 6. 最终完成状态
搜索完成后，状态消息更新为：
```
🎉 搜索任务完成！
🎯 关键词：关键词
📅 时间范围：最近 X 天
📊 搜索了：100 个频道
🎯 找到：8 条匹配消息
✅ 状态：所有结果已发送完毕
```

## ⚙️ 配置选项

您可以在 `.env` 文件中调整通知频率：

```env
# 状态更新间隔（每搜索几个频道更新一次状态消息）
STATUS_UPDATE_INTERVAL=5

# 进度报告间隔（每搜索几个频道发送一次独立的进度报告）
PROGRESS_REPORT_INTERVAL=20
```

### 配置说明：

- **STATUS_UPDATE_INTERVAL=5**：每搜索5个频道更新一次状态消息
- **PROGRESS_REPORT_INTERVAL=20**：每搜索20个频道发送一次进度报告

### 推荐设置：

- **快速反馈**：`STATUS_UPDATE_INTERVAL=3`, `PROGRESS_REPORT_INTERVAL=15`
- **标准反馈**：`STATUS_UPDATE_INTERVAL=5`, `PROGRESS_REPORT_INTERVAL=20`
- **减少通知**：`STATUS_UPDATE_INTERVAL=10`, `PROGRESS_REPORT_INTERVAL=30`

## 🎯 优势特点

### 1. 实时可见性
- 随时知道搜索进度
- 了解当前正在搜索的频道
- 实时看到找到的消息数量

### 2. 即时结果
- 找到匹配消息立即发送
- 不需要等待整个搜索完成
- 可以提前查看结果

### 3. 详细进度
- 百分比进度显示
- 当前频道名称
- 累计结果统计

### 4. 灵活配置
- 可调整通知频率
- 适应不同使用习惯
- 平衡信息量和干扰度

## 📊 控制台日志

同时，控制台也会显示详细的搜索日志：

```
🔍 开始搜索关键词: 关键词, 时间范围: ...
📋 正在获取对话列表...
📋 获取到 298 个对话
📋 找到 215 个频道和群组
🔍 正在搜索 (1/215): 频道名称
✅ 找到匹配消息 #1: 频道名称 - 时间
✅ 频道 频道名称 搜索完成，总共检查 500 条消息，时间范围内 498 条
🎉 搜索完成！共找到 X 条匹配消息
```

## 💡 使用建议

1. **大量频道搜索**：建议使用较大的间隔值，减少通知频率
2. **快速搜索**：可以使用较小的间隔值，获得更频繁的更新
3. **长时间搜索**：进度报告帮助您了解搜索是否正常进行
4. **即时查看**：找到的结果会立即发送，可以边搜索边查看

---

**注意**：所有的实时反馈都不会影响搜索性能，机器人会智能地处理通知发送。

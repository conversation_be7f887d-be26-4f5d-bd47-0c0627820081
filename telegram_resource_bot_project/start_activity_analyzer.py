"""
活跃度分析工具启动器
提供菜单选择不同的分析工具
"""

import asyncio
import sys
import os

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("📊 Telegram 频道/群组活跃度分析工具集")
    print("="*60)
    print("请选择要使用的工具:")
    print()
    print("1. 📊 完整活跃度分析 (chat_activity_analyzer.py)")
    print("   - 详细分析所有频道/群组的活跃度")
    print("   - 显示最后消息时间、成员数量等完整信息")
    print("   - 支持多种排序方式和CSV导出")
    print("   - 适合需要详细报告的场景")
    print()
    print("2. ⚡ 快速不活跃检查 (quick_activity_check.py)")
    print("   - 快速识别不活跃的频道/群组")
    print("   - 重点显示超过指定天数未活跃的频道")
    print("   - 速度更快，适合日常检查")
    print("   - 提供清理建议")
    print()
    print("3. 🎯 频道/群组管理 (manage_chats.py)")
    print("   - 管理搜索机器人的目标频道/群组")
    print("   - 选择性搜索配置")
    print("   - 查看所有频道/群组列表")
    print()
    print("4. ❌ 退出")
    print("="*60)

async def run_tool(choice):
    """运行选择的工具"""
    if choice == '1':
        print("\n🚀 启动完整活跃度分析工具...")
        try:
            from chat_activity_analyzer import main as analyzer_main
            await analyzer_main()
        except ImportError:
            print("❌ 无法导入 chat_activity_analyzer.py")
            print("请确保文件存在且没有语法错误")
        except Exception as e:
            print(f"❌ 运行分析工具时出错: {e}")
    
    elif choice == '2':
        print("\n⚡ 启动快速不活跃检查工具...")
        try:
            from quick_activity_check import main as quick_main
            await quick_main()
        except ImportError:
            print("❌ 无法导入 quick_activity_check.py")
            print("请确保文件存在且没有语法错误")
        except Exception as e:
            print(f"❌ 运行快速检查工具时出错: {e}")
    
    elif choice == '3':
        print("\n🎯 启动频道/群组管理工具...")
        try:
            from manage_chats import main as manage_main
            await manage_main()
        except ImportError:
            print("❌ 无法导入 manage_chats.py")
            print("请确保文件存在且没有语法错误")
        except Exception as e:
            print(f"❌ 运行管理工具时出错: {e}")

def check_dependencies():
    """检查依赖文件是否存在"""
    required_files = [
        'config.py',
        'chat_activity_analyzer.py',
        'quick_activity_check.py',
        'manage_chats.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都在当前目录中")
        return False
    
    return True

async def main():
    """主函数"""
    print("🔧 检查依赖文件...")
    
    if not check_dependencies():
        return
    
    print("✅ 依赖检查通过")
    
    while True:
        try:
            show_menu()
            choice = input("请选择 (1-4): ").strip()
            
            if choice in ['1', '2', '3']:
                await run_tool(choice)
                
                # 询问是否继续
                print("\n" + "="*60)
                continue_choice = input("是否继续使用其他工具? (y/N): ").strip().lower()
                if continue_choice != 'y':
                    break
                    
            elif choice == '4':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n❌ 用户中断操作")
            break
        except Exception as e:
            print(f"❌ 程序出错: {e}")
            break

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

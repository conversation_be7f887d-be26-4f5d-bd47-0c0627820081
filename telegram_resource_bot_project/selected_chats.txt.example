# 选择的频道和群组列表示例
# 格式：ID|标题|类型
# 以 # 开头的行为注释，会被忽略

# 这是一个示例文件，展示如何配置选择性搜索
# 实际使用时，请运行 'python manage_chats.py' 来生成真实的选择文件

# 示例频道
# -1001234567890|科技资讯频道|channel
# -1001234567891|电影资源分享|channel
# -1001234567892|软件下载站|channel

# 示例群组
# -1001234567893|技术讨论群|supergroup
# -1001234567894|资源分享群|group

# 使用说明：
# 1. 将此文件重命名为 selected_chats.txt
# 2. 或者运行 python manage_chats.py 来交互式选择
# 3. 在 .env 文件中设置 USE_SELECTIVE_SEARCH=true
# 4. 重启机器人即可使用选择性搜索功能

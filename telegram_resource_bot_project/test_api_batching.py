"""
测试Telegram API的分页机制
验证每次请求获取的消息数量
"""

import asyncio
import logging
import time
from telethon import TelegramClient
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class APIBatchTester:
    def __init__(self):
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.client = None
        self.request_count = 0
        self.message_count = 0

    async def init_client(self):
        """初始化客户端"""
        self.client = TelegramClient('test_session', self.api_id, self.api_hash)
        await self.client.start()
        logger.info("✅ 客户端初始化成功")

    async def test_message_batching(self, limit=500):
        """测试消息分页机制"""
        try:
            # 获取对话列表
            dialogs = await self.client.get_dialogs(limit=5)
            
            if not dialogs:
                logger.error("❌ 没有找到任何对话")
                return

            # 选择第一个有消息的频道/群组
            test_dialog = None
            for dialog in dialogs:
                # 检查是否有消息
                try:
                    messages = await self.client.get_messages(dialog.entity, limit=1)
                    if messages:
                        test_dialog = dialog
                        break
                except:
                    continue

            if not test_dialog:
                logger.error("❌ 没有找到可测试的频道")
                return

            logger.info(f"🧪 测试频道: {test_dialog.title}")
            logger.info(f"📊 计划获取: {limit} 条消息")
            
            # 重置计数器
            self.request_count = 0
            self.message_count = 0
            
            # 记录开始时间
            start_time = time.time()
            
            # 使用iter_messages获取消息
            logger.info("🔍 开始获取消息...")
            
            async for message in self.client.iter_messages(test_dialog.entity, limit=limit):
                self.message_count += 1
                
                # 每100条消息记录一次（推测的批次大小）
                if self.message_count % 100 == 0:
                    elapsed = time.time() - start_time
                    logger.info(f"📈 已获取: {self.message_count} 条消息 (耗时: {elapsed:.2f}秒)")
                
                # 如果是前几条消息，显示详细信息
                if self.message_count <= 5:
                    logger.info(f"  消息 #{self.message_count}: {message.date} - {message.text[:30] if message.text else '(无文本)'}...")

            # 记录结束时间
            end_time = time.time()
            total_time = end_time - start_time
            
            logger.info(f"✅ 获取完成!")
            logger.info(f"📊 总消息数: {self.message_count}")
            logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
            logger.info(f"📈 平均速度: {self.message_count/total_time:.1f} 条/秒")
            
            # 估算API请求次数
            estimated_requests = (self.message_count + 99) // 100  # 向上取整
            logger.info(f"🔢 估算API请求次数: {estimated_requests} 次")
            logger.info(f"📊 平均每次请求: {self.message_count/estimated_requests:.1f} 条消息")

        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")

    async def test_different_limits(self):
        """测试不同限制值的性能"""
        test_limits = [100, 200, 500, 1000]
        
        for limit in test_limits:
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 测试限制值: {limit}")
            logger.info(f"{'='*50}")
            
            await self.test_message_batching(limit)
            
            # 等待一下，避免API限制
            await asyncio.sleep(2)

    async def test_manual_batching(self):
        """手动分批获取，验证批次机制"""
        try:
            # 获取测试频道
            dialogs = await self.client.get_dialogs(limit=5)
            test_dialog = dialogs[0]
            
            logger.info(f"\n{'='*50}")
            logger.info(f"🧪 手动分批测试: {test_dialog.title}")
            logger.info(f"{'='*50}")
            
            batch_size = 100
            total_batches = 5
            offset_id = 0
            
            for batch_num in range(total_batches):
                start_time = time.time()
                
                # 获取一批消息
                messages = await self.client.get_messages(
                    test_dialog.entity,
                    limit=batch_size,
                    offset_id=offset_id
                )
                
                end_time = time.time()
                batch_time = end_time - start_time
                
                logger.info(f"📦 批次 {batch_num + 1}: 获取 {len(messages)} 条消息 (耗时: {batch_time:.2f}秒)")
                
                if messages:
                    # 更新offset_id为最后一条消息的ID
                    offset_id = messages[-1].id
                    
                    # 显示第一条和最后一条消息的时间
                    first_msg = messages[0]
                    last_msg = messages[-1]
                    logger.info(f"  时间范围: {last_msg.date} 到 {first_msg.date}")
                else:
                    logger.info("  没有更多消息")
                    break
                
                # 避免API限制
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"❌ 手动分批测试失败: {e}")

async def main():
    """主函数"""
    tester = APIBatchTester()
    
    try:
        await tester.init_client()
        
        # 测试不同的限制值
        await tester.test_different_limits()
        
        # 手动分批测试
        await tester.test_manual_batching()
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        if tester.client:
            await tester.client.disconnect()
            logger.info("🔌 客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main())

"""
配置文件
从 .env 文件读取所有配置选项
"""

import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv()

class Config:
    """配置类 - 所有配置都从 .env 文件读取"""

    # 基础配置
    USER_ID = int(os.getenv('USER_ID', 0))
    API_ID = int(os.getenv('API_ID', 0))
    API_HASH = os.getenv('API_HASH', '')
    BOT_TOKEN = os.getenv('BOT_TOKEN', '')
    PROXY_URL = os.getenv('PROXY_URL', '')

    # 搜索配置
    SEARCH_DELAY = float(os.getenv('SEARCH_DELAY', '2.0'))  # 搜索间隔（秒）
    CHANNEL_DELAY = float(os.getenv('CHANNEL_DELAY', '1.0'))  # 频道切换间隔（秒）
    FORWARD_DELAY = float(os.getenv('FORWARD_DELAY', '1.0'))  # 转发间隔（秒）

    # 限制配置
    MAX_RESULTS = int(os.getenv('MAX_RESULTS', '50'))  # 最大结果数量
    MAX_DAYS = int(os.getenv('MAX_DAYS', '30'))  # 最大搜索天数
    MAX_TEXT_LENGTH = int(os.getenv('MAX_TEXT_LENGTH', '1000'))  # 最大文本长度
    MAX_MESSAGES_PER_CHANNEL = int(os.getenv('MAX_MESSAGES_PER_CHANNEL', '1000'))  # 每个频道最大检查消息数

    # 批量处理配置
    BATCH_SIZE = int(os.getenv('BATCH_SIZE', '10'))

    # 通知频率配置
    STATUS_UPDATE_INTERVAL = int(os.getenv('STATUS_UPDATE_INTERVAL', '5'))  # 状态更新间隔（频道数）
    PROGRESS_REPORT_INTERVAL = int(os.getenv('PROGRESS_REPORT_INTERVAL', '20'))  # 进度报告间隔（频道数）

    # 搜索范围配置
    SEARCH_CHANNELS = os.getenv('SEARCH_CHANNELS', 'true').lower() == 'true'  # 是否搜索频道
    SEARCH_GROUPS = os.getenv('SEARCH_GROUPS', 'false').lower() == 'true'     # 是否搜索群组
    # TRY_FORWARD_GROUP_MESSAGES 已废弃 - 现在统一发送消息内容，避免收藏夹问题

    # 消息内容配置
    SEND_FULL_CONTENT = os.getenv('SEND_FULL_CONTENT', 'true').lower() == 'true'  # 是否发送完整消息内容

    # 选择性搜索配置
    USE_SELECTIVE_SEARCH = os.getenv('USE_SELECTIVE_SEARCH', 'false').lower() == 'true'  # 是否启用选择性搜索
    SELECTED_CHATS_FILE = os.getenv('SELECTED_CHATS_FILE', 'selected_chats.txt')  # 选中的频道/群组列表文件

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    
    @classmethod
    def validate(cls):
        """验证配置"""
        errors = []
        
        if not cls.USER_ID:
            errors.append("USER_ID 未设置或无效")
        
        if not cls.API_ID:
            errors.append("API_ID 未设置或无效")
        
        if not cls.API_HASH:
            errors.append("API_HASH 未设置")
        
        if not cls.BOT_TOKEN:
            errors.append("BOT_TOKEN 未设置")
        
        if errors:
            raise ValueError("配置错误:\n" + "\n".join(f"- {error}" for error in errors))
        
        return True

"""
快速修复搜索问题
简化版本的搜索功能测试
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
from dotenv import load_dotenv
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def quick_search_test(keyword="一键", days=3):
    """快速搜索测试"""
    # 加载配置
    load_dotenv('.env')
    
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    
    # 计算时间范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days)
    
    logger.info(f"搜索关键词: {keyword}")
    logger.info(f"搜索时间范围: {start_date.strftime('%Y-%m-%d %H:%M')} 到 {end_date.strftime('%Y-%m-%d %H:%M')}")
    
    # 连接客户端
    client = TelegramClient('user_session', api_id, api_hash)
    await client.start()
    
    results = []
    
    try:
        # 获取对话列表
        dialogs = await client.get_dialogs()
        
        # 找到频道和群组
        channels_and_groups = []
        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channels_and_groups.append(dialog)
        
        logger.info(f"找到 {len(channels_and_groups)} 个频道和群组")
        
        # 只测试前5个频道
        for i, dialog in enumerate(channels_and_groups[:5]):
            logger.info(f"正在搜索 ({i+1}/5): {dialog.title}")
            
            try:
                message_count = 0
                in_range_count = 0
                
                # 获取最近的消息
                async for message in client.iter_messages(dialog.entity, limit=100):
                    message_count += 1
                    
                    # 检查时间范围
                    if message.date < start_date:
                        break
                    
                    in_range_count += 1
                    
                    # 检查关键词匹配
                    if message.text and keyword.lower() in message.text.lower():
                        results.append({
                            'chat_title': dialog.title,
                            'date': message.date,
                            'text': message.text[:100] + '...' if len(message.text) > 100 else message.text
                        })
                        logger.info(f"✅ 找到匹配: {dialog.title} - {message.date}")
                
                logger.info(f"频道 {dialog.title}: 检查了 {message_count} 条消息，时间范围内 {in_range_count} 条")
                
            except Exception as e:
                logger.error(f"搜索频道 {dialog.title} 时出错: {e}")
            
            await asyncio.sleep(1)
    
    finally:
        await client.disconnect()
    
    # 显示结果
    logger.info(f"\n🎯 搜索完成！找到 {len(results)} 条匹配消息:")
    for i, result in enumerate(results, 1):
        logger.info(f"{i}. {result['chat_title']} - {result['date']}")
        logger.info(f"   内容: {result['text']}")
    
    return results

async def main():
    """主函数"""
    logger.info("🚀 开始快速搜索测试...")
    
    # 测试搜索
    results = await quick_search_test("一键", 3)
    
    if results:
        logger.info("✅ 搜索功能正常！")
    else:
        logger.info("⚠️  没有找到匹配结果，可能需要:")
        logger.info("1. 尝试其他关键词")
        logger.info("2. 扩大时间范围")
        logger.info("3. 检查频道是否有最近的消息")

if __name__ == "__main__":
    asyncio.run(main())

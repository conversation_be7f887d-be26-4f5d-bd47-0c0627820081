"""
专门测试 manage_chats.py 的代理连接
"""

import asyncio
import logging
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_manage_chats_proxy():
    """测试 manage_chats.py 的代理连接"""
    try:
        print("🧪 测试 manage_chats.py 代理连接")
        print("=" * 50)
        
        # 显示配置信息
        proxy_url = getattr(Config, 'PROXY_URL', None)
        print(f"📋 配置信息:")
        print(f"   API ID: {Config.API_ID}")
        print(f"   代理地址: {proxy_url or '未配置'}")
        print("=" * 50)
        
        # 导入并测试 ChatManager
        from manage_chats import ChatManager
        
        manager = ChatManager()
        
        print(f"🔧 ChatManager 配置:")
        print(f"   API ID: {manager.api_id}")
        print(f"   代理地址: {manager.proxy_url or '未配置'}")
        print(f"   选择文件: {manager.selected_chats_file}")
        print()
        
        # 测试初始化
        logger.info("🔗 测试 ChatManager 初始化...")
        await manager.init_client()
        
        # 测试获取用户信息
        logger.info("👤 测试获取用户信息...")
        me = await manager.client.get_me()
        logger.info(f"✅ 用户信息获取成功: {me.first_name} (@{me.username or 'N/A'})")
        
        # 测试获取对话列表
        logger.info("📋 测试获取对话列表...")
        channels, groups = await manager.list_all_chats()
        
        logger.info(f"✅ 对话列表获取成功:")
        logger.info(f"   频道数量: {len(channels)}")
        logger.info(f"   群组数量: {len(groups)}")
        
        # 显示前几个频道和群组
        print(f"\n📺 频道列表预览 (前3个):")
        for i, channel in enumerate(channels[:3], 1):
            username_info = f"@{channel['username']}" if channel['username'] else "私有频道"
            print(f"  {i}. {channel['title']} ({username_info})")
        
        print(f"\n👥 群组列表预览 (前3个):")
        for i, group in enumerate(groups[:3], 1):
            username_info = f"@{group['username']}" if group['username'] else "私有群组"
            type_icon = "🔒" if group['type'] == 'supergroup' else "👥"
            print(f"  {i}. {type_icon} {group['title']} ({username_info})")
        
        # 断开连接
        if manager.client:
            await manager.client.disconnect()
            logger.info("🔌 连接已断开")
        
        print(f"\n🎉 manage_chats.py 代理连接测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")
        
        # 提供故障排除建议
        print(f"\n🔧 故障排除建议:")
        print(f"1. 检查代理地址是否正确")
        print(f"2. 确认代理服务器是否正常运行")
        print(f"3. 验证代理用户名和密码")
        print(f"4. 尝试运行主程序测试: python start_bot.py --test")
        print(f"5. 检查防火墙设置")
        
        return False

async def compare_with_main_program():
    """与主程序的代理配置进行对比测试"""
    try:
        print(f"\n" + "=" * 60)
        print(f"🔄 对比测试：主程序 vs manage_chats.py")
        print(f"=" * 60)
        
        # 测试主程序的代理配置
        from telegram_resource_bot import TelegramResourceBot
        
        main_bot = TelegramResourceBot()
        
        print(f"📋 主程序配置:")
        print(f"   API ID: {main_bot.api_id}")
        print(f"   代理地址: {main_bot.proxy_url or '未配置'}")
        
        # 测试主程序初始化
        logger.info("🔗 测试主程序客户端初始化...")
        await main_bot.init_clients()
        
        # 测试获取用户信息
        me_main = await main_bot.client.get_me()
        logger.info(f"✅ 主程序用户信息: {me_main.first_name} (@{me_main.username or 'N/A'})")
        
        # 断开主程序连接
        if main_bot.client:
            await main_bot.client.disconnect()
        if main_bot.bot_app:
            await main_bot.bot_app.stop()
            await main_bot.bot_app.shutdown()
        
        print(f"✅ 主程序代理连接正常")
        
        # 现在测试 manage_chats
        logger.info("🔗 测试 manage_chats 代理连接...")
        success = await test_manage_chats_proxy()
        
        if success:
            print(f"\n🎉 两个程序的代理配置都正常工作！")
        else:
            print(f"\n⚠️ manage_chats.py 代理配置有问题")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 对比测试失败: {e}")
        return False

async def main():
    """主函数"""
    try:
        # 首先单独测试 manage_chats
        success1 = await test_manage_chats_proxy()
        
        if success1:
            # 如果成功，进行对比测试
            success2 = await compare_with_main_program()
            
            if success2:
                print(f"\n🎉 所有测试通过！manage_chats.py 代理配置正常。")
            else:
                print(f"\n⚠️ 对比测试发现问题。")
        else:
            print(f"\n❌ manage_chats.py 代理连接失败。")
            
    except Exception as e:
        logger.error(f"❌ 测试程序失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())

"""
测试完整内容模式和简洁信息模式
"""

import asyncio
import logging
from telegram import Bo<PERSON>
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockMessage:
    """模拟消息对象用于测试"""
    def __init__(self, text=None, media=None):
        self.text = text
        self.media = media

class MockMediaDocument:
    """模拟文档媒体对象"""
    def __init__(self, filename, size=None):
        self.document = MockDocument(filename, size)

class MockDocument:
    """模拟文档对象"""
    def __init__(self, filename, size=None):
        self.attributes = [MockAttribute(filename)]
        self.size = size or 1024000  # 默认1MB

class MockAttribute:
    """模拟文档属性"""
    def __init__(self, filename):
        self.file_name = filename

def clean_text_for_markdown(text: str) -> str:
    """清理文本中可能导致Markdown解析错误的字符"""
    if not text:
        return text
    
    import re
    
    cleaned = text
    cleaned = re.sub(r'(?<!\*)\*(?!\*)', r'\\*', cleaned)
    cleaned = re.sub(r'(?<!_)_(?!_)', r'\\_', cleaned)
    cleaned = cleaned.replace('[', '\\[').replace(']', '\\]')
    cleaned = cleaned.replace('`', '\\`')
    
    return cleaned

def format_file_size(size) -> str:
    """格式化文件大小"""
    try:
        if isinstance(size, str):
            return size
        
        size = int(size)
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    except:
        return "未知"

async def send_brief_info(bot, chat_id: int, message, message_link: str = None):
    """发送简洁的消息信息（测试版本）"""
    try:
        brief_parts = []
        
        # 添加消息类型和基本信息
        if message.text:
            # 显示消息的前50个字符作为预览
            preview = message.text[:50]
            if len(message.text) > 50:
                preview += "..."
            brief_parts.append(f"📝 **消息预览：** {clean_text_for_markdown(preview)}")
        
        # 处理媒体文件信息（简化版）
        if message.media:
            if hasattr(message.media, 'document') and message.media.document:
                if hasattr(message.media.document, 'attributes'):
                    for attr in message.media.document.attributes:
                        if hasattr(attr, 'file_name') and attr.file_name:
                            safe_filename = clean_text_for_markdown(attr.file_name)
                            file_size = format_file_size(message.media.document.size) if message.media.document.size else "未知"
                            brief_parts.append(f"📎 **文件：** {safe_filename} ({file_size})")
                            break
                    else:
                        brief_parts.append("📎 **文档文件**")
                else:
                    brief_parts.append("📎 **文档文件**")
            else:
                brief_parts.append("📎 **媒体文件**")
        
        # 添加查看完整内容的提示
        if message_link:
            brief_parts.append(f"🔗 **查看完整内容：** {message_link}")
        else:
            brief_parts.append("💡 **提示：** 点击上方链接查看完整消息内容")
        
        # 合并所有内容
        if brief_parts:
            brief_content = "\n".join(brief_parts)
            
            # 尝试发送Markdown，失败则降级为纯文本
            try:
                await bot.send_message(chat_id=chat_id, text=brief_content, parse_mode='Markdown')
            except Exception as markdown_error:
                logger.warning(f"简洁信息Markdown发送失败，改为纯文本: {markdown_error}")
                plain_content = brief_content.replace('**', '').replace('*', '')
                await bot.send_message(chat_id=chat_id, text=plain_content)
        else:
            # 如果没有内容，发送基本提示
            basic_info = "📋 **消息信息**"
            if message_link:
                basic_info += f"\n🔗 **查看完整内容：** {message_link}"
            await bot.send_message(chat_id=chat_id, text=basic_info, parse_mode='Markdown')
            
    except Exception as e:
        logger.error(f"发送简洁信息时出错: {e}")
        # 最后的降级处理
        try:
            fallback_text = "📋 消息信息"
            if message_link:
                fallback_text += f"\n查看完整内容：{message_link}"
            await bot.send_message(chat_id=chat_id, text=fallback_text)
        except Exception as e2:
            logger.error(f"发送简洁信息降级处理也失败: {e2}")
            raise

async def test_content_modes():
    """测试不同的内容显示模式"""
    try:
        # 验证配置
        Config.validate()
        
        # 创建机器人实例
        bot = Bot(token=Config.BOT_TOKEN)
        chat_id = Config.USER_ID
        
        logger.info("🧪 开始测试内容显示模式...")
        
        # 测试消息数据
        test_messages = [
            {
                "name": "纯文本消息",
                "message": MockMessage(text="这是一条测试消息，包含一些文本内容。用于测试简洁模式和完整模式的显示效果。"),
                "link": "https://t.me/testchannel/123"
            },
            {
                "name": "长文本消息",
                "message": MockMessage(text="这是一条很长的测试消息，包含大量文本内容。" * 10),
                "link": "https://t.me/testchannel/124"
            },
            {
                "name": "APK文件",
                "message": MockMessage(
                    text="最新版本的应用程序",
                    media=MockMediaDocument("app_v3.1.20_release.apk", 15728640)
                ),
                "link": "https://t.me/testchannel/125"
            },
            {
                "name": "ZIP文件",
                "message": MockMessage(
                    text="压缩包文件",
                    media=MockMediaDocument("resources_[2024]_pack*.zip", 52428800)
                ),
                "link": "https://t.me/testchannel/126"
            },
            {
                "name": "只有文件",
                "message": MockMessage(media=MockMediaDocument("document.pdf", 2097152)),
                "link": "https://t.me/testchannel/127"
            }
        ]
        
        for i, test_data in enumerate(test_messages, 1):
            logger.info(f"测试 {i}: {test_data['name']}")
            
            # 发送测试头部
            header = f"🧪 **测试 #{i}: {test_data['name']}**"
            await bot.send_message(chat_id=chat_id, text=header, parse_mode='Markdown')
            
            # 测试简洁信息模式
            await bot.send_message(chat_id=chat_id, text="📋 **简洁信息模式：**", parse_mode='Markdown')
            await send_brief_info(bot, chat_id, test_data['message'], test_data['link'])
            
            await asyncio.sleep(1)
            
            # 说明完整模式的效果
            full_mode_text = "📄 **完整内容模式：**\n"
            if test_data['message'].text:
                full_mode_text += f"会显示完整文本：{test_data['message'].text[:100]}{'...' if len(test_data['message'].text) > 100 else ''}\n"
            if test_data['message'].media:
                full_mode_text += "会显示完整媒体信息和文件详情"
            
            await bot.send_message(chat_id=chat_id, text=full_mode_text, parse_mode='Markdown')
            
            await asyncio.sleep(2)
        
        # 发送测试完成通知
        completion_text = """
🧪 **内容模式测试完成**

**简洁信息模式特点：**
✅ 只显示消息预览（前50字符）
✅ 显示文件名和大小
✅ 提供原始链接
✅ 节省聊天空间

**完整内容模式特点：**
✅ 显示完整消息文本
✅ 显示详细媒体信息
✅ 包含所有原始内容
✅ 适合详细查看

**配置方法：**
在 `.env` 文件中设置：
• `SEND_FULL_CONTENT=true` - 完整内容模式
• `SEND_FULL_CONTENT=false` - 简洁信息模式
        """
        
        await bot.send_message(chat_id=chat_id, text=completion_text, parse_mode='Markdown')
        
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")

async def main():
    """主函数"""
    await test_content_modes()

if __name__ == "__main__":
    asyncio.run(main())

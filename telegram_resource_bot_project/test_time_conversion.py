"""
测试时间转换功能
验证UTC时间到北京时间的转换是否正确
"""

from datetime import datetime, timezone, timedelta

def test_time_conversion():
    """测试时间转换功能"""
    print("🕒 时间转换功能测试")
    print("=" * 50)
    
    # 北京时间时区
    beijing_tz = timezone(timedelta(hours=8))
    
    def utc_to_beijing(utc_time):
        """将UTC时间转换为北京时间"""
        if utc_time is None:
            return None
        
        # 确保输入时间有时区信息
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)
        
        # 转换为北京时间
        beijing_time = utc_time.astimezone(beijing_tz)
        return beijing_time

    def format_beijing_time(utc_time, format_str='%Y-%m-%d %H:%M:%S'):
        """格式化UTC时间为北京时间字符串"""
        if utc_time is None:
            return "未知时间"
        
        beijing_time = utc_to_beijing(utc_time)
        return beijing_time.strftime(format_str)

    def format_beijing_time_short(utc_time):
        """格式化UTC时间为北京时间短格式（月-日 时:分）"""
        if utc_time is None:
            return "未知"
        
        beijing_time = utc_to_beijing(utc_time)
        return beijing_time.strftime('%m-%d %H:%M')
    
    # 测试用例
    test_cases = [
        # UTC时间示例
        datetime(2024, 1, 15, 6, 30, 0, tzinfo=timezone.utc),  # UTC 06:30
        datetime(2024, 1, 15, 14, 30, 0, tzinfo=timezone.utc), # UTC 14:30
        datetime(2024, 1, 15, 22, 30, 0, tzinfo=timezone.utc), # UTC 22:30
        datetime.now(timezone.utc),  # 当前UTC时间
    ]
    
    print("📊 测试结果:")
    print(f"{'UTC时间':<25} {'北京时间(完整)':<25} {'北京时间(短格式)':<15}")
    print("-" * 65)
    
    for utc_time in test_cases:
        utc_str = utc_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        beijing_full = format_beijing_time(utc_time)
        beijing_short = format_beijing_time_short(utc_time)
        
        print(f"{utc_str:<25} {beijing_full:<25} {beijing_short:<15}")
    
    print("\n✅ 时间转换功能测试完成")
    print("\n📝 说明:")
    print("• UTC时间 + 8小时 = 北京时间")
    print("• 机器人现在会显示北京时间而不是UTC时间")
    print("• 搜索逻辑仍然使用UTC时间，确保准确性")

if __name__ == "__main__":
    test_time_conversion()

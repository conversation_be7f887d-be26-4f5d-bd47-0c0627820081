"""
测试代理连接
验证manage_chats.py和主程序的代理配置是否正常工作
"""

import asyncio
import logging
from telethon import TelegramClient
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_proxy_connection():
    """测试代理连接"""
    try:
        print("🧪 代理连接测试")
        print("=" * 50)
        
        # 显示配置信息
        print(f"📋 配置信息:")
        print(f"   API ID: {Config.API_ID}")
        print(f"   API Hash: {Config.API_HASH[:8]}...")
        
        proxy_url = getattr(Config, 'PROXY_URL', None)
        if proxy_url:
            print(f"   代理地址: {proxy_url}")
            print(f"   代理状态: 已配置")
        else:
            print(f"   代理状态: 未配置（直连）")
        
        print("=" * 50)
        
        # 配置代理（使用与主程序相同的方式）
        proxy = None
        if proxy_url:
            logger.info(f"🌐 配置代理: {proxy_url}")
            try:
                # 解析代理URL，支持格式：http://host:port 或 socks5://host:port
                if proxy_url.startswith('http://'):
                    # 解析HTTP代理
                    proxy_part = proxy_url.replace('http://', '')
                    if '@' in proxy_part:
                        # 包含认证信息
                        auth_part, addr_part = proxy_part.split('@')
                        username, password = auth_part.split(':')
                        addr, port = addr_part.split(':')
                        proxy = {
                            'proxy_type': 'HTTP',
                            'addr': addr,
                            'port': int(port),
                            'username': username,
                            'password': password
                        }
                    else:
                        # 不包含认证信息
                        addr, port = proxy_part.split(':')
                        proxy = {
                            'proxy_type': 'HTTP',
                            'addr': addr,
                            'port': int(port)
                        }
                    logger.info("✅ HTTP代理配置成功")
                    logger.warning("⚠️ HTTP代理支持有限，建议使用SOCKS5代理")

                elif proxy_url.startswith('socks5://'):
                    # 解析SOCKS5代理
                    proxy_part = proxy_url.replace('socks5://', '')
                    if '@' in proxy_part:
                        # 包含认证信息
                        auth_part, addr_part = proxy_part.split('@')
                        username, password = auth_part.split(':')
                        addr, port = addr_part.split(':')
                        proxy = {
                            'proxy_type': 'SOCKS5',
                            'addr': addr,
                            'port': int(port),
                            'username': username,
                            'password': password
                        }
                    else:
                        # 不包含认证信息
                        addr, port = proxy_part.split(':')
                        proxy = {
                            'proxy_type': 'SOCKS5',
                            'addr': addr,
                            'port': int(port)
                        }
                    logger.info("✅ SOCKS5代理配置成功")
                else:
                    logger.warning(f"⚠️ 不支持的代理类型: {proxy_url}")

            except Exception as e:
                logger.error(f"❌ 代理配置失败: {e}")
                logger.info("🔄 将尝试直连")
                proxy = None
        
        # 创建客户端
        logger.info("🔗 创建Telegram客户端...")
        if proxy:
            client = TelegramClient('test_session', Config.API_ID, Config.API_HASH, proxy=proxy)
        else:
            client = TelegramClient('test_session', Config.API_ID, Config.API_HASH)
        
        # 测试连接
        logger.info("🚀 尝试连接到Telegram...")
        await client.start()
        
        # 获取用户信息
        me = await client.get_me()
        logger.info(f"✅ 连接成功！用户: {me.first_name} (@{me.username or 'N/A'})")
        
        # 测试获取对话列表
        logger.info("📋 测试获取对话列表...")
        dialogs = await client.get_dialogs(limit=5)
        logger.info(f"✅ 成功获取 {len(dialogs)} 个对话")
        
        # 显示前几个对话
        print("\n📺 对话列表预览:")
        for i, dialog in enumerate(dialogs[:3], 1):
            dialog_type = "频道" if hasattr(dialog.entity, 'broadcast') and dialog.entity.broadcast else "群组/私聊"
            print(f"  {i}. {dialog.title} ({dialog_type})")
        
        # 断开连接
        await client.disconnect()
        logger.info("🔌 连接已断开")
        
        print("\n🎉 代理连接测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 代理连接测试失败: {e}")
        print(f"\n❌ 测试失败: {e}")
        
        # 提供故障排除建议
        print("\n🔧 故障排除建议:")
        if proxy_url:
            print("1. 检查代理地址是否正确")
            print("2. 确认代理服务器是否正常运行")
            print("3. 验证代理用户名和密码")
            print("4. 尝试使用SOCKS5代理而不是HTTP代理")
            print("5. 检查防火墙设置")
        else:
            print("1. 检查网络连接")
            print("2. 确认API_ID和API_HASH是否正确")
            print("3. 检查是否需要配置代理")
        
        return False

async def test_manage_chats_proxy():
    """测试manage_chats.py的代理配置"""
    try:
        print("\n" + "=" * 60)
        print("🧪 测试manage_chats.py代理配置")
        print("=" * 60)
        
        # 导入ChatManager
        from manage_chats import ChatManager
        
        manager = ChatManager()
        
        print(f"📋 ChatManager配置:")
        print(f"   API ID: {manager.api_id}")
        print(f"   代理地址: {manager.proxy_url or '未配置'}")
        print(f"   选择文件: {manager.selected_chats_file}")
        
        # 测试初始化
        logger.info("🔗 测试ChatManager初始化...")
        await manager.init_client()
        
        # 测试获取对话
        logger.info("📋 测试获取对话列表...")
        channels, groups = await manager.list_all_chats()
        
        logger.info(f"✅ 成功获取 {len(channels)} 个频道，{len(groups)} 个群组")
        
        # 断开连接
        if manager.client:
            await manager.client.disconnect()
            logger.info("🔌 ChatManager连接已断开")
        
        print("✅ manage_chats.py代理配置测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ manage_chats.py代理配置测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🧪 Telegram代理连接测试工具")
    print("=" * 60)
    
    # 测试基本连接
    basic_test = await test_proxy_connection()
    
    if basic_test:
        # 测试manage_chats代理配置
        manage_test = await test_manage_chats_proxy()
        
        if manage_test:
            print("\n🎉 所有测试通过！代理配置正常工作。")
        else:
            print("\n⚠️ manage_chats.py代理配置有问题。")
    else:
        print("\n❌ 基本连接测试失败，请检查配置。")

if __name__ == "__main__":
    asyncio.run(main())

"""
测试消息转发功能
验证不同类型消息的转发效果
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat, MessageMediaDocument, MessageMediaPhoto
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_forward_functionality():
    """测试转发功能"""
    # 加载配置
    load_dotenv('.env')
    
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    bot_token = os.getenv('BOT_TOKEN')
    user_id = int(os.getenv('USER_ID'))
    
    # 连接客户端
    client = TelegramClient('user_session', api_id, api_hash)
    await client.start()
    
    bot = Bot(token=bot_token)
    
    try:
        # 获取对话列表
        dialogs = await client.get_dialogs()
        
        # 找到频道和群组
        channels_and_groups = []
        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channels_and_groups.append(dialog)
        
        logger.info(f"找到 {len(channels_and_groups)} 个频道和群组")
        
        # 测试前3个频道的消息转发
        test_count = 0
        for dialog in channels_and_groups[:3]:
            logger.info(f"\n=== 测试频道: {dialog.title} ===")
            
            try:
                # 获取最近的几条消息
                async for message in client.iter_messages(dialog.entity, limit=5):
                    test_count += 1
                    
                    logger.info(f"测试消息 {test_count}:")
                    logger.info(f"  时间: {message.date}")
                    logger.info(f"  文本: {message.text[:50] if message.text else '(无文本)'}...")
                    logger.info(f"  媒体: {'是' if message.media else '否'}")
                    
                    # 测试消息发送（新的方式，避免收藏夹问题）
                    try:
                        # 发送测试头部
                        header = f"🧪 **测试消息发送 #{test_count}**\n📺 {dialog.title}\n📅 {message.date.strftime('%m-%d %H:%M')}"
                        await bot.send_message(chat_id=user_id, text=header, parse_mode='Markdown')

                        # 直接发送消息内容（避免转发到收藏夹）
                        content_parts = []

                        if message.text:
                            content_parts.append(message.text)

                        if message.media:
                            if isinstance(message.media, MessageMediaDocument):
                                if message.media.document and message.media.document.attributes:
                                    for attr in message.media.document.attributes:
                                        if hasattr(attr, 'file_name') and attr.file_name:
                                            file_size = message.media.document.size if message.media.document.size else "未知"
                                            content_parts.append(f"📎 **文件：** {attr.file_name}\n📏 **大小：** {file_size} bytes")
                            elif isinstance(message.media, MessageMediaPhoto):
                                content_parts.append("🖼️ **图片文件**")

                        if content_parts:
                            full_content = "\n\n".join(content_parts)
                            await bot.send_message(chat_id=user_id, text=full_content, parse_mode='Markdown')
                        else:
                            await bot.send_message(chat_id=user_id, text="📝 **消息内容为空**")

                        logger.info(f"  ✅ 消息发送成功（避免收藏夹）")

                    except Exception as e:
                        logger.error(f"  ❌ 消息发送失败: {e}")

                        # 尝试简化发送
                        try:
                            if message.text:
                                await bot.send_message(chat_id=user_id, text=message.text)
                            else:
                                await bot.send_message(chat_id=user_id, text="❌ 无法显示消息内容")

                            logger.info(f"  ✅ 简化内容发送成功")

                        except Exception as e2:
                            logger.error(f"  ❌ 简化发送也失败: {e2}")
                    
                    await asyncio.sleep(1)
                    
                    if test_count >= 10:  # 限制测试数量
                        break
                
            except Exception as e:
                logger.error(f"测试频道 {dialog.title} 时出错: {e}")
            
            if test_count >= 10:
                break
        
        # 发送测试完成通知
        await bot.send_message(
            chat_id=user_id,
            text=f"🧪 **消息发送功能测试完成**\n📊 测试了 {test_count} 条消息\n✅ 请检查上方的发送效果\n💡 现在消息只会出现在对话中，不会出现在收藏夹"
        )
        
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    logger.info("🧪 开始测试消息发送功能...")
    await test_forward_functionality()
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())

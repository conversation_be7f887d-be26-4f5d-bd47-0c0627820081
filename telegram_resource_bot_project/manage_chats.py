"""
频道/群组管理工具
用于列出所有频道/群组，并允许用户选择要搜索的目标
"""

import asyncio
import logging
import os
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class ChatManager:
    def __init__(self):
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.proxy_url = getattr(Config, 'PROXY_URL', None)
        self.client = None
        self.selected_chats_file = Config.SELECTED_CHATS_FILE

    async def init_client(self):
        """初始化客户端"""
        try:
            # 配置代理（使用与主程序相同的方式）
            proxy = None
            if self.proxy_url:
                logger.info(f"🌐 使用代理: {self.proxy_url}")
                try:
                    # 解析代理URL，支持格式：http://host:port 或 socks5://host:port
                    if self.proxy_url.startswith('http://'):
                        # 解析HTTP代理
                        proxy_part = self.proxy_url.replace('http://', '')
                        if '@' in proxy_part:
                            # 包含认证信息
                            auth_part, addr_part = proxy_part.split('@')
                            username, password = auth_part.split(':')
                            addr, port = addr_part.split(':')
                            proxy = {
                                'proxy_type': 'HTTP',
                                'addr': addr,
                                'port': int(port),
                                'username': username,
                                'password': password
                            }
                        else:
                            # 不包含认证信息
                            addr, port = proxy_part.split(':')
                            proxy = {
                                'proxy_type': 'HTTP',
                                'addr': addr,
                                'port': int(port)
                            }
                        logger.info("✅ HTTP代理配置成功")

                    elif self.proxy_url.startswith('socks5://'):
                        # 解析SOCKS5代理
                        proxy_part = self.proxy_url.replace('socks5://', '')
                        if '@' in proxy_part:
                            # 包含认证信息
                            auth_part, addr_part = proxy_part.split('@')
                            username, password = auth_part.split(':')
                            addr, port = addr_part.split(':')
                            proxy = {
                                'proxy_type': 'SOCKS5',
                                'addr': addr,
                                'port': int(port),
                                'username': username,
                                'password': password
                            }
                        else:
                            # 不包含认证信息
                            addr, port = proxy_part.split(':')
                            proxy = {
                                'proxy_type': 'SOCKS5',
                                'addr': addr,
                                'port': int(port)
                            }
                        logger.info("✅ SOCKS5代理配置成功")
                    else:
                        logger.warning(f"⚠️ 不支持的代理类型: {self.proxy_url}")

                except Exception as e:
                    logger.error(f"❌ 代理配置失败: {e}")
                    logger.info("🔄 将尝试直连")
                    proxy = None

            # 创建客户端（使用与主程序相同的方式）
            self.client = TelegramClient('user_session', self.api_id, self.api_hash, proxy=proxy)
            await self.client.start()
            logger.info("✅ 客户端初始化成功")

        except Exception as e:
            logger.error(f"❌ 客户端初始化失败: {e}")
            raise

    async def list_all_chats(self):
        """列出所有频道和群组"""
        try:
            logger.info("📋 正在获取所有对话...")
            dialogs = await self.client.get_dialogs()
            
            channels = []
            groups = []
            
            for dialog in dialogs:
                if isinstance(dialog.entity, Channel):
                    if dialog.entity.megagroup:
                        # 超级群组
                        groups.append({
                            'id': dialog.entity.id,
                            'title': dialog.title,
                            'username': getattr(dialog.entity, 'username', None),
                            'type': 'supergroup',
                            'members': getattr(dialog.entity, 'participants_count', 'N/A')
                        })
                    else:
                        # 频道
                        channels.append({
                            'id': dialog.entity.id,
                            'title': dialog.title,
                            'username': getattr(dialog.entity, 'username', None),
                            'type': 'channel',
                            'members': getattr(dialog.entity, 'participants_count', 'N/A')
                        })
                elif isinstance(dialog.entity, Chat):
                    # 普通群组
                    groups.append({
                        'id': dialog.entity.id,
                        'title': dialog.title,
                        'username': None,
                        'type': 'group',
                        'members': getattr(dialog.entity, 'participants_count', 'N/A')
                    })
            
            return channels, groups
            
        except Exception as e:
            logger.error(f"❌ 获取对话列表失败: {e}")
            return [], []

    def display_chats(self, channels, groups):
        """显示所有频道和群组"""
        print(f"\n{'='*80}")
        print(f"📺 频道列表 ({len(channels)} 个)")
        print(f"{'='*80}")
        
        for i, channel in enumerate(channels, 1):
            username_info = f"@{channel['username']}" if channel['username'] else "私有频道"
            print(f"{i:3d}. 📺 {channel['title']}")
            print(f"     ID: {channel['id']} | {username_info} | 成员: {channel['members']}")
            print()
        
        print(f"\n{'='*80}")
        print(f"👥 群组列表 ({len(groups)} 个)")
        print(f"{'='*80}")
        
        for i, group in enumerate(groups, len(channels) + 1):
            type_icon = "🔒" if group['type'] == 'supergroup' else "👥"
            username_info = f"@{group['username']}" if group['username'] else "私有群组"
            print(f"{i:3d}. {type_icon} {group['title']}")
            print(f"     ID: {group['id']} | {username_info} | 成员: {group['members']}")
            print()

    def load_selected_chats(self):
        """加载已选择的频道/群组"""
        if not os.path.exists(self.selected_chats_file):
            return set()
        
        try:
            with open(self.selected_chats_file, 'r', encoding='utf-8') as f:
                selected_ids = set()
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 提取ID（格式：ID|标题）
                        if '|' in line:
                            chat_id = line.split('|')[0].strip()
                            try:
                                selected_ids.add(int(chat_id))
                            except ValueError:
                                continue
                return selected_ids
        except Exception as e:
            logger.error(f"❌ 读取选择文件失败: {e}")
            return set()

    def save_selected_chats(self, selected_chats, all_chats):
        """保存选择的频道/群组"""
        try:
            with open(self.selected_chats_file, 'w', encoding='utf-8') as f:
                f.write("# 选择的频道和群组列表\n")
                f.write("# 格式：ID|标题|类型\n")
                f.write("# 以 # 开头的行为注释，会被忽略\n\n")
                
                for chat in all_chats:
                    if chat['id'] in selected_chats:
                        f.write(f"{chat['id']}|{chat['title']}|{chat['type']}\n")
                
            logger.info(f"✅ 已保存 {len(selected_chats)} 个选择到 {self.selected_chats_file}")
            
        except Exception as e:
            logger.error(f"❌ 保存选择文件失败: {e}")

    async def interactive_selection(self):
        """交互式选择频道/群组"""
        channels, groups = await self.list_all_chats()
        all_chats = channels + groups
        
        if not all_chats:
            print("❌ 没有找到任何频道或群组")
            return
        
        # 显示所有频道和群组
        self.display_chats(channels, groups)
        
        # 加载已有选择
        selected_ids = self.load_selected_chats()
        
        print(f"\n{'='*80}")
        print(f"🎯 选择性搜索配置")
        print(f"{'='*80}")
        print(f"当前已选择: {len(selected_ids)} 个频道/群组")
        
        if selected_ids:
            print("\n已选择的频道/群组:")
            for chat in all_chats:
                if chat['id'] in selected_ids:
                    type_icon = {"channel": "📺", "supergroup": "🔒", "group": "👥"}[chat['type']]
                    print(f"  ✅ {type_icon} {chat['title']}")
        
        print(f"\n操作选项:")
        print(f"1. 添加频道/群组到选择列表")
        print(f"2. 从选择列表中移除频道/群组")
        print(f"3. 清空选择列表")
        print(f"4. 显示当前选择")
        print(f"5. 保存并退出")
        print(f"6. 退出不保存")
        
        while True:
            try:
                choice = input(f"\n请选择操作 (1-6): ").strip()
                
                if choice == '1':
                    # 添加选择
                    print(f"\n输入要添加的频道/群组编号（用空格分隔，如：1 3 5）:")
                    numbers = input("编号: ").strip().split()
                    
                    for num_str in numbers:
                        try:
                            num = int(num_str)
                            if 1 <= num <= len(all_chats):
                                chat = all_chats[num - 1]
                                selected_ids.add(chat['id'])
                                type_icon = {"channel": "📺", "supergroup": "🔒", "group": "👥"}[chat['type']]
                                print(f"  ✅ 已添加: {type_icon} {chat['title']}")
                            else:
                                print(f"  ❌ 编号 {num} 超出范围")
                        except ValueError:
                            print(f"  ❌ 无效编号: {num_str}")
                
                elif choice == '2':
                    # 移除选择
                    if not selected_ids:
                        print("❌ 当前没有选择任何频道/群组")
                        continue
                    
                    print(f"\n当前选择的频道/群组:")
                    selected_chats = [chat for chat in all_chats if chat['id'] in selected_ids]
                    for i, chat in enumerate(selected_chats, 1):
                        type_icon = {"channel": "📺", "supergroup": "🔒", "group": "👥"}[chat['type']]
                        print(f"{i:3d}. {type_icon} {chat['title']}")
                    
                    print(f"\n输入要移除的编号（用空格分隔）:")
                    numbers = input("编号: ").strip().split()
                    
                    for num_str in numbers:
                        try:
                            num = int(num_str)
                            if 1 <= num <= len(selected_chats):
                                chat = selected_chats[num - 1]
                                selected_ids.remove(chat['id'])
                                type_icon = {"channel": "📺", "supergroup": "🔒", "group": "👥"}[chat['type']]
                                print(f"  ❌ 已移除: {type_icon} {chat['title']}")
                            else:
                                print(f"  ❌ 编号 {num} 超出范围")
                        except ValueError:
                            print(f"  ❌ 无效编号: {num_str}")
                
                elif choice == '3':
                    # 清空选择
                    confirm = input("确认清空所有选择? (y/N): ").strip().lower()
                    if confirm == 'y':
                        selected_ids.clear()
                        print("✅ 已清空所有选择")
                
                elif choice == '4':
                    # 显示当前选择
                    if selected_ids:
                        print(f"\n当前选择 ({len(selected_ids)} 个):")
                        for chat in all_chats:
                            if chat['id'] in selected_ids:
                                type_icon = {"channel": "📺", "supergroup": "🔒", "group": "👥"}[chat['type']]
                                print(f"  ✅ {type_icon} {chat['title']}")
                    else:
                        print("❌ 当前没有选择任何频道/群组")
                
                elif choice == '5':
                    # 保存并退出
                    if selected_ids:
                        self.save_selected_chats(selected_ids, all_chats)
                        print(f"✅ 已保存选择并退出")
                    else:
                        print("⚠️ 没有选择任何频道/群组，将清空选择文件")
                        if os.path.exists(self.selected_chats_file):
                            os.remove(self.selected_chats_file)
                    break
                
                elif choice == '6':
                    # 退出不保存
                    print("❌ 已退出，未保存更改")
                    break
                
                else:
                    print("❌ 无效选择，请输入 1-6")
                    
            except KeyboardInterrupt:
                print("\n\n❌ 用户中断操作")
                break
            except Exception as e:
                print(f"❌ 操作出错: {e}")

async def main():
    """主函数"""
    manager = ChatManager()

    try:
        # 显示配置信息
        print("🎯 Telegram 频道/群组选择工具")
        print("=" * 50)
        print(f"📋 配置信息:")
        print(f"   API ID: {manager.api_id}")
        print(f"   代理设置: {'已配置' if manager.proxy_url else '未配置'}")
        if manager.proxy_url:
            print(f"   代理地址: {manager.proxy_url}")
        print(f"   选择文件: {manager.selected_chats_file}")
        print("=" * 50)

        await manager.init_client()
        await manager.interactive_selection()

    except Exception as e:
        logger.error(f"❌ 程序运行失败: {e}")
    finally:
        if manager.client:
            await manager.client.disconnect()
            logger.info("🔌 客户端已断开连接")

if __name__ == "__main__":
    asyncio.run(main())

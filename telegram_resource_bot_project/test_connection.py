"""
测试Telegram连接的简单脚本
用于验证配置是否正确
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telethon import TelegramClient
from telegram import Bot
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_user_client():
    """测试用户客户端"""
    try:
        logger.info("🔍 测试用户客户端连接...")
        
        # 初始化用户客户端
        client = TelegramClient('test_session', Config.API_ID, Config.API_HASH)
        await client.start()
        
        # 获取用户信息
        me = await client.get_me()
        logger.info(f"✅ 用户客户端连接成功")
        logger.info(f"   用户名: {me.first_name} {me.last_name or ''}")
        logger.info(f"   用户名: @{me.username or 'N/A'}")
        logger.info(f"   用户ID: {me.id}")
        
        # 检查用户ID是否匹配
        if me.id == Config.USER_ID:
            logger.info("✅ 用户ID匹配配置")
        else:
            logger.warning(f"⚠️  用户ID不匹配！配置: {Config.USER_ID}, 实际: {me.id}")
        
        # 测试获取对话列表
        logger.info("🔍 测试获取对话列表...")
        dialogs = await client.get_dialogs(limit=10)
        logger.info(f"✅ 成功获取 {len(dialogs)} 个对话")
        
        # 显示前几个对话
        logger.info("📋 对话列表（前5个）:")
        for i, dialog in enumerate(dialogs[:5]):
            logger.info(f"   {i+1}. {dialog.title} (ID: {dialog.id})")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"❌ 用户客户端测试失败: {e}")
        return False

async def test_bot():
    """测试机器人"""
    try:
        logger.info("🔍 测试机器人连接...")
        
        # 初始化机器人
        bot = Bot(token=Config.BOT_TOKEN)
        
        # 获取机器人信息
        bot_info = await bot.get_me()
        logger.info(f"✅ 机器人连接成功")
        logger.info(f"   机器人名: {bot_info.first_name}")
        logger.info(f"   用户名: @{bot_info.username}")
        logger.info(f"   机器人ID: {bot_info.id}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 机器人测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("🚀 开始连接测试...")
    
    # 加载配置
    if os.path.exists('.env'):
        load_dotenv('.env')
        logger.info("✅ 已加载 .env 配置文件")
    
    try:
        # 验证配置
        Config.validate()
        logger.info("✅ 配置验证通过")
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return
    
    # 测试用户客户端
    user_ok = await test_user_client()
    
    # 测试机器人
    bot_ok = await test_bot()
    
    # 总结
    logger.info("=" * 50)
    if user_ok and bot_ok:
        logger.info("🎉 所有测试通过！可以启动机器人了")
        logger.info("运行命令: python start_bot.py")
    else:
        logger.error("❌ 测试失败，请检查配置")
        if not user_ok:
            logger.error("   - 用户客户端配置有问题")
        if not bot_ok:
            logger.error("   - 机器人配置有问题")

if __name__ == "__main__":
    asyncio.run(main())

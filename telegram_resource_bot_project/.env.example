# Telegram资源搜索机器人配置文件
# 复制此文件为 .env 并填入真实的配置信息

# ================================
# 基础配置（必填）
# ================================

# 用户账号ID（数字）
# 获取方法：向 @userinfobot 发送消息获取您的用户ID
USER_ID=123456789

# Telegram API配置（从 https://my.telegram.org 获取）
API_ID=12345678
API_HASH=abcdef1234567890abcdef1234567890

# 机器人Token（从 @BotFather 获取）
BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz

# ================================
# 网络配置（可选）
# ================================

# 代理配置（如果需要代理访问）
# 支持格式：http://host:port 或 socks5://host:port
# 示例：
# PROXY_URL=http://127.0.0.1:7890
# PROXY_URL=socks5://127.0.0.1:1080
PROXY_URL=

# ================================
# 搜索配置
# ================================

# 搜索间隔（秒）- 控制搜索频率，避免触发限制
SEARCH_DELAY=0.5

# 频道切换间隔（秒）- 搜索完一个频道后的等待时间
CHANNEL_DELAY=0.5

# 转发间隔（秒）- 转发消息之间的间隔
FORWARD_DELAY=1.0

# ================================
# 搜索范围配置
# ================================

# 是否搜索频道（true/false）
# 推荐：true - 频道通常包含资源分享，转发成功率高
SEARCH_CHANNELS=true

# 是否搜索群组（true/false）
# 注意：群组消息转发可能因权限限制而失败
# 推荐：false - 避免转发权限问题
SEARCH_GROUPS=false

# ================================
# 限制配置
# ================================

# 最大结果数量 - 单次搜索最多返回多少条结果
MAX_RESULTS=200

# 最大搜索天数 - 用户可以搜索的最大天数范围
MAX_DAYS=30

# 最大文本长度 - 转发消息时的最大文本长度
MAX_TEXT_LENGTH=10000

# 批量处理大小 - 每处理多少条消息后暂停一次
BATCH_SIZE=10

# ================================
# 通知频率配置
# ================================

# 状态更新间隔（每搜索几个频道更新一次状态消息）
# 建议：1-5，数值越小更新越频繁
STATUS_UPDATE_INTERVAL=1

# 进度报告间隔（每搜索几个频道发送一次独立的进度报告）
# 建议：10-50，数值越大报告越少
PROGRESS_REPORT_INTERVAL=20

# ================================
# 日志配置
# ================================

# 日志级别（DEBUG, INFO, WARNING, ERROR）
# DEBUG - 显示所有调试信息
# INFO - 显示一般信息（推荐）
# WARNING - 只显示警告和错误
# ERROR - 只显示错误
LOG_LEVEL=INFO

# ================================
# 配置说明
# ================================

# 快速配置建议：
# 1. 资源搜索场景（推荐）：
#    SEARCH_CHANNELS=true
#    SEARCH_GROUPS=false
#    MAX_RESULTS=200
#    SEARCH_DELAY=0.5

# 2. 保守配置（网络较慢）：
#    SEARCH_DELAY=2.0
#    CHANNEL_DELAY=1.0
#    FORWARD_DELAY=2.0
#    MAX_RESULTS=50

# 3. 激进配置（网络很快）：
#    SEARCH_DELAY=0.1
#    CHANNEL_DELAY=0.1
#    FORWARD_DELAY=0.5
#    MAX_RESULTS=500

# 注意事项：
# - 间隔时间过短可能触发Telegram限制
# - 搜索群组可能导致转发失败
# - 结果数量过多会影响性能
# - 建议先使用默认配置，再根据实际情况调整

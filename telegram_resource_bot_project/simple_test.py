"""
简化的连接测试脚本
直接读取.env文件进行测试
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telethon import TelegramClient
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def main():
    """主测试函数"""
    logger.info("🚀 开始简化连接测试...")
    
    # 加载.env文件
    if os.path.exists('.env'):
        load_dotenv('.env')
        logger.info("✅ 已加载 .env 配置文件")
    else:
        logger.error("❌ 未找到 .env 文件")
        return
    
    # 直接从环境变量读取配置
    user_id = os.getenv('USER_ID')
    api_id = os.getenv('API_ID')
    api_hash = os.getenv('API_HASH')
    bot_token = os.getenv('BOT_TOKEN')
    
    logger.info(f"USER_ID: {user_id}")
    logger.info(f"API_ID: {api_id}")
    logger.info(f"API_HASH: {api_hash[:10]}..." if api_hash else "None")
    logger.info(f"BOT_TOKEN: {bot_token[:20]}..." if bot_token else "None")
    
    # 验证配置
    if not all([user_id, api_id, api_hash, bot_token]):
        logger.error("❌ 配置不完整")
        return
    
    try:
        user_id = int(user_id)
        api_id = int(api_id)
    except ValueError:
        logger.error("❌ USER_ID 或 API_ID 格式错误")
        return
    
    # 测试用户客户端
    logger.info("🔍 测试用户客户端...")
    try:
        client = TelegramClient('test_user_session', api_id, api_hash)
        await client.start()
        
        me = await client.get_me()
        logger.info(f"✅ 用户客户端连接成功")
        logger.info(f"   用户名: {me.first_name} {me.last_name or ''}")
        logger.info(f"   用户名: @{me.username or 'N/A'}")
        logger.info(f"   用户ID: {me.id}")
        logger.info(f"   是否机器人: {'是' if me.bot else '否'}")
        
        if me.bot:
            logger.error("❌ 当前登录的是机器人账号！请用您的个人账号登录")
        elif me.id == user_id:
            logger.info("✅ 用户ID匹配配置")
        else:
            logger.warning(f"⚠️  用户ID不匹配！配置: {user_id}, 实际: {me.id}")
            logger.info("建议更新 .env 文件中的 USER_ID")
        
        # 测试获取对话列表
        if not me.bot:
            logger.info("🔍 测试获取对话列表...")
            dialogs = await client.get_dialogs(limit=5)
            logger.info(f"✅ 成功获取 {len(dialogs)} 个对话")
        
        await client.disconnect()
        
    except Exception as e:
        logger.error(f"❌ 用户客户端测试失败: {e}")
        return
    
    # 测试机器人
    logger.info("🔍 测试机器人...")
    try:
        bot = Bot(token=bot_token)
        bot_info = await bot.get_me()
        logger.info(f"✅ 机器人连接成功")
        logger.info(f"   机器人名: {bot_info.first_name}")
        logger.info(f"   用户名: @{bot_info.username}")
        logger.info(f"   机器人ID: {bot_info.id}")
        
    except Exception as e:
        logger.error(f"❌ 机器人测试失败: {e}")
        return
    
    logger.info("🎉 所有测试通过！可以启动机器人了")

if __name__ == "__main__":
    asyncio.run(main())

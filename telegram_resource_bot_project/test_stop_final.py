"""
最终停止命令测试
验证任务取消机制是否有效
"""

import asyncio
import logging
import time

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TaskCancelTest:
    """测试任务取消机制"""
    
    def __init__(self):
        self.stop_search = False
        self.search_task = None
        
    async def simulate_long_operation(self, duration=10):
        """模拟长时间运行的操作"""
        logger.info(f"🔄 开始模拟长时间操作 ({duration}秒)")
        
        try:
            for i in range(duration * 10):  # 每0.1秒检查一次
                # 检查停止标志
                if self.stop_search:
                    logger.info("🛑 检测到停止标志，退出操作")
                    return "stopped_by_flag"
                
                # 检查任务是否被取消
                if asyncio.current_task().cancelled():
                    logger.info("🛑 任务被取消")
                    raise asyncio.CancelledError()
                
                await asyncio.sleep(0.1)
                
                if i % 10 == 0:  # 每秒报告一次
                    logger.info(f"⏳ 操作进行中... {i//10 + 1}/{duration}秒")
            
            logger.info("✅ 操作正常完成")
            return "completed"
            
        except asyncio.CancelledError:
            logger.info("🛑 操作被取消")
            raise
    
    async def test_stop_by_flag(self):
        """测试通过标志停止"""
        logger.info("\n" + "="*50)
        logger.info("📋 测试1: 通过标志停止")
        
        self.stop_search = False
        
        # 启动停止任务（3秒后设置停止标志）
        async def set_stop_flag():
            await asyncio.sleep(3)
            logger.info("🛑 设置停止标志")
            self.stop_search = True
        
        stop_task = asyncio.create_task(set_stop_flag())
        
        start_time = time.time()
        result = await self.simulate_long_operation(10)
        end_time = time.time()
        
        elapsed = end_time - start_time
        logger.info(f"⏱️  总耗时: {elapsed:.2f}秒")
        logger.info(f"📊 结果: {result}")
        
        if not stop_task.done():
            stop_task.cancel()
        
        return result == "stopped_by_flag" and elapsed < 5
    
    async def test_stop_by_cancel(self):
        """测试通过取消任务停止"""
        logger.info("\n" + "="*50)
        logger.info("📋 测试2: 通过取消任务停止")
        
        self.stop_search = False
        
        # 创建长时间操作任务
        operation_task = asyncio.create_task(self.simulate_long_operation(10))
        
        # 3秒后取消任务
        async def cancel_task():
            await asyncio.sleep(3)
            logger.info("🛑 取消任务")
            operation_task.cancel()
        
        cancel_task_ref = asyncio.create_task(cancel_task())
        
        start_time = time.time()
        try:
            result = await operation_task
            end_time = time.time()
            elapsed = end_time - start_time
            logger.info(f"⏱️  总耗时: {elapsed:.2f}秒")
            logger.info(f"📊 结果: {result}")
            return False  # 不应该到达这里
        except asyncio.CancelledError:
            end_time = time.time()
            elapsed = end_time - start_time
            logger.info(f"⏱️  总耗时: {elapsed:.2f}秒")
            logger.info("✅ 任务成功被取消")
            
            if not cancel_task_ref.done():
                cancel_task_ref.cancel()
            
            return elapsed < 5
    
    async def test_combined_approach(self):
        """测试组合方法（标志 + 任务取消）"""
        logger.info("\n" + "="*50)
        logger.info("📋 测试3: 组合方法（标志 + 任务取消）")
        
        self.stop_search = False
        self.search_task = None
        
        # 模拟搜索任务
        async def search_operation():
            return await self.simulate_long_operation(10)
        
        # 创建搜索任务
        self.search_task = asyncio.create_task(search_operation())
        
        # 3秒后停止
        async def stop_search():
            await asyncio.sleep(3)
            logger.info("🛑 执行停止操作")
            self.stop_search = True
            if self.search_task and not self.search_task.done():
                logger.info("🛑 取消搜索任务")
                self.search_task.cancel()
        
        stop_task = asyncio.create_task(stop_search())
        
        start_time = time.time()
        try:
            result = await self.search_task
            end_time = time.time()
            elapsed = end_time - start_time
            logger.info(f"⏱️  总耗时: {elapsed:.2f}秒")
            logger.info(f"📊 结果: {result}")
            return result == "stopped_by_flag" and elapsed < 5
        except asyncio.CancelledError:
            end_time = time.time()
            elapsed = end_time - start_time
            logger.info(f"⏱️  总耗时: {elapsed:.2f}秒")
            logger.info("✅ 搜索任务成功被取消")
            
            if not stop_task.done():
                stop_task.cancel()
            
            return elapsed < 5

async def main():
    """主测试函数"""
    logger.info("🧪 停止命令最终测试")
    logger.info("测试任务取消机制的有效性\n")
    
    tester = TaskCancelTest()
    
    # 运行所有测试
    test1_result = await tester.test_stop_by_flag()
    await asyncio.sleep(1)
    
    test2_result = await tester.test_stop_by_cancel()
    await asyncio.sleep(1)
    
    test3_result = await tester.test_combined_approach()
    
    # 总结结果
    logger.info("\n" + "="*50)
    logger.info("🎯 测试总结:")
    logger.info(f"  测试1 (标志停止): {'✅ 通过' if test1_result else '❌ 失败'}")
    logger.info(f"  测试2 (任务取消): {'✅ 通过' if test2_result else '❌ 失败'}")
    logger.info(f"  测试3 (组合方法): {'✅ 通过' if test3_result else '❌ 失败'}")
    
    all_passed = test1_result and test2_result and test3_result
    
    if all_passed:
        logger.info("\n🎉 所有测试通过！停止机制工作正常")
        logger.info("💡 机器人应该能够快速响应停止命令")
    else:
        logger.info("\n⚠️  部分测试失败，停止机制可能需要进一步调整")
    
    logger.info("\n💡 建议:")
    logger.info("1. 重启机器人测试实际效果")
    logger.info("2. 发送搜索命令后立即发送停止命令")
    logger.info("3. 观察是否在5秒内停止")

if __name__ == "__main__":
    asyncio.run(main())

# 单频道消息限制配置说明

## 🎯 问题背景

用户发现机器人日志显示"总共检查 500 条消息"，这是因为之前代码中硬编码了单个频道的消息检查限制。

### 原始问题
```python
# 硬编码限制
async for message in self.client.iter_messages(
    dialog.entity,
    limit=500  # 固定限制500条
):
```

## ⚙️ 新增配置

### 配置项说明
```bash
# 每个频道最大检查消息数量
# 建议值：1000-2000（太大会影响性能，太小可能遗漏消息）
# 注意：这是每个频道检查的消息数量，不是匹配结果数量
MAX_MESSAGES_PER_CHANNEL=1000
```

### 配置位置
在 `.env` 文件中添加：
```bash
MAX_MESSAGES_PER_CHANNEL=1000
```

## 📊 两种限制的区别

### 1. MAX_MESSAGES_PER_CHANNEL（新增）
- **作用**：控制每个频道最多检查多少条消息
- **默认值**：1000条
- **影响**：如果频道在指定天数内有超过1000条消息，只检查最新的1000条

### 2. MAX_RESULTS（原有）
- **作用**：控制整个搜索任务最多返回多少条匹配结果
- **默认值**：500条
- **影响**：所有频道总共最多返回500条匹配结果

## 🔄 工作流程

### 搜索流程示例
假设配置：
- `MAX_MESSAGES_PER_CHANNEL=1000`
- `MAX_RESULTS=50`

**执行过程：**
1. **频道A**：检查最新1000条消息 → 找到5条匹配
2. **频道B**：检查最新1000条消息 → 找到8条匹配
3. **频道C**：检查最新1000条消息 → 找到12条匹配
4. **继续搜索...**：直到总匹配数达到50条
5. **停止搜索**：返回50条结果

### 日志显示变化

**修改前：**
```
✅ 频道 测试频道 搜索完成，总共检查 500 条消息，时间范围内 450 条
```

**修改后：**
```
✅ 频道 测试频道 搜索完成，总共检查 1000 条消息，时间范围内 950 条
```

## 🎛️ 配置建议

### 根据使用场景调整

#### 1. 快速搜索（推荐新用户）
```bash
MAX_MESSAGES_PER_CHANNEL=500
MAX_RESULTS=20
```
- **优点**：搜索速度快，资源消耗少
- **缺点**：可能遗漏较早的消息

#### 2. 平衡搜索（推荐日常使用）
```bash
MAX_MESSAGES_PER_CHANNEL=1000
MAX_RESULTS=50
```
- **优点**：平衡速度和覆盖范围
- **缺点**：适中的资源消耗

#### 3. 深度搜索（推荐详细查找）
```bash
MAX_MESSAGES_PER_CHANNEL=2000
MAX_RESULTS=100
```
- **优点**：覆盖范围广，不易遗漏
- **缺点**：搜索时间较长，资源消耗大

#### 4. 极限搜索（谨慎使用）
```bash
MAX_MESSAGES_PER_CHANNEL=5000
MAX_RESULTS=200
```
- **优点**：最大覆盖范围
- **缺点**：可能触发API限制，搜索时间很长

## ⚠️ 注意事项

### 性能影响
- **值越大**：搜索越全面，但速度越慢
- **值越小**：搜索越快，但可能遗漏消息

### API限制
- Telegram API有频率限制
- 过大的值可能导致临时封禁
- 建议不超过3000条/频道

### 内存使用
- 每条消息都会暂时存储在内存中
- 大量消息可能导致内存不足
- 建议根据服务器配置调整

## 🧪 测试建议

### 测试不同配置
1. **设置较小值测试**：
   ```bash
   MAX_MESSAGES_PER_CHANNEL=100
   ```

2. **运行搜索命令**：
   ```
   /search 测试 1
   ```

3. **观察日志输出**：
   ```
   ✅ 频道 测试频道 搜索完成，总共检查 100 条消息
   ```

4. **逐步增加值**：
   ```bash
   MAX_MESSAGES_PER_CHANNEL=500
   MAX_MESSAGES_PER_CHANNEL=1000
   MAX_MESSAGES_PER_CHANNEL=2000
   ```

### 性能监控
- 观察搜索完成时间
- 监控内存使用情况
- 检查是否有API限制警告

## 📈 优化建议

### 根据频道特点调整
- **高活跃频道**：适当增加限制值
- **低活跃频道**：可以使用较小值
- **重要频道**：优先搜索，使用较大值

### 动态调整策略
1. **初始设置**：使用默认值1000
2. **观察效果**：检查是否遗漏重要消息
3. **调整优化**：根据实际需求增减
4. **定期评估**：根据使用情况调整

## 🔧 技术细节

### 代码实现
```python
# 配置读取
self.max_messages_per_channel = Config.MAX_MESSAGES_PER_CHANNEL

# 使用配置
async for message in self.client.iter_messages(
    dialog.entity,
    limit=self.max_messages_per_channel  # 使用配置值
):
```

### 默认值设置
- 如果未配置，默认使用1000条
- 兼容旧版本配置文件
- 自动应用新配置

## 📋 配置验证

### 检查当前配置
查看机器人启动时的配置信息：
```
📋 配置信息：
   - 每频道最大检查消息数：1000
   - 全局最大结果数：50
```

### 验证配置效果
1. 修改配置文件
2. 重启机器人
3. 运行搜索命令
4. 观察日志中的"总共检查 X 条消息"

## 🎉 总结

通过添加 `MAX_MESSAGES_PER_CHANNEL` 配置：

### ✅ 解决的问题
- 消除硬编码限制
- 提供灵活的配置选项
- 允许用户根据需求调整

### ✅ 带来的好处
- **可配置性**：用户可以自定义检查范围
- **性能控制**：避免过度消耗资源
- **覆盖优化**：平衡速度和完整性
- **向后兼容**：不影响现有功能

### 💡 使用建议
- 新用户：使用默认值1000
- 有经验用户：根据需求调整
- 定期评估：根据使用效果优化配置

现在您可以通过调整 `MAX_MESSAGES_PER_CHANNEL` 来控制每个频道检查的消息数量，不再受硬编码的500条限制！

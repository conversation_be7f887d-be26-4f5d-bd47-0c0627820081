"""
Telegram资源搜索机器人
功能：通过机器人命令搜索频道内的资源并转发结果
作者：AI Assistant
创建时间：2024
"""

import os
import asyncio
import logging
import re
from datetime import datetime, timedelta, timezone
from typing import List, Optional
import time

# Telegram相关库
from telethon import TelegramClient, events
from telethon.tl.types import Channel, Chat, User, MessageMediaDocument, MessageMediaPhoto
from telegram import Update, Bot
from telegram.ext import Application, CommandHandler, ContextTypes

# 本地配置
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# 隐藏httpx的INFO日志
logging.getLogger('httpx').setLevel(logging.WARNING)
logging.getLogger('telegram.ext').setLevel(logging.WARNING)

class TelegramResourceBot:
    """Telegram资源搜索机器人主类"""
    
    def __init__(self):
        """初始化机器人配置"""
        # 验证配置
        Config.validate()

        # 从配置类获取配置
        self.user_id = Config.USER_ID
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.bot_token = Config.BOT_TOKEN
        self.proxy_url = Config.PROXY_URL

        # 初始化客户端
        self.client = None
        self.bot_app = None
        self.is_searching = False

        # 搜索配置
        self.search_delay = Config.SEARCH_DELAY
        self.channel_delay = Config.CHANNEL_DELAY
        self.forward_delay = Config.FORWARD_DELAY
        self.max_results = Config.MAX_RESULTS
        self.max_days = Config.MAX_DAYS
        self.max_text_length = Config.MAX_TEXT_LENGTH
        self.max_messages_per_channel = Config.MAX_MESSAGES_PER_CHANNEL
        self.batch_size = Config.BATCH_SIZE
        self.status_update_interval = Config.STATUS_UPDATE_INTERVAL
        self.progress_report_interval = Config.PROGRESS_REPORT_INTERVAL

        # 搜索范围配置
        self.search_channels = getattr(Config, 'SEARCH_CHANNELS', True)  # 是否搜索频道
        self.search_groups = getattr(Config, 'SEARCH_GROUPS', False)     # 是否搜索群组（默认关闭）
        # try_forward_group_messages 已废弃 - 现在统一发送消息内容

        # 消息内容配置
        self.send_full_content = getattr(Config, 'SEND_FULL_CONTENT', True)  # 是否发送完整消息内容

        # 选择性搜索配置
        self.use_selective_search = getattr(Config, 'USE_SELECTIVE_SEARCH', False)  # 是否启用选择性搜索
        self.selected_chats_file = getattr(Config, 'SELECTED_CHATS_FILE', 'selected_chats.txt')  # 选中的频道/群组文件

        # 时区配置
        self.beijing_tz = timezone(timedelta(hours=8))  # 北京时间时区

    def utc_to_beijing(self, utc_time):
        """将UTC时间转换为北京时间"""
        if utc_time is None:
            return None

        # 确保输入时间有时区信息
        if utc_time.tzinfo is None:
            utc_time = utc_time.replace(tzinfo=timezone.utc)

        # 转换为北京时间
        beijing_time = utc_time.astimezone(self.beijing_tz)
        return beijing_time

    def format_beijing_time(self, utc_time, format_str='%Y-%m-%d %H:%M:%S'):
        """格式化UTC时间为北京时间字符串"""
        if utc_time is None:
            return "未知时间"

        beijing_time = self.utc_to_beijing(utc_time)
        return beijing_time.strftime(format_str)

    def format_beijing_time_short(self, utc_time):
        """格式化UTC时间为北京时间短格式（月-日 时:分）"""
        if utc_time is None:
            return "未知"

        beijing_time = self.utc_to_beijing(utc_time)
        return beijing_time.strftime('%m-%d %H:%M')

    async def init_clients(self):
        """初始化Telegram客户端"""
        try:
            # 配置代理（如果提供）
            proxy = None
            if self.proxy_url:
                # 解析代理URL，支持格式：http://host:port 或 socks5://host:port
                if self.proxy_url.startswith('http://'):
                    proxy = {
                        'proxy_type': 'HTTP',
                        'addr': self.proxy_url.replace('http://', '').split(':')[0],
                        'port': int(self.proxy_url.replace('http://', '').split(':')[1])
                    }
                elif self.proxy_url.startswith('socks5://'):
                    proxy = {
                        'proxy_type': 'SOCKS5',
                        'addr': self.proxy_url.replace('socks5://', '').split(':')[0],
                        'port': int(self.proxy_url.replace('socks5://', '').split(':')[1])
                    }

            # 初始化用户客户端（用于搜索消息）
            self.client = TelegramClient('user_session', self.api_id, self.api_hash, proxy=proxy)
            await self.client.start()

            # 验证用户客户端是否为正确的用户
            me = await self.client.get_me()

            # 检查是否是机器人账号
            if me.bot:
                logger.error(f"❌ 错误：当前登录的是机器人账号 (@{me.username})，不是用户账号！")
                logger.error("请删除 user_session.session 文件，然后用您的个人账号重新登录")
                raise ValueError("当前登录的是机器人账号，需要用户账号")

            if me.id != self.user_id:
                logger.warning(f"警告：当前登录用户ID ({me.id}) 与配置的USER_ID ({self.user_id}) 不匹配")
                logger.warning("建议运行 python fix_user_id.py 来修复用户ID")

            logger.info(f"用户客户端初始化成功 - 用户: {me.first_name} (ID: {me.id})")

            # 初始化机器人应用（用于接收命令和发送消息）
            bot_proxy = None
            if proxy:
                # 为机器人配置代理
                if proxy['proxy_type'] == 'HTTP':
                    bot_proxy = f"http://{proxy['addr']}:{proxy['port']}"
                elif proxy['proxy_type'] == 'SOCKS5':
                    bot_proxy = f"socks5://{proxy['addr']}:{proxy['port']}"

            if bot_proxy:
                self.bot_app = Application.builder().token(self.bot_token).proxy_url(bot_proxy).build()
            else:
                self.bot_app = Application.builder().token(self.bot_token).build()

            # 添加命令处理器
            self.bot_app.add_handler(CommandHandler("search", self.handle_search_command))
            self.bot_app.add_handler(CommandHandler("start", self.handle_start_command))
            self.bot_app.add_handler(CommandHandler("help", self.handle_help_command))

            # 添加错误处理器
            self.bot_app.add_error_handler(self.error_handler)

            logger.info("机器人应用初始化成功")

            # 测试连接
            await self.test_connections()

        except Exception as e:
            logger.error(f"客户端初始化失败: {e}")
            raise

    async def test_connections(self):
        """测试连接状态"""
        try:
            # 测试用户客户端
            me = await self.client.get_me()
            logger.info(f"✅ 用户客户端连接正常 - {me.first_name} (@{me.username or 'N/A'})")

            # 测试获取对话列表
            dialogs = await self.client.get_dialogs(limit=5)
            logger.info(f"✅ 成功获取对话列表，共 {len(dialogs)} 个对话（测试前5个）")

            # 测试机器人
            bot = Bot(token=self.bot_token)
            bot_info = await bot.get_me()
            logger.info(f"✅ 机器人连接正常 - {bot_info.first_name} (@{bot_info.username})")

        except Exception as e:
            logger.error(f"❌ 连接测试失败: {e}")
            raise

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """全局错误处理器"""
        try:
            error = context.error
            logger.error(f"❌ 机器人运行时错误: {error}")

            # 网络连接错误
            if "ConnectError" in str(error) or "NetworkError" in str(error):
                logger.error("🌐 网络连接错误，可能的原因：")
                logger.error("   1. 网络连接不稳定")
                logger.error("   2. 代理配置问题")
                logger.error("   3. Telegram API服务器暂时不可用")

                if update and update.effective_chat:
                    try:
                        await context.bot.send_message(
                            chat_id=update.effective_chat.id,
                            text="❌ **网络连接错误**\n\n"
                                 "可能的原因：\n"
                                 "• 网络连接不稳定\n"
                                 "• 代理配置问题\n"
                                 "• 服务器暂时不可用\n\n"
                                 "请稍后重试，或检查网络设置",
                            parse_mode='Markdown'
                        )
                    except:
                        pass  # 如果连发送错误消息都失败，就不处理了

            # 权限错误
            elif "Forbidden" in str(error):
                logger.error("🔒 权限错误，机器人可能被禁用或权限不足")

            # 其他错误
            else:
                logger.error(f"🔧 其他错误: {error}")

        except Exception as e:
            logger.error(f"错误处理器本身出错: {e}")

        # 重置搜索状态
        self.is_searching = False

    async def check_network_connection(self):
        """检查网络连接状态"""
        try:
            # 测试机器人连接
            bot = Bot(token=self.bot_token)
            await bot.get_me()
            return True
        except Exception as e:
            logger.error(f"网络连接检查失败: {e}")
            return False

    async def safe_send_message(self, bot, chat_id, text, parse_mode=None, max_retries=3):
        """安全发送消息，带重试机制"""
        for retry in range(max_retries):
            try:
                return await bot.send_message(
                    chat_id=chat_id,
                    text=text,
                    parse_mode=parse_mode
                )
            except Exception as e:
                logger.warning(f"发送消息失败 (重试 {retry+1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    await asyncio.sleep(2 ** retry)  # 指数退避
                else:
                    logger.error(f"发送消息最终失败: {text[:50]}...")
                    raise

    async def safe_edit_message(self, message, text, parse_mode=None, max_retries=3):
        """安全编辑消息，带重试机制"""
        if not message:
            return

        for retry in range(max_retries):
            try:
                return await message.edit_text(text=text, parse_mode=parse_mode)
            except Exception as e:
                logger.warning(f"编辑消息失败 (重试 {retry+1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    await asyncio.sleep(2 ** retry)  # 指数退避
                else:
                    logger.error(f"编辑消息最终失败: {text[:50]}...")
                    return  # 编辑失败不抛出异常，继续执行
    
    async def handle_start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/start命令"""
        if update.effective_user.id != self.user_id:
            await update.message.reply_text("❌ 您没有权限使用此机器人")
            return
            
        welcome_text = """
🤖 **Telegram资源搜索机器人**

欢迎使用！此机器人可以帮您搜索频道内的资源。

**使用方法：**
`/search 资源名称 天数` - 开始搜索

**示例：**
`/search 影视 3` - 搜索最近3天内包含"影视"的消息
`/search 电影 7` - 搜索最近7天内包含"电影"的消息

**功能特点：**
✅ 支持搜索所有已加入的频道
✅ 支持文字和文件名匹配
✅ 智能频率控制，避免限制
✅ 自动转发匹配结果

输入 `/help` 查看更多帮助信息。
        """
        await update.message.reply_text(welcome_text, parse_mode='Markdown')
    
    async def handle_help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/help命令"""
        if update.effective_user.id != self.user_id:
            await update.message.reply_text("❌ 您没有权限使用此机器人")
            return
            
        help_text = """
📖 **帮助文档**

**命令格式：**
`/search <关键词> <天数>` - 开始搜索

**参数说明：**
• 关键词：要搜索的资源名称（支持中英文）
• 天数：搜索范围，1-30天（建议不超过7天）

**搜索范围：**
• 全部搜索模式：在所有已加入的频道/群组中搜索
• 选择性搜索模式：只在指定的频道/群组中搜索
• 包括文字消息和文件名
• 支持模糊匹配

**消息显示模式：**
• 完整内容模式：显示完整消息内容和媒体信息
• 简洁信息模式：只显示预览和链接，节省空间
• 可在配置文件中切换：`SEND_FULL_CONTENT=true/false`

**选择性搜索：**
• 启用方法：设置 `USE_SELECTIVE_SEARCH=true`
• 管理工具：运行 `python manage_chats.py` 选择频道
• 效果：只在指定频道/群组中搜索，大幅提升效率

**注意事项：**
• 搜索过程可能需要一些时间，请耐心等待
• 为避免触发限制，搜索速度会自动控制
• 建议搜索范围不要过大
• 所有消息都只在对话中显示，不会出现在收藏夹

**状态说明：**
🔍 搜索中... - 正在搜索频道消息
✅ 搜索完成 - 已完成搜索并发送结果
❌ 搜索失败 - 遇到错误，请重试
        """
        await update.message.reply_text(help_text, parse_mode='Markdown')



    async def handle_search_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/search命令"""
        if update.effective_user.id != self.user_id:
            await update.message.reply_text("❌ 您没有权限使用此机器人")
            return

        if self.is_searching:
            await update.message.reply_text("🔍 正在搜索中，请等待当前搜索完成...")
            return

        # 解析命令参数
        try:
            args = context.args
            if len(args) != 2:
                await update.message.reply_text(
                    "❌ 命令格式错误！\n正确格式：`/search 资源名称 天数`\n示例：`/search 影视 3`",
                    parse_mode='Markdown'
                )
                return

            keyword = args[0]
            days = int(args[1])

            if days < 1 or days > self.max_days:
                await update.message.reply_text(f"❌ 天数范围应在1-{self.max_days}之间")
                return

        except ValueError:
            await update.message.reply_text("❌ 天数必须是数字！")
            return
        except Exception as e:
            await update.message.reply_text(f"❌ 参数解析错误：{e}")
            return

        # 开始搜索
        self.is_searching = True

        # 尝试发送状态消息，如果失败则重试
        status_message = None
        for retry in range(3):
            try:
                status_message = await update.message.reply_text(
                    f"🔍 **开始搜索任务**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 状态：正在初始化...",
                    parse_mode='Markdown'
                )
                break
            except Exception as e:
                logger.warning(f"发送状态消息失败 (重试 {retry+1}/3): {e}")
                if retry < 2:
                    await asyncio.sleep(2)  # 等待2秒后重试
                else:
                    # 最后一次重试失败，发送简单消息
                    try:
                        status_message = await update.message.reply_text(
                            f"🔍 开始搜索: {keyword} (最近{days}天)"
                        )
                    except:
                        logger.error("无法发送任何状态消息，继续搜索...")
                        pass

        try:
            # 执行搜索（传递update和状态消息用于实时通知）
            results = await self.search_messages(keyword, days, update, status_message)

            if not results:
                await status_message.edit_text(
                    f"😔 **搜索完成 - 无结果**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 结果：未找到匹配消息\n\n"
                    f"💡 **建议：**\n"
                    f"• 尝试其他关键词\n"
                    f"• 扩大搜索时间范围\n"
                    f"• 检查是否已加入相关频道",
                    parse_mode='Markdown'
                )
            else:
                await status_message.edit_text(
                    f"✅ **搜索任务完成！**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 找到：**{len(results)}** 条匹配消息\n"
                    f"📋 所有结果已发送完毕",
                    parse_mode='Markdown'
                )

        except Exception as e:
            logger.error(f"搜索过程中出错: {e}")
            await status_message.edit_text(
                f"❌ **搜索任务失败**\n"
                f"🎯 关键词：**{keyword}**\n"
                f"📅 时间范围：最近 **{days}** 天\n"
                f"💥 错误：{str(e)[:100]}...\n\n"
                f"请稍后重试或联系管理员",
                parse_mode='Markdown'
            )
        finally:
            self.is_searching = False

    async def search_messages(self, keyword: str, days: int, update=None, status_message=None) -> List[dict]:
        """搜索频道消息"""
        results = []
        search_count = 0
        found_count = 0

        # 计算搜索时间范围（使用UTC时区）
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)

        # 转换为北京时间用于日志显示
        beijing_start = self.format_beijing_time(start_date)
        beijing_end = self.format_beijing_time(end_date)
        logger.info(f"🔍 开始搜索关键词: {keyword}")
        logger.info(f"📅 搜索时间范围 (北京时间): {beijing_start} 到 {beijing_end}")
        logger.info(f"📅 搜索时间范围 (UTC): {start_date} 到 {end_date}")

        # 创建机器人实例用于发送即时通知
        bot = None
        chat_id = None
        if update:
            from telegram import Bot
            bot = Bot(token=self.bot_token)
            chat_id = update.effective_chat.id

            # 记录转发目标信息
            logger.info(f"📍 转发目标信息:")
            logger.info(f"   Chat ID: {chat_id}")
            logger.info(f"   Chat Type: {update.effective_chat.type}")
            logger.info(f"   Chat Title: {getattr(update.effective_chat, 'title', '私聊')}")
            logger.info(f"   User ID: {update.effective_user.id}")
            logger.info(f"   User Name: {update.effective_user.first_name}")

            # 确认是否发送到正确的用户
            if update.effective_user.id == self.user_id:
                logger.info(f"✅ 确认：消息将发送到配置的用户账户")
            else:
                logger.warning(f"⚠️  警告：发送者 ({update.effective_user.id}) 与配置用户 ({self.user_id}) 不匹配")

        try:
            # 更新状态：连接检查
            if status_message:
                await self.safe_edit_message(
                    status_message,
                    f"🔍 **搜索进行中**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 状态：检查连接状态...",
                    parse_mode='Markdown'
                )

            # 确保用户客户端已连接
            if not self.client.is_connected():
                await self.client.connect()
                logger.info("重新连接用户客户端")

            # 更新状态：获取对话列表
            if status_message:
                await self.safe_edit_message(
                    status_message,
                    f"🔍 **搜索进行中**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 状态：正在获取频道列表...",
                    parse_mode='Markdown'
                )

            # 获取所有对话（频道、群组等）- 使用用户客户端
            logger.info("📋 正在获取对话列表...")
            dialogs = await self.client.get_dialogs()
            logger.info(f"📋 获取到 {len(dialogs)} 个对话")

            # 根据配置过滤频道和群组
            channels_only = []
            groups_only = []
            for dialog in dialogs:
                if isinstance(dialog.entity, Channel):
                    # 通过 megagroup 属性区分频道和超级群组
                    if dialog.entity.megagroup:
                        # 超级群组
                        groups_only.append(dialog)
                    else:
                        # 频道
                        channels_only.append(dialog)
                elif isinstance(dialog.entity, Chat):
                    # 普通群组
                    groups_only.append(dialog)

            logger.info(f"📋 找到 {len(channels_only)} 个频道，{len(groups_only)} 个群组")

            # 根据配置决定搜索范围
            if self.use_selective_search:
                # 选择性搜索模式
                search_targets = await self.get_selected_chats(channels_only, groups_only)
                logger.info(f"📋 选择性搜索模式：将搜索 {len(search_targets)} 个指定的频道/群组")
            else:
                # 全部搜索模式
                search_targets = []
                if self.search_channels:
                    search_targets.extend(channels_only)
                    logger.info(f"📋 将搜索 {len(channels_only)} 个频道")
                if self.search_groups:
                    search_targets.extend(groups_only)
                    logger.info(f"📋 将搜索 {len(groups_only)} 个群组（注意：群组消息转发可能受限）")

            if not search_targets:
                if self.use_selective_search:
                    logger.warning("⚠️ 选择性搜索模式下没有找到指定的频道/群组")
                    logger.warning("💡 请运行 'python manage_chats.py' 来选择要搜索的频道/群组")
                else:
                    logger.warning("⚠️ 没有可搜索的目标（频道和群组搜索都被禁用）")
                return []

            channels_and_groups = search_targets
            logger.info(f"📋 总共将搜索 {len(channels_and_groups)} 个目标")

            # 更新状态：开始搜索
            if status_message:
                await status_message.edit_text(
                    f"🔍 **搜索进行中**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 状态：开始搜索 {len(channels_and_groups)} 个频道...\n"
                    f"🎯 已找到：0 条匹配消息",
                    parse_mode='Markdown'
                )

            for i, dialog in enumerate(channels_and_groups):

                try:
                    logger.info(f"🔍 正在搜索 ({i+1}/{len(channels_and_groups)}): {dialog.title}")

                    # 更新状态消息（根据配置的间隔更新）
                    if status_message and i % self.status_update_interval == 0:
                        try:
                            progress_percent = int((i / len(channels_and_groups)) * 100)
                            await status_message.edit_text(
                                f"🔍 **搜索进行中**\n"
                                f"🎯 关键词：**{keyword}**\n"
                                f"📅 时间范围：最近 **{days}** 天\n"
                                f"📊 进度：{i}/{len(channels_and_groups)} 个频道 ({progress_percent}%)\n"
                                f"📍 当前：{dialog.title[:25]}{'...' if len(dialog.title) > 25 else ''}\n"
                                f"🎯 已找到：{found_count} 条匹配消息",
                                parse_mode='Markdown'
                            )
                        except Exception as e:
                            logger.warning(f"更新状态消息失败: {e}")

                    # 获取消息历史（从最新开始搜索）
                    message_count = 0
                    checked_count = 0
                    found_in_range = 0

                    # 获取消息
                    messages = []
                    async for message in self.client.iter_messages(
                        dialog.entity,
                        limit=self.max_messages_per_channel  # 使用配置的消息数量限制
                    ):
                        messages.append(message)
                        checked_count += 1

                        # 如果消息时间早于搜索范围，停止获取
                        if message.date < start_date:
                            break

                    # 处理获取到的消息
                    for message in messages:
                        # 检查消息时间是否在范围内
                        if message.date >= start_date and message.date <= end_date:
                            found_in_range += 1
                            message_count += 1

                            # 检查是否匹配关键词
                            if await self.is_message_match(message, keyword):
                                result = {
                                    'message': message,
                                    'chat': dialog.entity,
                                    'chat_title': dialog.title,
                                    'date': message.date,
                                    'match_type': await self.get_match_type(message, keyword)
                                }
                                results.append(result)
                                found_count += 1
                                beijing_time_str = self.format_beijing_time(message.date)
                                logger.info(f"✅ 找到匹配消息 #{found_count}: {dialog.title} - {beijing_time_str} (北京时间)")

                                # 立即发送找到的消息
                                if bot and chat_id:
                                    try:
                                        await self.send_single_result(bot, chat_id, result, found_count)
                                        # 添加小延迟，避免发送过快
                                        await asyncio.sleep(0.5)
                                    except Exception as e:
                                        logger.warning(f"发送即时结果失败: {e}")

                                # 限制结果数量
                                if len(results) >= self.max_results:
                                    logger.info(f"🎯 达到最大结果数量限制: {self.max_results}")
                                    return results

                            search_count += 1

                            # 频率控制
                            if search_count % self.batch_size == 0:
                                await asyncio.sleep(self.search_delay)

                    logger.info(f"✅ 频道 {dialog.title} 搜索完成，总共检查 {checked_count} 条消息，时间范围内 {found_in_range} 条")

                except Exception as e:
                    logger.warning(f"搜索频道 {dialog.title} 时出错: {e}")
                    continue

                # 频道间延迟
                await asyncio.sleep(self.channel_delay)

        except Exception as e:
            logger.error(f"搜索消息时出错: {e}")
            raise

        logger.info(f"🎉 搜索完成！共找到 {len(results)} 条匹配消息")

        # 更新最终状态
        if status_message:
            try:
                await self.safe_edit_message(
                    status_message,
                    f"🎉 **搜索任务完成！**\n"
                    f"🎯 关键词：**{keyword}**\n"
                    f"📅 时间范围：最近 **{days}** 天\n"
                    f"📊 搜索了：{len(channels_and_groups)} 个频道\n"
                    f"🎯 找到：**{found_count}** 条匹配消息\n"
                    f"✅ 状态：所有结果已发送完毕",
                    parse_mode='Markdown'
                )
            except Exception as e:
                logger.warning(f"更新最终状态失败: {e}")

        return results

    async def get_selected_chats(self, channels_only, groups_only):
        """获取选择的频道/群组"""
        try:
            import os

            if not os.path.exists(self.selected_chats_file):
                logger.warning(f"⚠️ 选择文件不存在: {self.selected_chats_file}")
                logger.info("💡 请运行 'python manage_chats.py' 来选择要搜索的频道/群组")
                return []

            # 读取选择的ID列表
            selected_ids = set()
            with open(self.selected_chats_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # 提取ID（格式：ID|标题|类型）
                        if '|' in line:
                            try:
                                chat_id = int(line.split('|')[0].strip())
                                selected_ids.add(chat_id)
                            except ValueError:
                                continue

            if not selected_ids:
                logger.warning("⚠️ 选择文件中没有有效的频道/群组ID")
                return []

            # 从所有频道/群组中筛选出选择的
            all_chats = channels_only + groups_only
            selected_chats = []

            for dialog in all_chats:
                if dialog.entity.id in selected_ids:
                    selected_chats.append(dialog)

            # 记录匹配情况
            found_ids = {dialog.entity.id for dialog in selected_chats}
            missing_ids = selected_ids - found_ids

            if missing_ids:
                logger.warning(f"⚠️ 有 {len(missing_ids)} 个选择的频道/群组未找到（可能已退出或删除）")

            logger.info(f"✅ 成功匹配 {len(selected_chats)} 个选择的频道/群组")

            return selected_chats

        except Exception as e:
            logger.error(f"❌ 读取选择文件失败: {e}")
            return []

    async def is_message_match(self, message, keyword: str) -> bool:
        """检查消息是否匹配关键词"""
        keyword_lower = keyword.lower()

        # 检查消息文本
        if message.text and keyword_lower in message.text.lower():
            return True

        # 检查文件名
        if message.media:
            if isinstance(message.media, MessageMediaDocument):
                if message.media.document and message.media.document.attributes:
                    for attr in message.media.document.attributes:
                        if hasattr(attr, 'file_name') and attr.file_name:
                            if keyword_lower in attr.file_name.lower():
                                return True

            # 检查图片说明
            if isinstance(message.media, MessageMediaPhoto) and message.text:
                if keyword_lower in message.text.lower():
                    return True

        # 检查消息标题（如果有）
        if hasattr(message, 'title') and message.title:
            if keyword_lower in message.title.lower():
                return True

        return False

    async def get_match_type(self, message, keyword: str) -> str:
        """获取匹配类型"""
        keyword_lower = keyword.lower()

        if message.text and keyword_lower in message.text.lower():
            return "文本消息"

        if message.media and isinstance(message.media, MessageMediaDocument):
            if message.media.document and message.media.document.attributes:
                for attr in message.media.document.attributes:
                    if hasattr(attr, 'file_name') and attr.file_name:
                        if keyword_lower in attr.file_name.lower():
                            return f"文件名: {attr.file_name}"

        return "其他匹配"

    async def send_single_result(self, bot, chat_id: int, result: dict, result_number: int):
        """立即发送单个搜索结果"""
        try:
            message = result['message']
            chat_title = result['chat_title']
            match_type = result['match_type']
            chat_entity = result['chat']

            # 记录发送目标
            logger.info(f"📤 正在发送结果 #{result_number} 到 chat_id: {chat_id}")

            # 生成消息链接
            message_link = await self.generate_message_link(chat_entity, message)

            # 构建包含链接的头部信息（使用北京时间）
            beijing_time_str = self.format_beijing_time_short(message.date)
            header = f"🎯 **#{result_number}** | 📺 {chat_title} | 📅 {beijing_time_str} | 🔍 {match_type}"

            # 发送头部信息（带错误处理）
            try:
                await bot.send_message(chat_id=chat_id, text=header, parse_mode='Markdown')
                logger.info(f"📤 已发送头部信息到 chat_id: {chat_id}")
            except Exception as e:
                logger.warning(f"发送Markdown头部失败，改为纯文本: {e}")
                # 移除Markdown格式，使用纯文本
                plain_header = header.replace('**', '').replace('*', '')
                await bot.send_message(chat_id=chat_id, text=plain_header)
                logger.info(f"📤 已发送纯文本头部信息到 chat_id: {chat_id}")

            # 单独发送链接，避免Markdown解析影响
            if message_link:
                link_text = f"🔗 原始链接：{message_link}"
                try:
                    await bot.send_message(chat_id=chat_id, text=link_text)
                    logger.info(f"📤 已发送链接信息到 chat_id: {chat_id}")
                except Exception as e:
                    logger.warning(f"发送链接失败: {e}")

            # 检查消息来源类型
            is_from_group = isinstance(chat_entity, Chat) or (isinstance(chat_entity, Channel) and chat_entity.megagroup)
            source_type = "群组" if is_from_group else "频道"

            # 根据配置决定是否发送完整消息内容
            if message.text or message.media:
                if self.send_full_content:
                    # 发送完整消息内容
                    try:
                        await self.send_message_content(bot, chat_id, message, message_link)
                        logger.info(f"✅ 成功发送{source_type}完整消息内容 #{result_number}")
                    except Exception as e:
                        logger.error(f"❌ 发送{source_type}消息内容失败: {e}")
                        # 发送错误提示
                        error_text = f"❌ 无法显示此消息内容\n📺 消息来源：{chat_title}"
                        if message_link:
                            error_text += f"\n🔗 原始链接：{message_link}"
                        await bot.send_message(chat_id=chat_id, text=error_text)
                else:
                    # 简洁模式：不发送额外信息，只有头部信息和链接
                    logger.info(f"✅ 简洁模式：已发送{source_type}头部信息和链接 #{result_number}")
                    # 简洁模式下不需要发送额外的消息3

        except Exception as e:
            logger.error(f"发送单个结果时出错: {e}")



    async def generate_message_link(self, chat_entity, message) -> str:
        """生成消息链接"""
        try:
            # 获取频道/群组的用户名或ID
            if hasattr(chat_entity, 'username') and chat_entity.username:
                # 公开频道，使用用户名
                chat_identifier = chat_entity.username
                link = f"https://t.me/{chat_identifier}/{message.id}"
            else:
                # 私有频道或群组，使用ID
                # 对于私有频道，链接格式为 https://t.me/c/频道ID/消息ID
                # 需要去掉ID前面的-100前缀
                chat_id = str(chat_entity.id)
                if chat_id.startswith('-100'):
                    chat_id = chat_id[4:]  # 去掉-100前缀
                link = f"https://t.me/c/{chat_id}/{message.id}"

            logger.debug(f"生成消息链接: {link}")
            return link

        except Exception as e:
            logger.warning(f"生成消息链接失败: {e}")
            return None

    def clean_text_for_markdown(self, text: str) -> str:
        """清理文本中可能导致Markdown解析错误的字符"""
        if not text:
            return text

        # 转义可能导致问题的Markdown字符
        # 但保留基本的格式
        import re

        # 移除或转义可能有问题的字符
        # 保留文本内容，但避免Markdown解析错误
        cleaned = text

        # 转义未配对的星号
        cleaned = re.sub(r'(?<!\*)\*(?!\*)', r'\\*', cleaned)

        # 转义未配对的下划线
        cleaned = re.sub(r'(?<!_)_(?!_)', r'\\_', cleaned)

        # 转义方括号
        cleaned = cleaned.replace('[', '\\[').replace(']', '\\]')

        # 转义反引号
        cleaned = cleaned.replace('`', '\\`')

        return cleaned

    async def send_message_content(self, bot, chat_id: int, message, message_link: str = None):
        """发送消息内容（避免消息出现在收藏夹中）"""
        try:
            # 添加消息内容标识
            content_parts = []

            # 处理文本消息
            if message.text:
                # 清理文本内容，避免Markdown解析错误
                cleaned_text = self.clean_text_for_markdown(message.text)
                content_parts.append(cleaned_text)

            # 处理媒体文件信息
            if message.media:
                media_info = self.get_media_info(message)
                if media_info:
                    content_parts.append(media_info)

            # 合并所有内容
            if content_parts:
                full_content = "\n\n".join(content_parts)

                # 如果内容过长，分段发送
                if len(full_content) > 4000:  # Telegram消息长度限制
                    # 分段发送长文本
                    for i in range(0, len(full_content), 4000):
                        chunk = full_content[i:i+4000]
                        if i > 0:
                            chunk = f"📄 **(续)** {chunk}"

                        # 尝试发送Markdown，失败则降级为纯文本
                        try:
                            await bot.send_message(chat_id=chat_id, text=chunk, parse_mode='Markdown')
                        except Exception as markdown_error:
                            logger.warning(f"Markdown发送失败，改为纯文本: {markdown_error}")
                            plain_chunk = chunk.replace('**', '').replace('*', '')
                            await bot.send_message(chat_id=chat_id, text=plain_chunk)

                        await asyncio.sleep(0.5)  # 避免发送过快
                else:
                    # 尝试发送Markdown，失败则降级为纯文本
                    try:
                        await bot.send_message(chat_id=chat_id, text=full_content, parse_mode='Markdown')
                    except Exception as markdown_error:
                        logger.warning(f"Markdown发送失败，改为纯文本: {markdown_error}")
                        plain_content = full_content.replace('**', '').replace('*', '')
                        await bot.send_message(chat_id=chat_id, text=plain_content)
            else:
                # 如果没有内容，发送提示
                await bot.send_message(chat_id=chat_id, text="📝 消息内容为空或无法显示")

        except Exception as e:
            logger.error(f"发送消息内容时出错: {e}")
            # 最后的降级处理：发送纯文本
            try:
                if message.text:
                    # 完全移除所有特殊字符，只保留纯文本
                    simple_content = message.text
                    await bot.send_message(chat_id=chat_id, text=simple_content)
                elif message.media:
                    # 如果只有媒体文件，发送简化的文件信息
                    try:
                        media_info = self.get_media_info(message)
                        if media_info:
                            # 完全移除Markdown格式
                            simple_media_info = media_info.replace('**', '').replace('*', '').replace('📎', '文件:').replace('📏', '大小:')
                            await bot.send_message(chat_id=chat_id, text=simple_media_info)
                        else:
                            await bot.send_message(chat_id=chat_id, text="媒体文件")
                    except:
                        await bot.send_message(chat_id=chat_id, text="媒体文件")
                else:
                    await bot.send_message(chat_id=chat_id, text="无法显示消息内容")
            except Exception as e2:
                logger.error(f"发送简化消息内容也失败: {e2}")
                # 最后的尝试：发送一个简单的错误消息
                try:
                    await bot.send_message(chat_id=chat_id, text="消息发送失败")
                except:
                    pass  # 如果连这个都失败，就放弃

    def get_media_info(self, message) -> str:
        """获取媒体文件信息"""
        try:
            if isinstance(message.media, MessageMediaDocument):
                if message.media.document and message.media.document.attributes:
                    for attr in message.media.document.attributes:
                        if hasattr(attr, 'file_name') and attr.file_name:
                            file_size = message.media.document.size if message.media.document.size else "未知"
                            # 清理文件名中可能导致Markdown问题的字符
                            safe_filename = self.clean_text_for_markdown(attr.file_name)
                            return f"📎 **文件：** {safe_filename}\n📏 **大小：** {self.format_file_size(file_size)}"

            elif isinstance(message.media, MessageMediaPhoto):
                return "🖼️ **图片文件**"

            return "📎 **媒体文件**"

        except Exception as e:
            logger.debug(f"获取媒体信息失败: {e}")
            return "📎 **媒体文件**"

    def format_file_size(self, size) -> str:
        """格式化文件大小"""
        try:
            if isinstance(size, str):
                return size

            size = int(size)
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            elif size < 1024 * 1024 * 1024:
                return f"{size / (1024 * 1024):.1f} MB"
            else:
                return f"{size / (1024 * 1024 * 1024):.1f} GB"
        except:
            return "未知"

    async def forward_results(self, update: Update, results: List[dict], keyword: str):
        """发送搜索结果（避免消息出现在收藏夹中）"""
        bot = Bot(token=self.bot_token)
        chat_id = update.effective_chat.id

        # 按时间排序（最新的在前）
        results.sort(key=lambda x: x['date'], reverse=True)

        forwarded_count = 0

        try:
            # 发送搜索结果摘要
            summary_text = f"🎯 **搜索结果摘要**\n\n"
            summary_text += f"🔍 关键词：**{keyword}**\n"
            summary_text += f"📊 找到：**{len(results)}** 条消息\n\n"

            # 按频道分组统计
            channel_stats = {}
            for result in results:
                channel_name = result['chat_title']
                if channel_name not in channel_stats:
                    channel_stats[channel_name] = 0
                channel_stats[channel_name] += 1

            summary_text += "📈 **频道分布：**\n"
            for channel, count in sorted(channel_stats.items(), key=lambda x: x[1], reverse=True):
                summary_text += f"• {channel}: {count} 条\n"

            summary_text += f"\n📤 开始转发消息..."

            await bot.send_message(chat_id=chat_id, text=summary_text, parse_mode='Markdown')

            # 转发每条匹配的消息
            for i, result in enumerate(results):
                try:
                    message = result['message']
                    chat_title = result['chat_title']
                    match_type = result['match_type']

                    # 构建消息头部信息（使用北京时间）
                    beijing_time_str = self.format_beijing_time(message.date)
                    header = f"🔗 **来源：** {chat_title}\n"
                    header += f"📅 **时间：** {beijing_time_str} (北京时间)\n"
                    header += f"🎯 **匹配：** {match_type}\n"
                    header += f"📍 **序号：** {i+1}/{len(results)}\n"
                    header += "─" * 30

                    # 发送头部信息
                    await bot.send_message(chat_id=chat_id, text=header, parse_mode='Markdown')

                    # 发送消息内容（避免消息出现在收藏夹中）
                    if message.text or message.media:
                        try:
                            # 直接发送消息内容，不使用转发功能
                            await self.send_message_content(bot, chat_id, message)
                            forwarded_count += 1
                        except Exception as e:
                            # 如果发送失败，尝试简化内容
                            logger.warning(f"发送消息内容失败，尝试简化发送: {e}")

                            content = ""
                            if message.text:
                                content = message.text[:self.max_text_length]  # 限制长度
                                if len(message.text) > self.max_text_length:
                                    content += "...(内容过长，已截断)"

                            if message.media and isinstance(message.media, MessageMediaDocument):
                                if message.media.document and message.media.document.attributes:
                                    for attr in message.media.document.attributes:
                                        if hasattr(attr, 'file_name') and attr.file_name:
                                            content += f"\n📎 文件: {attr.file_name}"

                            if content:
                                await bot.send_message(chat_id=chat_id, text=content)

                    # 控制转发频率
                    await asyncio.sleep(self.forward_delay)

                except Exception as e:
                    logger.error(f"转发第 {i+1} 条消息时出错: {e}")
                    continue

            # 发送完成通知（使用北京时间）
            current_beijing_time = self.format_beijing_time(datetime.now(timezone.utc))
            completion_text = f"✅ **发送完成！**\n\n"
            completion_text += f"📊 成功发送：**{forwarded_count}** 条消息\n"
            completion_text += f"🎯 关键词：**{keyword}**\n"
            completion_text += f"⏰ 完成时间：{current_beijing_time} (北京时间)"

            await bot.send_message(chat_id=chat_id, text=completion_text, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"发送结果时出错: {e}")
            await bot.send_message(chat_id=chat_id, text=f"❌ 发送过程中出现错误：{e}")

    async def run(self):
        """运行机器人"""
        try:
            # 初始化客户端
            await self.init_clients()

            logger.info("🤖 Telegram资源搜索机器人启动成功")
            logger.info("⏳ 等待命令中...")

            # 启动机器人应用
            await self.bot_app.initialize()
            await self.bot_app.start()
            await self.bot_app.updater.start_polling()

            # 保持运行
            while True:
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭...")
        except Exception as e:
            logger.error(f"运行时出错: {e}")
        finally:
            # 清理资源
            if self.bot_app:
                await self.bot_app.stop()
                await self.bot_app.shutdown()
            if self.client:
                await self.client.disconnect()
            logger.info("机器人已停止")


async def main():
    """主函数"""
    try:
        bot = TelegramResourceBot()
        await bot.run()
    except Exception as e:
        logger.error(f"程序启动失败: {e}")


if __name__ == "__main__":
    # 运行机器人
    asyncio.run(main())

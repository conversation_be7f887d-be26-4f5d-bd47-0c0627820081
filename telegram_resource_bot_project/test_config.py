"""
测试配置加载
验证 .env 文件配置是否正确加载
"""

from config import Config

def test_config():
    """测试配置加载"""
    print("🧪 测试配置加载...")
    print("=" * 50)
    
    # 基础配置
    print("📋 基础配置:")
    print(f"  USER_ID: {Config.USER_ID}")
    print(f"  API_ID: {Config.API_ID}")
    print(f"  API_HASH: {Config.API_HASH[:10]}..." if Config.API_HASH else "  API_HASH: 未设置")
    print(f"  BOT_TOKEN: {Config.BOT_TOKEN[:20]}..." if Config.BOT_TOKEN else "  BOT_TOKEN: 未设置")
    print(f"  PROXY_URL: {Config.PROXY_URL if Config.PROXY_URL else '未设置'}")
    
    print("\n⚙️ 搜索配置:")
    print(f"  SEARCH_DELAY: {Config.SEARCH_DELAY}秒")
    print(f"  CHANNEL_DELAY: {Config.CHANNEL_DELAY}秒")
    print(f"  FORWARD_DELAY: {Config.FORWARD_DELAY}秒")
    
    print("\n🎯 搜索范围配置:")
    print(f"  SEARCH_CHANNELS: {Config.SEARCH_CHANNELS}")
    print(f"  SEARCH_GROUPS: {Config.SEARCH_GROUPS}")
    
    print("\n📊 限制配置:")
    print(f"  MAX_RESULTS: {Config.MAX_RESULTS}")
    print(f"  MAX_DAYS: {Config.MAX_DAYS}")
    print(f"  MAX_TEXT_LENGTH: {Config.MAX_TEXT_LENGTH}")
    print(f"  BATCH_SIZE: {Config.BATCH_SIZE}")
    
    print("\n📢 通知配置:")
    print(f"  STATUS_UPDATE_INTERVAL: {Config.STATUS_UPDATE_INTERVAL}")
    print(f"  PROGRESS_REPORT_INTERVAL: {Config.PROGRESS_REPORT_INTERVAL}")
    
    print("\n📝 日志配置:")
    print(f"  LOG_LEVEL: {Config.LOG_LEVEL}")
    
    print("\n" + "=" * 50)
    
    # 验证配置
    try:
        Config.validate()
        print("✅ 配置验证通过！")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
    
    # 搜索范围建议
    print("\n💡 搜索范围建议:")
    if Config.SEARCH_CHANNELS and not Config.SEARCH_GROUPS:
        print("  ✅ 推荐配置：只搜索频道，避免群组转发问题")
    elif Config.SEARCH_CHANNELS and Config.SEARCH_GROUPS:
        print("  ⚠️  同时搜索频道和群组，注意群组转发可能失败")
    elif not Config.SEARCH_CHANNELS and Config.SEARCH_GROUPS:
        print("  ⚠️  只搜索群组，可能遇到转发权限问题")
    else:
        print("  ❌ 频道和群组搜索都被禁用，无法进行搜索")

if __name__ == "__main__":
    test_config()

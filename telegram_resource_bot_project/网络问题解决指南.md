# 网络问题解决指南

## 🚨 错误分析

您遇到的错误 `httpx.ConnectError` 表示机器人无法连接到Telegram API服务器。

### 错误原因
- 网络连接不稳定
- 代理配置问题
- 防火墙阻止连接
- Telegram API服务器暂时不可用

## 🔧 解决方案

### 1. 立即诊断
运行网络诊断脚本：
```bash
python network_check.py
```

这会检查：
- 基础网络连接
- 代理连接状态
- Telegram API端点
- 机器人连接状态

### 2. 检查代理配置

如果您在中国大陆，需要配置代理：

#### 在 `.env` 文件中添加：
```env
# HTTP代理
PROXY_URL=http://127.0.0.1:7890

# 或者SOCKS5代理
PROXY_URL=socks5://127.0.0.1:1080
```

#### 常见代理软件端口：
- **Clash**: `http://127.0.0.1:7890`
- **V2Ray**: `socks5://127.0.0.1:1080`
- **Shadowsocks**: `socks5://127.0.0.1:1080`
- **Proxifier**: 根据配置而定

### 3. 验证代理是否工作

#### 方法1：浏览器测试
1. 配置浏览器使用相同的代理
2. 访问 https://api.telegram.org
3. 如果能访问，说明代理正常

#### 方法2：命令行测试
```bash
# Windows (PowerShell)
Invoke-WebRequest -Uri "https://api.telegram.org" -Proxy "http://127.0.0.1:7890"

# 或者使用curl
curl --proxy http://127.0.0.1:7890 https://api.telegram.org
```

### 4. 重启和重试

#### 重启代理软件
1. 关闭代理软件
2. 等待10秒
3. 重新启动代理软件
4. 确认代理正在运行

#### 重启机器人
```bash
# 停止机器人 (Ctrl+C)
# 然后重新启动
python start_bot.py
```

### 5. 更换网络环境

如果代理问题持续：
- 尝试手机热点
- 更换WiFi网络
- 使用VPN服务

## 🛠️ 程序改进

我已经为程序添加了以下改进：

### 1. 错误处理器
- 自动捕获网络错误
- 提供友好的错误提示
- 自动重置搜索状态

### 2. 重试机制
- 消息发送失败自动重试
- 指数退避策略
- 最多重试3次

### 3. 安全发送方法
- `safe_send_message()`: 安全发送消息
- `safe_edit_message()`: 安全编辑消息
- 网络异常时不会崩溃

## 📋 故障排除步骤

### 步骤1：运行诊断
```bash
python network_check.py
```

### 步骤2：检查输出
查看哪些连接失败：
- ❌ 表示连接失败
- ⚠️ 表示连接异常
- ✅ 表示连接正常

### 步骤3：根据结果采取行动

#### 如果基础网络失败
- 检查网络连接
- 重启路由器
- 联系网络服务商

#### 如果代理连接失败
- 检查代理软件是否运行
- 验证代理端口是否正确
- 尝试不同的代理服务器

#### 如果Telegram API失败
- 配置或更换代理
- 检查防火墙设置
- 尝试不同的网络环境

### 步骤4：测试修复
```bash
python start_bot.py --test
```

## 🔄 常见解决方案

### 方案1：配置代理
```env
PROXY_URL=http://127.0.0.1:7890
```

### 方案2：更换代理端口
```env
PROXY_URL=socks5://127.0.0.1:1080
```

### 方案3：使用公共代理
```env
PROXY_URL=http://proxy-server:port
```

### 方案4：移除代理配置
```env
# PROXY_URL=
```

## 📞 获取帮助

### 检查代理软件日志
- Clash: 查看连接日志
- V2Ray: 检查access.log
- Shadowsocks: 查看连接状态

### 网络测试命令
```bash
# 测试DNS解析
nslookup api.telegram.org

# 测试连接
telnet api.telegram.org 443

# 测试HTTP连接
curl -I https://api.telegram.org
```

## 💡 预防措施

1. **稳定的代理**: 使用稳定的代理服务
2. **备用方案**: 配置多个代理选项
3. **网络监控**: 定期检查网络状态
4. **日志记录**: 保存错误日志以便分析

---

**记住**: 网络问题通常是暂时的，大多数情况下重新配置代理或重启网络连接就能解决。

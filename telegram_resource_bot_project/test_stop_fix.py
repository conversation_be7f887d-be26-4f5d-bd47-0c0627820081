"""
测试停止命令修复
验证停止命令是否能够正确中断搜索任务
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class StopTestBot:
    """测试停止功能的简化机器人"""
    
    def __init__(self):
        Config.validate()
        self.api_id = Config.API_ID
        self.api_hash = Config.API_HASH
        self.client = None
        self.stop_search = False
        self.is_searching = False
        
    async def init_client(self):
        """初始化客户端"""
        self.client = TelegramClient('user_session', self.api_id, self.api_hash)
        await self.client.start()
        logger.info("✅ 客户端初始化成功")
        
    async def simulate_stop_command(self, delay_seconds=5):
        """模拟在指定延迟后发送停止命令"""
        await asyncio.sleep(delay_seconds)
        logger.info(f"🛑 模拟停止命令（{delay_seconds}秒后）")
        self.stop_search = True
        
    async def test_search_with_stop(self, keyword="测试", days=7, stop_after=5):
        """测试搜索过程中的停止功能"""
        logger.info(f"🧪 开始测试：搜索'{keyword}'，{stop_after}秒后停止")
        
        # 启动停止任务
        stop_task = asyncio.create_task(self.simulate_stop_command(stop_after))
        
        # 开始搜索
        self.is_searching = True
        self.stop_search = False
        
        try:
            results = await self.search_messages_with_stop_check(keyword, days)
            logger.info(f"🎯 搜索结果：找到 {len(results)} 条匹配消息")
            
            if self.stop_search:
                logger.info("✅ 停止命令成功生效！")
                return True
            else:
                logger.info("⚠️  搜索正常完成，未被停止")
                return False
                
        except Exception as e:
            logger.error(f"❌ 测试过程中出错: {e}")
            return False
        finally:
            self.is_searching = False
            self.stop_search = False
            # 取消停止任务
            if not stop_task.done():
                stop_task.cancel()
                
    async def search_messages_with_stop_check(self, keyword: str, days: int):
        """带停止检查的搜索方法"""
        results = []
        
        # 计算时间范围
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        logger.info(f"🔍 开始搜索关键词: {keyword}")
        
        # 获取对话列表
        dialogs = await self.client.get_dialogs()
        channels_and_groups = [d for d in dialogs if isinstance(d.entity, (Channel, Chat))]
        
        logger.info(f"📋 找到 {len(channels_and_groups)} 个频道和群组")
        
        for i, dialog in enumerate(channels_and_groups):
            # 主要停止检查点
            if self.stop_search:
                logger.info(f"🛑 在频道循环中检测到停止命令 ({i}/{len(channels_and_groups)})")
                break
                
            logger.info(f"🔍 搜索频道 ({i+1}/{len(channels_and_groups)}): {dialog.title}")
            
            try:
                # 获取消息（带停止检查）
                messages = []
                checked_count = 0
                
                async for message in self.client.iter_messages(dialog.entity, limit=100):
                    # 消息获取过程中的停止检查
                    if self.stop_search:
                        logger.info("🛑 在消息获取过程中检测到停止命令")
                        break
                        
                    messages.append(message)
                    checked_count += 1
                    
                    if message.date < start_date:
                        break
                        
                    # 每10条消息检查一次
                    if checked_count % 10 == 0:
                        if self.stop_search:
                            logger.info("🛑 在消息获取批次中检测到停止命令")
                            break
                
                # 如果在消息获取中被停止，跳出频道循环
                if self.stop_search:
                    break
                    
                # 处理消息
                for message in messages:
                    if self.stop_search:
                        logger.info("🛑 在消息处理中检测到停止命令")
                        break
                        
                    if (message.date >= start_date and 
                        message.text and 
                        keyword.lower() in message.text.lower()):
                        
                        results.append({
                            'message': message,
                            'chat_title': dialog.title,
                            'date': message.date
                        })
                        logger.info(f"✅ 找到匹配消息: {dialog.title}")
                
                # 如果在消息处理中被停止，跳出频道循环
                if self.stop_search:
                    break
                    
                # 模拟频道间延迟
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.warning(f"搜索频道 {dialog.title} 时出错: {e}")
                continue
        
        if self.stop_search:
            logger.info(f"🛑 搜索被停止！已找到 {len(results)} 条匹配消息")
        else:
            logger.info(f"🎉 搜索完成！共找到 {len(results)} 条匹配消息")
            
        return results

async def main():
    """主测试函数"""
    logger.info("🚀 开始停止命令修复测试...")
    
    bot = StopTestBot()
    
    try:
        await bot.init_client()
        
        # 测试1：5秒后停止
        logger.info("\n" + "="*50)
        logger.info("📋 测试1：5秒后停止搜索")
        success1 = await bot.test_search_with_stop("测试", 7, 5)
        
        await asyncio.sleep(2)
        
        # 测试2：3秒后停止（更快）
        logger.info("\n" + "="*50)
        logger.info("📋 测试2：3秒后停止搜索")
        success2 = await bot.test_search_with_stop("消息", 7, 3)
        
        # 总结
        logger.info("\n" + "="*50)
        logger.info("🎯 测试总结:")
        logger.info(f"  测试1 (5秒停止): {'✅ 成功' if success1 else '❌ 失败'}")
        logger.info(f"  测试2 (3秒停止): {'✅ 成功' if success2 else '❌ 失败'}")
        
        if success1 and success2:
            logger.info("🎉 停止命令修复成功！")
        else:
            logger.info("⚠️  停止命令可能仍有问题，需要进一步调试")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
    finally:
        if bot.client:
            await bot.client.disconnect()

if __name__ == "__main__":
    asyncio.run(main())

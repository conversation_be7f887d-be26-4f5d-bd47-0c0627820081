# 代理配置说明

## 🌐 概述

本项目的所有组件都支持代理配置，包括：
- 主搜索机器人 (`telegram_resource_bot.py`)
- 频道管理工具 (`manage_chats.py`)
- 各种测试脚本

## ⚙️ 配置方法

### 在 `.env` 文件中配置

```bash
# 代理配置（可选）
# 支持HTTP和SOCKS5代理
# 格式：协议://用户名:密码@主机:端口

# SOCKS5代理（推荐）
PROXY_URL=socks5://username:password@127.0.0.1:1080

# 或者无认证的SOCKS5代理
PROXY_URL=socks5://127.0.0.1:1080

# HTTP代理（兼容性有限）
PROXY_URL=***************************************

# 如果不需要代理，注释掉或删除这一行
# PROXY_URL=
```

## 🔧 支持的代理类型

### 1. SOCKS5代理（推荐）

**格式：**
```bash
# 带认证
PROXY_URL=socks5://username:password@host:port

# 无认证
PROXY_URL=socks5://host:port
```

**示例：**
```bash
PROXY_URL=socks5://user:pass@127.0.0.1:1080
PROXY_URL=socks5://*************:1080
```

**优点：**
- ✅ 完全兼容Telethon
- ✅ 支持TCP和UDP流量
- ✅ 性能优秀
- ✅ 稳定可靠

### 2. HTTP代理（有限支持）

**格式：**
```bash
# 带认证
PROXY_URL=*****************************:port

# 无认证
PROXY_URL=http://host:port
```

**示例：**
```bash
PROXY_URL=*******************************
PROXY_URL=http://proxy.example.com:3128
```

**注意：**
- ⚠️ Telethon对HTTP代理支持有限
- ⚠️ 可能出现连接问题
- ⚠️ 建议优先使用SOCKS5代理

## 🧪 测试代理配置

### 运行代理测试

```bash
# 测试代理连接
python test_proxy_connection.py

# 专门测试 manage_chats.py 代理
python test_manage_chats_proxy.py
```

### 测试输出示例

```
🧪 代理连接测试
==================================================
📋 配置信息:
   API ID: 12345678
   API Hash: abcd1234...
   代理地址: socks5://127.0.0.1:1080
   代理状态: 已配置
==================================================
🌐 配置代理: socks5://127.0.0.1:1080
🔧 配置SOCKS5代理...
✅ SOCKS5代理配置成功
🔗 创建Telegram客户端...
🚀 尝试连接到Telegram...
✅ 连接成功！用户: 张三 (@zhangsan)
📋 测试获取对话列表...
✅ 成功获取 5 个对话
🔌 连接已断开

🎉 代理连接测试成功！
```

## 🛠️ 常见代理软件配置

### 1. Shadowsocks

**配置示例：**
```bash
# 本地SOCKS5端口通常是1080
PROXY_URL=socks5://127.0.0.1:1080
```

### 2. V2Ray

**配置示例：**
```bash
# 检查V2Ray的SOCKS5入站端口
PROXY_URL=socks5://127.0.0.1:1080
```

### 3. Clash

**配置示例：**
```bash
# Clash的SOCKS5端口通常是7891
PROXY_URL=socks5://127.0.0.1:7891
```

### 4. 企业代理

**配置示例：**
```bash
# 企业HTTP代理
PROXY_URL=http://username:<EMAIL>:8080

# 企业SOCKS5代理
PROXY_URL=socks5://username:<EMAIL>:1080
```

## 🔍 故障排除

### 常见问题

#### 1. 连接超时
```
❌ 代理连接测试失败: TimeoutError
```

**解决方法：**
- 检查代理服务器是否正常运行
- 验证代理地址和端口是否正确
- 确认防火墙没有阻止连接

#### 2. 认证失败
```
❌ 代理连接测试失败: Authentication failed
```

**解决方法：**
- 检查用户名和密码是否正确
- 确认代理服务器是否需要认证
- 尝试不带认证的连接

#### 3. 代理类型不支持
```
⚠️ 不支持的代理类型: https
```

**解决方法：**
- 使用支持的代理类型（socks5或http）
- 检查代理URL格式是否正确

#### 4. manage_chats.py 代理连接失败
```
❌ manage_chats.py 连接失败，但主程序正常
```

**解决方法：**
```bash
# 专门测试 manage_chats.py
python test_manage_chats_proxy.py

# 检查代理配置一致性
python test_proxy_connection.py
```

#### 5. 缺少依赖库
```
❌ 缺少python-socks库，请安装: pip install python-socks
```

**解决方法：**
```bash
pip install python-socks
```

### 调试步骤

1. **检查配置文件**
   ```bash
   cat .env | grep PROXY_URL
   ```

2. **测试代理连接**
   ```bash
   python test_proxy_connection.py
   ```

3. **检查代理服务器**
   ```bash
   # 测试代理是否可达
   telnet proxy_host proxy_port
   ```

4. **查看详细日志**
   ```bash
   # 设置详细日志级别
   LOG_LEVEL=DEBUG
   ```

## 📋 各组件代理支持

### 主程序
- **文件：** `telegram_resource_bot.py`
- **支持：** ✅ 完全支持
- **配置：** 自动读取 `PROXY_URL`

### 管理工具
- **文件：** `manage_chats.py`
- **支持：** ✅ 完全支持
- **配置：** 自动读取 `PROXY_URL`

### 测试脚本
- **文件：** `test_*.py`
- **支持：** ✅ 完全支持
- **配置：** 自动读取 `PROXY_URL`

## 🌍 网络环境建议

### 国内用户
- **推荐：** 使用SOCKS5代理
- **软件：** Shadowsocks、V2Ray、Clash等
- **端口：** 通常为1080或7891

### 企业用户
- **推荐：** 联系IT部门获取代理配置
- **格式：** 通常为HTTP代理
- **认证：** 可能需要域账户认证

### 海外用户
- **推荐：** 直连（不配置代理）
- **备选：** 如有需要可配置本地代理

## 🔒 安全注意事项

### 1. 代理凭据保护
- 不要在代码中硬编码代理密码
- 使用 `.env` 文件存储敏感信息
- 确保 `.env` 文件不被提交到版本控制

### 2. 代理服务器选择
- 使用可信的代理服务器
- 避免使用免费的公共代理
- 定期更换代理密码

### 3. 网络监控
- 注意代理流量使用情况
- 监控异常连接活动
- 及时更新代理软件

## 🎉 总结

通过正确配置代理，您可以：
- ✅ 在受限网络环境中正常使用
- ✅ 保护隐私和安全
- ✅ 提高连接稳定性
- ✅ 绕过地理限制

所有项目组件都会自动使用相同的代理配置，确保一致的网络行为。

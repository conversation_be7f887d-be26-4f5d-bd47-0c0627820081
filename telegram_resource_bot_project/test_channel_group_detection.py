"""
测试频道和群组识别
验证修复后的频道/群组判断逻辑
"""

import asyncio
import logging
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat
from config import Config

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_channel_group_detection():
    """测试频道和群组识别"""
    logger.info("🧪 开始测试频道和群组识别...")
    
    try:
        # 验证配置
        Config.validate()
        
        # 初始化客户端
        client = TelegramClient('user_session', Config.API_ID, Config.API_HASH)
        await client.start()
        
        logger.info("✅ 客户端连接成功")
        
        # 获取对话列表
        logger.info("📋 正在获取对话列表...")
        dialogs = await client.get_dialogs()
        logger.info(f"📋 获取到 {len(dialogs)} 个对话")
        
        # 分类统计
        channels_only = []
        groups_only = []
        private_chats = []
        other_types = []
        
        for dialog in dialogs:
            if isinstance(dialog.entity, Channel):
                # 通过 megagroup 属性区分频道和超级群组
                if dialog.entity.megagroup:
                    # 超级群组
                    groups_only.append(dialog)
                    logger.debug(f"🔍 超级群组: {dialog.title} (ID: {dialog.entity.id})")
                else:
                    # 频道
                    channels_only.append(dialog)
                    logger.debug(f"📺 频道: {dialog.title} (ID: {dialog.entity.id})")
            elif isinstance(dialog.entity, Chat):
                # 普通群组
                groups_only.append(dialog)
                logger.debug(f"👥 普通群组: {dialog.title} (ID: {dialog.entity.id})")
            else:
                # 私聊或其他类型
                if hasattr(dialog.entity, 'first_name'):
                    private_chats.append(dialog)
                    logger.debug(f"👤 私聊: {dialog.title} (ID: {dialog.entity.id})")
                else:
                    other_types.append(dialog)
                    logger.debug(f"❓ 其他类型: {dialog.title} (ID: {dialog.entity.id})")
        
        # 输出统计结果
        logger.info("\n" + "="*60)
        logger.info("📊 对话分类统计:")
        logger.info(f"📺 频道: {len(channels_only)} 个")
        logger.info(f"👥 群组: {len(groups_only)} 个")
        logger.info(f"👤 私聊: {len(private_chats)} 个")
        logger.info(f"❓ 其他: {len(other_types)} 个")
        logger.info(f"📋 总计: {len(dialogs)} 个")
        
        # 显示前几个频道和群组的详细信息
        logger.info("\n📺 频道列表（前10个）:")
        for i, dialog in enumerate(channels_only[:10]):
            entity = dialog.entity
            username = f"@{entity.username}" if entity.username else "无用户名"
            logger.info(f"  {i+1}. {dialog.title} ({username}) - ID: {entity.id}")
        
        logger.info("\n👥 群组列表（前10个）:")
        for i, dialog in enumerate(groups_only[:10]):
            entity = dialog.entity
            if isinstance(entity, Channel):
                group_type = "超级群组"
                username = f"@{entity.username}" if entity.username else "无用户名"
            else:
                group_type = "普通群组"
                username = "无用户名"
            logger.info(f"  {i+1}. {dialog.title} ({group_type}, {username}) - ID: {entity.id}")
        
        # 验证特定的"豌豆鱼"
        logger.info("\n🔍 查找'豌豆鱼':")
        found_wandouyu = False
        for dialog in dialogs:
            if "豌豆鱼" in dialog.title:
                entity = dialog.entity
                if isinstance(entity, Channel):
                    if entity.megagroup:
                        entity_type = "超级群组"
                        category = "群组"
                    else:
                        entity_type = "频道"
                        category = "频道"
                elif isinstance(entity, Chat):
                    entity_type = "普通群组"
                    category = "群组"
                else:
                    entity_type = "其他"
                    category = "其他"
                
                logger.info(f"✅ 找到: {dialog.title}")
                logger.info(f"   类型: {entity_type}")
                logger.info(f"   分类: {category}")
                logger.info(f"   ID: {entity.id}")
                logger.info(f"   用户名: @{entity.username if hasattr(entity, 'username') and entity.username else '无'}")
                found_wandouyu = True
                break
        
        if not found_wandouyu:
            logger.info("❌ 未找到'豌豆鱼'")
        
        # 测试搜索范围配置
        logger.info("\n⚙️ 当前搜索配置:")
        logger.info(f"  SEARCH_CHANNELS: {Config.SEARCH_CHANNELS}")
        logger.info(f"  SEARCH_GROUPS: {Config.SEARCH_GROUPS}")
        
        # 根据配置决定搜索范围
        search_targets = []
        if Config.SEARCH_CHANNELS:
            search_targets.extend(channels_only)
            logger.info(f"📺 将搜索 {len(channels_only)} 个频道")
        if Config.SEARCH_GROUPS:
            search_targets.extend(groups_only)
            logger.info(f"👥 将搜索 {len(groups_only)} 个群组")
        
        logger.info(f"📋 总共将搜索 {len(search_targets)} 个目标")
        
        # 关闭客户端
        await client.disconnect()
        logger.info("✅ 测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await test_channel_group_detection()

if __name__ == "__main__":
    asyncio.run(main())

# 消息内容模式配置说明

## 🎯 功能概述

新增了消息内容显示模式配置，用户可以选择：
- **完整内容模式**：显示完整消息内容和媒体信息
- **简洁信息模式**：只显示预览和链接，节省聊天空间

## ⚙️ 配置方法

### 在 `.env` 文件中设置

```bash
# 消息内容配置
# 是否发送完整消息内容（true/false）
# true: 发送完整消息内容和媒体信息（默认）
# false: 只发送简洁信息和原始链接，用户可通过链接查看完整内容
SEND_FULL_CONTENT=true
```

### 配置选项说明

| 配置值 | 模式 | 说明 |
|--------|------|------|
| `true` | 完整内容模式 | 显示完整消息文本和详细媒体信息 |
| `false` | 简洁信息模式 | 只显示预览和链接，节省空间 |

## 📊 两种模式对比

### 完整内容模式 (`SEND_FULL_CONTENT=true`)

**显示内容：**
```
🎯 #1 | 📺 频道名称 | 📅 12-25 14:30 | 🔍 文本消息
🔗 原始链接：https://t.me/channel/123

这里是完整的消息文本内容，包含所有原始文字、
格式和详细信息...

📎 文件：app_v3.1.20_release.apk
📏 大小：15.2 MB
```

**特点：**
- ✅ 显示完整消息文本
- ✅ 显示详细媒体信息
- ✅ 包含所有原始内容
- ❌ 占用较多聊天空间

### 简洁信息模式 (`SEND_FULL_CONTENT=false`)

**显示内容：**
```
🎯 #1 | 📺 频道名称 | 📅 12-25 14:30 | 🔍 文本消息
🔗 原始链接：https://t.me/channel/123

📝 消息预览：这里是完整的消息文本内容，包含所有原始文字...
📎 文件：app_v3.1.20_release.apk (15.2 MB)
🔗 查看完整内容：https://t.me/channel/123
```

**特点：**
- ✅ 只显示前50字符预览
- ✅ 显示文件名和大小
- ✅ 提供原始链接跳转
- ✅ 节省聊天空间

## 🎨 使用场景

### 完整内容模式适合：
- **详细查看**：需要立即查看完整内容
- **离线使用**：网络不稳定时避免跳转
- **内容分析**：需要分析完整文本内容
- **存档目的**：保存完整信息记录

### 简洁信息模式适合：
- **快速浏览**：快速扫描搜索结果
- **节省空间**：聊天记录保持整洁
- **按需查看**：只查看感兴趣的内容
- **大量结果**：搜索结果较多时

## 🔄 配置切换

### 切换到简洁模式
1. 编辑 `.env` 文件：
   ```bash
   SEND_FULL_CONTENT=false
   ```

2. 重启机器人：
   ```bash
   python start_bot.py
   ```

3. 测试效果：
   ```
   /search 测试 1
   ```

### 切换到完整模式
1. 编辑 `.env` 文件：
   ```bash
   SEND_FULL_CONTENT=true
   ```

2. 重启机器人并测试

## 🧪 测试功能

### 运行测试脚本
```bash
python test_content_modes.py
```

### 测试内容
- 纯文本消息显示效果
- 长文本消息预览效果
- APK文件信息显示
- ZIP文件信息显示
- 只有文件的消息处理

### 预期结果
测试脚本会展示两种模式的显示效果对比，帮助您选择合适的模式。

## 💡 最佳实践

### 推荐配置

**日常使用（推荐简洁模式）：**
```bash
SEND_FULL_CONTENT=false
```
- 保持聊天整洁
- 快速浏览结果
- 按需点击链接查看详情

**详细分析（推荐完整模式）：**
```bash
SEND_FULL_CONTENT=true
```
- 需要详细分析内容
- 网络环境不稳定
- 需要离线查看内容

### 使用技巧

1. **链接跳转**：
   - 简洁模式下点击链接可查看原始消息
   - 支持所有Telegram客户端
   - 保持与原始消息的完整关联

2. **空间管理**：
   - 简洁模式减少聊天记录占用
   - 完整模式便于内容搜索
   - 根据使用习惯选择

3. **性能考虑**：
   - 简洁模式发送速度更快
   - 完整模式处理时间稍长
   - 两种模式都支持错误降级

## 🔧 技术细节

### 简洁信息生成逻辑
```python
# 文本预览（前50字符）
preview = message.text[:50] + "..." if len(message.text) > 50 else message.text

# 文件信息简化显示
file_info = f"{filename} ({file_size})"

# 链接提示
link_prompt = f"查看完整内容：{message_link}"
```

### 错误处理机制
- Markdown解析失败 → 自动降级为纯文本
- 链接生成失败 → 显示基本信息
- 发送失败 → 多级降级处理

## 📋 配置验证

### 检查当前配置
查看机器人启动日志，确认配置加载：
```
✅ 环境变量检查通过
📋 消息内容模式：完整内容模式 / 简洁信息模式
```

### 验证功能效果
1. 发送搜索命令
2. 观察结果显示格式
3. 确认是否符合预期

## 🎉 总结

通过这个配置选项，用户可以：
- **灵活选择**：根据需求选择显示模式
- **优化体验**：平衡详细程度和空间占用
- **保持功能**：两种模式都保留完整功能
- **简单配置**：一个配置项即可切换

无论选择哪种模式，都能通过原始链接访问完整内容，确保信息的完整性和可访问性！

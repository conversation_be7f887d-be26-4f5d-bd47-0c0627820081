
# 复制此文件为 .env 并填入真实的配置信息

# 用户账号ID（数字）
USER_ID=5105735192

# Telegram API配置（从 https://my.telegram.org 获取）
API_ID=28381106
API_HASH=ded433aacc9bbbd3c036759b8302c843

# 机器人Token（从 @BotFather 获取）
BOT_TOKEN=6277475670:AAG9WtxxxvcmDEj70NDz2jU3gxaYFRUJSHg

# 代理配置（可选，如果需要代理访问）
# 支持格式：http://host:port 或 socks5://host:port
PROXY_URL=http://127.0.0.1:10808

# 示例：
# PROXY_URL=http://127.0.0.1:7890
# PROXY_URL=socks5://127.0.0.1:1080

# 高级配置（可选）
# 搜索间隔（秒）
SEARCH_DELAY=0.5

# 频道切换间隔（秒）
CHANNEL_DELAY=0.5

# 转发间隔（秒）
FORWARD_DELAY=1.0

# 最大结果数量
MAX_RESULTS=500

# 最大搜索天数
MAX_DAYS=365

# 最大文本长度
MAX_TEXT_LENGTH=10000

# 每个频道最大检查消息数量
# 建议值：1000-2000（太大会影响性能，太小可能遗漏消息）
# 注意：这是每个频道检查的消息数量，不是匹配结果数量
MAX_MESSAGES_PER_CHANNEL=1000

# 批量处理大小
BATCH_SIZE=10

# 通知频率配置
# 状态更新间隔（每搜索几个频道更新一次状态消息）
STATUS_UPDATE_INTERVAL=1

# 进度报告间隔（每搜索几个频道发送一次独立的进度报告）
PROGRESS_REPORT_INTERVAL=1

# 搜索范围配置
# 是否搜索频道（true/false）
SEARCH_CHANNELS=false

# 是否搜索群组（true/false）
# 注意：群组消息转发可能因权限限制而失败，建议保持关闭
SEARCH_GROUPS=true

# 群组消息处理方式配置已废弃
# 现在所有消息都统一发送内容，避免收藏夹问题
# TRY_FORWARD_GROUP_MESSAGES=true  # 已废弃，不再使用

# 消息内容配置
# 是否发送完整消息内容（true/false）
# true: 发送完整消息内容和媒体信息（默认）
# false: 只发送简洁信息和原始链接，用户可通过链接查看完整内容
SEND_FULL_CONTENT=false

# 选择性搜索配置
# 是否启用选择性搜索模式（true/false）
# true: 只在指定的频道/群组中搜索
# false: 在所有频道/群组中搜索（默认）
USE_SELECTIVE_SEARCH=true

# 选中的频道/群组列表文件
# 当启用选择性搜索时，从此文件读取要搜索的频道/群组列表
# SELECTED_CHATS_FILE=entertainment_channels.txt
 SELECTED_CHATS_FILE=tech_channels.txt
# SELECTED_CHATS_FILE=test.txt

# 日志级别（DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO

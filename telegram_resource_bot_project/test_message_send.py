"""
测试消息发送功能
验证机器人是否能正确发送消息到用户账户
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_message_sending():
    """测试消息发送"""
    # 加载配置
    load_dotenv('.env')
    
    bot_token = os.getenv('BOT_TOKEN')
    user_id = int(os.getenv('USER_ID'))
    
    if not bot_token or not user_id:
        logger.error("❌ 缺少BOT_TOKEN或USER_ID配置")
        return
    
    logger.info(f"🤖 机器人Token: {bot_token[:20]}...")
    logger.info(f"👤 目标用户ID: {user_id}")
    
    try:
        # 创建机器人实例
        bot = Bot(token=bot_token)
        
        # 获取机器人信息
        bot_info = await bot.get_me()
        logger.info(f"✅ 机器人信息: {bot_info.first_name} (@{bot_info.username})")
        
        # 发送测试消息
        logger.info(f"📤 正在发送测试消息到用户 {user_id}...")
        
        test_message = await bot.send_message(
            chat_id=user_id,
            text="🧪 **测试消息**\n\n"
                 "这是一条测试消息，用于验证机器人是否能正确发送消息到您的账户。\n\n"
                 "如果您收到这条消息，说明转发功能正常工作！\n\n"
                 f"⏰ 发送时间: {asyncio.get_event_loop().time()}",
            parse_mode='Markdown'
        )
        
        logger.info(f"✅ 测试消息发送成功！")
        logger.info(f"   消息ID: {test_message.message_id}")
        logger.info(f"   发送到: {test_message.chat.id}")
        logger.info(f"   聊天类型: {test_message.chat.type}")
        
        # 发送第二条消息确认
        await bot.send_message(
            chat_id=user_id,
            text="✅ 消息发送测试完成！\n\n"
                 "如果您看到这两条消息，说明机器人的转发功能工作正常。\n"
                 "搜索到的资源消息会以相同的方式发送给您。"
        )
        
        logger.info("🎉 所有测试消息发送成功！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        
        # 分析错误类型
        if "Forbidden" in str(e):
            logger.error("🔒 权限错误：")
            logger.error("   1. 您可能没有启动与机器人的对话")
            logger.error("   2. 请在Telegram中找到您的机器人")
            logger.error("   3. 发送 /start 命令开始对话")
            logger.error("   4. 然后重新运行此测试")
        elif "Chat not found" in str(e):
            logger.error("👤 用户ID错误：")
            logger.error("   1. 请检查 .env 文件中的 USER_ID 是否正确")
            logger.error("   2. 可以通过 @userinfobot 获取正确的用户ID")
        else:
            logger.error(f"🔧 其他错误: {e}")

async def main():
    """主函数"""
    logger.info("🧪 开始测试消息发送功能...")
    await test_message_sending()
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())

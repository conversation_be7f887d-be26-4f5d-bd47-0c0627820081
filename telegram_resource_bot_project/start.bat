@echo off
chcp 65001 >nul
echo 🚀 启动Telegram资源搜索机器人...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 📦 检查依赖...
pip show telethon >nul 2>&1
if errorlevel 1 (
    echo 📥 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查配置文件
if not exist .env (
    echo ⚠️  未找到配置文件 .env
    echo 请复制 .env.example 为 .env 并填入配置信息
    pause
    exit /b 1
)

REM 启动机器人
echo ✅ 启动机器人...
python start_bot.py

pause

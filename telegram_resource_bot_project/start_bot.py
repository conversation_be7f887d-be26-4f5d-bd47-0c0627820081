"""
Telegram资源搜索机器人启动脚本
支持从.env文件加载环境变量
"""

import os
import sys
from dotenv import load_dotenv

def main():
    """主启动函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Telegram资源搜索机器人')
    parser.add_argument('--test', action='store_true', help='运行连接测试')
    parser.add_argument('--network', action='store_true', help='运行网络诊断')
    args = parser.parse_args()

    if args.test:
        print("🔍 运行连接测试...")
    elif args.network:
        print("🌐 运行网络诊断...")
    else:
        print("🚀 正在启动Telegram资源搜索机器人...")

    # 加载.env文件
    if os.path.exists('.env'):
        load_dotenv('.env')
        print("✅ 已加载 .env 配置文件")
    else:
        print("⚠️  未找到 .env 文件，将使用系统环境变量")

    # 检查必要的环境变量
    required_vars = ['USER_ID', 'API_ID', 'API_HASH', 'BOT_TOKEN']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print("❌ 缺少必要的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请设置这些环境变量或创建 .env 文件")
        print("参考 .env.example 文件进行配置")
        sys.exit(1)

    print("✅ 环境变量检查通过")

    # 根据参数选择运行模式
    try:
        import asyncio

        if args.test:
            # 运行测试
            from test_connection import main as test_main
            print("🔍 开始连接测试...")
            asyncio.run(test_main())
        elif args.network:
            # 运行网络诊断
            from network_check import main as network_main
            print("🌐 开始网络诊断...")
            asyncio.run(network_main())
        else:
            # 运行机器人
            from telegram_resource_bot import main as bot_main
            print("🤖 启动机器人...")
            asyncio.run(bot_main())

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 程序已停止")
    except Exception as e:
        print(f"❌ 运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

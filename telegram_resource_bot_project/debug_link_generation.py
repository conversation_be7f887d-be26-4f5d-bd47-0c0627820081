"""
调试链接生成问题
测试是否有下划线被错误处理
"""

import re

def test_link_generation():
    """测试链接生成逻辑"""
    print("🔍 链接生成测试")
    print("=" * 50)
    
    # 模拟频道用户名（包含下划线）
    test_usernames = [
        "Aliyun_4K_Movies",
        "test_channel",
        "my_awesome_channel",
        "normal_channel",
        "channel_with_multiple_underscores"
    ]
    
    # 模拟消息ID
    message_id = 38813
    
    print("📊 测试结果:")
    print(f"{'原始用户名':<30} {'生成的链接':<50}")
    print("-" * 80)
    
    for username in test_usernames:
        # 直接生成链接（正确的方式）
        correct_link = f"https://t.me/{username}/{message_id}"
        
        # 测试是否有地方错误处理了链接
        # 模拟 clean_text_for_markdown 方法
        def clean_text_for_markdown(text):
            if not text:
                return text
            
            cleaned = text
            # 转义未配对的下划线
            cleaned = re.sub(r'(?<!_)_(?!_)', r'\\_', cleaned)
            return cleaned
        
        # 如果错误地对链接使用了清理方法
        wrong_link = clean_text_for_markdown(correct_link)
        
        print(f"{username:<30} {correct_link:<50}")
        if wrong_link != correct_link:
            print(f"{'❌ 错误处理后:':<30} {wrong_link:<50}")
        print()

def test_actual_generation():
    """测试实际的链接生成方法"""
    print("\n🧪 实际链接生成方法测试")
    print("=" * 50)
    
    class MockChatEntity:
        def __init__(self, username):
            self.username = username
    
    class MockMessage:
        def __init__(self, message_id):
            self.id = message_id
    
    def generate_message_link(chat_entity, message):
        """复制实际的链接生成方法"""
        try:
            # 获取频道/群组的用户名或ID
            if hasattr(chat_entity, 'username') and chat_entity.username:
                # 公开频道，使用用户名
                chat_identifier = chat_entity.username
                link = f"https://t.me/{chat_identifier}/{message.id}"
                return link
            else:
                # 私有频道逻辑...
                return None
        except Exception as e:
            print(f"生成链接失败: {e}")
            return None
    
    # 测试用例
    test_cases = [
        ("Aliyun_4K_Movies", 38813),
        ("test_channel", 12345),
        ("normal_channel", 67890)
    ]
    
    print("📋 实际生成结果:")
    for username, msg_id in test_cases:
        chat_entity = MockChatEntity(username)
        message = MockMessage(msg_id)
        
        link = generate_message_link(chat_entity, message)
        print(f"用户名: {username}")
        print(f"生成链接: {link}")
        print(f"是否包含下划线: {'是' if '_' in link else '否'}")
        print()

def analyze_problem():
    """分析可能的问题原因"""
    print("\n🔍 问题分析")
    print("=" * 50)
    
    print("可能的原因:")
    print("1. 链接在某个地方被错误地传递给了 clean_text_for_markdown 方法")
    print("2. 在发送消息时，链接被 Markdown 处理器修改了")
    print("3. Telegram Bot API 在处理链接时自动转义了下划线")
    print("4. 显示时的问题，而不是生成时的问题")
    print()
    
    print("需要检查的地方:")
    print("1. send_single_result 方法中的链接处理")
    print("2. 消息发送时是否对链接进行了额外处理")
    print("3. Markdown 解析是否影响了链接")
    print("4. 日志中记录的链接是否正确")

if __name__ == "__main__":
    test_link_generation()
    test_actual_generation()
    analyze_problem()

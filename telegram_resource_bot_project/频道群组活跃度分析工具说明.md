# Telegram 频道/群组活跃度分析工具说明

## 📋 工具概述

本工具集提供了三个不同的程序来分析和管理您的 Telegram 频道和群组的活跃度，帮助您识别不活跃的频道/群组，优化您的 Telegram 使用体验。

## 🛠️ 工具列表

### 1. 📊 完整活跃度分析工具 (`chat_activity_analyzer.py`)

**功能特点：**
- 详细分析所有频道/群组的活跃度
- 显示最后消息时间、成员数量等完整信息
- 支持多种排序方式（按活跃度、成员数、名称）
- 提供详细的统计报告
- 支持导出到 CSV 文件
- 适合需要详细报告和数据分析的场景

**输出信息：**
- 频道/群组名称和类型
- 用户名（如果是公开的）
- 成员数量
- 最后消息时间
- 距今天数
- 活跃度统计（今天、一周、一月内活跃数量）

### 2. ⚡ 快速不活跃检查工具 (`quick_activity_check.py`)

**功能特点：**
- 快速识别不活跃的频道/群组
- 重点显示超过指定天数未活跃的频道
- 速度更快，适合日常检查
- 提供清理建议
- 可自定义不活跃天数标准

**输出信息：**
- 不活跃频道/群组列表（按不活跃天数排序）
- 无消息记录的频道/群组
- 简洁的统计信息和建议

### 3. 🎯 频道/群组管理工具 (`manage_chats.py`)

**功能特点：**
- 管理搜索机器人的目标频道/群组
- 选择性搜索配置
- 查看所有频道/群组列表
- 交互式选择界面

## 🚀 使用方法

### 方法一：使用启动器（推荐）

1. **Windows 用户：**
   ```bash
   双击运行 start_activity_tools.bat
   ```

2. **其他系统用户：**
   ```bash
   python start_activity_analyzer.py
   ```

### 方法二：直接运行单个工具

```bash
# 完整活跃度分析
python chat_activity_analyzer.py

# 快速不活跃检查
python quick_activity_check.py

# 频道/群组管理
python manage_chats.py
```

## ⚙️ 配置要求

### 必需配置

确保您的 `.env` 文件包含以下配置：

```env
# 基础配置
API_ID=your_api_id
API_HASH=your_api_hash
USER_ID=your_user_id

# 代理配置（可选）
PROXY_URL=http://127.0.0.1:7890
# 或
PROXY_URL=socks5://127.0.0.1:1080
```

### 依赖文件

- `config.py` - 配置管理
- `user_session.session` - Telegram 会话文件（首次运行时自动创建）

## 📊 使用场景

### 1. 定期清理不活跃频道

**推荐使用：** 快速不活跃检查工具

```bash
python quick_activity_check.py
```

- 设置不活跃标准（如 30 天、60 天、90 天）
- 快速识别长期不活跃的频道/群组
- 根据建议进行清理

### 2. 详细活跃度报告

**推荐使用：** 完整活跃度分析工具

```bash
python chat_activity_analyzer.py
```

- 生成完整的活跃度报告
- 导出 CSV 文件进行进一步分析
- 了解整体的频道/群组活跃度分布

### 3. 搜索机器人配置

**推荐使用：** 频道/群组管理工具

```bash
python manage_chats.py
```

- 选择要搜索的频道/群组
- 配置选择性搜索功能

## 📈 输出示例

### 完整活跃度分析输出

```
========================================
📊 频道/群组活跃度分析报告
========================================
📈 总计: 45 个频道/群组
🕒 分析时间: 2024-01-15 14:30:25
📋 排序方式: 按活跃度

序号 类型 频道/群组名称              用户名          成员数   最后消息时间         距今天数
----------------------------------------------------------------------------------------------------
1    📺  科技资讯频道              @tech_news      1250     2024-01-15 10:25:30  今天
2    🔒  Python学习群             私有            856      2024-01-14 18:45:12  1天前
3    👥  朋友聊天群                私有            25       2024-01-10 09:15:45  5天前
...

========================================
📊 活跃度统计:
🟢 今天活跃: 8 个
🟡 一周内活跃: 25 个
🟠 一月内活跃: 38 个
🔴 超过一月未活跃: 5 个
⚫ 无消息记录: 2 个
```

### 快速不活跃检查输出

```
====================================
🔍 不活跃频道/群组检查报告
====================================
📊 检查统计:
   总检查数量: 45
   不活跃标准: 超过 30 天未发消息
   不活跃数量: 5
   无消息数量: 2
   活跃数量: 38

🔴 不活跃频道/群组 (5 个):
序号 类型 名称                          用户名          成员数   距今天数
--------------------------------------------------------------------------------
1    📺  旧技术频道                    @old_tech       450      85天前
2    🔒  废弃项目群                    私有            12       62天前
3    👥  临时活动群                    私有            156      45天前
...

💡 建议:
   - 考虑退出长期不活跃的频道/群组以减少干扰
   - 特别关注超过 90 天未活跃的频道/群组
```

## 🔧 高级功能

### CSV 导出

完整活跃度分析工具支持将结果导出为 CSV 文件：

1. 运行分析工具
2. 选择 "4. 导出到CSV文件"
3. 文件将保存为 `chat_activity_report_YYYYMMDD_HHMMSS.csv`

### 自定义不活跃标准

快速检查工具允许您自定义不活跃天数标准：

- 默认：30 天
- 可设置任意天数（如 7、15、60、90 天等）
- 根据个人需求调整标准

## ⚠️ 注意事项

1. **API 限制：** 工具会自动添加延迟以避免触发 Telegram API 限制
2. **权限要求：** 需要用户账号权限，无法访问您没有权限的私有频道/群组
3. **网络要求：** 如果在受限网络环境，请配置代理
4. **数据准确性：** 某些频道可能由于权限限制无法获取准确的成员数量

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 确认代理配置是否正确
   - 验证 API 凭据

2. **权限错误**
   - 确保使用的是用户账号而非机器人账号
   - 检查是否有访问目标频道/群组的权限

3. **配置错误**
   - 验证 `.env` 文件格式
   - 确认所有必需配置项都已设置

### 获取帮助

如果遇到问题，请检查：
1. 控制台输出的错误信息
2. 网络连接状态
3. 配置文件内容
4. Python 环境和依赖包

## 📝 更新日志

- **v1.0** - 初始版本，包含三个分析工具
- 支持完整活跃度分析和快速检查
- 提供 CSV 导出功能
- 集成代理支持

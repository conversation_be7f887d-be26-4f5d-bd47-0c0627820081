# 配置说明文档

## 📋 概述

所有配置现在都统一在 `.env` 文件中管理，您只需要修改 `.env` 文件即可调整机器人的所有行为。

## 🔧 配置文件

### 主配置文件
- **`.env`** - 实际使用的配置文件（包含真实的API密钥等）
- **`.env.example`** - 配置模板文件（包含示例值和详细说明）

### 使用方法
1. 复制 `.env.example` 为 `.env`
2. 编辑 `.env` 文件，填入真实的配置信息
3. 重启机器人使配置生效

## 📝 配置项详解

### 基础配置（必填）

```bash
# 用户账号ID（数字）
USER_ID=5105735192

# Telegram API配置
API_ID=28381106
API_HASH=ded433aacc9bbbd3c036759b8302c843

# 机器人Token
BOT_TOKEN=6277475670:AAG9WtxxxvcmDEj70NDz2jU3gxaYFRUJSHg
```

### 网络配置（可选）

```bash
# 代理配置（如果需要）
PROXY_URL=http://127.0.0.1:10808
```

### 搜索配置

```bash
# 搜索间隔（秒）- 控制搜索频率
SEARCH_DELAY=0.5

# 频道切换间隔（秒）
CHANNEL_DELAY=0.5

# 转发间隔（秒）
FORWARD_DELAY=1.0
```

### 搜索范围配置 ⭐

```bash
# 是否搜索频道（推荐开启）
SEARCH_CHANNELS=true

# 是否搜索群组（推荐关闭）
SEARCH_GROUPS=false
```

### 限制配置

```bash
# 最大结果数量
MAX_RESULTS=200

# 最大搜索天数
MAX_DAYS=30

# 最大文本长度
MAX_TEXT_LENGTH=10000

# 批量处理大小
BATCH_SIZE=10
```

### 通知频率配置

```bash
# 状态更新间隔（每搜索几个频道更新一次）
STATUS_UPDATE_INTERVAL=1

# 进度报告间隔（每搜索几个频道发送一次报告）
PROGRESS_REPORT_INTERVAL=20
```

### 日志配置

```bash
# 日志级别（DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO
```

## 🎯 常用配置场景

### 1. 资源搜索场景（推荐）

```bash
SEARCH_CHANNELS=true
SEARCH_GROUPS=false
MAX_RESULTS=200
SEARCH_DELAY=0.5
CHANNEL_DELAY=0.5
FORWARD_DELAY=1.0
```

**适用于**：主要搜索频道资源，避免群组转发问题

### 2. 保守配置（网络较慢）

```bash
SEARCH_DELAY=2.0
CHANNEL_DELAY=1.0
FORWARD_DELAY=2.0
MAX_RESULTS=50
STATUS_UPDATE_INTERVAL=5
```

**适用于**：网络较慢或担心触发限制的情况

### 3. 激进配置（网络很快）

```bash
SEARCH_DELAY=0.1
CHANNEL_DELAY=0.1
FORWARD_DELAY=0.5
MAX_RESULTS=500
STATUS_UPDATE_INTERVAL=1
```

**适用于**：网络很快且需要快速搜索的情况

### 4. 调试配置

```bash
LOG_LEVEL=DEBUG
STATUS_UPDATE_INTERVAL=1
PROGRESS_REPORT_INTERVAL=5
MAX_RESULTS=10
```

**适用于**：调试问题或测试功能

## ⚙️ 配置修改步骤

### 1. 编辑配置
```bash
# 使用文本编辑器打开 .env 文件
notepad .env
# 或
vim .env
```

### 2. 修改配置项
找到要修改的配置项，更改其值：
```bash
# 例如：修改搜索范围
SEARCH_CHANNELS=true
SEARCH_GROUPS=true  # 改为 true 开启群组搜索
```

### 3. 保存并重启
保存文件后重启机器人：
```bash
python start_bot.py
```

## 🔍 配置验证

启动机器人时会显示当前配置：

```
📋 找到 180 个频道，35 个群组
📋 将搜索 180 个频道
📋 总共将搜索 180 个目标
```

## ⚠️ 注意事项

### 搜索范围配置
- **SEARCH_CHANNELS=true**：推荐开启，频道转发成功率高
- **SEARCH_GROUPS=false**：推荐关闭，群组转发可能失败

### 性能配置
- **间隔时间过短**：可能触发Telegram限制
- **结果数量过多**：会影响性能和用户体验
- **日志级别DEBUG**：会产生大量日志，仅调试时使用

### 安全配置
- **不要分享 .env 文件**：包含敏感的API密钥
- **使用 .env.example**：分享配置模板而不是实际配置

## 🚀 快速开始

1. **复制配置模板**：
   ```bash
   cp .env.example .env
   ```

2. **编辑基础配置**：
   ```bash
   USER_ID=你的用户ID
   API_ID=你的API_ID
   API_HASH=你的API_HASH
   BOT_TOKEN=你的机器人Token
   ```

3. **使用推荐配置**：
   ```bash
   SEARCH_CHANNELS=true
   SEARCH_GROUPS=false
   MAX_RESULTS=200
   ```

4. **启动机器人**：
   ```bash
   python start_bot.py
   ```

## 📞 配置支持

如果遇到配置问题：

1. **检查语法**：确保没有多余的空格或特殊字符
2. **检查值类型**：数字配置不要加引号，布尔值使用 true/false
3. **查看日志**：启动时会显示配置加载情况
4. **使用默认值**：删除有问题的配置行，使用默认值

现在您可以通过简单地编辑 `.env` 文件来调整机器人的所有行为，无需修改代码！

"""
测试停止命令功能
模拟搜索过程中的停止操作
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from telegram import Bot

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_stop_functionality():
    """测试停止功能"""
    # 加载配置
    load_dotenv('.env')
    
    bot_token = os.getenv('BOT_TOKEN')
    user_id = int(os.getenv('USER_ID'))
    
    if not bot_token or not user_id:
        logger.error("❌ 缺少BOT_TOKEN或USER_ID配置")
        return
    
    try:
        # 创建机器人实例
        bot = Bot(token=bot_token)
        
        logger.info("🧪 开始测试停止命令功能...")
        
        # 发送测试说明
        test_intro = """🧪 **停止命令测试**

这个测试将帮助您验证 `/stop` 命令的功能。

**测试步骤：**
1. 启动机器人：`python start_bot.py`
2. 发送搜索命令：`/search 测试 7`
3. 在搜索过程中发送：`/stop`
4. 观察搜索是否正确停止

**预期结果：**
• 搜索立即停止
• 收到停止确认消息
• 可以立即开始新的搜索

**测试场景：**
✅ 搜索进行中时停止
✅ 没有搜索时发送停止命令
✅ 停止后立即开始新搜索

请按照上述步骤进行测试！"""

        await bot.send_message(
            chat_id=user_id,
            text=test_intro,
            parse_mode='Markdown'
        )
        
        # 模拟不同的停止场景
        scenarios = [
            {
                'title': '场景1：没有搜索时使用停止命令',
                'description': '发送 `/stop` 命令（当前没有搜索任务）',
                'expected': '应该收到"没有正在进行的搜索任务"的提示'
            },
            {
                'title': '场景2：搜索进行中使用停止命令',
                'description': '先发送 `/search 关键词 天数`，然后立即发送 `/stop`',
                'expected': '搜索应该立即停止，收到停止确认消息'
            },
            {
                'title': '场景3：停止后立即开始新搜索',
                'description': '停止搜索后，立即发送新的搜索命令',
                'expected': '新搜索应该正常开始，不受之前停止的影响'
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            scenario_text = f"""📋 **{scenario['title']}**

🔍 **操作：**
{scenario['description']}

✅ **预期结果：**
{scenario['expected']}

---"""
            
            await bot.send_message(
                chat_id=user_id,
                text=scenario_text,
                parse_mode='Markdown'
            )
            
            await asyncio.sleep(1)
        
        # 发送测试总结
        summary = """🎯 **测试总结**

**停止命令的特点：**
• 🛑 立即停止当前搜索
• 📊 显示已找到的结果统计
• ✅ 重置搜索状态
• 🔄 允许立即开始新搜索

**使用建议：**
• 搜索范围过大时可以及时停止
• 发现足够结果时可以提前结束
• 需要修改搜索条件时先停止再重新搜索

**注意事项：**
• 停止命令只对当前用户有效
• 停止后已找到的结果会保留
• 可以随时重新开始搜索

现在您可以开始实际测试了！"""

        await bot.send_message(
            chat_id=user_id,
            text=summary,
            parse_mode='Markdown'
        )
        
        logger.info("✅ 测试说明发送完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")

async def main():
    """主函数"""
    logger.info("🧪 开始停止命令测试...")
    await test_stop_functionality()
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())

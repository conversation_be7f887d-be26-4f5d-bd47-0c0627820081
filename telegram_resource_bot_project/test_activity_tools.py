"""
活跃度分析工具测试脚本
测试各个工具的基本功能是否正常
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_config():
    """测试配置文件"""
    try:
        from config import Config
        
        print("🔧 测试配置文件...")
        
        # 检查必需配置
        required_configs = ['API_ID', 'API_HASH', 'USER_ID']
        missing_configs = []
        
        for config_name in required_configs:
            value = getattr(Config, config_name, None)
            if not value:
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"❌ 缺少必需配置: {', '.join(missing_configs)}")
            return False
        
        print(f"✅ 配置文件检查通过")
        print(f"   API_ID: {Config.API_ID}")
        print(f"   代理设置: {'已配置' if getattr(Config, 'PROXY_URL', None) else '未配置'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入配置文件: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_client_connection():
    """测试客户端连接"""
    try:
        from telethon import TelegramClient
        from config import Config
        
        print("\n🔗 测试客户端连接...")
        
        # 简化的代理配置
        proxy = None
        proxy_url = getattr(Config, 'PROXY_URL', None)
        if proxy_url:
            print(f"🌐 使用代理: {proxy_url}")
            try:
                if proxy_url.startswith('http://'):
                    proxy_part = proxy_url.replace('http://', '')
                    addr, port = proxy_part.split(':')
                    proxy = {'proxy_type': 'HTTP', 'addr': addr, 'port': int(port)}
                elif proxy_url.startswith('socks5://'):
                    proxy_part = proxy_url.replace('socks5://', '')
                    addr, port = proxy_part.split(':')
                    proxy = {'proxy_type': 'SOCKS5', 'addr': addr, 'port': int(port)}
            except Exception as e:
                print(f"⚠️ 代理配置解析失败: {e}")
                proxy = None
        
        # 创建客户端
        client = TelegramClient('test_session', Config.API_ID, Config.API_HASH, proxy=proxy)
        
        # 测试连接
        await client.start()
        
        # 获取用户信息
        me = await client.get_me()
        print(f"✅ 连接成功！用户: {me.first_name} (@{me.username or 'N/A'})")
        
        # 测试获取对话列表
        dialogs = await client.get_dialogs(limit=5)
        print(f"✅ 成功获取 {len(dialogs)} 个对话")
        
        await client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 客户端连接测试失败: {e}")
        return False

async def test_activity_analyzer_import():
    """测试活跃度分析工具导入"""
    try:
        print("\n📊 测试完整活跃度分析工具导入...")
        
        # 测试导入
        from chat_activity_analyzer import ChatActivityAnalyzer
        
        # 创建实例
        analyzer = ChatActivityAnalyzer()
        
        print("✅ 完整活跃度分析工具导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入完整活跃度分析工具: {e}")
        return False
    except Exception as e:
        print(f"❌ 完整活跃度分析工具测试失败: {e}")
        return False

async def test_quick_checker_import():
    """测试快速检查工具导入"""
    try:
        print("\n⚡ 测试快速检查工具导入...")
        
        # 测试导入
        from quick_activity_check import QuickActivityChecker
        
        # 创建实例
        checker = QuickActivityChecker()
        
        print("✅ 快速检查工具导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入快速检查工具: {e}")
        return False
    except Exception as e:
        print(f"❌ 快速检查工具测试失败: {e}")
        return False

async def test_manage_chats_import():
    """测试管理工具导入"""
    try:
        print("\n🎯 测试管理工具导入...")
        
        # 测试导入
        from manage_chats import ChatManager
        
        # 创建实例
        manager = ChatManager()
        
        print("✅ 管理工具导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入管理工具: {e}")
        return False
    except Exception as e:
        print(f"❌ 管理工具测试失败: {e}")
        return False

def check_files():
    """检查必要文件是否存在"""
    print("📁 检查必要文件...")
    
    required_files = [
        'config.py',
        'chat_activity_analyzer.py',
        'quick_activity_check.py',
        'manage_chats.py',
        'start_activity_analyzer.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    # 检查 .env 文件
    if not os.path.exists('.env'):
        print("⚠️ 未找到 .env 文件，请确保已正确配置")
    else:
        print("✅ .env")
    
    return True

async def main():
    """主测试函数"""
    print("🧪 活跃度分析工具测试")
    print("=" * 50)
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败，请确保所有必要文件都存在")
        return
    
    # 测试配置
    if not await test_config():
        print("\n❌ 配置测试失败，请检查 .env 文件配置")
        return
    
    # 测试工具导入
    tests = [
        test_activity_analyzer_import(),
        test_quick_checker_import(),
        test_manage_chats_import()
    ]
    
    results = await asyncio.gather(*tests, return_exceptions=True)
    
    import_success = all(result is True for result in results if not isinstance(result, Exception))
    
    if not import_success:
        print("\n❌ 工具导入测试失败")
        return
    
    # 测试客户端连接（可选）
    print("\n" + "=" * 50)
    test_connection = input("是否测试 Telegram 客户端连接? (y/N): ").strip().lower()
    
    if test_connection == 'y':
        if await test_client_connection():
            print("\n🎉 所有测试通过！工具可以正常使用。")
        else:
            print("\n⚠️ 客户端连接测试失败，请检查网络和配置")
    else:
        print("\n✅ 基础测试通过！工具导入正常。")
    
    print("\n📋 使用说明:")
    print("1. 运行 python start_activity_analyzer.py 使用启动器")
    print("2. 或直接运行单个工具:")
    print("   - python chat_activity_analyzer.py (完整分析)")
    print("   - python quick_activity_check.py (快速检查)")
    print("   - python manage_chats.py (管理工具)")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n❌ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        logger.exception("测试失败")

"""
测试时区修复的脚本
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv
from telethon import TelegramClient
from telethon.tl.types import Channel, Chat

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_timezone_fix():
    """测试时区修复"""
    # 加载配置
    load_dotenv('.env')
    
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    
    # 计算时间范围（使用UTC时区）
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=3)
    
    logger.info(f"搜索时间范围 (UTC): {start_date} 到 {end_date}")
    
    # 连接客户端
    client = TelegramClient('user_session', api_id, api_hash)
    await client.start()
    
    try:
        # 获取对话列表
        dialogs = await client.get_dialogs()
        
        # 找到频道和群组
        channels_and_groups = []
        for dialog in dialogs:
            if isinstance(dialog.entity, (Channel, Chat)):
                channels_and_groups.append(dialog)
        
        logger.info(f"找到 {len(channels_and_groups)} 个频道和群组")
        
        # 测试前3个频道
        for i, dialog in enumerate(channels_and_groups[:3]):
            logger.info(f"\n=== 测试频道 {i+1}: {dialog.title} ===")
            
            try:
                message_count = 0
                in_range_count = 0
                
                # 获取最近的消息
                async for message in client.iter_messages(dialog.entity, limit=50):
                    message_count += 1
                    
                    # 显示消息时间信息
                    if message_count <= 3:
                        logger.info(f"消息 {message_count} 时间: {message.date} (类型: {type(message.date)})")
                    
                    # 检查时间范围（现在应该不会出错）
                    if message.date >= start_date:
                        in_range_count += 1
                        if in_range_count <= 3:
                            logger.info(f"  ✅ 时间范围内: {message.date}")
                    else:
                        if message_count <= 10:
                            logger.info(f"  ❌ 超出范围: {message.date}")
                        break
                
                logger.info(f"频道 {dialog.title}: 检查了 {message_count} 条消息，时间范围内 {in_range_count} 条")
                
            except Exception as e:
                logger.error(f"测试频道 {dialog.title} 时出错: {e}")
            
            await asyncio.sleep(1)
    
    finally:
        await client.disconnect()

async def main():
    """主函数"""
    logger.info("🔍 开始测试时区修复...")
    await test_timezone_fix()
    logger.info("✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())

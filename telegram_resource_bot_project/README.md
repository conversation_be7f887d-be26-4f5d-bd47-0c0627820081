# Telegram资源搜索机器人

一个功能强大的Telegram机器人，可以搜索您加入的所有频道中的资源并转发匹配结果。

## 功能特点

- 🔍 **智能搜索**：支持在所有加入的频道中搜索指定关键词
- 📱 **多格式支持**：支持文字消息和文件名匹配
- ⏰ **时间范围**：可指定搜索最近几天的消息
- 📤 **智能发送**：将匹配结果发送到您的对话中（避免出现在收藏夹）
- 🎛️ **显示模式**：支持完整内容和简洁信息两种显示模式
- 🛡️ **频率控制**：智能控制搜索频率，避免触发Telegram限制
- 🔒 **权限控制**：只有指定用户可以使用机器人
- 🌐 **代理支持**：支持HTTP和SOCKS5代理

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 获取必要的配置信息

#### 获取Telegram API配置
1. 访问 [https://my.telegram.org](https://my.telegram.org)
2. 登录您的Telegram账号
3. 创建新应用，获取 `API_ID` 和 `API_HASH`

#### 创建机器人
1. 在Telegram中找到 [@BotFather](https://t.me/BotFather)
2. 发送 `/newbot` 创建新机器人
3. 按提示设置机器人名称和用户名
4. 获取机器人的 `BOT_TOKEN`

#### 获取用户ID
1. 在Telegram中找到 [@userinfobot](https://t.me/userinfobot)
2. 发送任意消息获取您的 `USER_ID`

### 3. 配置环境变量

复制 `.env.example` 为 `.env` 并填入配置信息：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
USER_ID=您的用户ID
API_ID=您的API_ID
API_HASH=您的API_HASH
BOT_TOKEN=您的机器人Token

# 代理配置（可选）
PROXY_URL=socks5://127.0.0.1:1080  # SOCKS5代理（推荐）
# PROXY_URL=http://127.0.0.1:8080   # HTTP代理（兼容性有限）

# 消息显示模式配置
SEND_FULL_CONTENT=true  # true=完整内容，false=简洁信息

# 选择性搜索配置
USE_SELECTIVE_SEARCH=false  # true=只搜索指定频道，false=搜索所有频道
```

## 使用方法

### 启动机器人

```bash
# 先测试连接
python start_bot.py --test

# 如果测试通过，启动机器人
python start_bot.py
```

### 机器人命令

#### `/start`
显示欢迎信息和基本使用说明

#### `/help`
显示详细的帮助文档

#### `/search <关键词> <天数>`
搜索包含指定关键词的消息

**示例：**
- `/search 影视 3` - 搜索最近3天内包含"影视"的消息
- `/search 电影 7` - 搜索最近7天内包含"电影"的消息
- `/search 软件 1` - 搜索最近1天内包含"软件"的消息

### 搜索范围

- **频道类型**：所有已加入的频道和群组（或选择性搜索指定频道）
- **消息类型**：文字消息、文件名、图片说明等
- **匹配方式**：模糊匹配（不区分大小写）
- **时间范围**：1-30天（建议不超过7天）

### 选择性搜索

如果您加入了很多频道但只想在特定几个中搜索：

1. **启用选择性搜索**：
   ```bash
   # 在 .env 文件中设置
   USE_SELECTIVE_SEARCH=true
   ```

2. **选择要搜索的频道**：
   ```bash
   python manage_chats.py
   ```

3. **正常使用搜索命令**（只会在选择的频道中搜索）

### 代理配置

如果需要使用代理连接Telegram：

1. **配置代理**：
   ```bash
   # 在 .env 文件中设置（推荐SOCKS5）
   PROXY_URL=socks5://127.0.0.1:1080
   ```

2. **测试代理连接**：
   ```bash
   python test_proxy_connection.py
   ```

详细配置请参考：[代理配置说明.md](代理配置说明.md)

## 工作原理

1. **命令解析**：机器人接收 `/search` 命令并解析参数
2. **权限验证**：检查发送者是否为授权用户
3. **频道遍历**：获取所有已加入的频道和群组
4. **消息搜索**：在指定时间范围内搜索匹配的消息
5. **结果整理**：按时间排序并统计搜索结果
6. **智能发送**：将匹配的消息内容发送到用户对话中

## 安全和限制

### 频率控制
- 搜索消息间隔：2秒
- 频道切换间隔：1秒
- 转发消息间隔：1秒
- 批量检查：每10条消息暂停一次

### 结果限制
- 最大结果数量：50条
- 消息内容长度：1000字符（超出部分截断）
- 搜索时间范围：最多30天

### 权限控制
- 只有配置的 `USER_ID` 可以使用机器人
- 其他用户发送命令会收到权限拒绝提示

## 故障排除

### 常见问题

1. **"缺少必要的环境变量"**
   - 检查 `.env` 文件是否存在且配置正确
   - 确保所有必要的变量都已设置

2. **"导入模块失败"**
   - 运行 `pip install -r requirements.txt` 安装依赖

3. **"The API access for bot users is restricted"**
   - 这是正常现象，机器人Token无法获取用户对话列表
   - 程序已修复，使用用户客户端进行搜索

4. **"搜索失败"**
   - 先运行 `python start_bot.py --test` 测试连接
   - 检查网络连接和代理配置
   - 确认Telegram API配置是否正确
   - 确保用户ID与登录账号匹配

5. **"消息发送问题"**
   - 机器人现在直接发送消息内容，避免收藏夹问题
   - 所有消息只会出现在对话中，不会出现在收藏夹

6. **"用户ID不匹配"**
   - 确保 `.env` 中的 `USER_ID` 与实际登录的账号ID一致
   - 可以通过 [@userinfobot](https://t.me/userinfobot) 获取正确的用户ID

### 日志信息

程序运行时会输出详细的日志信息，包括：
- 客户端初始化状态
- 搜索进度
- 错误信息
- 转发状态

## 注意事项

1. **合规使用**：请遵守Telegram的使用条款和相关法律法规
2. **频率限制**：避免过于频繁的搜索，以免触发Telegram的限制
3. **隐私保护**：不要在公共场所运行或分享配置信息
4. **资源消耗**：大范围搜索可能消耗较多时间和网络资源

## 技术架构

- **用户客户端**：使用 `telethon` 库操作Telegram用户账号
- **机器人服务**：使用 `python-telegram-bot` 库处理机器人命令
- **异步编程**：使用 `asyncio` 提高搜索和转发效率
- **配置管理**：支持环境变量和 `.env` 文件配置

## 许可证

本项目仅供学习和个人使用，请勿用于商业用途。

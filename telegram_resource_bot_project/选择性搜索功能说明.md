# 选择性搜索功能说明

## 🎯 功能概述

选择性搜索功能允许用户指定只在特定的频道和群组中进行搜索，而不是搜索所有已加入的频道/群组。这对于加入了大量频道但只关心特定几个的用户非常有用。

## ⚙️ 配置方法

### 1. 启用选择性搜索

在 `.env` 文件中设置：
```bash
# 启用选择性搜索模式
USE_SELECTIVE_SEARCH=true

# 指定选择文件路径（可选，默认为 selected_chats.txt）
SELECTED_CHATS_FILE=selected_chats.txt
```

### 2. 选择要搜索的频道/群组

运行管理工具：
```bash
python manage_chats.py
```

## 🛠️ 使用流程

### 第一步：运行管理工具

```bash
cd telegram_resource_bot_project
python manage_chats.py
```

**注意：** 管理工具会自动使用 `.env` 文件中的代理配置，无需额外设置。

### 第二步：查看所有频道/群组

工具会显示您加入的所有频道和群组：

```
================================================================================
📺 频道列表 (25 个)
================================================================================
  1. 📺 科技资讯频道
     ID: -1001234567890 | @tech_news | 成员: 5000

  2. 📺 电影资源分享
     ID: -1001234567891 | 私有频道 | 成员: 3000

================================================================================
👥 群组列表 (15 个)
================================================================================
 26. 🔒 技术讨论群
     ID: -1001234567892 | @tech_group | 成员: 500

 27. 👥 朋友聊天群
     ID: -1001234567893 | 私有群组 | 成员: 50
```

### 第三步：选择操作

```
🎯 选择性搜索配置
================================================================================
当前已选择: 0 个频道/群组

操作选项:
1. 添加频道/群组到选择列表
2. 从选择列表中移除频道/群组
3. 清空选择列表
4. 显示当前选择
5. 保存并退出
6. 退出不保存
```

### 第四步：添加选择

选择操作 `1`，然后输入要添加的编号：
```
输入要添加的频道/群组编号（用空格分隔，如：1 3 5）:
编号: 1 2 26

  ✅ 已添加: 📺 科技资讯频道
  ✅ 已添加: 📺 电影资源分享
  ✅ 已添加: 🔒 技术讨论群
```

### 第五步：保存配置

选择操作 `5` 保存并退出：
```
✅ 已保存 3 个选择到 selected_chats.txt
```

## 📁 选择文件格式

`selected_chats.txt` 文件格式：
```
# 选择的频道和群组列表
# 格式：ID|标题|类型
# 以 # 开头的行为注释，会被忽略

-1001234567890|科技资讯频道|channel
-1001234567891|电影资源分享|channel
-1001234567892|技术讨论群|supergroup
```

## 🔄 两种搜索模式

### 全部搜索模式（默认）
```bash
USE_SELECTIVE_SEARCH=false
```
- 在所有已加入的频道/群组中搜索
- 受 `SEARCH_CHANNELS` 和 `SEARCH_GROUPS` 配置控制

### 选择性搜索模式
```bash
USE_SELECTIVE_SEARCH=true
```
- 只在指定的频道/群组中搜索
- 忽略 `SEARCH_CHANNELS` 和 `SEARCH_GROUPS` 配置
- 从 `selected_chats.txt` 文件读取搜索目标

## 📊 效果对比

### 场景示例
假设您加入了：
- 100个频道
- 50个群组
- 但只关心其中的5个频道和2个群组

### 全部搜索模式
```
搜索目标: 150个频道/群组
搜索时间: 约15-30分钟
API请求: 1500-3000次
```

### 选择性搜索模式
```
搜索目标: 7个指定频道/群组
搜索时间: 约1-2分钟
API请求: 70-140次
```

**效率提升：** 搜索时间减少90%以上！

## 🎯 使用建议

### 适用场景

1. **资源搜索**：只关心特定的资源分享频道
2. **主题搜索**：只在相关主题的频道/群组中搜索
3. **高效搜索**：加入了大量频道但只需要搜索少数几个
4. **测试调试**：在开发测试时只搜索特定频道

### 配置策略

#### 1. 按主题分组
```
# 技术相关
tech_channels.txt - 包含所有技术频道

# 娱乐相关  
entertainment_channels.txt - 包含娱乐频道

# 新闻相关
news_channels.txt - 包含新闻频道
```

#### 2. 按重要性分级
```
# 高优先级频道（经常搜索）
high_priority_channels.txt

# 中等优先级频道（偶尔搜索）
medium_priority_channels.txt

# 低优先级频道（很少搜索）
low_priority_channels.txt
```

## 🔧 高级用法

### 1. 多个选择文件

可以为不同用途创建多个选择文件：

```bash
# 搜索技术资源时
SELECTED_CHATS_FILE=tech_channels.txt

# 搜索娱乐资源时  
SELECTED_CHATS_FILE=entertainment_channels.txt
```

### 2. 动态切换

通过修改 `.env` 文件快速切换搜索模式：

```bash
# 快速搜索（选择性）
USE_SELECTIVE_SEARCH=true
SELECTED_CHATS_FILE=quick_search.txt

# 全面搜索（全部）
USE_SELECTIVE_SEARCH=false
```

### 3. 手动编辑选择文件

您也可以直接编辑 `selected_chats.txt` 文件：

```bash
# 添加新的频道ID
echo "-1001234567894|新频道|channel" >> selected_chats.txt

# 查看当前选择
cat selected_chats.txt
```

## ⚠️ 注意事项

### 1. 文件路径
- 选择文件路径相对于项目根目录
- 确保文件编码为 UTF-8

### 2. ID格式
- 频道/群组ID必须是完整的数字ID
- 包含 `-100` 前缀的长ID格式

### 3. 同步问题
- 如果退出了某个频道/群组，选择文件中的对应条目会被忽略
- 建议定期运行管理工具更新选择

### 4. 权限要求
- 需要有读取频道/群组列表的权限
- 某些私有频道可能无法搜索

### 5. 代理配置
- 管理工具自动使用 `.env` 文件中的 `PROXY_URL` 配置
- 支持HTTP和SOCKS5代理
- 建议使用SOCKS5代理以获得更好的兼容性

## 🚀 性能优化

### 搜索效率提升

选择性搜索的性能优势：

1. **减少API调用**：只搜索指定频道，大幅减少API请求
2. **缩短搜索时间**：搜索时间与选择的频道数量成正比
3. **降低资源消耗**：减少内存和网络使用
4. **避免限制**：减少触发Telegram API限制的风险

### 最佳实践

1. **精选频道**：只选择真正需要搜索的频道
2. **定期更新**：定期检查和更新选择列表
3. **分类管理**：为不同用途创建不同的选择文件
4. **测试验证**：添加新频道后测试搜索效果

## 🎉 总结

选择性搜索功能为用户提供了：

- **🎯 精准搜索**：只在关心的频道中搜索
- **⚡ 高效执行**：大幅提升搜索速度
- **🛠️ 灵活配置**：支持多种使用场景
- **📊 资源节约**：减少不必要的API调用

通过合理使用这个功能，您可以显著提升搜索效率，快速找到需要的资源！

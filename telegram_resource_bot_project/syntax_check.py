"""
语法检查脚本
检查所有活跃度分析工具的语法是否正确
"""

import ast
import os

def check_syntax(filename):
    """检查单个文件的语法"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析语法
        ast.parse(source)
        print(f"✅ {filename} - 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} - 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else ''}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename} - 检查失败: {e}")
        return False

def main():
    print("🔍 语法检查工具")
    print("=" * 40)
    
    files_to_check = [
        'chat_activity_analyzer.py',
        'quick_activity_check.py',
        'manage_chats.py',
        'start_activity_analyzer.py',
        'verify_tools.py'
    ]
    
    all_good = True
    
    for filename in files_to_check:
        if os.path.exists(filename):
            if not check_syntax(filename):
                all_good = False
        else:
            print(f"⚠️ {filename} - 文件不存在")
            all_good = False
    
    print("\n" + "=" * 40)
    if all_good:
        print("🎉 所有文件语法检查通过！")
        print("\n现在可以尝试运行:")
        print("python start_activity_analyzer.py")
    else:
        print("❌ 发现语法错误，请修复后再试")

if __name__ == "__main__":
    main()

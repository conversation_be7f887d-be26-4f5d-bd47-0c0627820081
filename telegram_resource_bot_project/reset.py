"""
重置脚本
清理会话文件和缓存，重新开始
"""

import os
import shutil
import glob

def reset_project():
    """重置项目"""
    print("🔄 开始重置项目...")
    
    # 要删除的文件和文件夹
    items_to_remove = [
        'user_session.session',
        'user_session.session-journal',
        'test_session.session',
        'test_session.session-journal',
        '__pycache__',
        '*.pyc'
    ]
    
    removed_count = 0
    
    for item in items_to_remove:
        if '*' in item:
            # 处理通配符
            for file_path in glob.glob(item):
                try:
                    os.remove(file_path)
                    print(f"✅ 删除文件: {file_path}")
                    removed_count += 1
                except Exception as e:
                    print(f"⚠️  删除文件失败 {file_path}: {e}")
        else:
            # 处理具体文件或文件夹
            if os.path.exists(item):
                try:
                    if os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"✅ 删除文件夹: {item}")
                    else:
                        os.remove(item)
                        print(f"✅ 删除文件: {item}")
                    removed_count += 1
                except Exception as e:
                    print(f"⚠️  删除失败 {item}: {e}")
    
    if removed_count == 0:
        print("ℹ️  没有找到需要清理的文件")
    else:
        print(f"🎉 重置完成！清理了 {removed_count} 个项目")
    
    print("\n📋 下一步操作:")
    print("1. 确保 .env 文件配置正确")
    print("2. 运行: python fix_user_id.py")
    print("3. 运行: python start_bot.py --test")
    print("4. 如果测试通过，运行: python start_bot.py")

if __name__ == "__main__":
    reset_project()

"""
快速测试停止命令修复
验证停止命令的响应时间
"""

import asyncio
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class StopTestSimulator:
    """模拟停止测试"""
    
    def __init__(self):
        self.stop_search = False
        self.is_searching = False
        
    async def simulate_search_with_stop_checks(self, total_items=1000, stop_after_seconds=3):
        """模拟搜索过程，测试停止响应时间"""
        logger.info(f"🧪 开始模拟搜索 {total_items} 个项目，{stop_after_seconds}秒后停止")
        
        self.is_searching = True
        self.stop_search = False
        
        # 启动停止任务
        stop_task = asyncio.create_task(self.simulate_stop_command(stop_after_seconds))
        
        start_time = time.time()
        processed_items = 0
        
        try:
            # 模拟搜索循环
            for i in range(total_items):
                # 主要停止检查点
                if self.stop_search:
                    stop_time = time.time()
                    response_time = stop_time - start_time - stop_after_seconds
                    logger.info(f"🛑 搜索在第 {i} 项时停止")
                    logger.info(f"⏱️  停止响应时间: {response_time:.2f} 秒")
                    return response_time
                
                # 模拟处理项目
                processed_items = i + 1
                
                # 模拟一些工作
                await asyncio.sleep(0.01)  # 10ms per item
                
                # 每10个项目检查一次停止
                if i % 10 == 0:
                    if self.stop_search:
                        stop_time = time.time()
                        response_time = stop_time - start_time - stop_after_seconds
                        logger.info(f"🛑 搜索在批次检查时停止 (第 {i} 项)")
                        logger.info(f"⏱️  停止响应时间: {response_time:.2f} 秒")
                        return response_time
            
            # 如果搜索完成而没有被停止
            logger.info(f"✅ 搜索正常完成，处理了 {processed_items} 个项目")
            return None
            
        finally:
            self.is_searching = False
            self.stop_search = False
            if not stop_task.done():
                stop_task.cancel()
    
    async def simulate_stop_command(self, delay_seconds):
        """模拟停止命令"""
        await asyncio.sleep(delay_seconds)
        logger.info(f"🛑 发送停止命令 (延迟 {delay_seconds} 秒)")
        self.stop_search = True

async def test_stop_responsiveness():
    """测试停止响应性"""
    logger.info("🚀 开始停止响应性测试...")
    
    simulator = StopTestSimulator()
    
    test_cases = [
        {"items": 1000, "stop_after": 2, "name": "快速停止测试"},
        {"items": 2000, "stop_after": 5, "name": "中等停止测试"},
        {"items": 500, "stop_after": 1, "name": "极速停止测试"},
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"📋 测试 {i}: {test_case['name']}")
        logger.info(f"   项目数量: {test_case['items']}")
        logger.info(f"   停止延迟: {test_case['stop_after']} 秒")
        
        response_time = await simulator.simulate_search_with_stop_checks(
            test_case['items'], 
            test_case['stop_after']
        )
        
        if response_time is not None:
            results.append({
                "test": test_case['name'],
                "response_time": response_time,
                "acceptable": response_time < 1.0  # 1秒内响应算合格
            })
            logger.info(f"✅ 测试完成，响应时间: {response_time:.2f} 秒")
        else:
            logger.info("⚠️  测试未被停止（可能是正常完成）")
        
        await asyncio.sleep(1)  # 测试间隔
    
    # 总结结果
    logger.info(f"\n{'='*50}")
    logger.info("🎯 测试总结:")
    
    if results:
        for result in results:
            status = "✅ 合格" if result["acceptable"] else "❌ 不合格"
            logger.info(f"  {result['test']}: {result['response_time']:.2f}s {status}")
        
        avg_response = sum(r["response_time"] for r in results) / len(results)
        acceptable_count = sum(1 for r in results if r["acceptable"])
        
        logger.info(f"\n📊 统计:")
        logger.info(f"  平均响应时间: {avg_response:.2f} 秒")
        logger.info(f"  合格测试数量: {acceptable_count}/{len(results)}")
        
        if acceptable_count == len(results):
            logger.info("🎉 所有测试都通过！停止功能响应良好")
        else:
            logger.info("⚠️  部分测试未通过，可能需要进一步优化")
    else:
        logger.info("❌ 没有有效的测试结果")

async def main():
    """主函数"""
    logger.info("🧪 停止命令快速测试工具")
    logger.info("这个测试模拟了机器人搜索过程中的停止响应")
    logger.info("目标：停止命令应在1秒内生效\n")
    
    await test_stop_responsiveness()
    
    logger.info("\n💡 如果测试通过，说明停止命令修复成功")
    logger.info("💡 如果测试失败，可能需要进一步调整停止检查频率")

if __name__ == "__main__":
    asyncio.run(main())

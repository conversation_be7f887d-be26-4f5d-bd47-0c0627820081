#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试下载链接提取问题
"""

import sys
import os
import requests
from bs4 import BeautifulSoup

# 添加斌哥游戏更新监控目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '斌哥游戏更新监控'))

from game_monitor import GameMonitor

def debug_game_page(game_id=885):
    """调试游戏页面内容"""
    print(f"🔍 调试游戏 {game_id} 的页面内容")
    print("=" * 50)
    
    url = f"https://bg.denwq.cn/appshop/info/id/{game_id}.html"
    print(f"URL: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1 Edg/*********',
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'referer': 'https://bg.denwq.cn/f/1.html',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1'
        }
        cookies = {
            'xr_fullscreen_cookies': '1',
            'hyphp_lang': 'zh-CN'
        }

        response = requests.get(url, headers=headers, cookies=cookies, timeout=30)
        response.raise_for_status()

        print(f"✅ 成功获取页面，状态码: {response.status_code}")
        print(f"页面大小: {len(response.text)} 字符")
        
        # 保存页面内容到文件
        with open(f"game_{game_id}_page.html", 'w', encoding='utf-8') as f:
            f.write(response.text)
        print(f"📁 页面内容已保存到 game_{game_id}_page.html")
        
        # 解析页面
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # 查找网盘链接容器
        print("\n🔍 查找网盘链接容器...")
        wangpan_div = soup.find('div', id='wangpan')
        if wangpan_div:
            print("✅ 找到 wangpan 容器")
            print(f"容器内容长度: {len(str(wangpan_div))}")
            
            # 查找所有链接
            links = wangpan_div.find_all('a', href=True)
            print(f"找到 {len(links)} 个链接:")
            
            for i, link in enumerate(links, 1):
                href = link.get('href')
                text = link.get_text(strip=True)
                print(f"  {i}. 文本: '{text}' | 链接: {href}")
        else:
            print("❌ 未找到 wangpan 容器")
            
            # 查找所有可能的下载相关元素
            print("\n🔍 查找所有可能的下载链接...")
            all_links = soup.find_all('a', href=True)
            download_keywords = ['下载', 'download', 'pan.', 'drive.', '网盘', '云盘', 'baidu', 'lanzou']
            
            found_links = []
            for link in all_links:
                href = link.get('href')
                text = link.get_text(strip=True)
                
                if any(keyword in href.lower() or keyword in text.lower() for keyword in download_keywords):
                    found_links.append((text, href))
            
            if found_links:
                print(f"找到 {len(found_links)} 个可能的下载链接:")
                for i, (text, href) in enumerate(found_links, 1):
                    print(f"  {i}. 文本: '{text}' | 链接: {href}")
            else:
                print("❌ 未找到任何下载相关链接")
        
        # 查找页面中的所有div元素，看看是否有其他容器
        print("\n🔍 查找所有div元素...")
        divs = soup.find_all('div')
        download_divs = []
        for div in divs:
            div_id = div.get('id', '')
            div_class = ' '.join(div.get('class', []))
            div_text = div.get_text(strip=True)[:100]  # 前100个字符
            
            if any(keyword in div_id.lower() or keyword in div_class.lower() or keyword in div_text.lower() 
                   for keyword in ['下载', 'download', '网盘', '云盘']):
                download_divs.append((div_id, div_class, div_text))
        
        if download_divs:
            print(f"找到 {len(download_divs)} 个可能包含下载信息的div:")
            for i, (div_id, div_class, div_text) in enumerate(download_divs, 1):
                print(f"  {i}. ID: '{div_id}' | Class: '{div_class}' | 文本: '{div_text}'")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_game_page(885)  # 调试快来当领主游戏

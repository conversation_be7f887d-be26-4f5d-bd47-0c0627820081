import requests
import json
import os
from message_push import wxpusher

# 常量定义
COMICS = {
    "4864": "中国惊奇先生",
    "5836": "镇魂街",
    "4907": "狐妖小红娘"
}
API_URL = "http://111.180.202.218:1314/appv1/comic/chapter"
PARAMS = {
    "id": ""  # 漫画ID将在循环中动态设置
}
RECORD_FILE = "last_chapters.json"
CONTENT_TYPE = 1  # 1 表示文字


def load_last_chapters(file_path: str) -> dict:
    """
    加载上次记录的章节信息。

    参数:
        file_path (str): 记录文件的路径。

    返回:
        dict: 漫画ID到最后章节ID的映射。
    """
    if not os.path.exists(file_path):
        return {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except json.JSONDecodeError:
        print(f"记录文件 {file_path} 格式错误，重新初始化。")
        return {}


def save_last_chapters(file_path: str, data: dict) -> None:
    """
    保存当前的章节记录到文件。

    参数:
        file_path (str): 记录文件的路径。
        data (dict): 漫画ID到最后章节ID的映射。
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存记录时发生错误: {e}")


def fetch_latest_chapter(comic_id: str) -> dict:
    """
    获取指定漫画的最新章节信息。

    参数:
        comic_id (str): 漫画的唯一标识符。

    返回:
        dict: 最新章节的数据。

    抛出:
        requests.exceptions.RequestException: 请求过程中发生的异常。
        KeyError: JSON响应中缺少预期的键。
        IndexError: 列表为空，无法获取最后一个元素。
    """
    params = PARAMS.copy()
    params["id"] = comic_id
    response = requests.get(API_URL, params=params)
    response.raise_for_status()
    data = response.json()
    latest_chapter = data['list'][-1]
    return latest_chapter


def format_message(comic_name: str, chapter: dict) -> str:
    """
    格式化章节信息为消息内容。

    参数:
        comic_name (str): 漫画名称。
        chapter (dict): 章节的数据。

    返回:
        str: 格式化后的消息内容。
    """
    return (
        f"【{comic_name}】最新话：{chapter['name']}\n"
        f"页数：{chapter['pnum']}\n"
        f"更新时间：{chapter['addtime']}\n"
        f"id：{chapter['id']}"
    )


def main():
    """
    主函数，获取最新章节信息并通过微信推送。
    """
    # 加载上次记录的章节信息
    last_chapters = load_last_chapters(RECORD_FILE)
    updated_chapters = last_chapters.copy()

    for comic_id, comic_name in COMICS.items():
        try:
            latest_chapter = fetch_latest_chapter(comic_id)
            latest_chapter_id = latest_chapter['id']
            last_chapter_id = last_chapters.get(comic_id)

            if last_chapter_id != latest_chapter_id:
                # 有新章节，发送推送
                message_title = latest_chapter['name']
                message_content = format_message(comic_name, latest_chapter)
                wxpusher(f'{comic_name}更新: {message_title}', message_content, CONTENT_TYPE)
                print(f"成功推送 {comic_name} 的新章节: {latest_chapter}")

                # 更新记录
                updated_chapters[comic_id] = latest_chapter_id
            else:
                print(f"{comic_name} 没有新章节。")
        except requests.exceptions.HTTPError as http_err:
            print(f"{comic_name} 的HTTP错误: {http_err}")
        except requests.exceptions.RequestException as req_err:
            print(f"{comic_name} 的请求过程中发生错误: {req_err}")
        except KeyError as key_err:
            print(f"{comic_name} 的响应数据格式错误，缺少键: {key_err}")
        except IndexError:
            print(f"{comic_name} 的章节列表为空，无法获取最新章节。")
        except Exception as err:
            print(f"{comic_name} 发生未知错误: {err}")

    # 保存更新后的章节记录
    save_last_chapters(RECORD_FILE, updated_chapters)


if __name__ == "__main__":
    main()

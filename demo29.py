import requests
import warnings

# 忽略InsecureRequestWarning警告
warnings.filterwarnings('ignore', message='Unverified HTTPS request')
headers = {
    "Host": "bg.denwq.cn",
    "sec-ch-ua": "\"Not A(Brand\";v=\"24\", \"Chromium\";v=\"110\", \"Microsoft Edge Simulate\";v=\"110\", \"Lemur\";v=\"110\"",
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "\"Android\"",
    "upgrade-insecure-requests": "1",
    "dnt": "1",
    "user-agent": "Mozilla/5.0 (Linux; Android 12; RMX2072) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Mobile Safari/537.36",
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "sec-fetch-site": "same-origin",
    "sec-fetch-mode": "navigate",
    "sec-fetch-user": "?1",
    "sec-fetch-dest": "document",
    "referer": "https://bg.denwq.cn/",
    "accept-encoding": "gzip, deflate, br",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
    "cookie": "HYBBS_HEX=E9%25252F6JO6hlCo1YzBOav8JkeO8SwHiFfQHuPSyJ67DKaTsOphi1OP27BRxPF4qyN6qtjXDdjk93TQ1VV0tee4CQseM4alMUVbdWAuG2APkcSpSaBS%252BrXK4t4PJKQsdCmqoPJFU%25252FR8NGXk9VAjBWywQPffvxgk%253D; xr_fullscreen_cookies=1; hyphp_lang=zh-CN; fresh_online_time=1745956996",
    "sec-gpc": "1"
}
def test_proxy(proxy_ip, proxy_port):
    proxy_url = f"http://{proxy_ip}:{proxy_port}"
    proxies = {
        "http": proxy_url,
        "https": proxy_url
    }
    try:
        test_url = "https://bg.denwq.cn/appshop.html"  # 简单的测试网站
        # 添加verify=False参数绕过SSL证书验证
        response = requests.get(test_url, headers=headers, proxies=proxies, timeout=5, verify=False)
        if response.status_code == 200:
            print(f"代理 {proxy_url} 可用，返回IP: {response.text}")
            return True
        return False
    except Exception as e:
        print(f"代理 {proxy_url} 测试失败: {e}")
        return False

# 从666.txt文件中读取代理列表
proxy_list = []
try:
    with open('666.txt', 'r') as file:
        for line in file:
            line = line.strip()
            if line:
                parts = line.split(':')
                if len(parts) == 2:
                    proxy_list.append({"ip": parts[0], "port": parts[1]})
except FileNotFoundError:
    print("错误: 666.txt文件不存在")
    exit(1)
except Exception as e:
    print(f"读取代理文件时出错: {e}")
    exit(1)

print(f"从文件中读取了 {len(proxy_list)} 个代理")

working_proxies = []
for proxy in proxy_list:
    if test_proxy(proxy["ip"], proxy["port"]):
        working_proxies.append(proxy)

print(f"可用代理数量: {len(working_proxies)}")

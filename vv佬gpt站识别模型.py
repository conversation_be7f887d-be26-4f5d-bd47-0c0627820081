import requests
from concurrent.futures import Thread<PERSON><PERSON>Executor, as_completed
import json
import os
from bs4 import BeautifulSoup

# 常量配置
URL = "https://share.aivvm.org/backend-api/system_hints"
COOKIE = (
    "cf_clearance=dvDceZYWGmY7H_Wdy838ODJW0480_XZIB85RykrRFHM-1736669012-1.2.1.1-1N9rMl5ZTcHj1YWiDSq5xlFXE2G_gK8o7IwseqKf0ZXMgJwwY56.ZsK_xmumn1KLH6Gr7ClSjLiAhFOC_J2Fn75yQvp.XXnyB53Jy9lWWFP3Xdd3BbDqtZ2odHyif92eHJl9NzqSoSU_DYAJZZixVUmLIEDM_bhidNi_Wm3vzPP2zcf_clNcQ9NRlkpon4lSNfWPyjPyn3vnzEdB6jl.5zpROwKAQPitQFEfYuxdAGyXJV8hhHiY.nGCBdDE70qmDWgQvwhi0Y4GVSuZJAV1eFohUjZ3lxG4o3BvMkUUvzj5X9dCq2HShJtHUUdBuWJL2gd_w8jJX1VeFiL7ik1pVg"
)
USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
    "AppleWebKit/537.36 (KHTML, like Gecko) "
    "Chrome/132.0.0.0 Safari/537.36 Edg/132.0.0.0"
)
START_TOKEN = 0
END_TOKEN = 300
MAX_WORKERS = 10  # 根据需要调整并发数

# 文件名配置
OUTPUT_FILE = "vvgpt.html"

# 用于保存有效 Token 对应链接的集合（使用集合避免重复）
valid_tokens = set()

def load_existing_tokens():
    """加载已存在的有效 Token 链接到 valid_tokens 集合中。"""
    if os.path.exists(OUTPUT_FILE):
        try:
            with open(OUTPUT_FILE, "r", encoding="utf-8") as f:
                soup = BeautifulSoup(f, "html.parser")
                ul = soup.find("ul")
                if ul:
                    for li in ul.find_all("li"):
                        a = li.find("a")
                        if a and 'href' in a.attrs:
                            valid_tokens.add(a['href'])
                else:
                    print(f"警告: {OUTPUT_FILE} 中未找到 <ul> 列表。")
        except Exception as e:
            print(f"警告: 无法解析 {OUTPUT_FILE}，错误: {e}")
    else:
        print(f"{OUTPUT_FILE} 不存在，将创建一个新的文件。")

def save_valid_tokens():
    """将 valid_tokens 集合中的链接保存到 OUTPUT_FILE 文件中，以 HTML 格式。"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>有效 Token 列表</title>
    </head>
    <body>
        <h1>有效 Token 列表</h1>
        <ul>
    """
    for token_url in sorted(valid_tokens):
        html_content += f'            <li><a href="{token_url}" target="_blank">{token_url}</a></li>\n'
    html_content += """
        </ul>
    </body>
    </html>
    """
    with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
        f.write(html_content)
    print(f"已将 {len(valid_tokens)} 个有效 Token 保存到 {OUTPUT_FILE}。")

def fetch_system_hints(session, token):
    headers = {
        "Authorization": f"Bearer {token}",
        'dnt': '1',
        "Cookie": COOKIE,
        "User-Agent": USER_AGENT,
        'oai-language': 'zh-CN',
    }
    try:
        response = session.get(URL, headers=headers, timeout=10)
        response.raise_for_status()  # 检查 HTTP 状态码

        if "推理" in response.text:
            token_url = f"https://share.aivvm.org/?token={token}&model=o1"
            print(f"有效 Token: {token_url}")
            # 将有效链接添加到全局集合中
            valid_tokens.add(token_url)

    except requests.exceptions.HTTPError as errh:
        if errh.response.status_code == 401:
            if 'Your OpenAI account has been deactivated' in errh.response.text:
                print(f'Token {token}: 该账号被 OpenAI 封禁')
            if 'Could not find user for provided access token' in errh.response.text:
                print(f'Token {token}: 该账号不存在')
        elif errh.response.status_code == 403:
            if '/cdn-cgi/challenge-platform/' in errh.response.text:
                print(f'Token {token}: 触发速率限制')
        elif errh.response.status_code == 502:
            print(f"Token {token}: 服务器宕机")
        else:
            print(f"Token {token}: HTTP 错误: {errh}")
    except requests.exceptions.ConnectionError as errc:
        print(f"Token {token}: 连接错误: {errc}")
    except requests.exceptions.Timeout as errt:
        print(f"Token {token}: 超时错误: {errt}")
    except requests.exceptions.RequestException as err:
        print(f"Token {token}: 其他请求错误: {err}")

def main():
    # 首先加载已有的有效 Token
    load_existing_tokens()

    with requests.Session() as session:
        # 在 Session 中设置全局 headers
        session.headers.update({
            "Cookie": COOKIE,
            "User-Agent": USER_AGENT,
        })

        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # 提交所有任务
            futures = {executor.submit(fetch_system_hints, session, i): i for i in range(START_TOKEN, END_TOKEN)}

            # 处理完成的任务
            for future in as_completed(futures):
                token = futures[future]
                try:
                    future.result()
                except Exception as exc:
                    print(f"Token {token}: 生成器异常: {exc}")

    # 所有请求处理完成后，将 valid_tokens 写入 HTML 文件
    save_valid_tokens()

if __name__ == "__main__":
    main()

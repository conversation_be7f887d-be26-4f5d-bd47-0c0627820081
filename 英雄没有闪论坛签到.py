import requests
import sys
import message_push  # 导入消息推送模块

BASE_URL = "https://hdapi.37.com/"


def get_headers():
    headers = {
        "Host": "hdapi.37.com",
        "pragma": "no-cache",
        "cache-control": "no-cache",
        "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Android WebView";v="128"',
        "sec-ch-ua-mobile": "?1",
        "user-agent": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) "
                      "AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 "
                      "Chrome/128.0.6613.40 Mobile Safari/537.36",
        "sec-ch-ua-platform": '"Android"',
        "accept": "*/*",
        "x-requested-with": "com.sqw.yxmys.yxmys_tap1",
        "sec-fetch-site": "same-site",
        "sec-fetch-mode": "no-cors",
        "sec-fetch-dest": "script",
        "referer": "https://activity.37.com/",
        "accept-encoding": "gzip, deflate, br, zstd",
        "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "cookie": (
            "ispass_37wan_com=e690c006%7C1%7C7b014cf425a52d530bbff9b1dbe6d6d3%7C1%7C2; "
            "passport_37wan_com=3562275630%7C13927009231%7C1743328108000%7Cdc7a9b93ef65097fa40036d5249f4ba0%7C2; "
        ),
    }
    return headers


def get_status():
    url = BASE_URL
    params = {
        "c": "api",
        "a": "status",
        "alias_info": "yxmyswxqd20250214",
        "f": "d202502/yxmyswxqd20250214",
        "game_id": "1051"
    }
    headers = get_headers()
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        error_msg = f"请求状态接口出现错误：{e}"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 状态请求", error_msg, content_type=1)
        sys.exit(1)

    try:
        data = response.json()
    except ValueError:
        error_msg = "状态接口响应不是有效的JSON格式"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 状态请求", error_msg, content_type=1)
        sys.exit(1)

    if not data.get('result') == 1:
        error_msg = f"状态接口调用失败，返回信息：{data.get('msg', '未知错误')}"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 状态请求", error_msg, content_type=1)
        sys.exit(1)

    return data


def sign_in():
    url = BASE_URL
    params = {
        "c": "api",
        "a": "sign",
        "alias_info": "yxmyswxqd20250214",
        "f": "d202502/yxmyswxqd20250214",
        "game_id": "1051",
        "app_pst": "NGUwYTdUQy82K0JScFFHM3dIUkNNMXpRUFdhTGVvMkpzUlJjT1liNWVxVXRVVy9pNDlub0dseXA5ZldmNE9IRmdYNkpIa1NuQjhraGlxUnRRbU9nQ2MxR3JoUXRSNmNsYUFjVExtV25KWnpjc3IvekVXNFJOcjFJY3BkK09sSEs3S24zd3pvRTlFcUdjTnRjWWNVb1pYZ3dFWHNBNEFBOHZvVnRWNWgyT1ExTDZHNkxNWHEveGl1UmhoeUJmL2Y0ZW5xZ29MRjlSWkU1YU5mMjZyZzJYaDAvWHBIaHUrUno1dFgrbThj",
    }
    headers = get_headers()
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        error_msg = f"签到请求出现错误：{e}"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 签到请求", error_msg, content_type=1)
        sys.exit(1)
    try:
        data = response.json()
    except ValueError:
        error_msg = "签到响应不是有效的JSON格式"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 签到请求", error_msg, content_type=1)
        sys.exit(1)

    if data.get('result') == 1:
        print("签到成功")
    elif data.get('result') == -10003:
        print("已签到，无需重复签到")
    else:
        error_msg = f"签到失败，返回信息：{data.get('msg', '未知错误')}"
        print(error_msg)
        message_push.wxpusher("签到程序错误 - 签到失败", error_msg, content_type=1)
        sys.exit(1)
    return data


def claim_gift(class_key, gift_name):
    url = BASE_URL
    params = {
        "c": "c_get_gift",
        "alias_info": "yxmyswxqd20250214",
        "feature_key": "get_gift_1",
        "f": "auto",
        "game_id": "1051",
        "class_key": class_key
    }
    headers = get_headers()
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        error_msg = f"领取礼包请求出现错误：{e}"
        print(error_msg)
        message_push.wxpusher(f"签到程序错误 - {gift_name}领取请求", error_msg, content_type=1)
        return None

    try:
        data = response.json()
    except ValueError:
        error_msg = "领取礼包响应不是有效的JSON格式"
        print(error_msg)
        message_push.wxpusher(f"签到程序错误 - {gift_name}领取请求", error_msg, content_type=1)
        return None

    if data.get('result') == 1:
        success_msg = f"{gift_name} 领取成功"
        print(success_msg)
        message_push.wxpusher("签到程序通知", success_msg, content_type=1)
    elif data.get('result') == -90043:
        # -90043 表示重复领取或条件不足
        error_msg = f"{gift_name} 领取失败：{data.get('msg', '未知错误')}"
        print(error_msg)
    else:
        error_msg = f"{gift_name} 领取失败，返回信息：{data.get('msg', '未知错误')}"
        print(error_msg)
        message_push.wxpusher(f"签到程序错误 - {gift_name}领取失败", error_msg, content_type=1)
    return data


def main():
    # 查询签到状态
    status_data = get_status()

    # 提取数据
    list_data = status_data.get('list', {})
    gift_status = list_data.get('gift_status', [])
    rule_arr = list_data.get('rule_arr', [])
    sign_total = list_data.get('sign_total', 0)
    gift_need_days = list_data.get('gift_need_days', [])
    check_sign = list_data.get('check_sign', False)

    print(f"当前签到天数：{sign_total}")

    # 检查是否已签到
    if not check_sign:
        print("今日尚未签到，开始签到...")
        sign_in()
        # 签到后再次查询状态
        status_data = get_status()
        list_data = status_data.get('list', {})
        gift_status = list_data.get('gift_status', [])
        rule_arr = list_data.get('rule_arr', [])
        sign_total = list_data.get('sign_total', 0)
        gift_need_days = list_data.get('gift_need_days', [])
        check_sign = list_data.get('check_sign', False)
    else:
        print("今日已签到")

    # 处理礼包领取
    class_keys = {
        0: 'otherlimit_common_1',  # 每日礼包
        1: 'otherlimit_common_2',  # 5天礼包
        2: 'otherlimit_common_3',  # 30天礼包
    }

    gift_names = {
        0: '每日登录礼包',
        1: '累计5天礼包',
        2: '累计30天礼包',
    }

    for i in range(len(gift_status)):
        status = gift_status[i]
        need_days = gift_need_days[i]
        required_days = rule_arr[i] if i < len(rule_arr) else None
        gift_name = gift_names.get(i, f"第{i}个礼包")
        class_key = class_keys.get(i)
        if need_days == 0 and status != 0:
            print(f"{gift_name} 可领取，开始领取...")
            claim_gift(class_key, gift_name)
        elif status == 0:
            print(f"{gift_name} 已领取")
        elif need_days > 0:
            print(f"{gift_name} 还需签到 {need_days} 天后可领取")
        else:
            print(f"{gift_name} 状态未知")

    # 再次查询状态以验证领取结果
    status_data = get_status()
    list_data = status_data.get('list', {})
    gift_status = list_data.get('gift_status', [])
    sign_total = list_data.get('sign_total', 0)

    print("最新礼包领取状态：")
    for i in range(len(gift_status)):
        status = gift_status[i]
        gift_name = gift_names.get(i, f"第{i}个礼包")
        if status == 0:
            print(f"{gift_name} 已领取")
        else:
            print(f"{gift_name} 未领取")

    print(f"当前签到天数：{sign_total}")


if __name__ == '__main__':
    main()
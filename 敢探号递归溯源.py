import requests
import json
import os

# 文件路径
JSON_FILE = 'shop.json'


# 读取现有的JSON文件，如果不存在则创建一个空字典
def load_shop_data():
    if os.path.exists(JSON_FILE):
        with open(JSON_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        return {}


# 保存数据到JSON文件
def save_shop_data(data):
    with open(JSON_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)


# 获取商店基本信息
def get_shop_info(shop_id):
    url = f'https://www.91haoka.cn/api/plan-market/order-page/web/mini-store/info?shop_id={shop_id}'
    headers = {
        'Referer': f'https://www.91haoka.cn/webapp/weixiaodian/index.html?shop_id={shop_id}'
    }
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        data = response.json()
        if data['msg']['code'] == 0:
            return {
                "name": data['data']['name'],
                "kf_phone": data['data']['kf_phone'],
                "kf_qrcode": data['data']['kf_qrcode']
            }
        else:
            print(f"无法获取shop_id {shop_id} 的info信息。")
            return {
                "name": data['msg']['info'],
                "kf_phone": "",
                "kf_qrcode": ""
            }
    except requests.RequestException as e:
        print(f"请求shop_id {shop_id} 时发生错误: {e}")
        return ""


# 获取商店产品信息
def get_shop_products(shop_id):
    url = 'https://www.91haoka.cn/api/plan-market/web/mini-store/pages'
    params = {
        'shop_id': shop_id
    }
    headers = {
        'Referer': f'https://www.91haoka.cn/webapp/weixiaodian/index.html?shop_id={shop_id}',
    }
    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        data = response.json()
        if 'data' in data:
            return data['data']
        else:
            print(f"无法获取shop_id {shop_id} 的产品数据。")
            return []
    except requests.RequestException as e:
        print(f"请求产品数据时发生错误: {e}")
        return []


# 处理单个shop_id
def process_shop(shop_id, shop_data):
    if str(shop_id) in shop_data:
        print(f"shop_id {shop_id} 已存在，跳过。")
        return
    print(f"处理shop_id {shop_id}...")
    info = get_shop_info(shop_id)
    if not info:
        print(f"shop_id {shop_id} 的info为空，跳过。")
        return

    products = get_shop_products(shop_id)
    myOwn = []
    parent_products = {}
    parent_shop_ids = set()

    # 统计page_shop_id != product_shop_id的数量
    quality_level = 0

    for product in products:
        page_shop_id = product.get('page_shop_id')
        product_shop_id = product.get('product_shop_id')
        title = product.get('title', '')

        if page_shop_id == product_shop_id:
            myOwn.append(title)
        else:
            quality_level += 1
            parent_shop_ids.add(product_shop_id)
            if str(product_shop_id) not in parent_products:
                parent_products[str(product_shop_id)] = []
            parent_products[str(product_shop_id)].append(title)

    # 构建商店条目
    shop_entry = {
        "info": info,
        "link": f"https://www.91haoka.cn/webapp/weixiaodian/index.html?shop_id={shop_id}",
        "qualityLevel": quality_level,
        "parent": {},
        "products": {
            "myOwn": myOwn,
            "parent": parent_products
        }
    }

    # 获取父级商店的名称
    for parent_id in parent_shop_ids:
        parent_info = get_shop_info(parent_id)
        if parent_info:
            shop_entry["parent"][str(parent_id)] = parent_info
        else:
            shop_entry["parent"][str(parent_id)] = "未知小店"

    # 添加到shop_data
    shop_data[str(shop_id)] = shop_entry
    print(f"shop_id {shop_id} 已添加。")

    # 递归处理父级商店
    for parent_id in parent_shop_ids:
        process_shop(parent_id, shop_data)


# 主函数
def main():
    # 你可以在这里定义初始的shop_id列表
    initial_shop_ids = [416205]  # 示例

    shop_data = load_shop_data()

    for shop_id in initial_shop_ids:
        process_shop(shop_id, shop_data)

    save_shop_data(shop_data)
    print("所有商店信息已更新。")


if __name__ == "__main__":
    main()

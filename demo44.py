import requests


headers = {
    'Host': 'wap.hn.10086.cn',
    'sec-ch-ua-platform': '"Windows"',
    'X-Requested-With': 'XMLHttpRequest',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Accept': 'application/json, text/javascript, */*; q=0.01',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'sec-ch-ua-mobile': '?0',
    'Origin': 'https://wap.hn.10086.cn',
    'Sec-Fetch-Site': 'same-origin',
    'Sec-Fetch-Mode': 'cors',
    'Sec-<PERSON><PERSON>-Dest': 'empty',
    'Referer': 'https://wap.hn.10086.cn/shop/myOrder/querySimOrderList',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
}

data = {
    'psptId': '01EB0287D8AC12782B6D3FC30FDC12EF91BF95084B5DCC5CDA041DB1BD02082EDDFF6CE2BD77000D',
}

response = requests.post(
    'https://wap.hn.10086.cn/shop/myOrder/querySimOrderData',
    headers=headers,
    data=data,
)


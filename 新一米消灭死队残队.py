# 2024.3.27 1.43am
# 2024.5.24 1.33am

from time import sleep
from datetime import datetime
import yimi
# 定义你的日期时间字符串
date_time_str = "2024-05-23 00:00:00"
# 定义日期时间的格式
date_time_format = "%Y-%m-%d %H:%M:%S"
# 将字符串转换为datetime对象
date_time_obj = datetime.strptime(date_time_str, date_time_format)
# 一个人只能为人助力3个未组队完成的队
yimi.init()
ck = yimi.need_help()
ck1 = yimi.helper()
# 一个人只能为人助力3个未组队完成的队

temp = ''
activityId = '664eafd0514a7c2abbaddd8b'  # 活动代号，大概率随活动变
now_team = []
member = 0

for k in ck:
    dict = yimi.getLeaderTeamRecord(k)
    if dict:
        for i in dict:
            current_time_obj = datetime.strptime(i['createTime'], date_time_format)
            if date_time_obj < current_time_obj:
                if 'member' in i:
                    if len(i['member']) != 3:
                        print(f'当前为{k["name"]}的队伍{i["id"]},队伍人数为{len(i["member"])}')
                        member = len(i["member"])
                        for j in i['member']:
                            now_team.append(j['userId'])
                        print('开始补足剩余人数…………')
                        for m in ck1:
                            if m['userId'] not in now_team:
                                print(f'助力人:{m["name"]}')
                                is_success = yimi.join_team(m['authorization'], i['id'], m['userId'])
                                sleep(1)
                                if is_success == 1:
                                    member += 1
                                    print(f'当前助力人数为{member}')
                                if member == 3:
                                    now_team.clear()
                                    member = 0
                                    print('组队完成…………')
                                    break
                else:
                    print(f'当前为{k["name"]}的队伍{i["id"]},队伍人数为0')
                    print('开始补足剩余人数…………')
                    for m in ck1:
                        is_success = yimi.join_team(m['authorization'], i['id'], m['userId'])
                        sleep(1)
                        if is_success == 1:
                            member += 1
                            print(f'当前助力人数为{member}')
                        if member == 3:
                            now_team.clear()
                            member = 0
                            print('组队完成…………')
                            break

import requests
import fofa_sign
headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'Authorization': 'eyJhbGciOiJIUzUxMiIsImtpZCI6Ik5XWTVZakF4TVRkalltSTJNRFZsWXpRM05EWXdaakF3TURVMlkyWTNZemd3TUdRd1pUTmpZUT09IiwidHlwIjoiSldUIn0.eyJpZCI6NDk4NjYyLCJtaWQiOjEwMDI4ODQ2MSwidXNlcm5hbWUiOiJ6enp5ZHNmZHMiLCJleHAiOjE3MTY1NjM4NTN9.fiEvCdZBP43N3oDTEu1uy5PgYdGubMLK9hA4i1r8Cnc4eM8YfbytoQlBgGg_-dSyy2v0Oi_bESNZVHj09B6Dlw',
    'Connection': 'keep-alive',
    # 'Cookie': '__fcd=BRHf7wvdnXAN4ZHxDZnuk8LW; acw_tc=276aedf117165597697283252e6365924ece493adf00617a84e287c018d573',
    'Origin': 'https://fofa.info',
    'Referer': 'https://fofa.info/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36 Edg/125.0.0.0',
    'dnt': '1',
    'sec-ch-ua': '"Microsoft Edge";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-gpc': '1',
}

params = {
    'qbase64': 'KHRpdGxlPSJMaWJyZUNoYXQiKSAmJiBhZnRlcj0iMjAyNC0wNS0yMyIgJiYgYmVmb3JlPSIyMDI0LTA1LTI0Ig==',
    'full': 'false',
    'page': '1',
    'size': '50',
    'ts': '1716561304705',
    'sign': 'tUdU0LPW1F597U1DCvK/1/GnBnfGX/YiWO4PGhWCifoEEf91SmcM/lkruiE++q12IvyO8XvWYouh9DTkiK35dPHWQ0WdEeifcF96lhkGCwaoakVC+POhUitf6Dy4G1OfNoMgrfQwxLKUssWEkWuyCIWuSNMv7Y1GBnNbFImnkfqCznYXz4MOH0ANr1cV7OBEDqfvoed9xJWEYykpehBKbQe1Yfk4uimGB8Qb2zshydxtTjS+fHZ6azO6H4en9FjcUfcafzU5plJZKR/1dy5TQzs420MXuidq6XQXLgNoYiBThv69lG/Lk91bbddhPetrkhss7oTZrJKgfRBNAXO8yQ==',
    'app_id': '9e9fb94330d97833acfbc041ee1a76793f1bc691',
}

response = requests.get(fofa_sign.getUrlTest('KHRpdGxlPSJMaWJyZUNoYXQiKSAmJiBhZnRlcj0iMjAyNC0wNS0yMiIgJiYgYmVmb3JlPSIyMDI0LTA1LTI0Ig==',2), headers=headers)

print(response.json())
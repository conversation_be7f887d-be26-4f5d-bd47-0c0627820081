import requests
from time import sleep

X_App_Token = "54daf1eebbabd8b2cb59abc0afadcc11kJmNkFmNiRWOklTZjlTY3YDZ0EDZlJGO2kjYmRDM4kDN3IENxYDRxY0QElzMGhjQEF0M0M0M5IENBJ0N0QTQzAyO1EjLG9VMx8lM3AjMY1kUgsjM3AjMY1kUgsTZtxWYlJHI7UWbsFWZyByOGNkOzQkO0EkOGVkOEJjODJEI7AyOgsjN0ImMiN2MwQ2Y0QTYhJGZ0x647ac089"
Token = "dbcef620dWywSQBfn4jYW9yUh13qMgaTDnLdUzssHo_WvDMRWM-5e6f0414aLcH-JqOlWnLcWxVTri6rsM6mEV_ZRqcatSjTI8HNd8EulM1EBY_LuBM2___2vL1hb16kdJXlCg1KiwRPT9X6S4Pox-xRuCOyzNNwUazdNg24D1Jq0LZOJVivaulVrbuM6WyU8fUkSuzmN6Is2Ih6rOQ0U4LaWy3y362ApuDZm4yg3Uu_HDdF2xCZ-FHU2tpxPi7UNOQ9McCRi-9OvVFpg"

X_App_Device = "kJmNkFmNiRWOklTZjlTY3YDZ0EDZlJGO2kjYmRDM4kDN3IENxYDRxY0QElzMGhjQEF0M0M0M5IENBJ0N0QTQzAyO1EjLG9VMx8lM3AjMY1kUgsjM3AjMY1kUgsTZtxWYlJHI7UWbsFWZyByOGVjO5QjO5QjOCZkOCNjO2QEI7AyOgsjN0ImMiN2MwQ2Y0QTYhJGZ"
n = 2
fa_bu = 5 * n
zan = 10 * n
hui_fu = 8 * n
shou_cang = 4 * n
guan_zhu = 4 * n
zhuan_fa = 4 * n
get_jinyan = 0
wenzhang_ids = set()
uid_ids = set()
page = 1
daily_zan_count = 0
daily_zhuan_fa_count = 0
daily_fa_bu_count = 0
daily_hui_fu_count = 0
daily_shou_cang_count = 0
daily_guan_zhu_count = 0


# 点赞
def daily_zan(id):
    global X_App_Token
    global Token
    global X_App_Device
    global daily_zan_count
    url = f"https://api.coolapk.com/v6/feed/like?id={id}&detail=0"
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": X_App_Token,
        "X-App-Device": X_App_Device,
        "Cookie": f"uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}",
    }

    response = requests.post(url, headers=headers)

    if 'data' in response.json():
        print('点赞成功……')
        daily_zan_count += 1
    if 'error' in response.json():
        print('之前已点赞过…………')


# 删除转发
def daily_dedlete_zhuan_fa(id):
    global X_App_Token
    global Token
    global X_App_Device
    url = f'https://api.coolapk.com/v6/feed/deleteFeed?id={id}&notNotify=0'

    headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Id': 'com.coolapk.market',
        'X-App-Token': X_App_Token,
        'X-App-Device': X_App_Device,
        'Host': 'api.coolapk.com',
        'Accept-Encoding': 'gzip',
        'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}',
    }

    response = requests.post(url, headers=headers)
    if 'data' in response.json():
        print(response.json()['data'])
    else:
        print(response.json()['message'])


# 转发
def daily_zhuan_fa(id):
    global X_App_Token
    global Token
    global X_App_Device
    global daily_zhuan_fa_count
    url = 'https://api.coolapk.com/v6/feed/createFeed'

    headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Id': 'com.coolapk.market',
        'X-App-Token': X_App_Token,
        'X-App-Device': X_App_Device,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept-Encoding': 'gzip',
        'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}'
    }

    data = {
        'message': '看看',
        'type': 'feed',
        'forwardid': id
    }

    response = requests.post(url, headers=headers, data=data)
    if 'data' in response.json():
        print('转发成功……')
        daily_zhuan_fa_count += 1
        print('正在删除动态……')
        daily_dedlete_zhuan_fa(response.json()['data']['id'])
    else:
        print(response.json()['message'])


# 回复
def daily_hui_fu(id):
    global X_App_Token
    global Token
    global X_App_Device
    global daily_hui_fu_count

    url = f'https://api.coolapk.com/v6/feed/reply?id={id}&type=feed'

    headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Id': 'com.coolapk.market',
        'X-App-Token': X_App_Token,
        'X-App-Device': X_App_Device,
        'Content-Type': 'multipart/form-data; boundary=ef2ee895-af1f-4d8e-9249-bf3c1346dd81',
        'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}',
    }

    message = '看看'
    data = f'--ef2ee895-af1f-4d8e-9249-bf3c1346dd81\nContent-Disposition: form-data; name="message"\nContent-Length: {len(message.encode("utf-8"))}\n\n{message}\n--ef2ee895-af1f-4d8e-9249-bf3c1346dd81--'.encode(
        'utf-8')

    response = requests.post(url, headers=headers, data=data)

    if 'data' in response.json():
        print('回复成功……')
        daily_hui_fu_count += 1
    else:
        print('回复失败……')


# 收藏
def daily_shou_cang(id):
    global X_App_Token
    global Token
    global X_App_Device
    global daily_shou_cang_count
    url = f'https://api.coolapk.com/v6/collection/list?uid=&id={id}&type=feed&showDefault=1&page=1'

    headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Id': 'com.coolapk.market',
        'X-App-Token': X_App_Token,
        'X-App-Device': X_App_Device,
        'Accept-Encoding': 'gzip',
        'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}',
    }

    response = requests.get(url, headers=headers)
    if 'data' in response.json():
        print('收藏成功……')
        daily_shou_cang_count += 1
    else:
        print('收藏失败……')


# 关注
def daily_guan_zhu(uid):
    global X_App_Token
    global Token
    global X_App_Device
    global daily_guan_zhu_count
    url = f"https://api.coolapk.com/v6/user/follow?uid={uid}"

    headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
        'X-Requested-With': 'XMLHttpRequest',
        'X-App-Id': 'com.coolapk.market',
        'X-App-Token': X_App_Token,
        'X-App-Device': X_App_Device,
        'Accept-Encoding': 'gzip',
        'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}'
    }

    response = requests.post(url, headers=headers)

    if 'data' in response.json():
        print('已关注……')
        daily_guan_zhu_count += 1
    else:
        print('关注失败……')


# 获取板块文章id
while 1:
    url = f"https://api.coolapk.com/v6/page/dataList?url=%23%2Ffeed%2FmultiTagFeedList%3FlistType%3Ddateline_desc%26hiddenTagRelation%3D1%26tag%3D%25E8%2596%2585%25E7%25BE%258A%25E6%25AF%259B%25E5%25B0%258F%25E5%2588%2586%25E9%2598%259F&title=%E6%9C%80%E6%96%B0%E5%8F%91%E5%B8%83&subTitle=&page={page}"
    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": X_App_Token,
        "Accept-Encoding": "gzip",
    }

    response = requests.get(url, headers=headers)
    # 可以使用response.text获取响应内容
    for i in response.json()['data']:
        wenzhang_ids.add(i['id'])
        uid_ids.add(i['uid'])
    if len(wenzhang_ids) <= 20 or len(uid_ids) <= 20:
        page += 1
    else:
        wenzhang_ids = list(wenzhang_ids)
        uid_ids = list(uid_ids)
        print(f'已获取足够数量文章:{len(wenzhang_ids)}')
        print(f'已获取足够数量用户:{len(uid_ids)}')
        break

# 输出经验值
url = "https://api.coolapk.com/v6/user/profile?uid=3297413"

headers = {
    'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 12; RMX2072 Build/RKQ1.211103.002) (#Build; realme; RMX2072; RMX2072_11_F.15; 12) +CoolMarket/12.0.2-2201281-universal',
    'X-Requested-With': 'XMLHttpRequest',
    'X-App-Id': 'com.coolapk.market',
    'X-App-Token': X_App_Token,
    'X-App-Device': X_App_Device,
    'Accept-Encoding': 'gzip',
    'Cookie': f'uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token={Token}'
}

response = requests.post(url, headers=headers)
print(f"当前经验值：{response.json()['data']['experience']}")

# 主函数
print('正在执行点赞任务……')
for i in wenzhang_ids:
    if daily_zan_count < zan:
        daily_zan(i)
        print(f'已点赞{daily_zan_count}人')
        sleep(1)
    else:
        print('已达到点赞上限')
        break
print('正在执行转发任务……')
for i in wenzhang_ids:
    if daily_zhuan_fa_count < zhuan_fa:
        daily_zhuan_fa(i)
        print(f'已转发{daily_zhuan_fa_count}人')
        sleep(1)
    else:
        print('已达到转发上限')
        break
print('正在执行回复任务……')
for i in wenzhang_ids:
    if daily_hui_fu_count < hui_fu:
        daily_hui_fu(i)
        print(f'已回复{daily_hui_fu_count}人')
        sleep(1)
    else:
        print('已达到回复上限')
        break
print('正在执行收藏任务……')
for i in wenzhang_ids:
    if daily_shou_cang_count < shou_cang:
        daily_shou_cang(i)
        print(f'已收藏{daily_shou_cang_count}人')
        sleep(1)
    else:
        print('已达到收藏上限')
        break
for i in uid_ids:
    daily_guan_zhu(i)
    if daily_guan_zhu_count < guan_zhu:
        daily_guan_zhu(i)
        print(f'已关注{daily_guan_zhu_count}人')
        sleep(1)
    else:
        print('已达到关注上限')
        break

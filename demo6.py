import re
from datetime import datetime
import pytz
import requests


def print_red(text):
    red_text = "\033[91m{}\033[0m".format(text)
    print(red_text)


def print_green(text):
    green_text = "\033[92m{}\033[0m".format(text)
    print(green_text)


def report_content(id):
    url = "https://m.coolapk.com/mp/do"

    params = {
        "c": "feed",
        "m": "report"
    }

    headers = {
        "Host": "m.coolapk.com",
        "Connection": "keep-alive",
        "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Android WebView";v="128"',
        "Accept": "*/*",
        "sec-ch-ua-platform": '"Android"',
        "X-Requested-With": "XMLHttpRequest",
        "sec-ch-ua-mobile": "?1",
        "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22127RK46C Build/TKQ1.220905.001; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/128.0.6613.40 Mobile Safari/537.36 (#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) +CoolMarket/14.3.0-2407251-universal",
        "Content-Type": "multipart/form-data; boundary=----WebKitFormBoundarySajdrzu0vMDRub4F",
        "Origin": "https://m.coolapk.com",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": f"https://m.coolapk.com/mp/do?c=feed&m=report&type=feed_reply&id={id}",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
        "Cookie": "uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token=a5a39c80OXK-R88izWXgPgBw1BeHQgQNXg1Q9bkFIFvuIKTNur9UBSObdaDuucMLyFODuVFla6JZBEXzk3NMjDMe6Q5vGOCheP0pPJEpWdYz4XC3lHHyn0NbRV30dzrBK0AGYEbCB_iRSyiXTkAnE6aSN1QKPWBCOD43gKFpDRPAMhiVPxVOhH5BPr1pkg7eRZISN0eu3rOyPsX9k_lRdwxRAzeXpw; DID=DUivDWufDxT-Vt4NZryEWk81IYLWhXKmLQed; SESSID=f26049155f7bc6d03d22f7bd375b35a20f2e0cf7; ddid=9e8e0bf4-5774-4326-a1de-f520a3a81923"
    }

    data = f'''------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="requestHash"

af599110sjnjih
------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="submit"

1
------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="report_reason"

流量卡推广
------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="custom_report_reason"


------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="id"

{id}
------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="type"

feed_reply
------WebKitFormBoundarySajdrzu0vMDRub4F
Content-Disposition: form-data; name="pic"


------WebKitFormBoundarySajdrzu0vMDRub4F--
'''

    try:
        response = requests.post(url, params=params, headers=headers, data=data.encode('utf-8'))
        response.raise_for_status()
        print_green(response.json()['message'])
    except requests.RequestException as e:
        print(f"举报失败，ID: {id}")
        print("错误:", str(e))


def is_all_english(text):
    return text.isalpha() and text.isascii()


def get_reply_list(feed_id, page):
    url = f"https://api2.coolapk.com/v6/feed/replyList"

    params = {
        "id": feed_id,
        "listType": "lastupdate_desc",
        "page": page,
        "discussMode": 1,
        "feedType": "feed",
        "blockStatus": 0,
        "fromFeedAuthor": 0
    }

    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) +CoolMarket/14.3.0-2407251-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-Sdk-Int": "33",
        "X-Sdk-Locale": "zh-CN",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": "v3JDJ5JDEwJE5qWmxNVGcyTnpRdk5qaGxOekZtTy4yUkNwcG16QUVZWDdvaU8vbU1scGRvNGNlMnMuVVd1",
        "X-App-Version": "14.3.0",
        "X-App-Code": "2407251",
        "X-Api-Version": "14",
        "X-App-Device": "jFzN0IzNyEWN2QGNwgTZzAyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
        "X-Dark-Mode": "0",
        "X-App-Channel": "yyb",
        "X-App-Mode": "universal",
        "X-App-Supported": "2407251",
        "Cookie": "uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token=a5a39c80OXK-R88izWXgPgBw1BeHQgQNXg1Q9bkFIFvuIKTNur9UBSObdaDuucMLyFODuVFla6JZBEXzk3NMjDMe6Q5vGOCheP0pPJEpWdYz4XC3lHHyn0NbRV30dzrBK0AGYEbCB_iRSyiXTkAnE6aSN1QKPWBCOD43gKFpDRPAMhiVPxVOhH5BPr1pkg7eRZISN0eu3rOyPsX9k_lRdwxRAzeXpw; ddid=c44cd56e-c58f-4f80-aeeb-c043d030172a",
        "Host": "api2.coolapk.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip",
    }

    try:
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 如果响应状态不是200，将引发HTTPError异常
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        return None


def fetch_all_replies(feed_id):
    page = 1
    # print(f"评论开始")
    while True:
        result = get_reply_list(feed_id, page)

        if not result or 'data' not in result:
            print("错误响应")
            break

        if not result['data']:
            # print("评论结束")
            break

        for reply in result['data']:
            if 'id' in reply:
                # print(format_timestamp(reply['dateline']))
                # print('回复id -->', reply['id'])
                # print('用户id -->', reply['userInfo']['uid'])
                # print('用户等级 -->', reply['userInfo']['level'])
                # print('用户昵称 -->', reply['username'])
                # if reply['message']:
                #    print('回复内容 -->', jin_hua(reply['message']))
                if is_all_english(reply['username']) and reply['userInfo']['level'] == 0:
                    print_red('存在疑似目标')
                    report_content(reply['id'])
                # print('\n')

        page += 1


def format_timestamp(timestamp, timezone_str="Asia/Shanghai"):
    timezone = pytz.timezone(timezone_str)
    dt_object = datetime.fromtimestamp(timestamp, tz=timezone)
    return dt_object.strftime("%Y-%m-%d %H:%M:%S")


def jin_hua(title):
    # 移除所有 <a> 标签及其内容
    clean = re.sub(r'<a[^>]*>.*?</a>', '', title)

    # 移除其他HTML标签（如果有的话）
    clean = re.sub(r'<[^>]+>', '', clean)

    # 移除换行符
    clean = clean.replace('\n', '')

    # 移除多余的空白字符
    clean = re.sub(r'\s+', ' ', clean).strip()

    return clean


for page in range(1, 11):
    print(f"第{page}页")
    url = "https://api.coolapk.com/v6/page/dataList"

    params = {
        "url": "/page?url=/topic/tagFeedList?cacheExpires=60&type=feed&id=15957&title=我的流量套餐&sortField=dateline_desc",
        "title": "最新",
        "page": f"{page}"
    }

    headers = {
        "User-Agent": "Dalvik/2.1.0 (Linux; U; Android 13; 22127RK46C Build/TKQ1.220905.001) (#Build; Redmi; 22127RK46C; TKQ1.220905.001 test-keys; 13) +CoolMarket/14.3.0-2407251-universal",
        "X-Requested-With": "XMLHttpRequest",
        "X-Sdk-Int": "33",
        "X-Sdk-Locale": "zh-CN",
        "X-App-Id": "com.coolapk.market",
        "X-App-Token": "v3JDJ5JDEwJE5qWmxNVGRpTmpJdk1USTJOR1prTi5Ed2tjS3dZU0Z1YWxtT0RCZXpTU0hQSkdwWFBkMFVH",
        "X-App-Version": "14.3.0",
        "X-App-Code": "2407251",
        "X-Api-Version": "14",
        "X-App-Device": "jFzN0IzNyEWN2QGNwgTZzAyOzlXZr1CdzVGdgEDMw4SNwkDMyIjLxE1SUByODZDNLJ1NyEjMyAyOp1GZlJFI7kWbvFWaYByOgsDI7AyOkVWUM12SYh2VMlVSxgzaXVUeyplT0QnVtQFeEZWdXRkdpVFR",
        "X-Dark-Mode": "0",
        "X-App-Channel": "yyb",
        "X-App-Mode": "universal",
        "X-App-Supported": "2407251",
        "Cookie": "uid=3297413; username=%E8%82%86%E6%84%8F%E5%B8%8C; token=a5a39c80OXK-R88izWXgPgBw1BeHQgQNXg1Q9bkFIFvuIKTNur9UBSObdaDuucMLyFODuVFla6JZBEXzk3NMjDMe6Q5vGOCheP0pPJEpWdYz4XC3lHHyn0NbRV30dzrBK0AGYEbCB_iRSyiXTkAnE6aSN1QKPWBCOD43gKFpDRPAMhiVPxVOhH5BPr1pkg7eRZISN0eu3rOyPsX9k_lRdwxRAzeXpw; ddid=09185697-5dec-49d7-a699-01ebe5f3d1c2",
        "Host": "api.coolapk.com",
        "Connection": "Keep-Alive",
        "Accept-Encoding": "gzip"
    }

    response = requests.get(url, params=params, headers=headers)

    # 打印响应状态码
    print(f"Status Code: {response.status_code}")

    response = response.json()["data"]
    print(len(response))

    for i in response:
        if 'id' in i:
            print(format_timestamp(i['dateline']))
            # print('帖子id -->', i['id'])
            # print('用户昵称 -->', i['username'])
            if i['message_title']:
                print('标题 -->', jin_hua(i['message_title']))
            if i['message']:
                print('内容 -->', jin_hua(i['message']))
            fetch_all_replies(i['id'])
            print('\n')

a = ["612AB059DEDF1AE49910BC575AEDF97B",
     "4F12573F08ACBBEE0C87B879A1136C29",
     "C5340B6B972F2A6519E2AA5C8D1CA66E",
     "A1577946DE409D3AC4B4FDCFF4681625",
     "0A195F8492FD94D366CA8C02F5405D3C",
     "4E4B5F141AADE9638771B6226AD426D0",
     "9F5B8C584906AFA3B613D7E6F95620D0",
     "7B5E686A0CAF1C144620A3FFE04AFF1E",
     "D082916926793D99220819F05914C94F",
     "F9E7DBB640BB2094C6A7755066DCE5CA",
     "DA71409388CE531378225A6BBF96CDFE",
     "30D17316E31067004ED239DE9F121B0D",
     "8CC487234D54778E2F1C738C79B397BA",
     "E0087463F85940305DCA705308208EBB",
     "8C7A8DEAB8A6C8AC98EEE364EBB10F7A",
     "C7C200C1B37E1B01E5880FFBEA58A1E5",
     "4195F4CCB11A0EF8C1DD209E1CFCB160",
     "003430DCA0831B813D377F7609CF6F71",
     "A07B0E92FA199CB1EF432D315FFC5F62",
     "56E84F7F1ED88B70DFF8C4B2097BCDCF",
     "A6D7A29DB6C46B7D3F7CD6EC3AE110C6",
     "19DB0FE23AA9DC4864A9375CB7526362",
     "500B9537712BD828EFC8C4D036B9752A",
     "35161A49032726A1A48E435DD2874114",
     "44F823E462C9A080ED1B5141D25C64F5",
     "773D63918C47D32F33DDB2FB78C5820A"]
b = [9, 10, 19]
leng = len(a) - 1
for i in range(leng, -1, -1):
    if (i + 1) in b:
        a.pop(i)
for i in a:
    print(f"\"{i}\",")

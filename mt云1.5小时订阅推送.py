import requests
from random import randint


def wxpusherts(msg):
    url = 'http://wxpusher.zjiecode.com/api/send/message'
    data = {
        "appToken": "AT_Xkff9fQIsHWBA6jdN7WbsPu5765T7rTp",
        "content": msg,
        "summary": 'MT云间隔1.5小时推送',
        "contentType": 1,
        "topicIds": [],
        "uids": ["UID_gZcW49yOEvMfc5ygfJXC6Vjung0j"]
    }
    headers = {
        "Content-Type": "application/json"
    }
    req = requests.post(url, json=data, headers=headers)


def zh():
    a = '0123456789'
    b = [a[randint(0, 9)] for i in range(10)]
    return (''.join(b) + '@qq.com')


zhanghao = zh()
url1 = 'https://tmyun.cyou'
url2 = 'https://tmyun.cyou/api/v1/guest/comm/config'
url3 = 'https://tmyun.cyou/api/v1/passport/auth/register'
session = requests.Session()
a = session.get(url1)
b = session.get(url2)
headers = {
    'sec-ch-ua': r'"(Not(A:Brand";v="8", "Chromium";v="101"',
    'dnt': '1',
    'sec-ch-ua-mobile': r'?1',
    'user-agent': r'Mozilla/5.0 (Linux; Android 12; RMX2072) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.9999.0 Mobile Safari/537.36',
    'sec-ch-ua-platform': r'"Android"',
    'content-type': r'application/x-www-form-urlencoded',
    'accept': r'*/*',
    'origin': r'https://tmyun.cyou',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://tmyun.cyou/',
    'accept-language': 'zh-CN,zh;q=0.9'
}
data = {
    'email': zhanghao,
    'password': '1234qwer',
    'invite_code': '',
    'email_code': ''
}
c = session.post(url3, headers=headers, data=data)
msg = r'https://tmyunsub.xyz/api/v1/client/subscribe?token=' + c.json()['data']['token']
print(msg)
wxpusherts(msg)
